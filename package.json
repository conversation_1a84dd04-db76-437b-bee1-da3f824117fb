{"name": "vue-admin-beautiful", "version": "1.0.0", "author": "vue-admin-beautiful", "participants": [], "homepage": "https://chu1204505056.gitee.io/vue-admin-beautiful", "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:report": "vue-cli-service build --report", "globle": "npm install -g cnpm --registry=https://registry.npm.taobao.org&&cnpm i rimraf npm-check-updates nrm -g&&rimraf node_modules&&cnpm i", "lint": "vue-cli-service lint", "lint:style": "stylelint-config-prettier-check", "inspect": "vue-cli-service inspect", "template": "plop", "clear": "rimraf node_modules&&npm install --registry=https://registry.npm.taobao.org", "use:npm": "nrm use npm", "use:taobao": "nrm use taobao", "update": "ncu -u --reject sass-loader,sass&&cnpm i", "update:globle": "ncu -g --concurrency 10 --timeout 80000", "push": "start ./push.sh", "deploy": "start ./deploy.sh"}, "repository": {"type": "git", "url": "git+https://github.com/chuzhixin/vue-admin-beautiful.git"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "dependencies": {"axios": "^0.21.1", "chart.js": "^3.9.1", "chartjs-plugin-datasource-prometheus": "^1.0.11", "clipboard": "^2.0.8", "core-js": "^3.15.2", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "diff2html": "^3.4.17", "driver.js": "^0.9.8", "echarts": "^5.1.2", "el-table-infinite-scroll": "^2.0.2", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "jsencrypt": "^3.2.0", "lodash": "^4.17.21", "maptalks": "^0.49.4", "mapv": "^2.0.62", "md5": "^2.3.0", "mockjs": "^1.1.0", "moment": "^2.29.4", "momentjs": "^2.0.0", "nprogress": "^0.2.0", "prismjs": "^1.29.0", "qs": "^6.10.1", "screenfull": "^5.1.0", "sortablejs": "^1.13.0", "sql-formatter": "^15.3.1", "tencentcloud-sdk-nodejs-antiddos": "^4.0.924", "vab-icon": "^0.0.1", "validator": "^13.7.0", "vue": "^2.6.14", "vue-chartjs": "^4.1.1", "vue-count-to": "^1.0.13", "vue-drag-verify-img-chip": "^1.1.1", "vue-echarts": "6.0.0-rc.6", "vue-json-pretty": "^1.7.1", "vue-qrcode-reader": "^3.1.0", "vue-router": "^3.5.2", "vue-scrollto": "^2.20.0", "vue-seamless-scroll": "^1.1.23", "vue-sweetalert2": "^5.0.2", "vuedraggable": "^2.24.3", "vuex": "^3.6.2", "zx-count": "^0.3.7", "zx-layouts": "^0.6.25", "zx-magnifie": "^0.4.0", "zx-markdown-editor": "^0.0.2", "zx-player": "^1.0.2", "zx-quill": "^0.0.3", "zx-templates": "^0.0.26", "zx-verify": "^0.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.13", "@vue/cli-plugin-eslint": "^4.5.13", "@vue/cli-service": "^4.5.13", "@vue/composition-api": "^1.0.0-rc.12", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "babel-plugin-prismjs": "^2.1.0", "body-parser": "^1.19.0", "chalk": "^4.1.1", "chokidar": "^3.5.2", "eslint": "^7.29.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.12.1", "filemanager-webpack-plugin": "^6.1.4", "image-webpack-loader": "^7.0.1", "lint-staged": "^11.0.0", "plop": "^2.7.4", "prettier": "^2.8.8", "sass": "~1.32.13", "sass-loader": "^10.1.1", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.4.0", "svg-sprite-loader": "^6.0.9", "vue-prism": "^1.0.5", "vue-template-compiler": "^2.6.14", "webpackbar": "^4.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "vue-admin", "element-admin", "boilerplate", "admin-template", "management-system"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}}