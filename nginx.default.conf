server {
    listen       80;
    server_name  localhost;

    access_log  /data/log/nginx/access.log  main;
    error_log   /data/log/nginx/error.log;

    # 静态资源
    location / {
        root   /data/web;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # 前端代理
    location ^~ /后端服务名 {
        proxy_pass http://后端服务IP地址:8080;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Credentials: true;
        add_header Access-Control-Allow-Methods GET,POST,OPTIONS,PUT,DELETE;

        proxy_http_version 1.1;
        # 连接延时
        proxy_connect_timeout 3600s;
        proxy_read_timeout 3600s;
        proxy_send_timeout 3600s;
        # IP 穿透
        proxy_set_header        Host $proxy_host;
        proxy_set_header        X-Real-IP $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        # WebSocket 穿透
        proxy_set_header Origin "";
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}
}

