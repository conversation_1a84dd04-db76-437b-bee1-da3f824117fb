import Vue from 'vue'
import VueRouter from 'vue-router'
import Layout from '@/layouts'
import { publicPath, routerMode } from '@/config'
import IframeMD from '@/views/opsdocs/iframeMarkdown'

Vue.use(VueRouter)
export const constantRoutes = [
  {
    path: '/401',
    name: '401',
    component: () => import('@/views/401'),
    hidden: true,
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/404'),
    hidden: true,
  },
]

export const asyncRoutes = [
  {
    path: '/',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        name: 'Index',
        component: () => import('@/views/index/index'),
        meta: {
          title: '首页',
          icon: 'home',
          affix: true,
        },
      },
    ],
  },
  {
    path: '/toolbox',
    name: 'ToolBox',
    component: Layout,
    redirect: 'noRedirect',
    meta: { title: '工具', icon: 'toolbox', permissions: ['admin'] },
    children: [
      {
        path: 'commontool',
        name: 'CommonTool',
        component: () => import('@/views/toolbox/index'),
        meta: {
          title: '通用工具',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
      },
      {
        path: 'privacytool',
        name: 'PrivacyEnhancementTools',
        component: () => import('@/views/toolbox/pet'),
        meta: {
          title: '研发工具',
          requiredGroups: ['admins', 'dev', 'frt', 'test'],
        },
      },
      {
        path: 'diagtool',
        name: 'CSDiagTools',
        component: () => import('@/views/toolbox/diag'),
        meta: {
          title: '网络诊断查询',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'cs'],
        },
      },
    ],
  },
  {
    path: '/itsm',
    component: Layout,
    redirect: 'noRedirect',
    name: 'ITSM',
    meta: { title: '事故', icon: 'ambulance', permissions: ['admin'] },
    children: [
      {
        path: 'events',
        name: 'EventsList',
        component: () => import('@/views/itsm/events/index'),
        meta: {
          title: '事故管理',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
      },
      {
        path: 'eventlist',
        name: 'EventList',
        component: () => import('@/views/itsm/sla/index'),
        meta: {
          title: 'SLA统计',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
      },
    ],
  },
  {
    path: '/devops',
    component: Layout,
    redirect: 'noRedirect',
    name: 'DevOps',
    meta: { title: 'DevOps', icon: 'truck-monster', permissions: ['admin'] },
    children: [
      {
        path: 'workFlows/:flow_uuid',
        name: 'flowManagement',
        component: () =>
          import('@/views/devops/workFlows/subviews/flowMangement'),
        meta: {
          title: '版本工作流 ',
        },
        hidden: true,
      },
      // {
      //   path: 'upgradeFlow',
      //   name: 'upgradeFlowManagement',
      //   component: () => import('@/views/deployment/index'),
      //   meta: {
      //     title: '变更工作流(开发中)',
      //   },
      //   hidden: false,
      // },
      {
        path: 'upgrade/:upgrade_id',
        name: 'upgradeWizzard',
        component: () =>
          import('@/views/devops/upgrade/subviews/upgradeWizzard'),
        meta: {
          title: '变更向导',
        },
        hidden: true,
      },
      {
        path: 'releaseManage',
        name: 'releaseManagement',
        component: () => import('@/views/devops/workFlows/subviews/fmRelease'),
        meta: {
          title: '发版申请',
        },
        hidden: true,
      },
      {
        path: 'grayevent',
        name: 'grayeventManagement',
        component: () => import('@/views/devops/gray/dashboard'),
        meta: {
          title: '版本控制',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'sandboxes',
        name: 'sandBoxes',
        component: () => import('@/views/devops/sandBoxes/index'),
        meta: {
          title: '沙盒环境',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
      },
      {
        path: 'presm',
        name: 'presm',
        component: () => import('@/views/devops/presm/index'),
        meta: {
          title: '预发布环境',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
      },
      {
        path: 'sandboxes/portbind',
        name: 'portBind',
        component: () => import('@/views/devops/sandBoxes/tool/portbind/index'),
        meta: {
          title: '端口映射',
        },
        hidden: true,
      },
      {
        path: 'sandboxes/domainbind',
        name: 'domainBind',
        component: () =>
          import('@/views/devops/sandBoxes/tool/domainbind/index'),
        meta: {
          title: '域名映射',
        },
        hidden: true,
      },
      {
        path: 'sandboxManagement/:sbx_uuid',
        name: 'sandboxManagement',
        component: () =>
          import('@/views/devops/sandBoxes/subviews/sandboxMangement'),
        meta: {
          title: '沙盒管理',
        },
        hidden: true,
      },
      {
        path: 'presm/:pre_uuid',
        name: 'preManagement',
        component: () => import('@/views/devops/presm/subviews/preMangement'),
        meta: {
          title: '预发布管理',
        },
        hidden: true,
      },
      {
        path: 'configCenter/:sbx_uuid',
        name: 'configCenter',
        component: () =>
          import('@/views/devops/sandBoxes/subviews/configCenter'),
        meta: {
          title: '配置管理',
        },
        hidden: true,
      },
      {
        path: 'weakNetTest/:sbx_uuid',
        name: 'weakNetTest',
        component: () =>
          import('@/views/devops/sandBoxes/subviews/weakNetTest'),
        meta: {
          title: '弱网测试',
        },
        hidden: true,
      },
      {
        path: 'whitelist',
        name: 'uriWhitelist',
        component: () => import('@/views/devops/uriWhitelist/index'),
        meta: {
          title: 'URL白名单',
          requiredGroups: ['admins', 'dev', 'frt', 'test'],
        },
      },
      {
        path: 'projects',
        name: 'ProjectConfig',
        component: () => import('@/views/devops/projectConfig/index'),
        meta: {
          title: '微服务管理',
          requiredGroups: ['admins', 'dev', 'frt', 'test'],
        },
      },
      {
        path: 'fileupload',
        name: 'FileUpload',
        component: () => import('@/views/devops/fileUpload/index'),
        meta: {
          title: '运维文件上传',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt', 'cs'],
        },
      },
      {
        path: 'cloudaccount',
        name: 'CloudAccount',
        component: () => import('@/views/devops/accounts/accountManage'),
        meta: {
          title: 'API密钥管理',
          requiredGroups: ['admins', 'dev', 'frt', 'test'],
        },
      },
    ],
  },
  {
    path: '/devmonitor',
    component: Layout,
    redirect: 'noRedirect',
    name: 'DevMonitor',
    meta: {
      title: '监控',
      icon: 'chalkboard',
      permissions: ['admin'],
    },
    children: [
      {
        path: 'panicManage',
        name: 'PanicManage',
        component: () => import('@/views/itsm/panic/panicManage'),
        meta: { title: '熔断和降级' },
        hidden: true,
      },
      {
        path: 'alterCenter',
        name: 'AlertCenter',
        component: () => import('@/views/monitoring/alertcenter/index'),
        meta: { title: '告警中心', icon: 'monitor' },
        hidden: false,
      },
      {
        path: 'errcode',
        name: 'errCode',
        component: () => import('@/views/monitoring/kafka/codeIndex'),
        meta: {
          title: '错误码监控',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'apigwstat',
        name: 'apigwStat',
        component: () => import('@/views/monitoring/apigateway/index'),
        meta: {
          title: 'RPC监控',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'elkmonitor',
        name: 'elkMonitor',
        component: () => import('@/views/monitoring/elk/elkView'),
        meta: {
          title: 'HTTP监控',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'event',
        name: 'eventNotify',
        component: () => import('@/views/monitoring/kafka/eventIndex'),
        meta: {
          title: '事件上报',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'businessdata',
        name: 'businessData',
        component: () => import('@/views/monitoring/business/dashboard.vue'),
        meta: {
          title: '业务监控',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'business/alert',
        name: 'businessAlert',
        component: () =>
          import('@/views/monitoring/business/alertDashboard.vue'),
        hidden: true,
        meta: {
          title: '业务告警图',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
      },
      {
        path: 'weeklyreport',
        name: 'weeklyreport',
        meta: {
          title: '成本周报',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        component: () => import('@/views/monitoring/report/WeeklyMain.vue'),
        hidden: false,
      },
      {
        path: 'countryside',
        name: 'countrySide',
        component: () => import('@/views/itsm/ruisu/index.vue'),
        meta: {
          title: '海外流量',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: true,
      },
      {
        path: 'rislog',
        name: 'risLog',
        component: () => import('@/views/monitoring/ris/ImageSearchLogs.vue'),
        meta: {
          title: '图搜日志分析',
          requiredGroups: ['admins', 'dev'],
        },
        hidden: false,
      },
      {
        path: 'logcenter',
        name: 'logCenter',
        component: () => import('@/views/logcenter/LogTrackingSystem.vue'),
        meta: {
          title: '全链路日志跟踪',
          requiredGroups: ['admins'],
        },
        hidden: false,
      },

      {
        path: 'xlogcrypt',
        name: 'xLogCrypt',
        component: () => import('@/views/monitoring/xlogcrypt/index'),
        meta: {
          title: 'xlog日志分析',
          requiredGroups: ['admins', 'dev', 'frt', 'test', 'pdt'],
        },
        hidden: false,
      },
      {
        path: 'xlogcrypt/:file_id',
        name: 'xLogCryptInfo',
        component: () =>
          import('@/views/monitoring/xlogcrypt/components/xlogCryptInfo'),
        meta: {
          title: 'Xlog日志详情',
        },
        hidden: true,
      },
    ],
  },
  {
    path: '/security',
    component: Layout,
    redirect: 'noRedirect',
    name: 'Security',
    meta: { title: '安全', icon: 'shield-alt', permissions: ['admin'] },
    children: [
      {
        path: 'index',
        name: 'Index',
        component: () => import('@/views/security/index.vue'),
        meta: {
          title: '反爬与流控',
          icon: 'traffic-light',
          requiredGroups: ['admins'],
        },
        mode: 'history',
        hidden: false,
      },
      // {
      //   path: 'base/config',
      //   name: 'BaseConfig',
      //   component: () => import('@/views/security/base/index.vue'),
      //   meta: {
      //     title: '基础配置',
      //     icon: 'cog',
      //     requiredGroups: ['admins'],
      //   },
      //   hidden: true,
      // },
      // {
      //   path: 'ipblack',
      //   name: 'IpBlackList',
      //   component: () => import('@/views/security/ipblack/index.vue'),
      //   meta: {
      //     title: 'IP黑名单',
      //     requiredGroups: ['admins'],
      //   },
      //   hidden: true,
      // },
      // {
      //   path: 'antibot',
      //   component: Layout,
      //   name: 'AntiBot',
      //   meta: {
      //     title: 'AntiBot管理',
      //     icon: 'robot',
      //   },
      //   hidden: true,
      //   children: [
      //     {
      //       path: 'whitelist',
      //       name: 'AntiBotWhitelist',
      //       component: () => import('@/views/security/antibot/whitelist.vue'),
      //       meta: {
      //         title: 'IP白名单',
      //         requiredGroups: ['admins'],
      //       },
      //       hidden: true,
      //     },
      //     {
      //       path: 'restricteduris',
      //       name: 'AntiRestrictedUris',
      //       component: () =>
      //         import('@/views/security/antibot/restricteduris.vue'),
      //       meta: {
      //         title: '核心API',
      //         requiredGroups: ['admins'],
      //       },
      //       hidden: true,
      //     },
      //   ],
      // },
      // {
      //   path: 'flowctrl',
      //   component: Layout,
      //   name: 'FlowControl',
      //   meta: { title: '流控管理', icon: 'tachometer-alt' },
      //   hidden: true,
      //   children: [
      //     {
      //       path: 'config',
      //       name: 'FlowControlConfig',
      //       component: () =>
      //         import('@/views/security/flowcontrol/flowctrlConfig.vue'),
      //       meta: {
      //         title: '流控配置',
      //         requiredGroups: ['admins'],
      //       },
      //       hidden: true,
      //     },
      //     // {
      //     //   path: "rule",
      //     //   name: "FlowControlRule",
      //     //   component: () => import("@/views/security/flowcontrol/flowctrlRule.vue"),
      //     //   meta: {
      //     //     title: "流控规则",
      //     //     requiredGroups: ["admins"]
      //     //   },
      //     //   hidden: true
      //     // }
      //   ],
      // },
      {
        path: 'panicManage',
        name: 'PanicManage',
        component: () => import('@/views/security/panic/panicManage'),
        meta: {
          title: '熔断降级',
          icon: 'thermometer-full',
          requiredGroups: ['admins', 'dev', 'frt', 'test'],
        },
        hidden: false,
      },
    ],
  },
  {
    path: '/personnelManagement',
    component: Layout,
    redirect: 'noRedirect',
    name: 'PersonnelManagement',
    meta: { title: '配置', icon: 'users-cog', permissions: ['admin'] },
    children: [
      {
        path: 'userManagement',
        name: 'UserManagement',
        component: () =>
          import('@/views/personnelManagement/userManagement/index'),
        meta: {
          title: '用户',
          requiredGroups: ['opsadmin', 'admins'],
        },
      },
      {
        path: 'roleManagement',
        name: 'RoleManagement',
        component: () =>
          import('@/views/personnelManagement/roleManagement/index'),
        meta: {
          title: '角色管理',
        },
        hidden: true,
      },
    ],
  },
  {
    path: '/opsdocs',
    component: Layout,
    redirect: 'noRedirect',
    children: [
      {
        path: 'opsdocs',
        name: 'opsDocs',
        component: IframeMD,
        meta: {
          title: '文档',
          icon: 'book',
          requiredGroups: ['admins'],
        },
        props: true,
      },
    ],
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true,
  },
]

const router = new VueRouter({
  base: publicPath,
  mode: routerMode,
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRoutes.map((route) => {
    if (route.meta && route.meta.title) {
      route.meta.title =
        typeof route.meta.title === 'function'
          ? route.meta.title
          : () => route.meta.title
    }
    return route
  }),
})

export function resetRouter() {
  location.reload()
}

export default router
