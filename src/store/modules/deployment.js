import {
  getPersonnel,
  getServices,
  saveBasicInfo,
  saveServiceOrder,
  triggerBuild,
  getBuildStatus,
  deployToPreRelease,
  getPreReleaseStatus,
  getReleaseQueue,
  updateQueueItem,
  deployToProduction,
  getDeploymentStatus,
  pauseDeployment,
  resumeDeployment,
  rollbackDeployment,
  getGrayscaleStrategies,
  saveGrayscaleStrategy,
  deployToGrayscale,
  promoteGrayscaleToProd,
  getReleaseHistory,
} from '@/api/deployment'

const state = {
  activeStep: 0,
  deploymentId: null,
  basicInfo: {
    releaseName: '',
    expectedReleaseDate: '',
    personnel: [],
    changelog: '',
    bugfixes: [],
  },
  selectedServices: [],
  serviceOrder: [],
  buildInfo: {
    buildId: null,
    status: '',
    progress: 0,
    logs: '',
    images: [],
  },
  preRelease: {
    deploymentId: null,
    status: '',
    progress: 0,
    logs: '',
  },
  releaseQueue: [],
  productionDeployment: {
    deploymentId: null,
    status: '',
    progress: 0,
    logs: '',
  },
  releaseHistory: [],
  grayscale: {
    strategies: [],
    selectedStrategy: null,
    deploymentId: null,
    status: '',
    progress: 0,
  },
  personnelList: [],
  servicesList: [],
  deploymentType: 'standard', // standard or grayscale
}

const mutations = {
  SET_ACTIVE_STEP(state, step) {
    state.activeStep = step
  },
  SET_DEPLOYMENT_ID(state, id) {
    state.deploymentId = id
  },
  SET_BASIC_INFO(state, basicInfo) {
    state.basicInfo = { ...basicInfo }
  },
  UPDATE_BASIC_INFO(state, { key, value }) {
    state.basicInfo[key] = value
  },
  SET_SELECTED_SERVICES(state, services) {
    state.selectedServices = services
  },
  SET_SERVICE_ORDER(state, order) {
    state.serviceOrder = order
  },
  SET_BUILD_INFO(state, buildInfo) {
    state.buildInfo = { ...buildInfo }
  },
  UPDATE_BUILD_STATUS(state, { status, progress, logs, images }) {
    state.buildInfo.status = status
    state.buildInfo.progress = progress
    state.buildInfo.logs = logs
    if (images) state.buildInfo.images = images
  },
  SET_PRE_RELEASE(state, preRelease) {
    state.preRelease = { ...preRelease }
  },
  UPDATE_PRE_RELEASE_STATUS(state, { status, progress, logs }) {
    state.preRelease.status = status
    state.preRelease.progress = progress
    state.preRelease.logs = logs
  },
  SET_RELEASE_QUEUE(state, queue) {
    state.releaseQueue = queue
  },
  SET_PRODUCTION_DEPLOYMENT(state, deployment) {
    state.productionDeployment = { ...deployment }
  },
  UPDATE_PRODUCTION_STATUS(state, { status, progress, logs }) {
    state.productionDeployment.status = status
    state.productionDeployment.progress = progress
    state.productionDeployment.logs = logs
  },
  SET_RELEASE_HISTORY(state, history) {
    state.releaseHistory = history
  },
  SET_GRAYSCALE_STRATEGIES(state, strategies) {
    state.grayscale.strategies = strategies
  },
  SET_SELECTED_GRAYSCALE_STRATEGY(state, strategy) {
    state.grayscale.selectedStrategy = strategy
  },
  SET_GRAYSCALE_DEPLOYMENT(state, deployment) {
    state.grayscale.deploymentId = deployment.deploymentId
    state.grayscale.status = deployment.status
    state.grayscale.progress = deployment.progress
  },
  SET_PERSONNEL_LIST(state, personnel) {
    state.personnelList = personnel
  },
  SET_SERVICES_LIST(state, services) {
    state.servicesList = services
  },
  SET_DEPLOYMENT_TYPE(state, type) {
    state.deploymentType = type
  },
}

const actions = {
  async fetchPersonnel({ commit }) {
    try {
      const response = await getPersonnel()
      commit('SET_PERSONNEL_LIST', response.data)
      return response.data
    } catch (error) {
      console.error('获取人员列表失败:', error)
      throw error
    }
  },

  async fetchServices({ commit }) {
    try {
      const response = await getServices()
      commit('SET_SERVICES_LIST', response.data)
      return response.data
    } catch (error) {
      console.error('获取服务列表失败:', error)
      throw error
    }
  },

  async submitBasicInfo({ commit, state }) {
    try {
      const response = await saveBasicInfo(state.basicInfo)
      commit('SET_DEPLOYMENT_ID', response.data.id)
      return response.data
    } catch (error) {
      console.error('保存基本信息失败:', error)
      throw error
    }
  },

  async submitServiceOrder({ commit, state }) {
    try {
      const response = await saveServiceOrder({
        deploymentId: state.deploymentId,
        services: state.serviceOrder,
      })
      return response.data
    } catch (error) {
      console.error('保存服务顺序失败:', error)
      throw error
    }
  },

  async startBuild({ commit, state }) {
    try {
      const response = await triggerBuild({
        deploymentId: state.deploymentId,
        services: state.selectedServices,
      })
      commit('SET_BUILD_INFO', {
        buildId: response.data.buildId,
        status: 'pending',
        progress: 0,
        logs: '',
        images: [],
      })
      return response.data
    } catch (error) {
      console.error('触发构建失败:', error)
      throw error
    }
  },

  async checkBuildStatus({ commit, state }) {
    if (!state.buildInfo.buildId) return

    try {
      const response = await getBuildStatus(state.buildInfo.buildId)
      commit('UPDATE_BUILD_STATUS', response.data)
      return response.data
    } catch (error) {
      console.error('获取构建状态失败:', error)
      throw error
    }
  },

  async deployToPreRelease({ commit, state }) {
    try {
      const response = await deployToPreRelease({
        deploymentId: state.deploymentId,
        images: state.buildInfo.images,
      })
      commit('SET_PRE_RELEASE', {
        deploymentId: response.data.deploymentId,
        status: 'pending',
        progress: 0,
        logs: '',
      })
      return response.data
    } catch (error) {
      console.error('部署到预发布环境失败:', error)
      throw error
    }
  },

  async checkPreReleaseStatus({ commit, state }) {
    if (!state.preRelease.deploymentId) return

    try {
      const response = await getPreReleaseStatus(state.preRelease.deploymentId)
      commit('UPDATE_PRE_RELEASE_STATUS', response.data)
      return response.data
    } catch (error) {
      console.error('获取预发布状态失败:', error)
      throw error
    }
  },

  async fetchReleaseQueue({ commit }) {
    try {
      const response = await getReleaseQueue()
      commit('SET_RELEASE_QUEUE', response.data.list)
      return response.data
    } catch (error) {
      console.error('获取发版队列失败:', error)
      throw error
    }
  },

  async updateQueueItemStatus({ commit, dispatch }, { id, data }) {
    try {
      await updateQueueItem(id, data)
      dispatch('fetchReleaseQueue')
    } catch (error) {
      console.error('更新队列项状态失败:', error)
      throw error
    }
  },

  async deployToProduction({ commit, state }) {
    try {
      const response = await deployToProduction({
        deploymentId: state.deploymentId,
        images: state.buildInfo.images,
        type: state.deploymentType,
      })
      commit('SET_PRODUCTION_DEPLOYMENT', {
        deploymentId: response.data.deploymentId,
        status: 'pending',
        progress: 0,
        logs: '',
      })
      return response.data
    } catch (error) {
      console.error('部署到生产环境失败:', error)
      throw error
    }
  },

  async checkProductionDeploymentStatus({ commit, state }) {
    if (!state.productionDeployment.deploymentId) return

    try {
      const response = await getDeploymentStatus(
        state.productionDeployment.deploymentId
      )
      commit('UPDATE_PRODUCTION_STATUS', response.data)
      return response.data
    } catch (error) {
      console.error('获取生产部署状态失败:', error)
      throw error
    }
  },

  async pauseProductionDeployment({ state }) {
    if (!state.productionDeployment.deploymentId) return

    try {
      return await pauseDeployment(state.productionDeployment.deploymentId)
    } catch (error) {
      console.error('暂停部署失败:', error)
      throw error
    }
  },

  async resumeProductionDeployment({ state }) {
    if (!state.productionDeployment.deploymentId) return

    try {
      return await resumeDeployment(state.productionDeployment.deploymentId)
    } catch (error) {
      console.error('恢复部署失败:', error)
      throw error
    }
  },

  async rollbackProductionDeployment({ state }) {
    if (!state.productionDeployment.deploymentId) return

    try {
      return await rollbackDeployment(state.productionDeployment.deploymentId)
    } catch (error) {
      console.error('回滚部署失败:', error)
      throw error
    }
  },

  async fetchGrayscaleStrategies({ commit }) {
    try {
      const response = await getGrayscaleStrategies()
      commit('SET_GRAYSCALE_STRATEGIES', response.data)
      return response.data
    } catch (error) {
      console.error('获取灰度策略失败:', error)
      throw error
    }
  },

  async saveGrayscaleStrategy({ commit }, strategy) {
    try {
      const response = await saveGrayscaleStrategy(strategy)
      return response.data
    } catch (error) {
      console.error('保存灰度策略失败:', error)
      throw error
    }
  },

  async deployToGrayscale({ commit, state }) {
    try {
      const response = await deployToGrayscale({
        deploymentId: state.deploymentId,
        images: state.buildInfo.images,
        strategyId: state.grayscale.selectedStrategy.id,
      })
      commit('SET_GRAYSCALE_DEPLOYMENT', {
        deploymentId: response.data.deploymentId,
        status: 'pending',
        progress: 0,
      })
      return response.data
    } catch (error) {
      console.error('部署到灰度环境失败:', error)
      throw error
    }
  },

  async promoteGrayscaleToProd({ state }) {
    if (!state.grayscale.deploymentId) return

    try {
      return await promoteGrayscaleToProd(state.grayscale.deploymentId)
    } catch (error) {
      console.error('将灰度环境提升到生产环境失败:', error)
      throw error
    }
  },

  async fetchReleaseHistory({ commit }, params) {
    try {
      const response = await getReleaseHistory(params)
      commit('SET_RELEASE_HISTORY', response.data.list)
      return response.data
    } catch (error) {
      console.error('获取发版历史失败:', error)
      throw error
    }
  },

  setActiveStep({ commit }, step) {
    commit('SET_ACTIVE_STEP', step)
  },

  setDeploymentType({ commit }, type) {
    commit('SET_DEPLOYMENT_TYPE', type)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
