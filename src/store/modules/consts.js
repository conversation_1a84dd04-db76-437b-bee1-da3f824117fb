/**
 * <AUTHOR>
 * @description 用户列表和显示名
 */

const state = () => ({
  allUserList: [], // 根状态
  applicantDisplayNameMap: {},
})

const mutations = {
  setAllUserList(state, userList) {
    state.allUserList = userList
  },
  setDisplayNameMap(state, userList) {
    state.applicantDisplayNameMap = userList
  },
}
const actions = {
  updateAllUserList({ commit }, userList) {
    commit('setAllUserList', userList)
  },
  updateDisplayNameMap({ commit }, displayNameMap) {
    commit('setDisplayNameMap', displayNameMap)
  },
}
export default { state, mutations, actions }
