import request from '@/utils/request'

export function getBizlines() {
  return request({
    url: '/tapd/api/v1/bizline/query',
    method: 'get',
  })
}

export function getIterations(data) {
  return request({
    url: '/tapd/api/v1/query/iteration',
    method: 'post',
    data,
  })
}

//缺陷统计
//https://yapi.in.szwego.com/project/192/interface/api/13949
export function getBugs(param) {
  return request({
    url: '/tapd/api/v1/bugs/query?' + param,
    method: 'get',
  })
}
