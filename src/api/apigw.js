import request from '@/utils/request'

export function getOverview(params) {
  return request({
    url: '/devmonitor/api/v1/apigw/overview',
    method: 'get',
    params,
  })
}

export function getTopN(params) {
  return request({
    url: '/devmonitor/api/v1/apigw/topn',
    method: 'get',
    params,
  })
}

export function getDetails(params) {
  return request({
    url: '/devmonitor/api/v1/apigw/details',
    method: 'get',
    params,
  })
}

export function getTimeseries(params) {
  return request({
    url: '/devmonitor/api/v1/apigw/timeseries',
    method: 'get',
    params,
  })
}

export function getFilterOptions(params) {
  return request({
    url: '/devmonitor/api/v1/apigw/filters',
    method: 'get',
    params,
  })
}

export function getTraceDetails(params) {
  return request({
    url: '/devmonitor/api/v1/apigw/trace-details',
    method: 'get',
    params,
  })
}
