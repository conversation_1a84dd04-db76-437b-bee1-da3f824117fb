import request from '@/utils/request' // 假设你有统一的request工具

// 获取访问日志列表
export function getAccessLogs(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    data: {
      query: params.query,
      sort: [{ '@timestamp': { order: 'desc' } }],
      size: params.size || 100,
      from: params.from || 0,
      _source: [
        '@timestamp',
        'verb',
        'npath',
        'status',
        'request_time',
        'remote_addr',
        'http_user_agent',
        'nparam',
        'request_body',
        'x_albumid',
        'x_req_id',
        'x_podid',
        'txid',
        'client_ip',
        'useragent.original',
        'body_bytes_sent',
      ],
    },
  })
}

// 获取统计数据
export function getLogStatistics(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    data: {
      query: params.query,
      aggs: {
        status_stats: {
          terms: {
            field: 'status',
            size: 10,
          },
        },
        response_time_stats: {
          histogram: {
            field: 'request_time',
            interval: 0.5,
            min_doc_count: 1,
          },
        },
        top_endpoints: {
          terms: {
            field: 'npath.keyword',
            size: 10,
          },
          aggs: {
            avg_response_time: {
              avg: {
                field: 'request_time',
              },
            },
          },
        },
        hourly_trend: {
          date_histogram: {
            field: '@timestamp',
            calendar_interval: 'hour',
          },
        },
        avg_response_time: {
          avg: {
            field: 'request_time',
          },
        },
        recent_errors: {
          filter: {
            range: {
              status: {
                gte: 400,
              },
            },
          },
          aggs: {
            latest_errors: {
              top_hits: {
                sort: [{ '@timestamp': { order: 'desc' } }],
                size: 5,
                _source: [
                  '@timestamp',
                  'request_uri',
                  'status',
                  'request_time',
                  'x_request_id',
                ],
              },
            },
          },
        },
      },
    },
  })
}

// 获取原始日志
export function getRawLog(logId) {
  return request({
    url: `/logcenter/logs/raw/${logId}`,
    method: 'get',
  })
}

// 获取调用链数据
export function getTraceChain(requestId, txId) {
  return request({
    url: '/logcenter/logs/trace/chain',
    method: 'get',
    params: {
      requestId,
      txId,
    },
  })
}

// 获取服务日志
export function getServiceLogs(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    data: {
      query: params.query,
    },
  })
}

// 获取图像搜索日志列表
export function getImageSearchLogs(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    data: {
      query: params.query,
      sort: params.sort || [{ '@timestamp': { order: 'desc' } }],
      size: params.size || 100,
      from: params.from || 0,
      _source: [
        '@timestamp',
        'status',
        'response_time',
        'request.albumId',
        'request.userAlbumId',
        'request.dbName',
        'request.url',
        'request.locScoreMin',
        'request.sel1',
        'request.sel2',
        'response.errcode',
        'response.result',
        'response.status',
        'response.success',
        'result_size',
        'message',
        'method',
        'request_path',
        'error_code',
      ],
    },
  })
}

// 获取图像搜索统计数据
export function getImageSearchStatistics(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    data: {
      query: params.query,
      size: 0,
      aggs: {
        // 状态码分布
        status_stats: {
          terms: {
            field: 'status',
            size: 10,
          },
        },
        // 响应时间分布
        response_time_stats: {
          histogram: {
            field: 'response_time',
            interval: 0.5,
            min_doc_count: 1,
          },
        },
        // 平均响应时间
        avg_response_time: {
          avg: {
            field: 'response_time',
          },
        },
        // 数据库分布
        db_distribution: {
          terms: {
            field: 'request.dbName.keyword',
            size: 10,
          },
        },
        // Album ID 统计
        album_stats: {
          terms: {
            field: 'request.albumId.keyword',
            size: 20,
          },
          aggs: {
            avg_response_time: {
              avg: {
                field: 'response_time',
              },
            },
            avg_result_count: {
              avg: {
                field: 'result_size',
              },
            },
          },
        },
        // 每小时趋势
        hourly_trend: {
          date_histogram: {
            field: '@timestamp',
            calendar_interval: 'hour',
            min_doc_count: 0,
          },
          aggs: {
            success_count: {
              filter: {
                term: {
                  status: 200,
                },
              },
            },
            error_count: {
              filter: {
                range: {
                  status: {
                    gte: 400,
                  },
                },
              },
            },
          },
        },
        // 最近的错误
        recent_errors: {
          filter: {
            range: {
              status: {
                gte: 400,
              },
            },
          },
          aggs: {
            latest_errors: {
              top_hits: {
                sort: [{ '@timestamp': { order: 'desc' } }],
                size: 10,
                _source: [
                  '@timestamp',
                  'request.url',
                  'request.albumId',
                  'status',
                  'response_time',
                  'message',
                ],
              },
            },
          },
        },
        // 相似度分布（分析搜索结果质量）
        similarity_distribution: {
          nested: {
            path: 'response.result',
          },
          aggs: {
            similarity_ranges: {
              range: {
                field: 'response.result.similarity',
                ranges: [
                  { key: 'low', to: 0.2 },
                  { key: 'medium', from: 0.2, to: 0.3 },
                  { key: 'high', from: 0.3 },
                ],
              },
            },
          },
        },
      },
    },
  })
}

// 获取单个日志详情
export function getImageSearchLogDetail(params) {
  return request({
    url: `/logcenter/${params.index}/_doc/${params.id}`,
    method: 'get',
  })
}

// 获取原始日志内容
export function getImageSearchRawLog(logId, index) {
  return request({
    url: `/logcenter/${index}/_doc/${logId}?format=json`,
    method: 'get',
  })
}

// 批量获取相关日志（通过request_id）
export function getRelatedLogs(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    data: {
      query: {
        bool: {
          must: [
            {
              match: {
                x_req_id: params.requestId,
              },
            },
            {
              range: {
                '@timestamp': {
                  gte: params.startTime,
                  lte: params.endTime,
                },
              },
            },
          ],
        },
      },
      sort: [{ '@timestamp': { order: 'asc' } }],
      size: 1000,
    },
  })
}

// 导出日志数据
export function exportImageSearchLogs(params) {
  return request({
    url: `/logcenter/${params.indices}/_search`,
    method: 'post',
    responseType: 'blob',
    data: {
      query: params.query,
      sort: [{ '@timestamp': { order: 'desc' } }],
      size: params.size || 10000,
      _source: params.fields || [
        '@timestamp',
        'status',
        'response_time',
        'request.albumId',
        'request.url',
        'response.result',
      ],
    },
  })
}
