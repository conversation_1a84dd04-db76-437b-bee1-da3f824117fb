import request from '@/utils/request'

const baseUrlXlog = '/xlog/api/v1'

export function postUploadXlogFile(formData) {
  return request({
    url: baseUrlXlog + '/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 120000, // 设置超时时间为 120秒 (120000毫秒)
  })
}

export function postDownloadXLogFile(file_id, filename) {
  const params = {
    filename: filename,
  }
  return request({
    url: `${baseUrlXlog}/download/${file_id}`,
    method: 'post',
    params: params,
    responseType: 'blob',
    timeout: 120000, // 设置超时时间为 120秒 (120000毫秒)
  })
}

export function getXlogList(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlXlog + '/query/list',
    method: 'get',
    params: params,
  })
}

export function getXLogDetailsList(param, filters, file_id) {
  const params = {
    ...param,
  }
  return request({
    url: `${baseUrlXlog}/details/${file_id}/group/by`,
    method: 'post',
    params: params,
    data: JSON.stringify(filters),
    timeout: 120000, // 设置超时时间为 120秒 (120000毫秒)
  })
}

export function getXLogFilters(file_id) {
  return request({
    url: `${baseUrlXlog}/details/${file_id}/filters`,
    method: 'get',
  })
}
