import request from '@/utils/request'

const baseUrlWaf = '/security/api/v1/waf'
const baseUrlDdos = '/security/api/v1/ddos'

export function gettableColumns(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/query/table/column',
    method: 'GET',
    params: params,
  })
}

export function getWafAttackDetails(data) {
  const params = {
    ...data,
  }

  return request({
    url: baseUrlWaf + '/attack/details',
    method: 'get',
    params: params,
  })
}

export function getWafAttackByIp(data) {
  const params = {
    ...data,
  }

  return request({
    url: baseUrlWaf + '/attack/aggregated/by/ip',
    method: 'get',
    params: params,
  })
}

export function getWafAttackByType(data) {
  const params = {
    ...data,
  }

  return request({
    url: baseUrlWaf + '/attack/aggregated/by/type',
    method: 'get',
    params: params,
  })
}

export function getBotIpWhiteList(data) {
  const params = {
    ...data,
  }

  return request({
    url: baseUrlWaf + '/bot/ip/whitelist',
    method: 'get',
    params: params,
    timeout: 120000, // 设置超时时间为 120秒 (120000毫秒)
  })
}

export function postBotIpWhiteList(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/bot/ip/whitelist',
    method: 'post',
    data,
  })
}

export function getBotRestrictedUris(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/bot/restricted/uris',
    method: 'get',
    params: data,
  })
}

export function postBotRestrictedUris(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/bot/restricted/uris',
    method: 'post',
    data,
  })
}

// /waf

export function getBaseConfig(data) {
  // 查询 get 请求
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/base/config',
    method: 'get',
    params: data,
  })
}

export function postBaseConfig(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/base/config',
    method: 'post',
    data,
  })
}

export function getFlowctrlConfig(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/flowctrl/config',
    method: 'get',
    params: data,
  })
}

export function postFlowctrlConfig(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/flowctrl/config',
    method: 'post',
    data,
  })
}

export function getFlowctrlRule(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/flowctrl/rule',
    method: 'get',
    params: data,
  })
}

export function postFlowctrlRule(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/flowctrl/rule',
    method: 'post',
    data,
  })
}

export function getSuspIpList(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/susp/captcha/scan',
    method: 'get',
    params: params,
    timeout: 120000,
  })
}

export function getSuspIp(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/susp/ip',
    method: 'get',
    params: params,
  })
}

export function postSuspIp(data) {
  return request({
    url: baseUrlWaf + '/susp/ip',
    method: 'post',
    data,
  })
}

export function deleteSuspIp(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/susp/ip',
    method: 'delete',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
  })
}

export function getCaptchaPass(data) {
  const params = {
    ...data,
  }
  return request({
    url: baseUrlWaf + '/captcha/pass',
    method: 'get',
    params: params,
  })
}

export function getProDomain(data) {
  const params = {
    ...data,
  }
  return request({
    url: `${baseUrlDdos}/query`,
    method: 'GET',
    params: params,
  })
}

export function postProDomain(data, domain) {
  return request({
    url: `${baseUrlDdos}/${domain}/add`,
    method: 'POST',
    data: data,
  })
}

export function putProDomain(data, domain) {
  return request({
    url: `${baseUrlDdos}/${domain}/change`,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: JSON.stringify(data),
  })
}

export function deleteProDomain(data, domain) {
  return request({
    url: `${baseUrlDdos}/${domain}/change`,
    method: 'DELETE',
    data: data,
  })
}
