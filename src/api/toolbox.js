// 工具箱相关接口信息
// author： wa<PERSON><PERSON><PERSON>, dongxin

import request from '@/utils/request'

export function getIPsService(ips) {
  return request({
    url: '/user/api/v1/ipsearch/pod?ip=' + ips,
    method: 'get',
  })
}

export function getQueryip(data) {
  return request({
    url: '/user/api/v1/ipsearch?ip=' + data,
    method: 'get',
  })
}

export function getNginxroute(data) {
  return request({
    url: '/user/api/v1/nginxrouter?uri=' + data,
    method: 'get',
  })
}

export function getSkywalkingSample() {
  return request({
    url: '/ci/api/v1/get_skywalking_sample',
    method: 'get',
  })
}

export function setSkywalkingSample(data) {
  return request({
    url: '/ci/api/v1/set_skywalking_sample',
    method: 'post',
    data,
  })
}

export function cleanMaven(data) {
  return request({
    url: '/toolbox/api/v1/mavenclean',
    method: 'post',
    data,
  })
}

export function getJenkinsMaven(data) {
  return request({
    url: '/toolbox/api/v1/client/jenkins',
    method: 'post',
    data,
  })
}

export function OpsReport(data) {
  return request({
    url: '/report/api/v1/oa?' + data,
    method: 'get',
  })
}

export function decodeAlbumID(data) {
  return request({
    url: 'user/api/v1/decodealbum?value=' + data,
    method: 'get',
  })
}

export function decodeAlbumIDs(data) {
  return request({
    url: '/user/api/v1/decodealbum',
    method: 'POST',
    data,
  })
}

export function getIPsCity(data) {
  return request({
    url: '/user/api/v1/ip2city',
    method: 'post',
    data,
  })
}

export function getOfficeIp() {
  return request({
    url: '/user/api/v1/getOfficeIp',
    method: 'get',
  })
}

export function aesDecrypt(data) {
  return request({
    url: '/toolbox/api/v1/aesencrypt?value=' + data,
    method: 'get',
  })
}

export function cnfTypeService(data) {
  return request({
    url: '/toolbox/api/v1/type/service',
    method: 'post',
    data,
  })
}

export function saveExclude(data) {
  return request({
    url: '/toolbox/api/v1/save/exclude/conf',
    method: 'post',
    data,
  })
}

export function ComplaintInfo(data) {
  const params = {
    ...data,
  }
  return request({
    url: '/file/api/v1/download/complaint/info',
    method: 'post',
    data: params,
  })
}

export function PostMongoReloadNode(data) {
  const params = {
    ...data,
  }
  console.log('PostMongoReloadNode：', params)
  return request({
    url: '/toolbox/api/v1/reload/mongodb/node',
    method: 'post',
    data: params,
  })
}

export function getDiagData(id) {
  return request({
    url: '/toolbox/api/v1/diag/' + id,
    method: 'get',
  })
}
