import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/user/api/v1/nginx/geturiwhilelist',
    method: 'post',
    data,
  })
}

export function doSave(data) {
  return request({
    url: '/user/api/v1/nginx/uriwhilelist',
    method: 'post',
    data,
  })
}

export function doUpdate(data) {
  return request({
    url: '/user/api/v1/nginx/audituriwhilelist',
    method: 'post',
    data,
  })
}
