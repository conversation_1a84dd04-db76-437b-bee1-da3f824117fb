import request from '@/utils/request'

export function getUploadRecord(data) {
  return request({
    url: '/file/api/v1/cos/query',
    method: 'post',
    data,
  })
}

export function getUploadPathlist(data) {
  return request({
    url: '/file/api/v1/cos/pathlist/query',
    method: 'post',
    data,
  })
}

export function getRefreshQuota() {
  return request({
    url: '/file/api/v1/cos/purge/quota',
    method: 'get',
  })
}

export function setRefreshCDN(data) {
  return request({
    url: '/file/api/v1/cos/purge/cache',
    method: 'post',
    data,
  })
}

export function getConsistency(data) {
  return request({
    url: '/file/api/v1/cos/compare/etag',
    method: 'post',
    data,
  })
}

export function getSignUrl(data) {
  return request({
    url: '/devtool/signurl?originurl=' + data,
    method: 'get',
  })
}
