import request from '@/utils/request'
import { encryptedData } from '@/utils/encrypt'
import { loginRSA, tokenName } from '@/config'

export async function login(data) {
  if (loginRSA) {
    data = await encryptedData(data)
  }
  return request({
    url: '/login',
    method: 'post',
    data,
  })
}

//从nginx获取authelia的用户和组信息
export function getUserInfo() {
  return request({
    url: '/user/api/v1/getuserinfo',
    method: 'post',
  })
}

//从获取首页app图标列表
export function getApplist() {
  return request({
    url: '/application/api/v1/index',
    method: 'get',
  })
}

//从获取首页动态通知列表
export function getNoticelist() {
  return request({
    url: '/user/api/v1/getnotices',
    method: 'get',
  })
}

export function logout() {
  return request({
    url: '/logout',
    method: 'post',
  })
}

export function register() {
  return request({
    url: '/register',
    method: 'post',
  })
}
