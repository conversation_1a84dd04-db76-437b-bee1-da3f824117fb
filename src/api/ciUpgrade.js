import request from '@/utils/request'
import axios from 'axios'
import { baseURL, contentType } from '@/config'

export function feishuApprovalInfo(data) {
  return request({
    url: '/feishu/api/v1/approval/info',
    method: 'get',
  })
}

export function feishuGetStatus(data) {
  return request({
    url: '/feishu/api/v1/approval/instance_id?instance_id=' + data,
    method: 'get',
  })
}

export function getUpgradeDetails(data) {
  return request({
    url: '/ci/api/v1/upgradePlan/query/details?upgrade_id=' + data,
    method: 'get',
  })
}
