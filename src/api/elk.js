import request from '@/utils/request'

export function getElkRules(params) {
  return request({
    url: '/monitoring/api/v1/elk/rules',
    method: 'get',
    params,
  })
}

export function getElkRule(id) {
  return request({
    url: `/monitoring/api/v1/elk/rules/${id}`,
    method: 'get',
  })
}

export function createElkRule(data) {
  return request({
    url: '/monitoring/api/v1/elk/rules',
    method: 'post',
    data,
  })
}

export function updateElkRule(id, data) {
  return request({
    url: `/monitoring/api/v1/elk/rules/${id}`,
    method: 'put',
    data,
  })
}

export function deleteElkRule(id) {
  return request({
    url: `/monitoring/api/v1/elk/rules/${id}`,
    method: 'delete',
  })
}

export function applyElkRules() {
  return request({
    url: '/monitoring/api/v1/elk/rules/apply',
    method: 'post',
  })
}

export function getAlertHistory(params) {
  return request({
    url: '/monitoring/api/v1/elk/alerts',
    method: 'get',
    params,
  })
}
export function getHttpAggregation(params) {
  return request({
    url: '/monitoring/api/v1/elk/http-aggregation',
    method: 'get',
    params,
  })
}
export function getNpathMetrics(data) {
  return request({
    url: '/monitoring/api/v1/elk/npath-metrics',
    method: 'get',
    params: data,
  })
}
