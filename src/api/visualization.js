// 数据可视化接口
// author： wa<PERSON><PERSON>hao

import request from '@/utils/request'

const baseUrl = '/visual/api/v1'

// 获取预发布环境总览数据
export function getEnvOverview(params = {}) {
  return request({
    url: `${baseUrl}/pre/env/overview`,
    method: 'GET',
    params,
    timeout: 10000, // 设置超时时间
  })
}

// 获取预发布环境趋势数据
export function getEnvTrend(params = {}) {
  const { start_time, end_time, ...otherParams } = params
  console.log('getEnvTrend params', params)
  return request({
    url: `${baseUrl}/pre/env/trend`,
    method: 'GET',
    params: {
      start_time,
      end_time,
      ...otherParams,
    },
  })
}

// 获取用户统计数据
export function getUserStats(params = {}) {
  const { pageNo = 1, pageSize = 10, ...otherParams } = params
  return request({
    url: `${baseUrl}/pre/env/users`,
    method: 'GET',
    params: {
      pageNo,
      pageSize,
      ...otherParams,
    },
  })
}

// 获取业务线统计数据
export function getBizlinesStats(params = {}) {
  return request({
    url: `${baseUrl}/pre/env/bizlines`,
    method: 'GET',
    params,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// 获取环境使用时长统计数据
export function getDurationStats() {
  return request({
    url: `${baseUrl}/pre/env/duration`,
    method: 'GET',
    timeout: 10000, // 设置超时时间
  })
}
