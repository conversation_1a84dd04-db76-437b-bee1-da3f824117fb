import axios from 'axios'

export async function httpGet(url) {
  // axios.defaults.withCredentials = true

  var resp = axios
    .get(url, {
      headers: {
        Accept: "'*/*'",
        'Accept-Encoding': 'utf-8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        Connection: 'keep-alive',
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36 Edg/107.0.1418.62',
      },
    })
    .then((response) => {
      if (response) {
        return response
      } else {
        // console.log('failed')
      }
    })
  return resp
}
