import request from '@/utils/request'

// 基本信息相关接口
export function getPersonnel() {
  return request({
    url: '/deployment/api/v1/personnel',
    method: 'get',
  })
}

export function saveBasicInfo(data) {
  return request({
    url: '/deployment/api/v1/basic-info',
    method: 'post',
    data,
  })
}

// 服务相关接口
export function getServices() {
  return request({
    url: '/deployment/api/v1/services',
    method: 'get',
  })
}

export function saveServiceOrder(data) {
  return request({
    url: '/deployment/api/v1/service-order',
    method: 'post',
    data,
  })
}

// 构建相关接口
export function triggerBuild(data) {
  return request({
    url: '/deployment/api/v1/build',
    method: 'post',
    data,
  })
}

export function getBuildStatus(buildId) {
  return request({
    url: `/deployment/api/v1/build/${buildId}/status`,
    method: 'get',
  })
}

// 预发布环境接口
export function deployToPreRelease(data) {
  return request({
    url: '/deployment/api/v1/pre-release',
    method: 'post',
    data,
  })
}

export function getPreReleaseStatus(deploymentId) {
  return request({
    url: `/deployment/api/v1/pre-release/${deploymentId}/status`,
    method: 'get',
  })
}

// 发版队列相关接口
export function getReleaseQueue() {
  return request({
    url: '/deployment/api/v1/queue',
    method: 'get',
  })
}

export function updateQueueItem(id, data) {
  return request({
    url: `/deployment/api/v1/queue/${id}`,
    method: 'put',
    data,
  })
}

// 标准发版相关接口
export function deployToProduction(data) {
  return request({
    url: '/deployment/api/v1/production',
    method: 'post',
    data,
  })
}

export function getDeploymentStatus(deploymentId) {
  return request({
    url: `/deployment/api/v1/production/${deploymentId}/status`,
    method: 'get',
  })
}

export function pauseDeployment(deploymentId) {
  return request({
    url: `/deployment/api/v1/production/${deploymentId}/pause`,
    method: 'post',
  })
}

export function resumeDeployment(deploymentId) {
  return request({
    url: `/deployment/api/v1/production/${deploymentId}/resume`,
    method: 'post',
  })
}

export function rollbackDeployment(deploymentId) {
  return request({
    url: `/deployment/api/v1/production/${deploymentId}/rollback`,
    method: 'post',
  })
}

// 灰度发版相关接口
export function getGrayscaleStrategies() {
  return request({
    url: '/deployment/api/v1/grayscale/strategies',
    method: 'get',
  })
}

export function saveGrayscaleStrategy(data) {
  return request({
    url: '/deployment/api/v1/grayscale/strategy',
    method: 'post',
    data,
  })
}

export function deployToGrayscale(data) {
  return request({
    url: '/deployment/api/v1/grayscale',
    method: 'post',
    data,
  })
}

export function promoteGrayscaleToProd(deploymentId) {
  return request({
    url: `/deployment/api/v1/grayscale/${deploymentId}/promote`,
    method: 'post',
  })
}

// 发版历史记录
export function getReleaseHistory(params) {
  return request({
    url: '/deployment/api/v1/history',
    method: 'get',
    params,
  })
}

export function getReleaseDetail(id) {
  return request({
    url: `/deployment/api/v1/history/${id}`,
    method: 'get',
  })
}
