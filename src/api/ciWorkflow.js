import request from '@/utils/request'
import axios from 'axios'
import { baseURL } from '@/config'

export function doCreateSandbox(data) {
  return request({
    url: '/ci/api/v1/sandbox/create',
    method: 'post',
    data,
  })
}

export function doUpdateSandbox(data) {
  return request({
    url: '/ci/api/v1/sandbox/update',
    method: 'post',
    data,
  })
}

export function getSandboxlist(data) {
  return request({
    url: '/ci/api/v1/sandbox/query',
    method: 'post',
    data,
  })
}

export function getSandboxstatistics() {
  return request({
    url: '/ci/api/v1/sandbox/statistics',
    method: 'get',
  })
}

export function getSandboxdetail(data) {
  return request({
    url: '/ci/api/v1/sandbox/detail',
    method: 'post',
    data,
  })
}

export function getAllProject(data) {
  return request({
    url: '/ci/api/v1/base/allproject',
    method: 'get',
    data,
  })
}

export function getProjectBranch(data) {
  return request({
    url: '/ci/api/v1/project/branches',
    method: 'post',
    data,
  })
}

//沙盒环境-新增服务
export function createSandboxProject(data) {
  return request({
    url: '/ci/api/v1/sbx/project/create',
    method: 'post',
    data,
  })
}

//沙盒环境-删除服务
export function deleteSandboxProject(data) {
  return request({
    url: '/ci/api/v1/sbx/project/delete',
    method: 'post',
    data,
  })
}

//沙盒环境-获取服务列表
export function getSandboxProject(data) {
  return request({
    url: '/ci/api/v1/sbx/project/query',
    method: 'post',
    data,
  })
}

//沙盒环境-构建服务
export function buildSandboxProject(data) {
  return request({
    url: '/ci/api/v1/sbx/project/build',
    method: 'post',
    data,
  })
}

//部署沙盒环境
export function deploySandbox(data) {
  return request({
    url: '/ci/api/v1/sbx/createns',
    method: 'post',
    data,
  })
}

//封存沙盒环境
export function sealSandbox(data) {
  return request({
    url: '/ci/api/v1/sbx/pause',
    method: 'post',
    data,
  })
}

//封存沙盒环境
export function earseSandbox(data) {
  return request({
    url: '/ci/api/v1/sbx/deletens',
    method: 'post',
    data,
  })
}

//获取沙盒日志
export function getOperationlogs(data) {
  return request({
    url: '/ci/api/v1/sbx/operation/logs?' + data,
    method: 'get',
  })
}

//微服务项目管理
export function getProject(data) {
  return request({
    url: '/ci/api/v1/project/query',
    method: 'post',
    data,
  })
}

export function addProject(data) {
  return request({
    url: '/ci/api/v1/project/create',
    method: 'post',
    data,
  })
}

export function editProject(data) {
  return request({
    url: '/ci/api/v1/project/update',
    method: 'post',
    data,
  })
}

export function delProject(data) {
  return request({
    url: '/ci/api/v1/project/query',
    method: 'post',
    data,
  })
}

//配置差异列表
export function getdiffList(data) {
  return request({
    url: '/ci/api/v1/compare/difference',
    method: 'post',
    data,
  })
}

//配置差异明细
export function getdiffDetail(data) {
  return request({
    url: '/ci/api/v1/compare/file',
    method: 'post',
    data,
  })
}

//覆盖差异配置到基线
export function writebackDiffence(data) {
  return request({
    url: '/ci/api/v1/compare/writeback',
    method: 'post',
    data,
  })
}

//服务管理
export function projectUpdate(data) {
  return request({
    url: '/ci/api/v1/sbx/project/update',
    method: 'post',
    data,
  })
}

// 测试环境pod列表
export function sandboxPodlist(data) {
  return request({
    url: '/kubepi/api/v1/proxy/dev-test/k8s/api/v1/pods?' + data,
    method: 'get',
  })
}

// 测试环境删除pod（销毁重建）
export function sandboxPodDelete(data) {
  return request({
    url: '/kubepi/api/v1/proxy/dev-test/k8s/api/v1' + data,
    method: 'delete',
  })
}

// 沙盒环境pod列表
export function perfPodlist(data) {
  return request({
    url: '/kubepi/api/v1/proxy/perf-test/k8s/api/v1/pods?' + data,
    method: 'get',
  })
}

// 沙盒环境删除pod（销毁重建）
export function perfPodDelete(data) {
  return request({
    url: '/kubepi/api/v1/proxy/perf-test/k8s/api/v1' + data,
    method: 'delete',
  })
}

// pod文件浏览器
export function getPodFiles(data) {
  return request({
    url: '/kubepi/api/v1/pod/files',
    method: 'post',
    data,
  })
}

// pod打开文件
export function openPodFiles(data) {
  return request({
    url: '/kubepi/api/v1/pod/files/open',
    method: 'post',
    data,
  })
}

// pod下载文件
export function downloadPodFiles(data) {
  return request({
    url: '',
    method: 'post',
    data,
  })
}

// 沙盒环境压测Deployments 列表
export function perfDeploymentslist(data) {
  return request({
    url: '/kubepi/api/v1/proxy/perf-test/k8s/apis/apps/v1/deployments?' + data,
    method: 'get',
  })
}

// 修改 deployments 的 replicas
export function perfDeploymentsReplicas(selfLink, params, data) {
  return request({
    url: '/kubepi/api/v1/proxy/perf-test/k8s' + selfLink + '/scale?' + params,
    method: 'PATCH',
    data,
  })
}

//jenkins构建日志获取
export function getJksProgressiveText(param) {
  var consolelog, size
  axios.defaults.withCredentials = true
  consolelog = axios
    .get(baseURL + '/jenkins/api/v1/progressiveText?' + param)
    .then((response) => {
      if (response) {
        return response
      } else {
        console.log('failed')
      }
    })
  return consolelog
}

//变更计划查询
export function queryUpgradePlan(data) {
  return request({
    url: '/ci/api/v1/upgradePlan/query',
    method: 'post',
    data,
  })
}

//变更计划更新
export function updateUpgradePlan(data) {
  return request({
    url: '/ci/api/v1/upgradePlan/update',
    method: 'post',
    data,
  })
}

//沙盒域名绑定
export function updateDomainBind(data) {
  return request({
    url: '/ci/api/v1/domainbind/update',
    method: 'post',
    data,
  })
}

//沙盒域名查询
export function queryDomainBind(data) {
  return request({
    url: '/ci/api/v1/domainbind/query',
    method: 'post',
    data,
  })
}

//端口绑定
export function getPortbind(data) {
  return request({
    url: '/ci/api/v1/sandbox/portbind?' + data,
    method: 'get',
  })
}

//端口添加
export function addPortbind(data) {
  return request({
    url: '/ci/api/v1/sandbox/portbind',
    method: 'post',
    data,
  })
}

//端口添加
export function deletePortbind(data) {
  return request({
    url: '/ci/api/v1/sandbox/portbind',
    method: 'delete',
    data,
  })
}

//灰度相关
//静态微服务配置
export function getVersioncfg() {
  return request({
    url: '/ci/api/v1/nss/versioncfg',
    method: 'get',
  })
}

//灰度任务列表
export function getGrayEvent() {
  return request({
    url: '/ci/api/v1/grayevent',
    method: 'get',
  })
}

//修改灰度任务列表
export function updateGrayEvent(data) {
  return request({
    url: '/ci/api/v1/grayevent',
    method: 'post',
    data,
  })
}

//灰度任务变更状态
export function updateGrayStatus(data) {
  return request({
    url: '/ci/api/v1/grayevent/updateStatus',
    method: 'post',
    data,
  })
}

//nss服务相关
export function getNssProjectVersionList(project_name, keyword) {
  return request({
    url:
      '/ci/api/v1/nss/versionlist?project_name=' +
      project_name +
      '&pageSize=10&keyword=' +
      keyword,
    method: 'get',
  })
}

export function getNssProjectCfg() {
  return request({
    url: '/ci/api/v1/nss/projectcfg',
    method: 'get',
  })
}

//nss 更新prd版本
export function updateNssPrd(data) {
  return request({
    url: '/ci/api/v1/nss/versioncfg/updatePrd',
    method: 'post',
    data,
  })
}

export function updateNssStg(data) {
  return request({
    url: '/ci/api/v1/nss/versioncfg/updateStg',
    method: 'post',
    data,
  })
}

export function updateNssCanary(data) {
  return request({
    url: '/ci/api/v1/nss/versioncfg/updateCanary',
    method: 'post',
    data,
  })
}

export function getNssBuildInfo(data) {
  var params = new URLSearchParams()
  for (let key in data) {
    params.append(key, data[key])
  }
  var queryString = params.toString()

  return request({
    url: '/ci/api/v1/nss/buildinfo?' + queryString,
    method: 'get',
  })
}

//降级相关
export function getUserErrors(data) {
  return request({
    url: '/panic/api/v1/kfkerror?pageNo=1&pageSize=10&time=' + data,
    method: 'get',
  })
}

export function getPanicList() {
  return request({
    url: '/panic/api/v1/urilist',
    method: 'get',
  })
}

export function updatePanicList(data) {
  return request({
    url: '/panic/api/v1/urilist',
    method: 'post',
    data,
  })
}

export function delPanicList(data) {
  return request({
    url: '/panic/api/v1/urilist',
    method: 'delete',
    data,
  })
}

export function cleanPanicList() {
  return request({
    url: '/panic/api/v1/urilist/clean/all',
    method: 'delete',
  })
}

export function syncPanicList() {
  return request({
    url: '/nginx/api/v1/panic/sync',
    method: 'get',
  })
}

export function panicDowngrade(data) {
  return request({
    url: '/panic/api/v1/hiesecurity',
    method: 'post',
    data,
  })
}

//云账号管理

export function queryCloudUser(data) {
  var params = new URLSearchParams()
  for (let key in data) {
    params.append(key, data[key])
  }
  var queryString = params.toString()
  return request({
    url: '/ci/api/v1/cloud/user?' + queryString,
    method: 'get',
  })
}

export function queryCloudSecretKey(data) {
  return request({
    url: '/ci/api/v1/cloud/user/changesk?account_id=' + data,
    method: 'get',
  })
}
