import request from '@/utils/request'

export function getList(data) {
  const datas = {
    ...data,
  }

  return request({
    url: '/user/api/v1/ssouserlist',
    method: 'post',
    data: datas,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

export function doEdit(data) {
  return request({
    url: '/user/api/v1/ssouserupdates',
    method: 'post',
    data,
  })
}

export function doDelete(data) {
  return request({
    url: '/userManagement/doDelete',
    method: 'post',
    data,
  })
}

export function doUpload() {
  return request({
    url: '/user/api/v1/ssoupdate',
    method: 'post',
  })
}

export function getRoles(data) {
  return request({
    url: '/user/api/v1/getRoles',
    method: 'get',
    data,
  })
}
