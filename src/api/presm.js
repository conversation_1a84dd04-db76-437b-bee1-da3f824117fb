// 预发布环境相关接口信息
// author： wa<PERSON><PERSON><PERSON>, dongxin
import request from '@/utils/request'

const baseUrl = '/ci/api/v1/presm/envs'

export function getEnvs(data) {
  const params = {
    pageNo: data.pageNo || 1,
    pageSize: data.pageSize || 15,
    keyword: data.keyword,
    status: data.status,
  }
  return request({
    url: `${baseUrl}`,
    method: 'GET',
    params: params,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// pre-uuid 的环境信息和服务信息
export function getEnv(preUuid) {
  return request({
    url: `${baseUrl}/${preUuid}`,
    method: 'GET',
  })
}

export function postEnv(data) {
  return request({
    url: `${baseUrl}`,
    method: 'POST',
    data: data,
  })
}

export function deleteEnv(preUuid) {
  return request({
    url: `${baseUrl}/${preUuid}`,
    method: 'DELETE',
  })
}

export function actionEnv(preUuid, data, operation) {
  return request({
    url: `${baseUrl}/${preUuid}/actions`,
    method: 'PUT',
    data: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
    },
    params: { operation: operation },
  })
}

export function getServices(preUuid) {
  return request({
    url: `${baseUrl}/${preUuid}/services`,
    method: 'GET',
  })
}

export function postService(preUuid, data) {
  return request({
    url: `${baseUrl}/${preUuid}/services`,
    method: 'POST',
    data: JSON.stringify(data),
  })
}

export function deleteService(preUuid, project_id) {
  return request({
    url: `${baseUrl}/${preUuid}/services/${project_id}`,
    method: 'DELETE',
  })
}

export function putService(preUuid, data) {
  return request({
    url: `${baseUrl}/${preUuid}/services/package`,
    method: 'PUT',
    data: JSON.stringify(data),
  })
}

// 日志相关
export function EnvLogs() {
  return request({
    url: `${baseUrl}/logs`,
    method: 'GET',
  })
}

export function EnvServieLogs(preUuid) {
  return request({
    url: `${baseUrl}/${preUuid}/logs`,
    method: 'GET',
  })
}

// 规则相关
export function EnvRules(data) {
  const params = {
    pageNo: data.pageNo || 1,
    pageSize: data.pageSize || 15,
    keyword: data.keyword,
  }

  return request({
    url: `${baseUrl}/grayrule`,
    method: 'GET',
    params: params,
  })
}

export function PutRules(data, albumId) {
  return request({
    url: `${baseUrl}/grayrule/${albumId}`,
    method: 'PUT',
    data: JSON.stringify(data),
  })
}

export function DeleteRules(albumId) {
  return request({
    url: `${baseUrl}/grayrule/${albumId}`,
    method: 'DELETE',
  })
}

export function EnvRule(preUuid) {
  return request({
    url: `${baseUrl}/grayrule/preuuid/${preUuid}`,
    method: 'GET',
  })
}

export function postEnvRules(data) {
  return request({
    url: `${baseUrl}/grayrule`,
    method: 'POST',
    data: JSON.stringify(data),
  })
}

export function getAlbumIdInfos(albumid) {
  return request({
    url: `${baseUrl}/grayrule/albumid/${albumid}`,
    method: 'GET',
  })
}

// traffic信息
export function getPreTraffic(preUuid) {
  return request({
    url: `${baseUrl}/traffic/${preUuid}`,
    method: 'GET',
  })
}

// jenkins build info 信息
export function getPreJenkinsBuildInfo(preUuid, data) {
  const params = {
    ...data,
  }

  return request({
    url: `${baseUrl}/buildinfo/${preUuid}`,
    method: 'GET',
    params: params,
  })
}

// =====================
// URI 灰度规则相关 API
// =====================

/**
 * 获取 URI 灰度规则数据，支持传入 query 参数用于分页、过滤
 * @param {Object} query 查询参数（例如 { pageNo, pageSize, keyword }）
 */
export function EnvRulesUri(query) {
  return request({
    url: `${baseUrl}/grayrule/uri`,
    method: 'GET',
    params: query,
  })
}

/**
 * 新增一条 URI 灰度规则
 * @param {Object} data 新增规则数据
 */
export function addUriRule(data) {
  return request({
    url: `${baseUrl}/grayrule/uri`,
    method: 'POST',
    data,
  })
}

/**
 * 更新 URI 灰度规则，根据 uri_id 进行更新
 * @param {Object} data 更新数据
 * @param {Number|String} uri_id URI 规则的ID
 */
export function PutUriRules(data, uri_id) {
  return request({
    url: `${baseUrl}/grayrule/uri/${uri_id}`,
    method: 'PUT',
    data,
  })
}

/**
 * 删除 URI 灰度规则，根据 uri_id 删除规则
 * @param {Number|String} uri_id URI 规则的ID
 */
export function DeleteUriRules(uri_id) {
  return request({
    url: `${baseUrl}/grayrule/uri/${uri_id}`,
    method: 'DELETE',
  })
}
