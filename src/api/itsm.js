import request from '@/utils/request'

export function slaEvents(data) {
  return request({
    url: '/itsm/api/v1/slaevents',
    method: 'POST',
    data,
  })
}

export function getEventsList(data) {
  return request({
    url: '/itsm/api/v1/getslaevents',
    method: 'POST',
    data,
  })
}

export function getSlaStats(data) {
  return request({
    url: '/itsm/api/v1/getslaanalyze?' + data,
    method: 'GET',
  })
}

export function WeeklyOpsReport(start_time, end_time) {
  const params = {
    start_time: start_time || null,
    end_time: end_time || null,
  }
  return request({
    url: '/itsm/api/v1/weekly/cost/report',
    method: 'get',
    params: params,
  })
}
