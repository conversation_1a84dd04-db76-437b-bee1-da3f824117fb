<template>
  <el-dialog
    title="新增变更计划"
    :visible.sync="dialogFormVisible"
    width="600px"
    :close-on-click-modal="false"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="变更日期">
            <el-date-picker
              v-model="form.plan_date"
              type="date"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="变更时间">
            <el-time-select
              v-model="form.plan_time"
              :picker-options="{
                start: '08:30',
                step: '00:30',
                end: '20:00',
              }"
              placeholder="选择时间"
            ></el-time-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="变更版本" prop="version_name">
            <el-input
              v-model.trim="form.version_name"
              autocomplete="off"
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="负责人">
            <el-input v-model.trim="form.owner" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="业务线">
            <el-select v-model="form.line_name" filterable placeholder="请选择">
              <el-option
                v-for="item in allBizlines"
                :key="item.tapd_id"
                :label="item.tapd_name"
                :value="item.tapd_name"
              >
                <span style="float: left">{{ item.tapd_name }}</span>
                <span style="float: right; color: #8492a6; font-size: 8px">
                  {{ item.tapd_id }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="info" @click="close">取 消</el-button>
      <el-button type="success" icon="el-icon-check" @click="saveApply">
        提 交
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getUserInfo } from '@/api/user'
  import { getBizlines } from '@/api/tapd'
  import { updateUpgradePlan } from '@/api/ciWorkflow'

  export default {
    name: 'PlanAdd',
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '申请中',
          1: '已审核',
          2: '已禁用',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        form: {
          upgrade_id: '',
          flow_uid: '0',
          version_name: '',
          owner: '',
          line_name: '',
          plan_date: '',
          plan_time: '',
        },
        allBizlines: [],

        rules: {
          version_name: [
            { required: true, trigger: 'blur', message: '请输入发版名称' },
          ],
          plan_date: [
            { required: true, trigger: 'blur', message: '请选择发版日期' },
          ],
          plan_time: [
            { required: true, trigger: 'blur', message: '请选择发版时间' },
          ],
        },
        title: '',
        isedit: false,
        currentUser: '',
        dialogFormVisible: false,
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      showEdit(val) {
        this.form = Object.assign({}, val)
        this.form.owner = this.currentUser
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },

      saveApply() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            this.$baseConfirm('确认提交变更计划吗？', null, async () => {
              const { msg } = await updateUpgradePlan(this.form)
              this.$baseMessage(msg, 'success')
              this.$emit('fetch-data')
              this.$emit('close-drawer')
              this.close()
            })
          } else {
            return false
          }
        })
      },
      async fetchData() {
        const { user } = await getUserInfo()
        this.currentUser = user
        const { data } = await getBizlines()
        this.allBizlines = data
        setTimeout(() => {}, 600)
      },
    },
  }
</script>
