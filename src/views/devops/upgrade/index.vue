<template>
  <div class="sandboxmain-container">
    <el-drawer
      :show-close="false"
      :visible.sync="showPlanlist"
      :with-header="false"
      direction="rtl"
      size="30%"
      @opened=""
      @closed=""
    >
      <el-card shadow="never">
        <div slot="header">
          <span><H3>变更计划列表</H3></span>
        </div>
        <el-alert title="提示:" type="success">
          <div class="ci-alert-list">
            选择当天或之后的日期才可以增加变更计划。
          </div>
        </el-alert>
        <p />
        <el-row>
          <el-col v-for="plan in selectedPlans" :key="plan.id" :span="24">
            <el-card shadow="hover">
              <div slot="header">
                <span>变更流水号：{{ plan.upgrade_id }}</span>
                <el-button
                  icon="el-icon-guide"
                  type="primary"
                  style="float: right"
                  size="mini"
                  @click="handleWizzard(plan)"
                >
                  查看
                </el-button>
              </div>
              <el-row :gutter="10">
                <el-col :span="4">
                  <div style="margin: auto; width: 100%">
                    <el-avatar
                      :size="70"
                      :src="getAvatar(plan.owner)"
                      shape="circle"
                    ></el-avatar>
                    <p class="cardDesc">{{ plan.displayname }}</p>
                  </div>
                </el-col>
                <el-col :offset="4" :span="16">
                  <p>版本名称：{{ plan.version_name }}</p>
                  <p>发版时间：{{ plan.time }}</p>
                  <p>状态：{{ plan.status | statusMap }}</p>
                  <p />
                </el-col>
              </el-row>
            </el-card>
          </el-col>
          <el-col v-if="canSchedule" :span="24">
            <el-card shadow="hover">
              <div style="text-align: center">
                <el-link
                  icon="el-icon-circle-plus-outline"
                  @click="handleAddPlan()"
                >
                  新增变更计划
                </el-link>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-card>
    </el-drawer>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card shadow="never">
          <div slot="header">
            <span><H2>版本变更日历</H2></span>
          </div>
          <el-calendar v-model="calendarDate">
            <div
              slot="dateCell"
              slot-scope="{ date, data }"
              class="dataCell"
              @click="handleCaleClick(data)"
            >
              <p :class="data.isSelected ? 'is-selected' : 'not-selected'">
                <b>{{ data.day.split('-')[2] }}</b>
              </p>
              <li
                v-for="list in calcData[data.day]"
                :class="getlistclass(list)"
              >
                ✩ {{ list.version_name }}
              </li>
            </div>
          </el-calendar>
          <el-alert title="提示:" type="success">
            <div class="ci-alert-list">选择日期，可以查看或新增发版计划。</div>
          </el-alert>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">本月变更数</p>
            <p class="title-success">
              <countTo
                :start-val="0"
                :end-val="15"
                :decimals="0"
                :duration="2000"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">变更时长（分）</p>
            <p class="title-success">
              <countTo
                :start-val="0"
                :end-val="120"
                :decimals="0"
                :duration="2000"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">变更回滚</p>
            <p class="title-danger">
              <countTo
                :start-val="0"
                :end-val="2"
                :decimals="0"
                :duration="2000"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">本周计划</p>
            <p class="title-warning">
              <countTo
                :start-val="0"
                :end-val="2"
                :decimals="0"
                :duration="2000"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="never">
          <div slot="header">
            <span>动态信息</span>
          </div>
          <el-timeline :reverse="true">
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="activity.timestamp"
              :color="activity.color"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
    <edit ref="edit" @fetch-data="fetchData" @close-drawer="closeDrawer"></edit>
  </div>
</template>
<script>
  import moment from 'moment'
  import countTo from 'vue-count-to'
  import Edit from './components/PlanAdd'
  import { queryUpgradePlan } from '@/api/ciWorkflow'

  export default {
    name: 'SlaInfo',
    components: { countTo, Edit },
    filters: {
      statusMap(status) {
        const statusMap = {
          0: '未开始',
          1: '变更中',
          2: '已完成',
          3: '已取消',
        }
        return statusMap[status]
      },
    },

    data() {
      return {
        showPlanlist: false,
        canSchedule: false,
        currentTimerange: '',
        calendarDate: new Date(),

        selectedDay: '',
        selectedPlans: [],
        calcData: {},
        activities: [
          {
            content: '速度更快的2.8版本相册服务重构版本上线了！',
            timestamp: '2021-10-14',
          },
          {
            content: '增长会员V129版本已经开始灰度。',
            timestamp: '2022-08-09',
          },
          {
            content: '订单「B219-部分冻结」版本变更失败，已经回滚代码。',
            timestamp: '2022-08-09 14:12:00',
          },

          {
            content: '谢牧一 预定了2022-08-11发布M113补丁版本',
            timestamp: '2022-08-09',
          },
          {
            content: '谢牧一 取消了2022-08-11发布M113补丁版本',
            timestamp: '2022-08-09',
          },
        ],
      }
    },
    //通过监听calendar的值来判断时间区间的变化，重新fetchdata
    watch: {
      calendarDate(val, oldval) {
        var f = false
        if (
          val &&
          moment(val).format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')
        ) {
          f = true
        } else if (
          val &&
          moment(val).toDate() < moment(oldval).startOf('month').toDate()
        ) {
          f = true
        } else if (
          val &&
          moment(val).toDate() > moment(oldval).endOf('month').toDate()
        ) {
          f = true
        }
        if (f) {
          this.fetchData()
        }
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      getlistclass(list) {
        return 'li-event-' + list.status
      },
      getAvatar(user) {
        return '/static/avatars/' + user + '.png'
      },
      handleCaleClick(data) {
        this.canSchedule =
          moment(moment().format('YYYY-MM-DD')).diff(
            moment(data.day),
            'minutes'
          ) <= 0
        this.selectedDay = data.day
        this.selectedPlans = this.calcData[data.day]
        this.showPlanlist = true
      },
      handleAddPlan() {
        var row = {}
        row.plan_date = this.selectedDay
        this.$refs['edit'].showEdit(row)
      },
      closeDrawer() {
        this.showPlanlist = false
      },
      handleWizzard(val) {
        this.$router.push({
          name: 'upgradeWizzard',
          params: {
            upgrade_id: val.upgrade_id,
          },
        })
      },
      async fetchData() {
        var queryform = {}
        queryform.str_time = moment(this.calendarDate)
          .subtract(1, 'month')
          .startOf('month')
          .format('YYYY-MM-DD')
        queryform.end_time = moment(this.calendarDate)
          .add(2, 'month')
          .startOf('month')
          .format('YYYY-MM-DD')
        queryform.pageSize = 100
        var { data, totalCount } = await queryUpgradePlan(queryform)
        this.calcData = data
        this.total = totalCount

        setTimeout(() => {
          this.listLoading = false
        }, 500)
      },
    },
  }
</script>
<style>
  .el-card__header {
    height: 80px;
  }
  .el-calendar-table .el-calendar-day {
    height: 100px;
  }
</style>
<style lang="scss" scoped>
  //div填充整个页面，方便响应单击事件
  .cardDesc {
    color: #0c0c0c;
    font-size: 12px;
    padding: 0 20px;
    line-height: 15%;
    max-width: 100px;
    white-space: nowrap;
  }

  .dataCell {
    width: 100%;
    height: 100%;
  }

  .not-selected {
    line-height: 10%;
  }

  .is-selected {
    color: #1989fa;
    line-height: 20%;
  }

  .li-event-0 {
    color: #0c0c0c;
    font-size: 12px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }

  .li-event-1 {
    color: #c62c2c;
    font-size: 12px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }

  .li-event-2 {
    color: #979ba5;
    font-size: 12px;
    white-space: nowrap;
    overflow-x: hidden;
    text-overflow: ellipsis;
  }

  .sla-panel {
    line-height: 0;
    text-align: center;
  }
  .sla-desc {
    font-size: 16px;
  }

  .title-danger {
    color: #f56c6c;
    font-size: 55px;
  }
  .title-warning {
    color: #e6a23c;
    font-size: 55px;
  }
  .title-success {
    color: #67c23a;
    font-size: 55px;
  }
  .title-info {
    color: #161616;
    font-size: 55px;
  }

  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .sandboxmain-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }
</style>
