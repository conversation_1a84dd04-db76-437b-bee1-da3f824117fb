<template>
  <el-row :gutter="10" type="flex">
    <!-- 外层col     -->
    <!-- 基本信息     -->
    <el-col :xs="24" :sm="24" :md="18" :lg="18" :xl="18">
      <el-card shadow="never">
        <el-descriptions
          class="margin-top"
          title="变更报告"
          :column="3"
          size="medium"
          border
        >
          <template slot="extra">
            <el-button
              icon="el-icon-refresh"
              type="primary"
              size="mini"
              @click="handleEdit"
            >
              刷新
            </el-button>
          </template>
          <el-descriptions-item>
            <template slot="label">变更ID</template>
            {{ upgrade_id }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更标题</template>
            {{ baseinfo.version_name }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <i class=""></i>
              所属业务
            </template>
            {{ baseinfo.line_name }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">创建人</template>
            {{ baseinfo.owner }}
          </el-descriptions-item>

          <el-descriptions-item v-html="true">
            <template slot="label">统一分支</template>
            {{ baseinfo.git_branch }}
          </el-descriptions-item>

          <el-descriptions-item v-html="true">
            <template slot="label">变更完成时间</template>
            {{ baseinfo.create_time }}
          </el-descriptions-item>

          <el-descriptions-item :span="24">
            <template slot="label">变更记录</template>
            <span style="white-space: pre-line">
              {{ baseinfo.change_log }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item :span="24">
            <template slot="label">质量评价</template>
            <el-rate
              v-model="ratescore"
              :max="10"
              disabled
              show-score
              text-color="#ff9900"
            ></el-rate>
          </el-descriptions-item>

          <el-descriptions-item label="评分明细">
            <el-table :data="scorelist" :row-class-name="getTableRowClass">
              <el-table-column prop="title" label="评分项"></el-table-column>
              <el-table-column
                prop="baseline"
                label="达标值"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="value"
                label="实际值"
                width="100"
              ></el-table-column>
              <el-table-column label="得分" width="120">
                <template slot-scope="{ row }">
                  <el-tag :type="getTagType(row.score)">{{ row.score }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  export default {
    name: 'Baseinfo',
    filters: {},
    data() {
      return {
        listLoading: false,
        upgrade_id: '',

        isEditing: false,
        elementLoadingText: '加载中...',
        baseinfo: {
          upgrade_id: 'U20230516152609',
          flow_uid: '0',
          version_name: 'bug修复',
          owner: 'dongxin',
          line_name: 'AI赋能',
          create_time: '2023-05-17 00:00:00',
          plan_date: '2023-05-17',
          plan_time: '08:30:00',
          tapd_link:
            'https://www.tapd.cn/54232736/prong/iterations/card_view?q=52abc58fbd23bdf766736a0df0a6fa97',
          prd_appover: '陈子乔',
          test_appover: '黄洁芳',
          front_appover: '任佳',
          back_appover: '苏任',
          arch_appover: '王志军',
          prd_confirm: '张清亮',
          qa_appover: '陈子乔',
          change_log: '1.后台-文字违规列表优化 \n2.文字违规列表优化',
          git_branch: 'vip-21212-hotfix',
        },

        ratescore: 9.6,
        scorelist: [
          {
            id: 1,
            code: null,
            title: '审批流程是否完整',
            baseline: 1,
            calc_type: 0,
            compare_type: 1,
            value: 1,
            score: 0,
          },
          {
            id: 2,
            code: null,
            title: '后端checklist无遗留',
            baseline: 1,
            calc_type: 0,
            compare_type: 1,
            value: 1,
            score: 0,
          },
          {
            id: 3,
            code: null,
            title: '前端checklist无遗留',
            baseline: 1,
            calc_type: 0,
            compare_type: 1,
            value: 1,
            score: 0,
          },
          {
            id: 4,
            code: null,
            title: '后端服务平均构建次数',
            baseline: 5,
            calc_type: 0,
            compare_type: -1,
            value: 1,
            score: 0,
          },
          {
            id: 5,
            code: null,
            title: '前端服务平均构建次数',
            baseline: 5,
            calc_type: 0,
            compare_type: -1,
            value: 1,
            score: 0,
          },
          {
            id: 6,
            code: null,
            title: '验收时长（分钟）',
            baseline: 120,
            calc_type: 2,
            compare_type: -1,
            value: 105,
            score: 0.1,
          },
          {
            id: 7,
            code: null,
            title: '回滚次数',
            baseline: 0,
            calc_type: 0,
            compare_type: -1,
            value: 0,
            score: 0,
          },
          {
            id: 8,
            code: null,
            title: '是否包含老相册服务',
            baseline: 0,
            calc_type: 0,
            compare_type: -1,
            value: 1,
            score: -0.5,
          },
          {
            id: 9,
            code: null,
            title: '灰度阶段后是否存在服务变更',
            baseline: 0,
            calc_type: 0,
            compare_type: -1,
            value: 0,
            score: 0,
          },
        ],

        activities: [
          {
            content: '辜凤莲 创建了变更计划。',
            timestamp: '2023-05-14 15:00',
          },
          {
            content: '辜凤莲 更新了变更基本信息。',
            timestamp: '2023-05-15 12:23',
          },
          {
            content: '辜凤莲 增加了后端服务acs。',
            timestamp: '2023-05-15 12:28',
          },

          {
            content: '邹涵东 增加了前端服务h5biz。',
            timestamp: '2023-05-18 14:23',
          },
        ],
        Checklist: [
          { checkitem: '后端服务镜像构建', progress: 100 },
          { checkitem: '前端服务构建', progress: 0 },
          { checkitem: '半弹窗文件构建', progress: 0 },
          { checkitem: '发版流程审批', progress: 30 },
        ],
      }
    },
    created() {
      this.upgrade_id = this.$route.params.upgrade_id
    },
    beforeDestroy() {},
    mounted() {},
    methods: {
      handleEdit() {
        this.isEditing = !this.isEditing
      },
      getColor(progress) {
        if (progress < 50) {
          return '#f5222d'
        } else if (progress < 100) {
          return '#faad14'
        } else {
          return '#52c41a'
        }
      },
      progressText(row) {
        return row.text
      },
      nextStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(1)
      },
      backStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(0)
      },
      getTableRowClass(row) {
        return row.row.score >= 0 ? 'success-row' : 'warning-row'
      },
      getTagType(score) {
        return score >= 0 ? 'success' : 'warning'
      },
    },
  }
</script>

<style>
  .el-table .warning-row {
    background: oldlace;
  }
</style>

<style lang="scss" scoped>
  .linktext {
    color: #979ba5;
    white-space: nowrap; /* 规定文本不换行 */
    overflow: hidden; /* 超出宽度隐藏 */
    text-overflow: ellipsis; /* 超出宽度显示省略号 */
    min-width: 100px; /* 规定最大宽度 */
    max-width: 400px; /* 规定最大宽度 */
    display: inline-block; /* 使文本宽度生效 */
  }
  .progress-table-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -10px;
    margin-bottom: 10px;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .stepcard {
    border: 2px dashed #ddd; /* 设置边框样式 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    //background-image: linear-gradient(to bottom, #f0f0f0, #eee);
  }
</style>
