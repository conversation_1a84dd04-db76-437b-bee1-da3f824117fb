<template>
  <el-row :gutter="10" type="flex">
    <!-- 外层col     -->
    <el-col :xs="24" :sm="24" :md="20" :lg="20" :xl="20">
      <!-- 基本信息     -->
      <el-card shadow="never">
        <div slot="header">
          <span><b>发布进度</b></span>
          <el-button
            icon=""
            type="primary"
            style="float: right; margin-left: 5px"
            size="mini"
            @click=""
          >
            顺序发布
          </el-button>

          <el-button
            icon=""
            type="warning"
            style="float: right; margin-left: 5px"
            size="mini"
            @click=""
          >
            暂停
          </el-button>

          <el-button
            icon=""
            type="danger"
            style="float: right; margin-left: 20px"
            size="mini"
            @click=""
          >
            全部回滚
          </el-button>
        </div>

        <el-row>
          <el-col :span="24">
            <el-table :data="services">
              <el-table-column prop="name" label="服务名称"></el-table-column>
              <el-table-column
                prop="instances"
                label="实例数"
              ></el-table-column>
              <el-table-column
                prop="details"
                label="当前状态"
              ></el-table-column>
              <el-table-column label="更新进度">
                <template slot-scope="{ row }">
                  <el-progress
                    :percentage="row.progress"
                    :status="row.status"
                  ></el-progress>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>
    </el-col>

    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
      <el-card class="stepcard" shadow="always">
        <div slot="header">
          <span>阶段进度</span>
          <el-button
            icon="el-icon-right"
            type="primary"
            style="float: right; margin-left: 5px"
            size="mini"
            @click="nextStep"
          >
            下阶段
          </el-button>

          <el-button
            icon="el-icon-back"
            type="primary"
            style="float: right; margin-left: 20px"
            size="mini"
            @click="backStep"
          >
            上阶段
          </el-button>
        </div>
        <div class="progress-table-container">
          <el-table :data="Checklist">
            <el-table-column
              show-overflow-tooltip
              prop="checkitem"
              label="任务"
            ></el-table-column>

            <el-table-column show-overflow-tooltip prop="status" label="状态">
              <template slot-scope="{ row }">
                <el-progress
                  :percentage="row.progress"
                  :color="getColor(row.progress)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-card shadow="never">
        <div slot="header">
          <span>动态信息</span>
        </div>
        <el-timeline :reverse="true">
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :timestamp="activity.timestamp"
            :color="activity.color"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  export default {
    name: 'Baseinfo',
    filters: {},
    data() {
      return {
        listLoading: false,
        upgrade_id: '',
        isEditing: false,
        elementLoadingText: '加载中...',
        baseinfo: {
          upgrade_id: 'U20230516152609',
          flow_uid: '0',
          version_name: 'bug修复',
          owner: 'dongxin',
          line_name: 'AI赋能',
          create_time: '2023-05-17 00:00:00',
          plan_date: '2023-05-17',
          plan_time: '08:30:00',
          tapd_link:
            'https://www.tapd.cn/54232736/prong/iterations/card_view?q=52abc58fbd23bdf766736a0df0a6fa97',
          prd_appover: '陈子乔',
          test_appover: '黄洁芳',
          front_appover: '任佳',
          back_appover: '苏任',
          arch_appover: '王志军',
          prd_confirm: '张清亮',
          qa_appover: '陈子乔',
          change_log: '1.后台-文字违规列表优化',
          git_branch: 'vip-21212-hotfix',
        },
        services: [
          {
            name: 'acs',
            instances: '60/60',
            details: '已发布',
            progress: 100,
            status: 'success',
          },
          {
            name: 'xwaypush',
            instances: '1/3',
            details: '暂停中',
            progress: 30,
            status: 'exception',
          },
          {
            name: 'increase',
            instances: '0/7',
            details: '等待中',
            progress: 0,
            status: 'warning',
          },
          {
            name: 'portal',
            instances: '0/15',
            details: '等待中',
            progress: 0,
            status: 'warning',
          },
          {
            name: 'h5album',
            instances: '0/1',
            details: '等待中',
            progress: 0,
            status: 'warning',
          },
        ],
        activities: [
          {
            content: '辜凤莲 创建了变更计划。',
            timestamp: '2023-05-14 15:00',
          },
          {
            content: '辜凤莲 更新了变更基本信息。',
            timestamp: '2023-05-15 12:23',
          },
          {
            content: '辜凤莲 增加了后端服务acs。',
            timestamp: '2023-05-15 12:28',
          },

          {
            content: '邹涵东 增加了前端服务h5biz。',
            timestamp: '2023-05-18 14:23',
          },
        ],
        Checklist: [
          { checkitem: '后端服务发布', progress: 30 },
          { checkitem: '前端文件更新', progress: 0 },
          { checkitem: '半弹窗文件更新', progress: 0 },
          { checkitem: '代码分支合并', progress: 0 },
        ],
      }
    },
    created() {
      this.upgrade_id = this.$route.params.upgrade_id
    },
    beforeDestroy() {},
    mounted() {},
    methods: {
      handleEdit() {
        this.isEditing = !this.isEditing
      },
      getColor(progress) {
        if (progress < 50) {
          return '#f5222d'
        } else if (progress < 100) {
          return '#faad14'
        } else {
          return '#52c41a'
        }
      },
      progressText(row) {
        return row.text
      },
      nextStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(4)
      },
      backStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(2)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .linktext {
    color: #979ba5;
    white-space: nowrap; /* 规定文本不换行 */
    overflow: hidden; /* 超出宽度隐藏 */
    text-overflow: ellipsis; /* 超出宽度显示省略号 */
    min-width: 100px; /* 规定最大宽度 */
    max-width: 400px; /* 规定最大宽度 */
    display: inline-block; /* 使文本宽度生效 */
  }
  .progress-table-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .stepcard {
    border: 2px dashed #ddd; /* 设置边框样式 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    //background-image: linear-gradient(to bottom, #f0f0f0, #eee);
  }
</style>
