<template>
  <el-row :gutter="10" type="flex">
    <!-- 外层col     -->
    <el-col :xs="24" :sm="24" :md="17" :lg="17" :xl="17">
      <!-- 构建区     -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card shadow="never">
          <el-button icon="el-icon-document-copy" type="primary" @click="">
            配置管理
          </el-button>
          <el-button icon="el-icon-upload" type="warning" @click="handleBuild">
            批量构建
          </el-button>

          <el-form
            :inline="true"
            :model="queryForm"
            style="float: right; margin-left: 5px"
            @submit.native.prevent
          >
            <el-form-item>
              <el-input
                v-model.trim="queryForm.keywords"
                placeholder="关键字过滤"
                clearable
              />
            </el-form-item>
          </el-form>

          <el-table
            v-loading="listLoading"
            :data="filteredData"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <el-table-column
              show-overflow-tooltip
              type="selection"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="appname"
              label="项目名称"
            ></el-table-column>

            <el-table-column show-overflow-tooltip label="类型">
              <template #default="{ row }">
                <el-tag :type="row.type | typeTypeMap">
                  {{ row.type | typeNameMap }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="git_branch"
              label="分支"
              width="120"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="latest_build_time"
              label="构建时间"
              width="150"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="publish_count"
              label="构建次数"
            ></el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="artifact_tag"
              label="制品TAG"
              width="150"
            ></el-table-column>

            <el-table-column show-overflow-tooltip label="操作" width="120">
              <template #default="{ row }">
                <el-button type="text" @click="handleBuild(row)">
                  打包构建
                </el-button>
                <el-button
                  v-if="row.status > 1"
                  type="text"
                  :disabled="!row.jenkins_buildno"
                  @click="handleGetbuildlog(row)"
                >
                  构建日志
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            background
            :current-page="queryForm.pageNo"
            :page-size="queryForm.pageSize"
            :layout="layout"
            :total="filteredData.length"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-card>
      </el-col>
    </el-col>

    <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="7">
      <el-card class="stepcard" shadow="always">
        <div slot="header">
          <span>阶段进度</span>
          <el-button
            icon="el-icon-right"
            type="primary"
            style="float: right; margin-left: 5px"
            size="mini"
            @click="nextStep"
          >
            下阶段
          </el-button>

          <el-button
            icon="el-icon-back"
            type="primary"
            style="float: right; margin-left: 20px"
            size="mini"
            @click="backStep"
          >
            上阶段
          </el-button>
        </div>
        <div class="progress-table-container">
          <el-table :data="Checklist">
            <el-table-column
              show-overflow-tooltip
              prop="checkitem"
              label="任务"
            ></el-table-column>

            <el-table-column show-overflow-tooltip prop="status" label="状态">
              <template slot-scope="{ row }">
                <el-progress
                  :percentage="row.progress"
                  :color="getColor(row.progress)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-card shadow="never">
        <div slot="header">
          <span>动态信息</span>
        </div>
        <el-timeline :reverse="true">
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :timestamp="activity.timestamp"
            :color="activity.color"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  export default {
    name: 'Baseinfo',
    filters: {
      typeTypeMap(status) {
        const statusMap = {
          0: 'success',
          1: 'warning',
          2: 'info',
        }
        return statusMap[status]
      },
      typeNameMap(status) {
        const statusMap = {
          0: '后端',
          1: '前端',
          2: '半弹窗',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        layout: 'total, sizes, prev, pager, next',
        listLoading: false,
        upgrade_id: '',
        isEditing: false,
        elementLoadingText: '加载中...',
        baseinfo: {
          upgrade_id: 'U20230516152609',
          flow_uid: '0',
          version_name: 'bug修复',
          owner: 'dongxin',
          line_name: 'AI赋能',
          create_time: '2023-05-17 00:00:00',
          plan_date: '2023-05-17',
          plan_time: '08:30:00',
          tapd_link:
            'https://www.tapd.cn/54232736/prong/iterations/card_view?q=52abc58fbd23bdf766736a0df0a6fa97',
          prd_appover: '陈子乔',
          test_appover: '黄洁芳',
          front_appover: '任佳',
          back_appover: '苏任',
          arch_appover: '王志军',
          prd_confirm: '张清亮',
          qa_appover: '陈子乔',
          change_log: '1.后台-文字违规列表优化',
          git_branch: 'vip-21212-hotfix',
        },
        upgradeServices: [
          {
            appname: 'increase',
            name: '会员增长',
            git_project: 'wsxc/szwego-increase',
            type: 0,
            id: 1,
            upgrade_id: 'U20230516152609',
            project_id: 19,
            update_time: '2023-05-26 11:26:53',
            create_time: '2023-05-26 11:27:44',
            status: 1,
            owner: 'dongxin',
            git_branch: 'hotfix-202305',
            latest_build_time: '2023-05-26 11:27:44',
            latest_sequence_id: null,
            publish_count: 21,
            artifact_tag: 'prod-hotfix-0523-001',
          },
          {
            appname: 'h5portal',
            name: '前端-portal',
            git_project: 'wsxch5/wsxc_portal',
            type: 1,
            id: 2,
            upgrade_id: 'U20230516152609',
            project_id: 26,
            update_time: '2023-05-26 11:26:53',
            create_time: '2023-05-26 11:27:44',
            status: 1,
            owner: 'dongxin',
            git_branch: 'hotfix-202305',
            latest_build_time: '2023-05-26 11:27:44',
            latest_sequence_id: null,
            publish_count: 21,
            artifact_tag: 'ndafg1daa',
          },
        ],
        activities: [
          {
            content: '辜凤莲 构建acs服务。',
            timestamp: '2023-05-24 15:00',
          },
          {
            content: 'acs服务镜像制作成功。',
            timestamp: '2023-05-25 12:23',
          },
          {
            content: '陈涛 构建increase服务。',
            timestamp: '2023-05-25 12:28',
          },

          {
            content: 'increase服务镜像构建失败',
            timestamp: '2023-05-28 14:23',
          },
        ],
        Checklist: [
          { checkitem: '后端服务镜像构建', progress: 100 },
          { checkitem: '前端文件构建', progress: 0 },
          { checkitem: '半弹窗文件构建', progress: 0 },
          { checkitem: '发版流程审批', progress: 30 },
        ],
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keywords: '',
        },
        selectRows: [],
      }
    },
    computed: {
      // 过滤数据
      filteredData() {
        if (this.queryForm.keywords === '') {
          return this.upgradeServices
        } else {
          return this.upgradeServices.filter(
            (item) =>
              item.name.includes(this.queryForm.keywords) ||
              item.appname.includes(this.queryForm.keywords) ||
              item.git_project.includes(this.queryForm.keywords)
          )
        }
      },
      // 当前页的数据
      currentPageData() {
        const start = (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        const end = this.queryForm.pageNo * this.queryForm.pageSize
        return this.filteredData.slice(start, end)
      },
    },
    created() {
      this.upgrade_id = this.$route.params.upgrade_id
    },
    beforeDestroy() {},
    mounted() {},
    methods: {
      handleEdit() {
        this.isEditing = !this.isEditing
      },
      getColor(progress) {
        if (progress < 50) {
          return '#f5222d'
        } else if (progress < 100) {
          return '#faad14'
        } else {
          return '#52c41a'
        }
      },
      setSelectRows(val) {
        this.selectRows = val
      },
      progressText(row) {
        return row.text
      },
      nextStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(2)
      },
      backStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(0)
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      handleBuild() {},
      handleGetbuildlog() {},
    },
  }
</script>

<style lang="scss" scoped>
  .linktext {
    color: #979ba5;
    white-space: nowrap; /* 规定文本不换行 */
    overflow: hidden; /* 超出宽度隐藏 */
    text-overflow: ellipsis; /* 超出宽度显示省略号 */
    min-width: 100px; /* 规定最大宽度 */
    max-width: 400px; /* 规定最大宽度 */
    display: inline-block; /* 使文本宽度生效 */
  }
  .progress-table-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .stepcard {
    border: 2px dashed #ddd; /* 设置边框样式 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    //background-image: linear-gradient(to bottom, #f0f0f0, #eee);
  }
</style>
