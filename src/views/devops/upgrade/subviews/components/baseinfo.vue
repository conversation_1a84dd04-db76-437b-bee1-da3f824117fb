<template>
  <el-row :gutter="10" type="flex">
    <!-- 外层col     -->
    <el-col :xs="24" :sm="24" :md="17" :lg="17" :xl="17">
      <!-- 基本信息     -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card shadow="never">
          <el-descriptions
            class="margin-top"
            title="基本信息"
            :column="3"
            size="medium"
            border
          >
            <template slot="extra">
              <el-button type="primary" size="mini" @click="handleEdit">
                编辑
              </el-button>
            </template>
            <el-descriptions-item>
              <template slot="label">变更ID</template>
              {{ upgrade_id }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">变更标题</template>
              <el-input
                v-model="baseinfo.version_name"
                :disabled="!isEditing"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class=""></i>
                所属业务
              </template>
              <el-input
                v-model="baseinfo.line_name"
                :disabled="!isEditing"
              ></el-input>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">创建人</template>
              {{ baseinfo.owner }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">计划日期</template>
              <el-input
                v-model="baseinfo.plan_date"
                :disabled="!isEditing"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">计划时间</template>
              <el-input
                v-model="baseinfo.plan_time"
                :disabled="!isEditing"
              ></el-input>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">测试验证</template>
              <el-select
                v-model="baseinfo.test_appover"
                filterable
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in getNameListByName('测试验证')()"
                  :key="item.open_id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">产品验证</template>
              <el-select
                v-model="baseinfo.prd_appover"
                filterable
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in getNameListByName('产品验证')()"
                  :key="item.open_id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">前端验证</template>
              <el-select
                v-model="baseinfo.front_appover"
                filterable
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in getNameListByName('前端验证')()"
                  :key="item.open_id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">后端验证</template>
              <el-select
                v-model="baseinfo.back_appover"
                filterable
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in getNameListByName('后端验证')()"
                  :key="item.open_id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">架构验证</template>
              <el-select
                v-model="baseinfo.arch_appover"
                filterable
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in getNameListByName('架构确认人')()"
                  :key="item.open_id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">产品验收</template>
              <el-select
                v-model="baseinfo.prd_confirm"
                filterable
                :disabled="!isEditing"
              >
                <el-option
                  v-for="item in getNameListByName('产品验收环节')()"
                  :key="item.node_id"
                  :label="item.name"
                  :value="item.name"
                ></el-option>
              </el-select>
            </el-descriptions-item>

            <el-descriptions-item v-html="true">
              <template slot="label">统一分支</template>
              <el-input
                v-model="baseinfo.git_branch"
                :disabled="!isEditing"
              ></el-input>
            </el-descriptions-item>

            <el-descriptions-item :span="16" v-html="true">
              <template slot="label">需求文档</template>
              <el-input
                v-if="isEditing"
                v-model="baseinfo.tapd_link"
              ></el-input>
              <a
                v-if="!isEditing"
                class="linktext"
                target="_blank"
                :href="baseinfo.tapd_link"
                :disabled="!isEditing"
              >
                {{ baseinfo.tapd_link }}
              </a>
            </el-descriptions-item>

            <el-descriptions-item :span="24">
              <template slot="label">变更记录</template>
              <el-input
                v-model="baseinfo.change_log"
                type="textarea"
                :rows="3"
                :disabled="!isEditing"
              ></el-input>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      <!--服务选择-->
      <!--后端-->
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <el-card shadow="never">
          <div slot="header">
            <span><b>后端服务</b></span>
            <el-button
              type="primary"
              style="float: right; margin-left: 20px"
              size="mini"
              @click="showdrawer = true"
            >
              编辑
            </el-button>
          </div>
          <el-table
            v-loading="listLoading"
            :data="backServices"
            :element-loading-text="elementLoadingText"
            @selection-change=""
          >
            <el-table-column
              show-overflow-tooltip
              prop="appname"
              label="项目名"
            ></el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="name"
              label="服务名称"
            ></el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!--前端-->
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <el-card shadow="never">
          <div slot="header">
            <span><b>前端服务</b></span>

            <el-button
              type="primary"
              style="float: right; margin-left: 20px"
              size="mini"
              @click="showdrawer = true"
            >
              编辑
            </el-button>
          </div>
          <el-table
            v-loading="listLoading"
            :data="frontServices"
            :element-loading-text="elementLoadingText"
          >
            <el-table-column
              show-overflow-tooltip
              prop="appname"
              label="项目名"
            ></el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="name"
              label="服务名称"
            ></el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!--半弹窗-->
      <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="8">
        <el-card shadow="never">
          <div slot="header">
            <span><b>半弹窗</b></span>

            <el-button
              type="primary"
              style="float: right; margin-left: 20px"
              size="mini"
              @click="showdrawer = true"
            >
              编辑
            </el-button>
          </div>
          <el-table
            v-loading="listLoading"
            :data="windowServices"
            :element-loading-text="elementLoadingText"
            @selection-change=""
          >
            <el-table-column
              show-overflow-tooltip
              prop="appname"
              label="项目名"
            ></el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="name"
              label="服务名称"
            ></el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-col>

    <el-col :xs="24" :sm="24" :md="7" :lg="7" :xl="7">
      <el-card class="stepcard" shadow="always">
        <div slot="header">
          <span>阶段进度</span>
          <el-button
            icon="el-icon-right"
            type="primary"
            style="float: right; margin-left: 5px"
            size="mini"
            @click="nextStep"
          >
            下阶段
          </el-button>

          <el-button
            icon="el-icon-refresh"
            type="warning"
            style="float: right; margin-left: 20px"
            size="mini"
            @click=""
          >
            自检
          </el-button>
        </div>
        <div class="progress-table-container">
          <el-table :data="baseChecklist">
            <el-table-column
              show-overflow-tooltip
              prop="checkitem"
              label="任务"
            ></el-table-column>

            <el-table-column show-overflow-tooltip prop="status" label="状态">
              <template slot-scope="{ row }">
                <el-progress
                  :percentage="row.progress"
                  :color="getColor(row.progress)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-card shadow="never">
        <div slot="header">
          <span>动态信息</span>
        </div>
        <el-timeline :reverse="true">
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :timestamp="activity.timestamp"
            :color="activity.color"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-col>

    <el-drawer
      :with-header="false"
      :visible.sync="showdrawer"
      direction="rtl"
      :size="660"
    >
      <el-card shadow="never">
        <div slot="header">
          <span>
            <b>{{ title }}</b>
            (已选：{{ ServicesCount }})
          </span>
        </div>

        <el-form
          :inline="true"
          :model="queryForm"
          style="float: right; margin-left: 5px"
          @submit.native.prevent
        >
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keywords"
              placeholder="过滤关键字"
              size="mini"
              clearable
            />
          </el-form-item>
        </el-form>

        <el-table :data="currentPageData">
          <el-table-column
            show-overflow-tooltip
            type="selection"
          ></el-table-column>
          <el-table-column
            property="appname"
            label="项目名"
            width="120"
          ></el-table-column>
          <el-table-column
            property="name"
            label="服务名"
            width="180"
          ></el-table-column>
          <el-table-column
            property="git_project"
            label="工程名"
          ></el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="filteredData.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-card>

      <div style="float: right; margin-left: 5px; padding: 50px">
        <el-button size="medium" @click="showdrawer = false">取 消</el-button>
        <el-button size="medium" type="primary" :loading="loading" @click="">
          {{ loading ? '保存中 ...' : '保 存' }}
        </el-button>
      </div>
    </el-drawer>
  </el-row>
</template>

<script>
  import { feishuApprovalInfo } from '@/api/ciUpgrade'

  export default {
    name: 'Baseinfo',
    filters: {},
    data() {
      return {
        title: '后端服务',
        activeTab: 'backend',
        showdrawer: false,
        layout: 'total, sizes, prev, pager, next',
        loading: false,
        listLoading: false,
        upgrade_id: '',
        isEditing: false,
        elementLoadingText: '加载中...',
        total: 128,
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keywords: '',
        },
        feishu_nodedata: [],
        baseinfo: {
          upgrade_id: 'U20230516152609',
          flow_uid: '0',
          version_name: 'bug修复',
          owner: 'dongxin',
          line_name: 'AI赋能',
          create_time: '2023-05-17 00:00:00',
          plan_date: '2023-05-17',
          plan_time: '08:30:00',
          tapd_link:
            'https://www.tapd.cn/54232736/prong/iterations/card_view?q=52abc58fbd23bdf766736a0df0a6fa97',
          prd_appover: '陈子乔',
          test_appover: '黄洁芳',
          front_appover: '任佳',
          back_appover: '苏任',
          arch_appover: '王志军',
          prd_confirm: '张清亮',
          qa_appover: '陈子乔',
          change_log: '1.后台-文字违规列表优化',
          git_branch: 'vip-21212-hotfix',
        },
        allProjects: [],
        backServices: [
          {
            appname: 'acs',
            git_project: 'wsxc/wego-album',
            name: '新相册',
            project: 'wego-album',
            type: '后端',
          },
          {
            appname: 'bizsync',
            git_project: 'wsxc/szwego-bizsync',
            name: '业务数据同步',
            project: 'bizsync',
            type: '后端',
          },
          {
            appname: 'scs-bat',
            git_project: 'wsxc/szwego-commodity',
            name: '新商品-报价单',
            project: 'commodity-bat',
            type: '后端',
          },
          {
            appname: 'minilive',
            git_project: 'wsxc/szwego-miniapplives',
            name: '小程序直播',
            project: 'miniapplives',
            type: '后端',
          },
          {
            appname: 'vips',
            git_project: 'wsxc/szwego-vipstatus',
            name: '会员状态服务',
            project: 'vipstatus',
            type: '后端',
          },
        ],
        frontServices: [
          {
            appname: 'h5portal',
            git_project: 'wsxch5/wsxc_portal',
            name: '前端-portal',
            project: 'wsxc_portal',
            type: '前端',
          },
          {
            appname: 'h5album',
            git_project: 'wsxch5/wsxc_album',
            name: '前端-album',
            project: 'wsxc_album',
            type: '前端',
          },
          {
            appname: 'h5order',
            git_project: 'wsxch5/wsxc_order',
            name: '前端-order',
            project: 'wsxc_order',
            type: '前端',
          },
          {
            appname: 'h5biz',
            git_project: 'wsxch5/wsxc_biz',
            name: '前端-biz',
            project: 'wsxc_biz',
            type: '前端',
          },
        ],
        windowServices: [
          {
            appname: 'popup_batchShareSvip',
            git_project: 'wsxch5/popup-workspaces',
            name: 'App半弹窗popup_batchShareSvip',
            project: 'popup_batchShareSvip',
            type: '前端',
          },
          {
            appname: 'popup_viewDevices',
            git_project: 'wsxch5/popup-workspaces',
            name: 'App半弹窗popup_viewDevices',
            project: 'popup_viewDevices',
            type: '前端',
          },
        ],
        activities: [
          {
            content: '辜凤莲 创建了变更计划。',
            timestamp: '2023-05-14 15:00',
          },
          {
            content: '辜凤莲 更新了变更基本信息。',
            timestamp: '2023-05-15 12:23',
          },
          {
            content: '辜凤莲 增加了后端服务acs。',
            timestamp: '2023-05-15 12:28',
          },

          {
            content: '邹涵东 增加了前端服务h5biz。',
            timestamp: '2023-05-18 14:23',
          },
        ],
        baseChecklist: [
          { checkitem: '基本信息填写', progress: 100 },
          { checkitem: '变更服务确认', progress: 100 },
          { checkitem: '代码合并检查', progress: 100 },
          { checkitem: '发版流程创建', progress: 30 },
        ],
      }
    },
    computed: {
      ServicesCount() {
        return this.backServices.length
      },
      // 过滤数据
      filteredData() {
        if (this.queryForm.keywords === '') {
          return this.allProjects
        } else {
          return this.allProjects.filter(
            (item) =>
              item.name.includes(this.queryForm.keywords) ||
              item.appname.includes(this.queryForm.keywords) ||
              item.git_project.includes(this.queryForm.keywords)
          )
        }
      },
      // 当前页的数据
      currentPageData() {
        const start = (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        const end = this.queryForm.pageNo * this.queryForm.pageSize
        return this.filteredData.slice(start, end)
      },
      //人员选择下拉框过滤数据
      getNameListByName1(name) {
        const node = this.feishu_nodedata.find((item) => item.name === name)
        return node ? node.name_list : []
      },
    },
    created() {
      this.upgrade_id = this.$route.params.upgrade_id
      this.fetchData()
    },
    beforeDestroy() {},
    mounted() {},
    methods: {
      handleEdit() {
        this.isEditing = !this.isEditing
      },
      getColor(progress) {
        if (progress < 50) {
          return '#f5222d'
        } else if (progress < 100) {
          return '#faad14'
        } else {
          return '#52c41a'
        }
      },
      progressText(row) {
        return row.text
      },
      nextStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(1)
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      getNameListByName(name) {
        return () => {
          const node = this.feishu_nodedata.find((item) => item.name === name)
          return node ? node.name_list : []
        }
      },
      async fetchData() {
        const { node_data } = await feishuApprovalInfo()
        this.feishu_nodedata = node_data
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>
<style>
  .el-descriptions-item__label {
    white-space: nowrap;
  }
</style>

<style lang="scss" scoped>
  .linktext {
    color: #979ba5;
    white-space: nowrap; /* 规定文本不换行 */
    overflow: hidden; /* 超出宽度隐藏 */
    text-overflow: ellipsis; /* 超出宽度显示省略号 */
    min-width: 100px; /* 规定最大宽度 */
    max-width: 450px; /* 规定最大宽度 */
    display: inline-block; /* 使文本宽度生效 */
  }
  .progress-table-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .stepcard {
    border: 2px dashed #ddd; /* 设置边框样式 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    //background-image: linear-gradient(to bottom, #f0f0f0, #eee);
  }
  .not-wrap-text .el-table__cell {
    white-space: nowrap;
  }
</style>
