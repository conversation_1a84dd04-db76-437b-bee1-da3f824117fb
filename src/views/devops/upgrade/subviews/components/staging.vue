<template>
  <el-row :gutter="10" type="flex">
    <!-- 外层col     -->
    <el-col :xs="24" :sm="24" :md="20" :lg="20" :xl="20">
      <!-- 后端服务     -->
      <el-card>
        <div slot="header">
          <span>后端服务</span>
          <el-button
            type="primary"
            style="float: right; margin-left: 20px"
            size="mini"
            @click=""
          >
            更新全部
          </el-button>
        </div>

        <el-col
          v-for="(service, index) in upgradeServices"
          :key="index"
          :xs="24"
          :sm="24"
          :md="8"
          :lg="8"
          :xl="8"
        >
          <el-card>
            <div slot="header">
              <span>{{ service.name }}</span>
              <el-button
                type="primary"
                style="float: right; margin-left: 20px"
                size="mini"
                :disabled="service.upgradeStatus === 'success'"
                @click=""
              >
                更新
              </el-button>
            </div>

            <div class="version-info">
              <div class="version-row">
                <span>当前镜像：</span>
                <el-tag type="info">{{ service.currentVersion }}</el-tag>
              </div>
              <div class="version-change-icon">
                <i class="el-icon-bottom"></i>
                <!-- 修改箭头方向 -->
              </div>
              <div class="version-row">
                <span>更新镜像：</span>
                <el-tag type="success">{{ service.artifact_tag }}</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-card>

      <!-- 前端服务     -->
      <el-card>
        <div slot="header">
          <span>前端服务</span>
          <el-button
            type="primary"
            style="float: right; margin-left: 20px"
            size="mini"
            @click=""
          >
            更新全部
          </el-button>
        </div>
        <el-col
          v-for="(service, index) in upgradeServices"
          :key="index"
          :xs="24"
          :sm="24"
          :md="8"
          :lg="8"
          :xl="8"
        >
          <el-card>
            <div slot="header">
              <span>{{ service.name }}</span>
              <el-button
                type="primary"
                style="float: right; margin-left: 20px"
                size="mini"
                :disabled="service.upgradeStatus === 'success'"
                @click=""
              >
                更新
              </el-button>
            </div>

            <div class="version-info">
              <div class="version-row">
                <span>当前目录：</span>
                <el-tag type="info">{{ service.currentVersion }}</el-tag>
              </div>
              <div class="version-change-icon">
                <i class="el-icon-bottom"></i>
                <!-- 修改箭头方向 -->
              </div>
              <div class="version-row">
                <span>更新目录：</span>
                <el-tag type="success">{{ service.artifact_tag }}</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-card>

      <!-- 半弹窗服务     -->
      <el-card>
        <div slot="header">
          <span>半弹窗</span>
          <el-button
            type="primary"
            style="float: right; margin-left: 20px"
            size="mini"
            @click=""
          >
            更新全部
          </el-button>
        </div>

        <el-col
          v-for="(service, index) in upgradeServices"
          :key="index"
          :xs="24"
          :sm="24"
          :md="8"
          :lg="8"
          :xl="8"
        >
          <el-card>
            <div slot="header">
              <span>{{ service.name }}</span>
              <el-button
                type="primary"
                style="float: right; margin-left: 20px"
                size="mini"
                :disabled="service.upgradeStatus === 'success'"
                @click=""
              >
                更新
              </el-button>
            </div>

            <div class="version-info">
              <div class="version-row">
                <span>当前目录：</span>
                <el-tag type="info">{{ service.currentVersion }}</el-tag>
              </div>
              <div class="version-change-icon">
                <i class="el-icon-bottom"></i>
                <!-- 修改箭头方向 -->
              </div>
              <div class="version-row">
                <span>更新目录：</span>
                <el-tag type="success">{{ service.artifact_tag }}</el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-card>
    </el-col>

    <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
      <el-card class="stepcard" shadow="always">
        <div slot="header">
          <span>阶段进度</span>
          <el-button
            icon="el-icon-right"
            type="primary"
            style="float: right; margin-left: 5px"
            size="mini"
            @click="nextStep"
          >
            下阶段
          </el-button>

          <el-button
            icon="el-icon-back"
            type="primary"
            style="float: right; margin-left: 20px"
            size="mini"
            @click="backStep"
          >
            上阶段
          </el-button>
        </div>
        <div class="progress-table-container">
          <el-table :data="Checklist">
            <el-table-column
              show-overflow-tooltip
              prop="checkitem"
              label="任务"
            ></el-table-column>

            <el-table-column show-overflow-tooltip prop="status" label="状态">
              <template slot-scope="{ row }">
                <el-progress
                  :percentage="row.progress"
                  :color="getColor(row.progress)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <el-card shadow="never">
        <div slot="header">
          <span>动态信息</span>
        </div>
        <el-timeline :reverse="true">
          <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :timestamp="activity.timestamp"
            :color="activity.color"
          >
            {{ activity.content }}
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  export default {
    name: 'Baseinfo',
    filters: {},
    data() {
      return {
        listLoading: false,
        upgrade_id: '',
        isEditing: false,
        elementLoadingText: '加载中...',
        baseinfo: {
          upgrade_id: 'U20230516152609',
          flow_uid: '0',
          version_name: 'bug修复',
          owner: 'dongxin',
          line_name: 'AI赋能',
          create_time: '2023-05-17 00:00:00',
          plan_date: '2023-05-17',
          plan_time: '08:30:00',
          tapd_link:
            'https://www.tapd.cn/54232736/prong/iterations/card_view?q=52abc58fbd23bdf766736a0df0a6fa97',
          prd_appover: '陈子乔',
          test_appover: '黄洁芳',
          front_appover: '任佳',
          back_appover: '苏任',
          arch_appover: '王志军',
          prd_confirm: '张清亮',
          qa_appover: '陈子乔',
          change_log: '1.后台-文字违规列表优化',
          git_branch: 'vip-21212-hotfix',
        },
        activities: [
          {
            content: '辜凤莲 创建了变更计划。',
            timestamp: '2023-05-14 15:00',
          },
          {
            content: '辜凤莲 更新了变更基本信息。',
            timestamp: '2023-05-15 12:23',
          },
          {
            content: '辜凤莲 增加了后端服务acs。',
            timestamp: '2023-05-15 12:28',
          },

          {
            content: '邹涵东 增加了前端服务h5biz。',
            timestamp: '2023-05-18 14:23',
          },
        ],
        Checklist: [
          { checkitem: '后端服务镜像构建', progress: 100 },
          { checkitem: '前端服务构建', progress: 0 },
          { checkitem: '半弹窗文件构建', progress: 0 },
          { checkitem: '功能验收', progress: 40 },
          { checkitem: '发版流程审批', progress: 30 },
        ],
        upgradeServices: [
          {
            appname: 'increase',
            name: '会员增长',
            git_project: 'wsxc/szwego-increase',
            type: 0,
            id: 1,
            upgrade_id: 'U20230516152609',
            project_id: 19,
            update_time: '2023-05-26 11:26:53',
            create_time: '2023-05-26 11:27:44',
            status: 1,
            owner: 'dongxin',
            git_branch: 'hotfix-202305',
            latest_build_time: '2023-05-26 11:27:44',
            latest_sequence_id: null,
            publish_count: 21,
            currentVersion: 'prod-vip128',
            artifact_tag: 'prod-hotfix-0523-001',
          },
          {
            appname: 'h5portal',
            name: '前端-portal',
            git_project: 'wsxch5/wsxc_portal',
            type: 1,
            id: 2,
            upgrade_id: 'U20230516152609',
            project_id: 26,
            update_time: '2023-05-26 11:26:53',
            create_time: '2023-05-26 11:27:44',
            status: 1,
            owner: 'dongxin',
            git_branch: 'hotfix-202305',
            latest_build_time: '2023-05-26 11:27:44',
            latest_sequence_id: null,
            publish_count: 21,
            currentVersion: 'prod-vip128',
            artifact_tag: 'ndafg1daa',
          },
        ],
      }
    },
    created() {
      this.upgrade_id = this.$route.params.upgrade_id
    },
    beforeDestroy() {},
    mounted() {},
    methods: {
      handleEdit() {
        this.isEditing = !this.isEditing
      },
      getColor(progress) {
        if (progress < 50) {
          return '#f5222d'
        } else if (progress < 100) {
          return '#faad14'
        } else {
          return '#52c41a'
        }
      },
      progressText(row) {
        return row.text
      },
      nextStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(3)
      },
      backStep() {
        const parent = this.$parent.$parent
        // 在父级 Vue 实例上调用名为 handleParentMethod 的方法
        parent.ChangeStep(1)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .linktext {
    color: #979ba5;
    white-space: nowrap; /* 规定文本不换行 */
    overflow: hidden; /* 超出宽度隐藏 */
    text-overflow: ellipsis; /* 超出宽度显示省略号 */
    min-width: 100px; /* 规定最大宽度 */
    max-width: 400px; /* 规定最大宽度 */
    display: inline-block; /* 使文本宽度生效 */
  }
  .progress-table-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: -10px;
    margin-bottom: 10px;
  }
  .stepcard {
    border: 2px dashed #ddd; /* 设置边框样式 */
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
    //background-image: linear-gradient(to bottom, #f0f0f0, #eee);
  }

  .version-info {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .version-row {
    display: flex;
    align-items: center;
  }

  .version-change-icon {
    font-size: 24px; /* 调整图标大小 */
    margin: 8px 0; /* 添加上下边距 */
  }
</style>
