<template>
  <div class="wizzard-container">
    <el-row :gutter="20" style="display: flex">
      <el-col
        :xs="24"
        :sm="24"
        :md="20"
        :lg="20"
        :xl="20"
        style="display: flex"
      >
        <el-card shadow="never" style="flex: 1">
          <el-steps
            :active="currentStep"
            finish-status="success"
            process-status="process"
          >
            <el-step
              title="基本信息"
              description="变更信息、上线前自检、回滚方案确认"
              :clickable="true"
              class="stepClickable"
              @click.native="handleStepClick(0)"
            ></el-step>

            <el-step
              title="打包构建"
              description="服务镜像构建、可用性验证"
              :clickable="true"
              class="stepClickable"
              @click.native="handleStepClick(1)"
            ></el-step>

            <el-step
              title="灰度验证"
              description="B环境发布验收、引流放量"
              :clickable="true"
              class="stepClickable"
              @click.native="handleStepClick(2)"
            ></el-step>

            <el-step
              title="现网发布"
              description="A环境发布"
              :clickable="true"
              class="stepClickable"
              @click.native="handleStepClick(3)"
            ></el-step>

            <el-step
              title="变更结束"
              description="完成、终止或回滚本次变更任务。"
              class="stepClickable"
              @click.native="handleStepClick(4)"
            ></el-step>
          </el-steps>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="4" :lg="4" :xl="4" style="display: flex">
        <el-card
          shadow="never"
          style="
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
          "
        >
          <el-button-group style="position: absolute; right: 0; top: 0">
            <el-tooltip content="点击创建或查看审批" placement="top-end">
              <el-button
                icon="el-icon-link"
                size="mini"
                @click="handleFeishu"
              ></el-button>
            </el-tooltip>
          </el-button-group>
          <div class="statistic-container">
            <el-statistic title="审批流程">
              <template slot="formatter">未创建</template>
            </el-statistic>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row>
      <baseinfo v-show="currentStep === 0"></baseinfo>
      <buildinfo v-show="currentStep === 1"></buildinfo>
      <staging v-show="currentStep === 2"></staging>
      <publish v-show="currentStep === 3"></publish>
      <finish v-show="currentStep === 4"></finish>
    </el-row>
  </div>
</template>

<script>
  import baseinfo from './components/baseinfo'
  import buildinfo from './components/buildinfo'
  import staging from './components/staging'
  import publish from './components/publish'
  import finish from './components/finish'
  import { feishuGetStatus, getUpgradeDetails } from '@/api/ciUpgrade'

  export default {
    name: 'UpgradeWizzard',
    components: { baseinfo, buildinfo, staging, publish, finish },
    filters: {},
    data() {
      return {
        upgrade_id: '',
        currentStep: 0,
      }
    },
    created() {
      this.upgrade_id = this.$route.params.upgrade_id
      this.fetchData()
    },
    beforeDestroy() {},
    mounted() {},
    methods: {
      ChangeStep(step) {
        this.currentStep = step
      },
      handleStepClick(step) {
        this.currentStep = step
      },
      handleFeishu() {
        this.feishulinkPop('1A44ABDC-2C42-4649-99CE-FB649B1239B6')
      },
      feishulinkPop(instance_code) {
        // console.log("https://applink.feishu.cn/client/mini_program/open?appId=cli_9cb844403dbb9108&mode=appCenter&path=pc%2Fpages%2Fin-process%2Findex%3FenableTrusteeship%3Dtrue%26instanceId%3D"+ instance_code +"%26source%3Dapproval_bot&relaunch=true")
        window.open(
          'https://applink.feishu.cn/client/mini_program/open?appId=cli_9cb844403dbb9108&mode=appCenter&path=pc%2Fpages%2Fin-process%2Findex%3FenableTrusteeship%3Dtrue%26instanceId%3D' +
            instance_code +
            '%26source%3Dapproval_bot&relaunch=true',
          '_blank'
        )
      },
      async fetchData() {
        const Loading = this.$baseLoading(6, '查询任务状态...')
        try {
          const response = await getUpgradeDetails(this.upgrade_id)

          if (response.status === 404) {
            // 关闭当前页面
            this.redirect === '/401'
          } else {
            const { data } = response
            setTimeout(() => {
              Loading.close()
            }, 500)
          }
        } catch (error) {
          console.error('Error fetching data:', error)
          this.$router.go(-1)
          Loading.close()
        }
      },
    },
  }
</script>

<style scoped>
  .stepClickable :hover {
    cursor: pointer;
  }
  .stepUnclickable :hover {
    cursor: not-allowed;
  }
  .statistic-container {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    height: 100%;
  }
</style>
