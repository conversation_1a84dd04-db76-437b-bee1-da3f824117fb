<template>
  <div class="uri-rule-management">
    <el-card shadow="never">
      <el-alert title="URI灰度规则说明" type="info">
        <div class="ci-alert-list">
          ○ 列出所有有效的 URI 规则（状态为有效，即 0）
        </div>
        <div class="ci-alert-list">○ 点击规则可进行编辑操作</div>
        <div class="ci-alert-list">○ URI 规则支持精准匹配和前缀匹配</div>
        <p />
      </el-alert>

      <vab-query-form>
        <vab-query-form-left-panel :span="12">
          <el-button icon="el-icon-plus" type="primary" @click="handleUriEdit">
            URI规则
          </el-button>
        </vab-query-form-left-panel>
        <vab-query-form-right-panel :span="12">
          <el-form :inline="true" :model="queryUriForm" @submit.native.prevent>
            <el-form-item>
              <el-input
                v-model.trim="queryUriForm.keyword"
                clearable
                placeholder="URI / 环境ID"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                icon="el-icon-search"
                type="primary"
                @click="queryUriData"
              >
                查询
              </el-button>
            </el-form-item>
          </el-form>
        </vab-query-form-right-panel>
      </vab-query-form>

      <div class="uri-table">
        <el-table
          v-if="uriDataLoaded"
          ref="uriTableRef"
          v-loading="uriTableLoading"
          :data="uriRuleList"
          :element-loading-text="elementLoadingText"
        >
          <el-table-column
            label="环境ID"
            min-width="100"
            prop="pre_uuid"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div
                class="droppable"
                @drop="handleUriDrop(scope.row)"
                @dragover.prevent
              >
                <el-link
                  :underline="false"
                  style="color: #3f9eff"
                  type="primary"
                  @click="handleUriEdit(scope.row)"
                >
                  {{ scope.row.pre_uuid }}
                </el-link>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="URI"
            min-width="220"
            prop="uri"
            show-overflow-tooltip
          >
            <template #default="scope">
              <el-link
                :underline="false"
                type="primary"
                @click="handleUriEdit(scope.row)"
              >
                {{ scope.row.uri }}
              </el-link>
            </template>
          </el-table-column>

          <el-table-column
            label="匹配类型"
            min-width="100"
            prop="match_type"
            show-overflow-tooltip
          >
            <template #default="scope">
              <el-tag :type="scope.row.match_type === 0 ? 'success' : 'danger'">
                {{ scope.row.match_type === 0 ? '精准匹配' : '前缀匹配' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="备注" prop="memo" show-overflow-tooltip>
            <template #default="scope">
              <div
                class="draggable"
                draggable="true"
                @dragstart="handleUriDragStart(scope.row)"
              >
                {{ scope.row.memo }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="添加时间"
            min-width="150"
            prop="create_time"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            header-align="center"
            label="操作"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div class="operation-buttons">
                <el-button
                  size="mini"
                  style="color: #f68989"
                  @click="handleDeleteUri(scope.row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        :current-page="queryUriForm.pageNo"
        :layout="layout"
        :page-size="queryUriForm.pageSize"
        :page-sizes="[15, 30, 50, 100]"
        :total="uriTotal"
        background
        @size-change="handleSizeChangeUri"
        @current-change="handleCurrentChangeUri"
      ></el-pagination>
    </el-card>

    <env-gray-uri-edit
      ref="env_gray_uri_edit"
      @fetch-data-uri="fetchUriData"
    ></env-gray-uri-edit>
  </div>
</template>

<script>
  import { EnvRulesUri, DeleteUriRules } from '@/api/presm'
  import EnvGrayUriEdit from './PreGrayUriEdit.vue'

  export default {
    name: 'UriRuleManagement',
    components: {
      EnvGrayUriEdit,
    },
    data() {
      return {
        queryUriForm: {
          pageNo: 1,
          pageSize: 15,
          keyword: '',
        },
        uriRuleList: [],
        uriTotal: 0,
        uriDataLoaded: false,
        uriTableLoading: false,
        elementLoadingText: '正在加载...',
        layout: 'total, sizes, prev, pager, next, jumper',
        draggedUri: null,
      }
    },
    mounted() {
      this.fetchUriData()
    },
    methods: {
      async fetchUriData() {
        try {
          this.uriTableLoading = true
          const { data, totalCount } = await EnvRulesUri(this.queryUriForm)
          this.uriRuleList = data || []
          this.uriTotal = totalCount
        } catch (error) {
          console.error('Error fetching uri data:', error)
          this.$baseMessage('获取URI规则失败', 'error')
        } finally {
          this.uriDataLoaded = true
          this.uriTableLoading = false
        }
      },

      handleUriEdit(row) {
        if (row?.id) {
          this.$refs.env_gray_uri_edit.showEdit(row)
        } else {
          this.$refs.env_gray_uri_edit.showEdit()
        }
      },

      async handleDeleteUri(row) {
        this.$baseConfirm('确认删除吗？', null, async () => {
          try {
            const { msg } = await DeleteUriRules(row.id)
            this.$baseMessage(msg, 'success')
            await this.fetchUriData()
          } catch (error) {
            console.error('Error deleting URI rule:', error)
            this.$baseMessage('删除失败', 'error')
          }
        })
      },

      handleUriDragStart(row) {
        this.draggedUri = row
      },

      handleUriDrop(targetRow) {
        if (this.draggedUri) {
          const updatedData = {
            pre_uuid: targetRow.pre_uuid,
            applicant: this.draggedUri.applicant,
            memo: this.draggedUri.memo,
            uri: this.draggedUri.uri,
          }
          this.updateUriEnvironment(updatedData, this.draggedUri.id)
          this.draggedUri = null
        }
      },

      queryUriData() {
        this.queryUriForm.pageNo = 1
        this.fetchUriData()
      },

      handleSizeChangeUri(val) {
        this.queryUriForm.pageSize = val
        this.fetchUriData()
      },

      handleCurrentChangeUri(val) {
        this.queryUriForm.pageNo = val
        this.fetchUriData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .ci-alert-list {
    color: #333;
    line-height: 1.6;
  }

  .uri-table {
    .draggable {
      cursor: grab;
    }

    .droppable {
      border: 2px dashed transparent;

      &:hover {
        border-color: #3f9eff;
      }
    }
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-button {
      padding: 0 8px;
      margin: 0;
      height: 28px;
      line-height: 28px;

      &:hover {
        opacity: 0.8;
      }
    }
  }
</style>
