<template>
  <el-dialog
    :close-on-click-modal="isedit"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-card>
        <el-row>
          <el-col :span="12">
            <el-form-item label="环境" prop="pre_uuid">
              <el-select
                v-model="form.pre_uuid"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in allPreUuid"
                  :key="item.pre_uuid"
                  :label="item.pre_alias"
                  :value="item.pre_uuid"
                >
                  <span style="float: left">{{ item.pre_alias }}</span>
                  <span style="float: right; color: #8492a6; font-size: 8px">
                    {{ item.pre_uuid }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item
              label="相册id"
              placeholder="请输入AlbumId"
              prop="albumid"
            >
              <el-input
                v-model="form.albumid"
                clearable
                placeholder="请输入AlbumId"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注" prop="memo">
              <el-input
                v-model.trim="form.memo"
                :rows="4"
                autocomplete="off"
                spellcheck="false"
                type="textarea"
                placeholder="默认为相册名称"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <album-info
        v-if="form.albumid"
        ref="albumInfo"
        :albumid="form.albumid"
        @info-loaded="handleAlbumInfoLoaded"
      />

      <el-row style="text-align: right">
        <div class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button
            type="primary"
            @click="isedit ? updateGrayRule() : saveGrayRule()"
          >
            {{ isedit ? '提交创建' : '提交创建' }}
          </el-button>
        </div>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
  import AlbumInfo from './AlbumInfo.vue'
  import { EnvRules, getEnvs, postEnvRules } from '@/api/presm'

  export default {
    name: 'PreGrayruleEdit',
    components: {
      AlbumInfo,
    },
    data() {
      const validateMemo = (rule, value, callback) => {
        if (!value || value.trim() === '') {
          callback(new Error('请输入备注信息'))
        } else {
          callback()
        }
      }

      return {
        showAlbumInfo: false,
        form: {
          pre_uuid: '',
          albumid: '',
          memo: '',
        },
        albumIdInfos: null,
        isedit: false,
        allPreUuid: [],
        grayRuleList: [],
        rules: {
          pre_uuid: [
            {
              required: true,
              trigger: 'blur',
              message: '请选择预发布环境',
            },
          ],
          albumid: [
            { required: true, message: '请输入相册ID', trigger: 'blur' },
            {
              validator: this.ListingAlbumId,
              trigger: 'blur',
            },
          ],
          memo: [
            { required: true, message: '请输入备注信息', trigger: 'blur' },
            { validator: validateMemo, trigger: ['blur', 'change'] },
          ],
        },
        title: '',
        dialogFormVisible: false,
      }
    },
    computed: {},
    watch: {
      'form.albumid': {
        async handler(newVal) {
          if (!this.$refs.form) {
            return
          }

          await this.$nextTick()
          this.form.memo = ''

          if (!newVal) {
            this.$refs.form.resetFields(['memo'])
          }
        },
        immediate: true,
      },
    },
    mounted() {
      this.fetchData()
      this.getGrayRules()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '环境用户创建'
          this.isedit = false
        } else {
          this.title = '更新信息'
          this.isedit = true
          this.form = {
            ...row,
          }
        }
        this.fetchData()
        this.getGrayRules()
        this.dialogFormVisible = true
      },
      close() {
        if (this.$refs.form) {
          this.$refs.form.resetFields()
        }
        this.dialogFormVisible = false
      },

      async ListingAlbumId(rule, value, callback) {
        const trimmedValue = value.replace(/\s+/g, '')
        this.form.albumid = trimmedValue
        if (!trimmedValue) {
          return callback(new Error('相册ID不能为空'))
        }

        try {
          const exists = await this.checkAlbumIdExists(trimmedValue)
          if (exists) {
            this.$baseMessage('警告：相册ID已存在', 'warning')
          } else {
            callback()
          }
        } catch (error) {
          callback(new Error('验证异常，联系运维处理'))
        }
      },

      checkAlbumIdExists(albumid) {
        return new Promise((resolve) => {
          if (!this.grayRuleList || !this.allPreUuid) {
            return resolve(false)
          }

          const allRunningOrCreatingPreUuid = this.allPreUuid.map(
            (env) => env.pre_uuid
          )

          const exists = this.grayRuleList.some(
            (rule) =>
              rule.albumid === albumid &&
              allRunningOrCreatingPreUuid.includes(rule.pre_uuid)
          )
          resolve(exists)
        })
      },

      async fetchData() {
        const param = {
          pageNo: 1,
          pageSize: 10000,
        }
        const { data } = await getEnvs(param)
        this.allPreUuid = data.filter(
          (item) => item.status === 1 || item.status === 2
        )
      },

      updateGrayRule() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            return true
          } else {
            return false
          }
        })
      },
      async getGrayRules() {
        try {
          const queryform = {
            pageNo: 1,
            pageSize: 10000,
          }
          const { data } = await EnvRules(queryform)
          this.grayRuleList = data
        } catch (error) {
          console.error('Error fetching data:', error)
        }
      },
      async saveGrayRule() {
        this.loading = true
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            try {
              const { msg } = await postEnvRules(this.form)
              this.$baseMessage(msg, 'success')
              await this.updateData()
              this.dialogFormVisible = false
            } catch (error) {
              console.error('保存失败:', error)
              this.$baseMessage('保存失败，请稍后再试', 'error')
            }
          } else {
            this.$baseMessage('请填写必填项', 'warning')
            return false
          }
          this.loading = false
        })
      },
      updateData() {
        this.close()
        this.$emit('fetch-data-gray')
      },
      handleAlbumInfoLoaded(info) {
        if (info && info.albumName) {
          this.$set(this.form, 'memo', info.albumName)
          this.$nextTick(() => {
            this.$refs.form.validateField('memo')
          })
        }
      },
    },
  }
</script>
