<template>
  <div class="gray-rule-management">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-alert title="" type="info" style="margin-bottom: 10px">
          <div class="ci-alert-list" style="color: #333; line-height: 1.6">
            <div>
              ○ 只列出环境状态为
              <strong style="color: #409eff">[创建中]</strong>
              和
              <strong style="color: #409eff">[运行中]</strong>
            </div>
            <div>
              ○ 点击
              <strong style="color: #409eff">[albumid]</strong>
              可查看相信信息
            </div>
            <div>
              ○
              <strong style="color: #409eff">[albumid]</strong>
              可拖拽到
              <strong style="color: #409eff">[环境名]</strong>
              实现
              <strong style="color: #409eff">[更新]</strong>
            </div>
          </div>
        </el-alert>

        <el-card shadow="never">
          <vab-query-form>
            <vab-query-form-left-panel :span="12">
              <el-button
                icon="el-icon-plus"
                type="primary"
                @click="handleGrayEdit"
              >
                规则
              </el-button>
            </vab-query-form-left-panel>
            <vab-query-form-right-panel :span="12">
              <el-form
                :inline="true"
                :model="queryGrayForm"
                @submit.native.prevent
              >
                <el-form-item>
                  <el-input
                    v-model.trim="queryGrayForm.keyword"
                    clearable
                    placeholder="albumid/环境ID"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="queryGrayData"
                  >
                    查询
                  </el-button>
                </el-form-item>
              </el-form>
            </vab-query-form-right-panel>
          </vab-query-form>

          <el-table
            v-if="isDataLoaded && allEnvData"
            ref="tableRef"
            v-loading="grayTableLoading"
            :data="displayGrayList"
            :element-loading-text="elementLoadingText"
            :span-method="arraySpanMethod"
            :row-class-name="rowClassName"
            class="gray-table"
          >
            <el-table-column
              label="环境名"
              min-width="100"
              prop="pre_uuid"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div
                  class="droppable"
                  @drop="handleDrop(scope.row)"
                  @dragover.prevent
                >
                  <el-link
                    :underline="false"
                    style="color: #3f9eff"
                    type="primary"
                    @click="handleRuleView(scope.row)"
                  >
                    {{ getEnvName(scope.row.pre_uuid) }}
                  </el-link>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="相册ID"
              min-width="160"
              prop="albumid"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div
                  class="draggable"
                  draggable="true"
                  @dragstart="handleDragStart(scope.row)"
                >
                  <el-link
                    :underline="false"
                    type="primary"
                    @click="showAlbumInfo(scope.row.albumid)"
                  >
                    {{ scope.row.albumid }}
                  </el-link>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="备注"
              min-width="120"
              prop="memo"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="环境状态"
              min-width="80"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div class="status-cell">
                  <el-tag :type="getEnvStatus(scope.row.pre_uuid).type">
                    {{ getEnvStatus(scope.row.pre_uuid).text }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              header-align="center"
              label="操作"
              min-width="100"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div class="presm-operation-buttons">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleDeleteRule(row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page="queryGrayForm.pageNo"
            :layout="layout"
            :page-size="queryGrayForm.pageSize"
            :page-sizes="[15, 30, 50, 100]"
            :total="grayTotal"
            background
            @size-change="handleSizeChangeGray"
            @current-change="handleCurrentChangeGray"
          ></el-pagination>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      :before-close="closeDialogAlbumIdInfo"
      :title="dialogAlbumId + '信息'"
      :visible.sync="dialogAlbumIdInfo"
      width="50%"
    >
      <album-info :albumid="dialogAlbumId"></album-info>
    </el-dialog>

    <env-gray-edit
      ref="envgrayedit"
      @fetch-data-gray="fetchGrayData"
    ></env-gray-edit>
  </div>
</template>

<script>
  import { EnvRules, DeleteRules, PutRules, EnvLogs } from '@/api/presm'
  import EnvGrayEdit from './PreGrayruleEdit.vue'
  import AlbumInfo from './AlbumInfo.vue'
  import { getUserInfo } from '@/api/user'

  export default {
    name: 'GrayRuleManagement',
    components: {
      EnvGrayEdit,
      AlbumInfo,
    },
    filters: {
      statusMap(status) {
        const statusMap = {
          0: '申请中',
          1: '创建中',
          2: '运行中',
          3: '已封存',
          4: '已销毁',
          5: '创建失败',
        }
        return statusMap[status]
      },
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'warning',
          2: 'success',
          3: 'danger',
          4: 'info',
          5: 'danger',
        }
        return statusMap[status]
      },
    },
    props: {
      allEnvData: {
        type: Array,
        required: true,
      },
      applicantDisplayNameMap: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        scrollOptions: {
          step: 0.5,
          limitMoveNum: 5,
          hoverStop: true,
          direction: 1,
          openWatch: true,
          waitTime: 1000,
          switchOffset: 30,
        },
        queryGrayForm: {
          pageNo: 1,
          pageSize: 15,
          keyword: '',
        },
        grayTableList: [],
        displayGrayList: [],
        grayTotal: 0,
        isDataLoaded: false,
        grayTableLoading: false,
        elementLoadingText: '正在加载...',
        layout: 'total, sizes, prev, pager, next, jumper',
        draggedRule: null,
        dialogAlbumId: '',
        dialogAlbumIdInfo: false,
        colorIndexes: {},
        grayColors: ['#e1e4e6', '#ddebf9', '#c9f4ff', '#bbdef0'],
        activities: [],
        resizeTimer: null,
        currentUser: null,
        isAdmin: false,
      }
    },
    computed: {},
    watch: {
      displayGrayList: {
        handler() {
          this.$nextTick(() => {
            this.initDynamicsHeight()
          })
        },
      },
      allEnvData: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            this.$nextTick(() => {
              this.initDynamicsHeight()
            })
          }
        },
      },
    },
    mounted() {
      this.fetchGrayData()
      this.$nextTick(() => {
        setTimeout(() => {
          this.initDynamicsHeight()
        }, 100)
      })
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
    },
    methods: {
      handleResize() {
        if (this.resizeTimer) clearTimeout(this.resizeTimer)
        this.resizeTimer = setTimeout(() => {
          this.initDynamicsHeight()
        }, 200)
      },

      initDynamicsHeight() {
        this.$nextTick(() => {
          const dynamicsCard = this.$refs.grayDynamicsCard?.$el
          const scrollContent = this.$refs.grayScrollContent
          if (!dynamicsCard || !scrollContent) return

          requestAnimationFrame(() => {
            const alertEl = this.$el.querySelector('.el-alert')
            const tableCardEl = this.$el.querySelector(
              '.el-card.is-never-shadow'
            )
            if (!tableCardEl) return

            const alertRect = alertEl?.getBoundingClientRect()
            const tableRect = tableCardEl.getBoundingClientRect()

            const alertHeight = alertRect ? alertRect.height : 0
            const tableHeight = tableRect.height
            const rightSideHeight = Math.ceil(alertHeight + tableHeight)

            dynamicsCard.style.height = `${rightSideHeight}px`

            const headerEl = dynamicsCard.querySelector('.el-card__header')
            const headerHeight = headerEl
              ? headerEl.getBoundingClientRect().height
              : 0
            const contentHeight = rightSideHeight - headerHeight - 20

            scrollContent.style.height = `${Math.max(contentHeight, 100)}px`
          })
        })
      },

      formatApplicant(applicant) {
        return this.applicantDisplayNameMap?.[applicant] || applicant
      },

      async fetchGrayData() {
        try {
          this.grayTableLoading = true
          const params = {
            pageNo: this.queryGrayForm.pageNo,
            pageSize: this.queryGrayForm.pageSize,
            keyword: this.queryGrayForm.keyword,
          }
          const { data, totalCount } = await EnvRules(params)

          this.displayGrayList = data
          this.grayTotal = totalCount
          this.isDataLoaded = true

          this.$nextTick(() => {
            this.initDynamicsHeight()
          })
        } catch (error) {
          console.error('Error fetching gray rules:', error)
          this.$baseMessage('获取规则列表失败', 'error')
        } finally {
          this.grayTableLoading = false
        }
      },

      handleGrayEdit(row) {
        if (row && row.pre_uuid) {
          this.$refs.envgrayedit.showEdit(row)
        } else {
          this.$refs.envgrayedit.showEdit()
        }
      },

      showAlbumInfo(albumid) {
        this.dialogAlbumId = albumid
        this.dialogAlbumIdInfo = true
      },

      closeDialogAlbumIdInfo() {
        this.dialogAlbumIdInfo = false
        this.dialogAlbumId = ''
      },

      handleDragStart(row) {
        this.draggedRule = row
      },

      handleDrop(targetRow) {
        if (this.draggedRule) {
          const updatedData = {
            pre_uuid: targetRow.pre_uuid,
            applicant: this.draggedRule.applicant,
            memo: this.draggedRule.memo,
            albumid: this.draggedRule.albumid,
          }
          this.updateGrayEnvironment(updatedData, this.draggedRule.albumid)
          this.draggedRule = null
        }
      },

      arraySpanMethod({ row, column, rowIndex }) {
        if (column.property === 'pre_uuid' || column.property === 'status') {
          const preUuid = row.pre_uuid
          const previousRow = this.displayGrayList[rowIndex - 1]

          if (rowIndex === 0 || preUuid !== previousRow?.pre_uuid) {
            const sameEnvRows = this.displayGrayList
              .slice(rowIndex)
              .findIndex((item) => item.pre_uuid !== preUuid)

            const rowSpan =
              sameEnvRows === -1
                ? this.displayGrayList.length - rowIndex
                : sameEnvRows

            return [rowSpan, 1]
          }
          return [0, 0]
        }
        return [1, 1]
      },

      rowClassName({ row }) {
        const currentEnv = row.pre_uuid?.trim()
        if (!currentEnv) return ''

        let colorIndex = this.colorIndexes[currentEnv]
        if (colorIndex === undefined) {
          colorIndex =
            Object.keys(this.colorIndexes).length % this.grayColors.length
          this.colorIndexes[currentEnv] = colorIndex
        }
        return `row-bg-${this.grayColors[colorIndex].slice(1)}`
      },

      queryGrayData() {
        this.queryGrayForm.pageNo = 1
        this.fetchGrayData()
      },

      handleSizeChangeGray(val) {
        this.queryGrayForm.pageSize = val
        this.queryGrayForm.pageNo = 1
        this.fetchGrayData()
      },

      handleCurrentChangeGray(val) {
        this.queryGrayForm.pageNo = val
        this.fetchGrayData()
      },

      async handleRuleView(row) {
        const envRow = this.allEnvData?.find(
          (item) => item.pre_uuid === row.pre_uuid
        )

        if (envRow) {
          envRow.albumid = row.albumid
          await this.handleView(envRow)
        } else {
          this.$baseMessage('不是运行中的环境，无法跳转', 'warning')
        }
      },

      async handleView(row) {
        if (!row.pre_uuid) {
          this.$baseMessage('没有找到对应环境', 'warning')
          return
        }

        if (row.status === 2) {
          if (row.pre_uuid === 'preprod') {
            try {
              const { user, groups } = await getUserInfo()
              this.currentUser = user

              if (groups.indexOf('admins') === -1) {
                this.$baseMessage(
                  '基线环境只能管理员操作，请联系管理员',
                  'warning'
                )
                return
              } else {
                this.isAdmin = groups.indexOf('admin') !== -1
              }
            } catch (error) {
              console.error('Error getting user info:', error)
              this.$baseMessage('获取用户信息失败', 'error')
              return
            }
          }

          this.$store.dispatch('consts/updateAllUserList', this.allUserList)
          this.$store.dispatch(
            'consts/updateDisplayNameMap',
            this.applicantDisplayNameMap
          )

          this.$router.push({
            name: 'preManagement',
            params: {
              pre_uuid: row.pre_uuid,
              pre_alias: row.pre_alias,
            },
          })
        } else {
          this.$baseMessage('只能对运行中的环境进行操作', 'warning')
        }
      },

      async handleDeleteRule(row) {
        this.$baseConfirm('确认删除该规则吗？', null, async () => {
          try {
            const { msg } = await DeleteRules(row.albumid)
            this.$baseMessage(msg, 'success')
            await this.fetchGrayData()
          } catch (error) {
            console.error('删除失败:', error)
            this.$baseMessage('删除失败', 'error')
          }
        })
      },

      async updateGrayEnvironment(data, albumid) {
        try {
          const { msg } = await PutRules(data, albumid)
          this.$baseMessage(msg, 'success')
          await this.fetchGrayData()
        } catch (error) {
          console.error('Error updating gray rule:', error)
          this.$baseMessage('更新失败', 'error')
        }
      },

      getEnvStatus(preUuid) {
        const env = this.allEnvData?.find((env) => env.pre_uuid === preUuid)
        if (!env) return { text: '未知', type: 'info' }

        const statusMap = {
          0: { text: '申请中', type: 'warning' },
          1: { text: '创建中', type: 'warning' },
          2: { text: '运行中', type: 'success' },
          3: { text: '已封存', type: 'danger' },
          4: { text: '已销毁', type: 'info' },
          5: { text: '创建失败', type: 'danger' },
        }

        return statusMap[env.status] || { text: '未知', type: 'info' }
      },

      getEnvName(preUuid) {
        const env = this.allEnvData?.find((env) => env.pre_uuid === preUuid)
        return env?.pre_alias || preUuid
      },
    },
  }
</script>

<style lang="scss" scoped>
  .gray-table {
    margin-bottom: 20px;

    .draggable {
      cursor: grab;
    }

    .droppable {
      border: 2px dashed transparent;

      &:hover {
        border-color: #3f9eff;
      }
    }

    ::v-deep {
      .row-bg-e1e4e6 {
        background-color: #e1e4e6 !important;
      }

      .row-bg-ddebf9 {
        background-color: #ddebf9 !important;
      }

      .row-bg-c9f4ff {
        background-color: #c9f4ff !important;
      }

      .row-bg-bbdef0 {
        background-color: #bbdef0 !important;
      }

      .el-table__row {
        td {
          background-color: transparent;
          border-bottom: none;
        }
      }

      .el-table__row:hover > td {
        background-color: transparent !important;
      }
    }
  }

  .presm-operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;

    .el-button {
      padding: 0 8px;
      margin: 0;
      height: 28px;
      line-height: 28px;
      color: #f68989;

      &:hover {
        opacity: 0.8;
      }
    }

    .el-divider--vertical {
      height: 1em;
      margin: 0 8px;
    }
  }

  .status-cell {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-tag {
      margin: 0;
      min-width: 64px;
      text-align: center;
    }
  }

  .el-pagination {
    margin-top: 15px;
    padding: 0;
  }
</style>
