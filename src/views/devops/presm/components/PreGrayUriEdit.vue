<template>
  <el-dialog
    title="URI规则编辑"
    :visible.sync="dialogVisible"
    width="40%"
    :before-close="handleClose"
  >
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      label-width="100px"
      :rules="rules"
    >
      <el-form-item label="环境ID" prop="pre_uuid">
        <el-input v-model.trim="ruleForm.pre_uuid" autocomplete="off" />
      </el-form-item>

      <el-form-item label="URI" prop="uri">
        <el-input v-model.trim="ruleForm.uri" autocomplete="off" />
      </el-form-item>

      <el-form-item label="匹配类型" prop="match_type">
        <el-select v-model="ruleForm.match_type" placeholder="请选择匹配类型">
          <el-option label="精准匹配" :value="0" />
          <el-option label="前缀匹配" :value="1" />
        </el-select>
      </el-form-item>

      <el-form-item label="备注" prop="memo">
        <el-input
          v-model.trim="ruleForm.memo"
          type="textarea"
          rows="3"
          autocomplete="off"
        />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { PutUriRules } from '@/api/presm'

  export default {
    name: 'PreGrayUriEdit',
    data() {
      return {
        dialogVisible: false,
        ruleForm: {
          id: null,
          pre_uuid: '',
          uri: '',
          match_type: 0,
          memo: '',
        },
        rules: {
          pre_uuid: [
            { required: true, message: '请输入环境ID', trigger: 'blur' },
          ],
          uri: [{ required: true, message: '请输入URI', trigger: 'blur' }],
          match_type: [
            { required: true, message: '请选择匹配类型', trigger: 'change' },
          ],
        },
      }
    },
    methods: {
      /**
       * 显示编辑对话框。若传入 row 数据，则为编辑模式；否则为新增模式。
       */
      showEdit(row) {
        if (row) {
          // 编辑操作，将 row 数据浅拷贝到 ruleForm 中
          this.ruleForm = Object.assign({}, row)
        } else {
          // 新增操作，初始化表单
          this.ruleForm = {
            id: null,
            pre_uuid: '',
            uri: '',
            match_type: 0,
            memo: '',
          }
        }
        this.dialogVisible = true
        // 在打开对话框后清除之前的校验提示
        this.$nextTick(() => {
          if (this.$refs.ruleForm) {
            this.$refs.ruleForm.clearValidate()
          }
        })
      },
      /**
       * 关闭当前对话框
       */
      handleClose() {
        this.dialogVisible = false
      },
      /**
       * 提交表单，在验证通过后调用 API 保存数据
       */
      handleSubmit() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            try {
              // 若 ruleForm.id 存在则为更新，否则为创建操作
              const ruleId = this.ruleForm.id || ''
              const { msg } = await PutUriRules(this.ruleForm, ruleId)
              this.$message.success(msg || '操作成功')
              this.dialogVisible = false
              // 通知父组件刷新数据
              this.$emit('fetch-data-uri')
            } catch (error) {
              console.error('URI规则提交出错：', error)
              this.$message.error('操作失败')
            }
          } else {
            this.$message.error('请检查输入项')
            return false
          }
        })
      },
    },
  }
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
