<template>
  <div>
    <el-card
      v-for="(info, index) in albumIdInfos"
      :key="info.userId"
      class="album-info-wrapper"
    >
      <el-row>
        <el-col :span="8">
          <div :ref="'imageWrapper' + index" class="image-wrapper">
            <img
              :ref="'albumIcon' + index"
              :src="info.albumIcon"
              class="album-icon"
            />
          </div>
        </el-col>
        <el-col :span="16" class="bottom">
          <el-form label-width="90px">
            <el-form-item label="昵称">
              <span>{{ info.nickName }}</span>
            </el-form-item>
            <el-form-item label="相册名称">
              <span>{{ info.albumName }}</span>
            </el-form-item>
            <el-form-item label="用户ID">
              <span>{{ info.userId }}</span>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>

    <el-card v-if="albumIdInfos.length === 0">
      <div style="text-align: center">
        <el-icon class="el-icon-warning"></el-icon>
        <span style="margin-left: 10px">
          未查询到AlbumId：
          <span style="color: red">{{ albumid }}</span>
          信息
        </span>
      </div>
    </el-card>
  </div>
</template>

<script>
  import { getAlbumIdInfos } from '@/api/presm'

  export default {
    name: 'AlbumInfo',
    props: {
      albumid: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        albumIdInfos: [],
      }
    },
    watch: {
      albumid: 'fetchAlbumInfo',
    },
    mounted() {
      this.fetchAlbumInfo() // 组件挂载时获取相册信息
    },
    methods: {
      formatDate(dateString) {
        const date = new Date(dateString)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0') // 月份从0开始
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      },

      async fetchAlbumInfo() {
        if (this.albumid) {
          const res = await getAlbumIdInfos(this.albumid)

          if (res.errcode === 0) {
            this.albumIdInfos = res.result
            if (this.albumIdInfos && this.albumIdInfos.length > 0) {
              this.$emit('info-loaded', this.albumIdInfos[0])
              this.$nextTick(() => {
                this.setImageWrapperHeights()
              })
            }
          } else {
            this.$baseMessage(res.errmsg, 'error')
          }
        }
      },
      setImageWrapperHeights() {
        this.albumIdInfos.forEach((_, index) => {
          const formElement = this.$el.querySelector('.bottom .el-form')
          if (formElement) {
            const formHeight = formElement.offsetHeight
            const imageWrapper = this.$refs['imageWrapper' + index][0]
            const albumIcon = this.$refs['albumIcon' + index][0]
            if (imageWrapper) {
              imageWrapper.style.height = `${formHeight}px`
            }
            // if (albumIcon) {
            //   albumIcon.style.height = `${formHeight / 1.5}px`
            //   albumIcon.style.width = `${formHeight / 1.5}px`
            // }
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .album-info-wrapper {
    /*  height: 100%; /* 让外层容器适应父容器的高度 */
    display: flex;
    flex-direction: column; /* 纵向排列 */
  }

  .image-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
  }

  .album-icon {
    max-width: 100%;
    max-height: 100%;
  }

  .bottom {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .el-form {
      margin-left: 40px;
    }

    .el-form-item {
      margin-bottom: 5px;
    }
  }
</style>
