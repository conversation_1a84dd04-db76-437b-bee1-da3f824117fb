<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="500px"
    @close="close"
  >
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="服务">
        <el-select
          v-model="form.name"
          clearable
          filterable
          :filter-method="filterProjects"
          placeholder="请选择"
          @change="handleChange"
        >
          <el-option
            v-for="item in filteredProjects"
            :key="item.appname"
            :label="`${item.name} ${item.appname}`"
            :value="
              item.appname +
              ',' +
              item.name +
              ',' +
              item.git_project +
              ',' +
              item.project_id +
              ',' +
              item.type
            "
          >
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.appname }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="分支">
        <el-select
          v-model="form.branch"
          :loading="branchLoading"
          autocomplete="on"
          clearable
          filterable
          placeholder="请先选择服务"
        >
          <el-option
            v-for="item in allbranch"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="工程名">
        <el-input v-model.trim="form.git_project" :readonly="true"></el-input>
      </el-form-item>

      <el-form-item label="所属端">
        <el-input v-model.trim="form.type" :readonly="true"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="doSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getAllProject, getProjectBranch } from '@/api/ciWorkflow'
  import { postService } from '@/api/presm'

  export default {
    name: 'AppManagement',
    data() {
      return {
        isedit: false,
        title: '',
        dialogFormVisible: false,
        branchLoading: false,
        form: {
          pre_uuid: '',
          project_id: '',
          project_branch: '',
          project: '',
          appname: '',
          branch: '',
          type: '',
          name: '',
        },
        allproject: [],
        allbranch: [],
        filteredProjects: [],
      }
    },

    created() {},
    methods: {
      showEdit(id, row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.form = { ...this.$options.data().from }
        } else {
          this.title = '编辑'
          this.isedit = true
          this.form = Object.assign({}, row)
        }
        this.form.pre_uuid = id
        this.dialogFormVisible = true
        this.fetchData()
      },

      close() {
        this.$refs['form'].resetFields()
        this.form = { ...this.$options.data().form }
        this.allbranch = []
        this.dialogFormVisible = false
      },
      async doSave() {
        console.log('this.form', this.form)
        const postForm = {
          project_id: this.form.project_id,
          project_branch: this.form.branch,
        }
        console.log('postForm', postForm)
        const { msg } = await postService(this.form.pre_uuid, postForm)
        this.$baseMessage(msg, 'success')
        this.$emit('fetch-data')
        this.close()
      },

      async handleChange(val) {
        if (val) {
          let array = val.split(',')
          this.form.appname = array[0]
          this.form.name = array[1]
          this.form.git_project = array[2]
          this.form.project_id = array[3]
          this.form.type = array[4]
          this.branchLoading = true
          const { data } = await getProjectBranch({ git_project: array[2] })
          this.allbranch = data
          this.branchLoading = false
          setTimeout(() => {}, 60000)
        } else {
          this.$refs['form'].resetFields()
          this.form = this.$options.data().form
          this.allbranch = []
        }
      },

      filterProjects(query) {
        if (!query) {
          this.filteredProjects = this.allproject
          return
        }

        const searchQuery = query.toLowerCase()
        this.filteredProjects = this.allproject.filter((item) => {
          const searchFields = [
            item.name,
            item.appname,
            item.git_project,
            item.project_id,
            item.type,
          ]

          return searchFields.some((field) => {
            if (!field) return false
            return String(field).toLowerCase().includes(searchQuery)
          })
        })
      },

      async fetchData() {
        const { data } = await getAllProject()
        this.allproject = data
        this.filteredProjects = data
        console.log('allproject', data)
        setTimeout(() => {}, 30000)
      },
    },
  }
</script>
