<template>
  <div class="visualization-container">
    <el-row :gutter="20">
      <!-- 总览卡片 -->
      <el-col :span="24">
        <el-card v-loading="loading.overview" class="overview-card">
          <div v-if="!overviewData.length" class="empty-data">
            <el-empty description="暂无数据"></el-empty>
          </div>
          <div slot="header" class="card-header">
            <span>预发布环境总览</span>
            <div class="header-right">
              <el-button
                :loading="isRefreshing"
                icon="el-icon-refresh"
                size="mini"
                @click="manualRefresh"
              ></el-button>

              <el-tooltip
                :content="autoRefresh === '1' ? '关闭自动刷新' : '开启自动刷新'"
                placement="top"
              >
                <el-switch
                  v-model="autoRefresh"
                  active-color="#13ce66"
                  active-text=""
                  active-value="1"
                  inactive-color="#ff4949"
                  inactive-text=""
                  inactive-value="0"
                  @change="handleRefreshChange"
                ></el-switch>
              </el-tooltip>
            </div>
          </div>
          <el-row :gutter="20">
            <el-col
              v-for="(item, index) in overviewData"
              :key="index"
              :span="6"
            >
              <div class="data-item">
                <i :class="item.icon" class="status-icon"></i>
                <div class="data-title">{{ item.title }}</div>
                <div :style="{ color: item.color }" class="data-value">
                  {{ item.value }}
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>

      <!-- 趋势图 -->
      <el-col :span="24">
        <el-card v-loading="loading.trend" class="trend-card">
          <div slot="header" class="card-header">
            <span>环境使用趋势</span>
            <div class="header-right">
              <el-date-picker
                v-model="dateRange"
                :picker-options="pickerOptions"
                align="right"
                end-placeholder="结束日期"
                range-separator="至"
                size="small"
                start-placeholder="开始日期"
                type="daterange"
                unlink-panels
                @change="handleDateChange"
              />
              <el-button
                icon="el-icon-download"
                size="mini"
                type="info"
                @click="exportData"
              ></el-button>
            </div>
          </div>
          <div class="chart-wrapper">
            <pre-chart :option="trendOptions" />
          </div>
        </el-card>
      </el-col>

      <!-- 用户和业务线统计 -->
      <el-col :span="12">
        <el-card v-loading="loading.user" class="stats-card">
          <div slot="header" class="card-header">
            <span>用户使用统计</span>
            <div class="header-right">
              <el-tooltip content="用户使用统计" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
          </div>
          <div class="chart-wrapper">
            <pre-chart :option="userOptions" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card v-loading="loading.bizline" class="stats-card">
          <div slot="header" class="card-header">
            <span>业务线分布</span>
            <div class="header-right">
              <el-tooltip content="用户使用统计" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
          </div>
          <div class="chart-wrapper">
            <pre-chart :option="bizLineOptions" />
          </div>
        </el-card>
      </el-col>

      <!-- 添加环境使用时长卡片 -->
      <el-col :span="24">
        <el-card v-loading="loading.duration" class="duration-card">
          <div slot="header" class="card-header">
            <span>环境使用时长分布</span>
            <div class="header-right">
              <el-tooltip content="环境使用时长统计" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
          </div>
          <div class="chart-wrapper">
            <pre-chart
              ref="durationChart"
              :option="durationOption"
              @click="handlePieClick"
            />
          </div>
        </el-card>
      </el-col>

      <!-- 在环境使用时长饼图后添加柱状图 -->
      <el-col :span="24">
        <el-card v-loading="loading.duration" class="duration-bar-card">
          <div slot="header" class="card-header">
            <span>环境使用时长分布-柱状图</span>
            <div class="header-right">
              <el-tooltip content="环境使用时长统计" placement="top">
                <i class="el-icon-info"></i>
              </el-tooltip>
            </div>
          </div>
          <div class="chart-wrapper">
            <pre-chart
              ref="durationBarChart"
              :option="durationBarOption"
              @click="handleBarClick"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加详情弹窗 -->

    <el-dialog
      :before-close="handleClose"
      :visible.sync="dialogVisible"
      title="环境详情"
      width="1000px"
    >
      <el-table
        v-loading="tableLoading"
        :data="paginatedData"
        style="width: 100%"
      >
        <el-table-column label="环境名称" min-width="120">
          <template #default="{ row }">
            <div v-if="row.status === 2">
              <el-link
                :underline="false"
                style="color: #3f9eff"
                type="primary"
                @click="handleViewClick(row)"
              >
                {{ row.pre_alias || row.pre_uuid }}
              </el-link>
            </div>
            <div v-else>
              {{ row.pre_alias || row.pre_uuid }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="业务线" min-width="120" prop="bizline" />
        <el-table-column label="申请人" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.displayname || scope.row.applicant }}
          </template>
        </el-table-column>
        <el-table-column
          label="版本"
          min-width="150"
          prop="version_code"
          show-overflow-tooltip
        />
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="statusConfig[scope.row.status]?.type" size="small">
              {{ statusConfig[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="使用时长(天)" width="120">
          <template slot-scope="scope">
            {{ (scope.row.duration / 24).toFixed(1) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加分页组件 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="currentEnvList.length"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getBizlinesStats,
    getDurationStats,
    getEnvOverview,
    getEnvTrend,
    getUserStats,
  } from '@/api/visualization'
  import PreChart from '@/views/monitoring/report/componets/ModuleElrow.vue'

  export default {
    name: 'Visualization',
    components: {
      PreChart,
    },

    props: {
      handleView: {
        type: Function,
        required: true,
      },
    },

    data() {
      return {
        overviewData: [],
        dateRange: [],
        pickerOptions: {
          shortcuts: [
            {
              text: '最近一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近两周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 14)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '最近一个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                picker.$emit('pick', [start, end])
              },
            },
          ],
        },
        trendOptions: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999',
              },
            },
          },
          grid: {
            top: '5%', // 顶部边距
            left: '3%', // 左侧边距
            right: '4%', // 右侧边距
            bottom: '15%', // 底部边距
            containLabel: true, // 包含标签
          },
          legend: {
            data: ['每日环境数', '环境趋势'], // 图例数据
            bottom: '1%', // 底部边距
            itemGap: 50, // 项间隔
            textStyle: {
              fontSize: 12, // 文字大小
            },
            itemWidth: 15, // 项宽度
            itemHeight: 10, // 项高度
            padding: [5, 10], // 内边距
          },
          xAxis: {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow',
            },
            axisLabel: {
              interval: 0,
              rotate: 30,
              formatter: (value) => {
                // 格式化日期显示
                return value.substring(5) // 只显示月-日
              },
            },
          },
          yAxis: {
            type: 'value',
            name: '环境数量',
            min: 0,
            minInterval: 1, // 保证Y轴刻度为整数
            axisLabel: {
              formatter: '{value}',
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          series: [
            {
              name: '每日环境数',
              type: 'bar',
              barWidth: '40%',
              data: [],
              itemStyle: {
                color: '#409EFF',
              },
              emphasis: {
                itemStyle: {
                  color: '#66b1ff',
                },
              },
            },
            {
              name: '环境趋势',
              type: 'line',
              smooth: true,
              data: [],
              itemStyle: {
                color: '#67C23A',
              },
              lineStyle: {
                width: 2,
              },
              symbol: 'circle',
              symbolSize: 6,
              emphasis: {
                scale: true,
              },
            },
          ],
        },
        userOptions: {
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              const { data } = params
              if (!data) return ''
              return `${data.name}<br/>
                      运行中: ${data.running_count}个<br/>
                      总环境数: ${data.total_count}个<br/>
                      占比: ${data.percentage}`
            },
          },
          legend: {
            orient: 'vertical',
            right: '5%',
            top: 'middle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 10,
            textStyle: {
              fontSize: 12,
            },
          },
          series: [
            {
              name: '用户分布',
              type: 'pie',
              radius: ['40%', '65%'],
              center: ['35%', '55%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: true,
                position: 'outside',
                formatter: (params) => {
                  const { name, data } = params
                  return `${name}\n${data.running_count}/${data.total_count}`
                },
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '12',
                  fontWeight: 'bold',
                },
              },
              data: [],
            },
          ],
        },
        bizLineOptions: {
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              const data = params.data
              return `${data.name}<br/>
                      运行中: ${data.running_count}<br/>
                      总数: ${data.total_count}<br/>
                      占比: ${data.percentage}`
            },
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '5%',
            top: 'middle',
            itemWidth: 10,
            itemHeight: 10,
            itemGap: 10,
            textStyle: {
              fontSize: 12,
            },
          },
          series: [
            {
              name: '业务线分布',
              type: 'pie',
              radius: ['40%', '65%'],
              center: ['35%', '55%'],
              avoidLabelOverlap: false,
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}\n{c}个',
                color: '#606266',
              },
              labelLine: {
                show: true,
                length: 10,
                length2: 10,
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '12',
                  fontWeight: 'bold',
                },
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              data: [],
            },
          ],
        },
        loading: {
          overview: false,
          trend: false,
          user: false,
          bizline: false,
          duration: false,
        },
        timer: null,
        refreshInterval: 60000, // 1分钟刷新一次
        autoRefresh: '1',
        isRefreshing: false,
        chartKey: 0, // 添加 key 用于强制重新渲染
        baseColors: [
          '#409EFF', // 蓝色
          '#67C23A', // 绿色
          '#E6A23C', // 黄色
          '#F56C6C', // 红色
          '#909399', // 灰色
          '#36CE9E', // 青色
          '#FF9F43', // 橙色
          '#8E44AD', // 紫色
          '#2980B9', // 深蓝
          '#E74C3C', // 深红
          '#16A085', // 青绿
          '#D35400', // 深橙
          '#27AE60', // 深绿
          '#7F8C8D', // 深灰
          '#C0392B', // 暗红
        ],
        charts: {
          trend: null,
          user: null,
          bizline: null,
        },
        durationData: [],
        durationOption: {},
        dialogVisible: false,
        currentEnvList: [],
        statusConfig: {
          1: { type: 'warning', label: '申请中' },
          2: { type: 'success', label: '运行中' },
          3: { type: 'info', label: '已封存' },
          4: { type: 'danger', label: '已释放' },
          5: { type: 'danger', label: '创建失败' },
        },
        durationBarOption: {}, // 新增柱状图配置
        currentPage: 1,
        pageSize: 10,
        tableLoading: false,
      }
    },
    computed: {
      // 计算分页后的数据
      paginatedData() {
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        console.log(
          'this.currentEnvListSlice',
          this.currentEnvList.slice(start, end)
        )
        return this.currentEnvList.slice(start, end)
      },
    },
    created() {
      this.fetchData()
      this.startAutoRefresh()
    },

    // beforeDestroy() {
    //   this.stopAutoRefresh()
    // },
    // 使用 activated 生命周期钩子（配合 keep-alive）
    activated() {
      this.$nextTick(() => {
        this.initCharts()
      })
    },
    // 组件被停用时清理资源
    deactivated() {
      this.stopAutoRefresh()
    },
    beforeDestroy() {
      this.stopAutoRefresh()
      // 清理图表实例
      Object.values(this.charts).forEach((chart) => {
        if (chart) {
          chart.dispose()
        }
      })
    },

    methods: {
      async fetchData() {
        if (
          this.loading.overview ||
          this.loading.trend ||
          this.loading.user ||
          this.loading.bizline ||
          this.loading.duration
        ) {
          return
        }

        try {
          // 设置所有模块的加载状态
          Object.keys(this.loading).forEach((key) => {
            this.loading[key] = true
          })

          // 并行请求所有数据
          const [overview, trend, users, bizlines, duration] =
            await Promise.all([
              getEnvOverview(),
              getEnvTrend(this.getDateRange()),
              getUserStats(),
              getBizlinesStats(),
              getDurationStats(),
            ])

          // 处理环境总览数据
          if (overview.code === 200) {
            this.overviewData = this.formatOverviewData(overview.data)
          } else {
            throw new Error(overview.msg || '获取总览数据失败')
          }

          // 处理环境时长数据
          if (duration.code === 200) {
            this.durationData = duration.data
            console.log('durationData', this.durationData)
            this.initDurationChart()
            // this.updateDurationOption()
            // this.updateDurationBarOption()
          }

          // 添加调试日志
          console.log('原始业务线数据:', bizlines)
          console.log('原始用户数据:', users)
          console.log('原始趋势数据:', trend)

          // 处理趋势数据
          if (trend.code === 200 && Array.isArray(trend.data)) {
            this.updateTrendChart(trend.data)
          } else {
            console.warn('趋势数据格式不正确:', trend)
          }

          // 处理用户统计数据
          if (users.code === 200 && Array.isArray(users.data)) {
            this.updateUserChart(users)
          } else {
            console.warn('用户统计数据格式不正确:', users)
          }

          // 处理业务线分布数据
          if (bizlines.code === 200) {
            // 传递完整的响应数据，包含 data 和 summary
            this.updateBizlineChart(bizlines)
          } else {
            console.warn('业务线数据格式不正确:', bizlines)
          }

          // 数据加载完成后，确保图表正确渲染
          this.$nextTick(() => {
            const chartWrappers = document.querySelectorAll(
              '.chart-wrapper .box-vabchart'
            )
            chartWrappers.forEach((wrapper) => {
              const chart = echarts.getInstanceByDom(wrapper)
              if (chart) {
                chart.resize()
              }
            })
          })
        } catch (error) {
          console.error('数据获取失败:', error)
          this.$baseMessage(this.getErrorMessage(error), 'error')
        } finally {
          // 重置所有加载状态
          Object.keys(this.loading).forEach((key) => {
            this.loading[key] = false
          })
        }
      },
      formatOverviewData(data) {
        // 检查数据有效性
        if (!data) {
          console.warn('总览数据为空')
          return []
        }

        // 基础指标
        const baseMetrics = [
          {
            title: '总环境数',
            value: data.total || 0,
            color: '#409EFF',
            icon: 'el-icon-monitor',
          },
        ]

        // 更新状态配置，与数据库状态对应
        const statusConfig = {
          0: {
            name: '申请中',
            color: '#909399',
            icon: 'el-icon-document',
            iconClass: 'applying-icon',
          },
          1: {
            name: '创建中',
            color: '#E6A23C',
            icon: 'el-icon-loading',
            iconClass: 'creating-icon',
          },
          2: {
            name: '使用中',
            color: '#67C23A',
            icon: 'el-icon-video-play',
            iconClass: 'running-icon',
          },
          3: {
            name: '已封存',
            color: '#909399',
            icon: 'el-icon-lock',
            iconClass: 'archived-icon',
          },
          4: {
            name: '已释放',
            color: '#909399',
            icon: 'el-icon-delete',
            iconClass: 'released-icon',
          },
          5: {
            name: '创建失败',
            color: '#F56C6C',
            icon: 'el-icon-circle-close',
            iconClass: 'failed-icon',
          },
        }

        if (
          data.status_distribution &&
          Array.isArray(data.status_distribution)
        ) {
          const statusMetrics = data.status_distribution.map((item) => {
            const status = statusConfig[item.status] || {
              name: '未知状态',
              color: '#909399',
              icon: 'el-icon-info',
              iconClass: 'unknown-icon',
            }

            return {
              title: status.name,
              value: item.count,
              color: status.color,
              icon: status.icon,
              iconClass: status.iconClass,
            }
          })

          return [...baseMetrics, ...statusMetrics]
        }

        return baseMetrics
      },
      // 根据状态获取对应的图标
      getStatusIcon(status) {
        const iconMap = {
          1: 'el-icon-video-play', // 运行中 - 播放图标
          2: 'el-icon-loading', // 创建中 - 加载图标
          3: 'el-icon-error', // 异常 - 错误图标
          4: 'el-icon-switch-button', // 已停止 - 开关图标
        }
        return iconMap[status] || 'el-icon-info'
      },
      updateTrendChart(data) {
        if (!data || !Array.isArray(data)) {
          console.warn('趋势数据无效')
          return
        }

        // 按日期排序
        const sortedData = [...data].sort(
          (a, b) => new Date(a.date) - new Date(b.date)
        )

        const dates = []
        const counts = []
        const trends = []
        let movingSum = 0
        const movingWindow = 7 // 7天移动平均

        sortedData.forEach((item, index) => {
          if (item && item.date) {
            dates.push(item.date)
            const count = Number(item.count) || 0
            counts.push(count)

            // 计算移动平均
            movingSum += count
            if (index >= movingWindow) {
              movingSum -= Number(sortedData[index - movingWindow].count) || 0
            }
            const avgCount =
              index < movingWindow
                ? movingSum / (index + 1)
                : movingSum / movingWindow

            trends.push(Number(avgCount.toFixed(1)))
          }
        })

        // 更新图表配置
        this.trendOptions = {
          ...this.trendOptions,
          xAxis: {
            ...this.trendOptions.xAxis,
            data: dates,
          },
          series: [
            {
              name: '每日环境数',
              type: 'bar',
              barWidth: '40%',
              data: counts,
              itemStyle: {
                color: '#409EFF',
              },
              emphasis: {
                itemStyle: {
                  color: '#66b1ff',
                },
              },
            },
            {
              name: '环境趋势',
              type: 'line',
              smooth: true,
              data: trends,
              itemStyle: {
                color: '#67C23A',
              },
              lineStyle: {
                width: 2,
              },
              symbol: 'circle',
              symbolSize: 6,
              emphasis: {
                scale: true,
              },
            },
          ],
        }
      },
      updateUserChart(response) {
        console.log('开始处理用户数据:', response)

        if (!response || !response.data || !Array.isArray(response.data)) {
          console.warn('用户分布数据无效:', response)
          return
        }

        const data = response.data
        const summary = response.summary || {}

        // 处理图表数据
        const chartData = data.map((item) => ({
          name: item.displayname || item.applicant,
          value: item.total_count, // 使用 total_count 作为值
          total_count: item.total_count, // 保存总数
          running_count: item.running_count, // 保存运行中数量
          percentage: item.percentage, // 保存百分比
          itemStyle: {
            color: this.getColorByName(item.applicant),
          },
        }))

        console.log('处理后的用户图表数据:', chartData)

        // 更新图表数据
        this.userOptions.series[0].data = chartData

        // 添加汇总信息
        this.$nextTick(() => {
          const userCard = document.querySelector('.stats-card')
          if (userCard) {
            let summaryEl = userCard.querySelector('.summary-info')
            if (!summaryEl) {
              summaryEl = document.createElement('div')
              summaryEl.className = 'summary-info'
              userCard.appendChild(summaryEl)
            }

            console.log('用户统计汇总数据:', summary)
            summaryEl.innerHTML = `
              <div class="summary-item">
                <span>用户总数：${summary.total_users || 0}</span>
                <span>总环境数：${summary.total_environments || 0}</span>
                <span>运行中：${summary.total_running || 0} (${
              summary.distribution?.running_percentage || '0%'
            })</span>
              </div>
            `
          }
        })
      },

      updateBizlineChart(response) {
        try {
          const data = response.data
          const summary = response.summary || {} // 获取汇总数据

          // 格式化数据
          const formattedData = data
            .sort((a, b) => b.total_count - a.total_count)
            .map((item) => ({
              name: item.bizline,
              value: item.total_count,
              running_count: item.running_count,
              percentage: item.percentage,
              itemStyle: {
                color: this.getColorByName(item.bizline),
              },
              label: {
                show: true,
                position: 'outside',
                formatter: (params) => {
                  return `${item.bizline}\n${item.running_count}/${item.total_count}`
                },
              },
            }))

          console.log('格式化后的业务线数据:', formattedData)

          // 更新图表配置
          this.bizLineOptions = {
            tooltip: {
              trigger: 'item',
              formatter: (params) => {
                const data = params.data
                return `${data.name}<br/>
                        运行中: ${data.running_count}<br/>
                        总数: ${data.value}<br/>
                        占比: ${data.percentage}`
              },
            },
            legend: {
              type: 'scroll',
              orient: 'vertical',
              right: '5%',
              top: 'middle',
              itemWidth: 10,
              itemHeight: 10,
              itemGap: 10,
              textStyle: {
                fontSize: 12,
              },
            },
            series: [
              {
                name: '业务线分布',
                type: 'pie',
                radius: ['40%', '65%'],
                center: ['35%', '55%'],
                avoidLabelOverlap: true,
                itemStyle: {
                  borderRadius: 4,
                  borderColor: '#fff',
                  borderWidth: 2,
                },
                label: {
                  show: true,
                  position: 'outside',
                  formatter: '{b}\n{c}',
                  color: '#606266',
                  fontSize: 12,
                  lineHeight: 16,
                },
                labelLine: {
                  show: true,
                  length: 15,
                  length2: 10,
                  smooth: true,
                },
                data: formattedData,
              },
            ],
          }

          // 更新汇总信息
          this.$nextTick(() => {
            // 使用更精确的选择器
            const bizLineCard = document.querySelector(
              '#pane-visualization > div > div.el-row > div:nth-child(4) > div'
            )
            if (bizLineCard) {
              let summaryEl = bizLineCard.querySelector('.summary-info')
              if (!summaryEl) {
                summaryEl = document.createElement('div')
                summaryEl.className = 'summary-info'
                bizLineCard.appendChild(summaryEl)
              }

              console.log('业务线统计汇总数据:', summary)
              summaryEl.innerHTML = `
                <div class="summary-item">
                  <span>业务线总数：${summary.total_bizlines || 0}</span>
                  <span>总环境数：${summary.total_environments || 0}</span>
                  <span>运行中：${summary.total_running || 0} (${
                summary.distribution?.running_percentage || '0%'
              })</span>
                </div>
              `
            } else {
              console.warn('未找到业务线卡片元素')
            }
          })
        } catch (error) {
          console.error('处理业务线数据时出错:', error)
        }
      },
      handleDateChange(val) {
        if (!val) {
          // 如果清空日期，则获取默认数据（最近7天）
          this.fetchTrendData()
          return
        }

        const [startDate, endDate] = val
        this.fetchTrendData({
          start_time: this.formatDate(startDate),
          end_time: this.formatDate(endDate),
        })
      },
      async fetchTrendData(params = {}) {
        try {
          this.loading.trend = true
          console.log('fetchTrendData params', params)
          const response = await getEnvTrend(params)

          if (response.code === 200 && Array.isArray(response.data)) {
            // 确保数据按日期排序
            const sortedData = response.data.sort(
              (a, b) => new Date(a.date) - new Date(b.date)
            )
            this.updateTrendChart(sortedData)
          } else {
            console.warn('趋势数据格式不符合预期:', response)
            this.$baseMessage('趋势数据格式不正确', 'warning')
          }
        } catch (error) {
          console.error('获取趋势数据失败:', error)
          this.$baseMessage(error.message || '获取趋势数据失败', 'error')
        } finally {
          this.loading.trend = false
        }
      },
      startAutoRefresh() {
        if (this.autoRefresh === '1' && !this.timer) {
          this.timer = setInterval(async () => {
            await this.fetchData()
          }, this.refreshInterval)
        }
      },
      stopAutoRefresh() {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }
      },
      getErrorMessage(error) {
        if (error.response) {
          return error.response.data.message || '服务器响应错误'
        }
        return error.message || '未知错误'
      },
      handleRefreshChange(value) {
        console.log('自动刷新状态:', value)
        if (value === '1') {
          this.startAutoRefresh()
        } else {
          this.stopAutoRefresh()
        }
      },
      async manualRefresh() {
        if (this.isRefreshing) return
        this.isRefreshing = true
        try {
          await this.fetchData()
        } finally {
          this.isRefreshing = false
        }
      },
      exportData() {
        try {
          const data = this.formatExportData()
          const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.setAttribute(
            'download',
            `预发布环境趋势数据_${this.formatDate(new Date())}.csv`
          )
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        } catch (error) {
          console.error('导出数据失败:', error)
          this.$baseMessage('导出数据失败', 'error')
        }
      },
      formatExportData() {
        const headers = ['日期', '每日环境数', '环境趋势']
        const rows = this.trendOptions.xAxis.data.map((date, index) => {
          return [
            date,
            this.trendOptions.series[0].data[index],
            this.trendOptions.series[1].data[index],
          ].join(',')
        })
        return [headers.join(','), ...rows].join('\n')
      },
      formatDate(date) {
        const d = new Date(date)
        const year = d.getFullYear()
        const month = String(d.getMonth() + 1).padStart(2, '0')
        const day = String(d.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      },
      getDateRange() {
        if (this.dateRange && this.dateRange.length === 2) {
          return {
            start_time: this.formatDate(this.dateRange[0]),
            end_time: this.formatDate(this.dateRange[1]),
          }
        }
        // 默认返回最近7天
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7) // 修改为7天
        return {
          start_time: this.formatDate(start),
          end_time: this.formatDate(end),
        }
      },
      getRandomColor() {
        return this.baseColors[
          Math.floor(Math.random() * this.baseColors.length)
        ]
      },
      getColorByName(name) {
        let hash = 0
        for (let i = 0; i < name.length; i++) {
          hash = name.charCodeAt(i) + ((hash << 5) - hash)
        }
        return this.baseColors[Math.abs(hash) % this.baseColors.length]
      },
      async initCharts() {
        try {
          // 先清除可能存在的旧图表实例
          Object.values(this.charts).forEach((chart) => {
            if (chart) {
              chart.dispose()
            }
          })

          // 重置加载状态
          Object.keys(this.loading).forEach((key) => {
            this.loading[key] = false
          })

          // 加载新数据
          await this.fetchData()

          // 确保 DOM 已更新并且图表容器已渲染
          this.$nextTick(() => {
            setTimeout(() => {
              const chartWrappers = document.querySelectorAll(
                '.chart-wrapper .box-vabchart'
              )
              chartWrappers.forEach((wrapper) => {
                const chart = echarts.getInstanceByDom(wrapper)
                if (chart) {
                  chart.resize()
                }
              })
            }, 200)
          })
        } catch (error) {
          console.error('初始化图表失败:', error)
        }
      },

      // 初始化环境时长图表
      async initDurationChart() {
        this.loading.duration = true
        try {
          const { data } = await getDurationStats()
          this.durationData = data
          this.updateDurationOption()
          this.updateDurationBarOption() // 添加柱状图更新
        } catch (error) {
          console.error('获取环境时长数据失败:', error)
          this.$message.error('获取环境时长数据失败')
        } finally {
          this.loading.duration = false
        }
      },
      // 更新图表配置
      updateDurationOption() {
        const pieData = this.durationData.map((item) => ({
          name: item.range,
          value: item.count,
          environments: item.environments || [], // 确保 environments 存在
          itemStyle: {
            color: this.getColorByName(item.range),
          },
        }))

        console.log('处理后的饼图数据:', pieData)

        this.durationOption = {
          tooltip: {
            trigger: 'item',
            formatter: (params) => {
              const { name, value, data } = params
              const envCount = data.environments ? data.environments.length : 0
              return `${name}<br/>环境数量: ${value}<br/>详细数: ${envCount}`
            },
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 'center',
          },
          series: [
            {
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}\n{c}个 ({d}%)',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '16',
                  fontWeight: 'bold',
                },
              },
              data: pieData,
            },
          ],
        }
      },
      // 处理饼图点击事件
      handlePieClick(params) {
        console.log('饼图点击事件参数:', params)

        if (params.componentType === 'series' && params.data) {
          const { name, environments } = params.data
          if (environments && environments.length > 0) {
            console.log(`显示 ${name} 区间的环境列表:`, environments)
            this.currentPage = 1 // 重置页码
            this.currentEnvList = environments
            this.dialogVisible = true
          } else {
            this.$message.info('该时间区间暂无环境数据')
          }
        }
      },
      // 处理弹窗关闭
      handleClose() {
        this.dialogVisible = false
        this.currentEnvList = []
        this.currentPage = 1 // 重置页码
        this.pageSize = 10 // 重置每页条数
      },
      // 更新柱状图配置
      updateDurationBarOption() {
        const barData = this.durationData.map((item) => ({
          range: item.range,
          total: item.count,
          running:
            item.environments?.filter((env) => env.status === 2).length || 0,
          environments: item.environments,
        }))

        console.log('durationBarData', barData)

        this.durationBarOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999',
              },
            },
          },
          grid: {
            top: '10%', // 顶部边距
            left: '3%', // 左侧边距
            right: '4%', // 右侧边距
            bottom: '10%', // 底部边距
            containLabel: true,
          },
          legend: {
            data: ['总数', '运行中'],
            bottom: '1%', // 底部边距
            itemGap: 50, // 项间隔
            textStyle: {
              fontSize: 12, // 文字大小
            },
            itemWidth: 15, // 项宽度
            itemHeight: 10, // 项高度
            padding: [5, 10], // 内边距
          },
          xAxis: {
            type: 'category',
            data: barData.map((item) => item.range),
            axisPointer: {
              type: 'shadow',
            },
            axisLabel: {
              interval: 0,
              rotate: 0, // 修改横轴图例不需要倾斜
              fontSize: 10,
            },
          },
          yAxis: {
            type: 'value',
            name: '环境数量',
            min: 0,
            minInterval: 1,
            axisLabel: {
              formatter: '{value}',
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          series: [
            {
              name: '总数',
              type: 'bar',
              barWidth: '40%',
              data: barData.map((item) => ({
                value: item.total,
                environments: item.environments,
                itemStyle: {
                  color: '#409EFF',
                },
              })),
              emphasis: {
                itemStyle: {
                  color: '#66b1ff',
                },
              },
            },
            {
              name: '运行中',
              type: 'bar',
              barWidth: '40%',
              data: barData.map((item) => ({
                value: item.running,
                environments: item.environments.filter(
                  (env) => env.status === 2
                ),
                itemStyle: {
                  color: '#67C23A',
                },
              })),
              emphasis: {
                itemStyle: {
                  color: '#85ce61',
                },
              },
            },
          ],
        }
      },
      // 处理柱状图点击事件
      handleBarClick(params) {
        console.log('柱状图点击事件参数:', params)
        if (params.seriesType === 'bar' && params.data) {
          const environments =
            params.seriesName === '运行中'
              ? params.data.environments.filter((env) => env.status === 2)
              : params.data.environments

          if (environments && environments.length > 0) {
            this.currentPage = 1 // 重置页码
            this.currentEnvList = environments
            this.dialogVisible = true
          } else {
            this.$message.info('该时间区间暂无环境数据')
          }
        }
      },
      // 处理每页条数变化
      handleSizeChange(val) {
        this.pageSize = val
        this.currentPage = 1 // 重置到第一页
      },

      // 处理页码变化
      handleCurrentChange(val) {
        this.currentPage = val
      },

      // 修改环境详情跳转方法
      async handleViewClick(row) {
        // 调用父组件传入的 handleView 方法
        this.handleView(row)
      },
    },
  }
</script>
<style lang="scss" scoped>
  .visualization-container {
    //padding: 0 !important;
    //margin: 0 !important;
    padding: 5px;
    background: #f5f7f8;

    .chart {
      height: 400px;
      width: 100%;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;

      .header-right {
        display: flex;
        align-items: center;
        gap: 10px;

        .el-button {
          margin-right: 0;
        }

        .el-tooltip {
          line-height: 1;
        }
      }
    }

    .overview-card {
      margin-bottom: 20px;

      .data-item {
        text-align: center;
        padding: 15px;
        background: #fafafa;
        border-radius: 4px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        }

        .status-icon {
          font-size: 24px;
          margin-bottom: 8px;
          transition: all 0.3s;

          // 申请中状态
          &.applying-icon {
            color: #909399;
            animation: pulse 1.5s infinite;
          }

          // 创建中状态
          &.creating-icon {
            color: #e6a23c;
            animation: rotate 1s linear infinite;
          }

          // 使用中状态
          &.running-icon {
            color: #67c23a;
            animation: pulse 2s infinite;
          }

          // 已封存状态
          &.archived-icon {
            color: #909399;
          }

          // 已释放状态
          &.released-icon {
            color: #909399;
            opacity: 0.7;
          }

          // 创建失败状态
          &.failed-icon {
            color: #f56c6c;
            animation: shake 0.5s ease-in-out;
          }
        }

        .data-title {
          font-size: 13px;
          color: #606266;
          margin-bottom: 8px;
        }

        .data-value {
          font-size: 22px;
          font-weight: 600;
        }
      }
    }

    .chart-wrapper {
      position: relative;
      width: 100%;
      // height: 300px;
      min-height: 300px;
      padding: 10px;

      ::v-deep .box-vabchart {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
    }

    .trend-card {
      .chart-wrapper {
        height: 300px;
      }
    }

    .stats-card {
      .chart-wrapper {
        height: 320px;
      }
    }

    .trend-card,
    .stats-card {
      margin-bottom: 20px;
    }

    ::v-deep .el-card__header {
      padding: 10px 15px;
      border-bottom: 1px solid #ebeef5;
    }

    ::v-deep .el-card__body {
      padding: 15px;
    }

    .empty-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }
  }

  // 添加动画效果
  @keyframes pulse {
    0% {
      opacity: 1;
    }

    50% {
      opacity: 0.6;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes shake {
    0%,
    100% {
      transform: translateX(0);
    }

    25% {
      transform: translateX(-2px);
    }

    75% {
      transform: translateX(2px);
    }
  }

  .stats-card {
    position: relative;

    .summary-info {
      position: absolute;
      bottom: 0px;
      left: 0;
      right: 0;
      // left: 50%;
      // transform: translateX(-50%);
      text-align: center;
      font-size: 12px;
      color: #606266;

      .summary-item {
        display: flex;
        gap: 15px;
        justify-content: center;
        align-items: center;

        span {
          background: #f5f7fa;
          padding: 4px 8px;
          border-radius: 4px;
          white-space: nowrap;

          &:not(:last-child)::after {
            content: '|';
            margin-left: 15px;
            color: #dcdfe6;
          }
        }
      }
    }
  }

  .chart-wrapper {
    min-height: 400px;
  }

  .duration-card {
    margin-bottom: 20px;

    .chart-wrapper {
      height: 400px;
    }
  }

  .el-dialog {
    .el-table {
      margin-top: 10px;
    }
  }

  .duration-bar-card {
    margin-bottom: 20px;

    .chart-wrapper {
      height: 300px;
    }
  }

  .pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }

  .el-dialog {
    .el-table {
      .el-link {
        font-size: 13px; // 保持与其他单元格字体大小一致

        &:hover {
          text-decoration: underline; // 鼠标悬停时显示下划线
        }
      }
    }
  }
</style>
