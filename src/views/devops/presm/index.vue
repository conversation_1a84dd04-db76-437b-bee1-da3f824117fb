<template>
  <div class="main-container">
    <el-row :gutter="24">
      <el-col :span="24">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane name="pre_mange">
            <span slot="label">
              <i class="el-icon-folder"></i>
              <b v-text="tabText_1"></b>
            </span>
            <env-management
              v-if="isDataReady"
              :all-user-list="allUserList"
              :applicant-display-name-map="applicantDisplayNameMap"
              :activities="activities"
              @update:env-data="handleEnvDataUpdate"
            />
          </el-tab-pane>

          <el-tab-pane name="gray_mange">
            <span slot="label">
              <i class="el-icon-user"></i>
              <b v-text="tabText_2"></b>
            </span>
            <gray-rule-management
              v-if="isDataReady"
              :all-env-data="allEnvData"
              :applicant-display-name-map="applicantDisplayNameMap"
            />
          </el-tab-pane>

          <el-tab-pane name="uri_mange">
            <span slot="label">
              <i class="el-icon-document"></i>
              <b>URI规则</b>
            </span>
            <uri-rule-management />
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { getList } from '@/api/userManagement'
  import { EnvLogs, EnvRules } from '@/api/presm'
  import EnvManagement from './components/EnvManagement.vue'
  import GrayRuleManagement from './components/GrayRuleManagement.vue'
  import UriRuleManagement from './components/UriRuleManagement.vue'

  export default {
    name: 'PreMain',
    components: {
      EnvManagement,
      GrayRuleManagement,
      UriRuleManagement,
    },
    data() {
      return {
        activeTab: 'pre_mange',
        tabText_1: ' 环境管理',
        tabText_2: ' 用户规则',
        allUserList: [],
        applicantDisplayNameMap: null,
        activities: null,
        allEnvData: null,
        timer: null,
        isDataReady: false,
        isEnvDataReady: false,
      }
    },
    watch: {
      activeTab: {
        immediate: true,
        async handler(newVal) {
          if (newVal === 'gray_mange' && !this.isEnvDataReady) {
            await this.fetchEnvData()
          }
          if (
            newVal === 'pre_mange' ||
            newVal === 'gray_mange' ||
            newVal === 'uri_mange'
          ) {
            this.startAutoRefresh()
          } else {
            this.stopAutoRefresh()
          }
        },
      },
    },
    async created() {
      await this.initData()
    },
    beforeDestroy() {
      this.stopAutoRefresh()
    },
    methods: {
      async initData() {
        try {
          await Promise.all([
            this.getUserList(),
            this.getLogsList(),
            this.fetchEnvData(),
          ])
          this.isDataReady = true
        } catch (error) {
          console.error('Error initializing data:', error)
          this.$baseMessage('初始化数据失败', 'error')
        }
      },

      async getUserList() {
        try {
          const { data } = await getList({ pageNo: 1, pageSize: 1000 })
          this.allUserList = data
          this.applicantDisplayNameMap = data.reduce((map, user) => {
            if (user?.username) {
              map[user.username] = user.displayname || user.username
            }
            return map
          }, {})
        } catch (error) {
          console.error('Error fetching user list:', error)
          throw error
        }
      },

      async getLogsList() {
        try {
          const { data } = await EnvLogs()
          this.activities = data
        } catch (error) {
          console.error('Error fetching logs:', error)
          throw error
        }
      },

      async fetchEnvData() {
        try {
          const { data } = await EnvRules({ pageSize: 1000 })
          this.allEnvData = data
          this.isEnvDataReady = true
        } catch (error) {
          console.error('Error fetching env data:', error)
          throw error
        }
      },

      handleEnvDataUpdate(data) {
        this.allEnvData = data
        this.isEnvDataReady = true
      },

      startAutoRefresh() {
        if (this.timer) {
          clearInterval(this.timer)
        }
        this.timer = setInterval(async () => {
          await this.getLogsList()
        }, 30000)
      },

      stopAutoRefresh() {
        if (this.timer) {
          clearInterval(this.timer)
          this.timer = null
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .main-container {
    padding: 0 2px 0 2px !important;
    margin: 0 !important;
  }
</style>
