<template>
  <div class="sandboxanagement-container">
    <el-drawer
      :visible.sync="jenkins_drawer"
      direction="rtl"
      size="50%"
      title="构建日志"
      @closed="handleDrawerClose"
      @opened="handleDrawerOpen"
    >
      <pre v-for="text in progressiveText">{{ text }}</pre>
      <el-button
        v-if="logloading"
        :loading="true"
        style="margin-left: 20px"
      ></el-button>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :lg="16" :md="16" :sm="24" :xl="16" :xs="24">
        <el-card shadow="never">
          <el-descriptions
            :column="3"
            border
            class="margin-top"
            size="medium"
            title="环境基本信息"
          >
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-box"></i>
                沙盒ID
              </template>
              {{ queryForm.sbx_uuid }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-thumb"></i>
                沙盒别名
              </template>
              {{ queryForm.sbx_alias }}
              <el-button size="mini" type="warning" @click="openDialog">
                设置
              </el-button>
              <el-dialog
                :visible.sync="aliasDialogVisible"
                center
                title="设置别名"
                width="30%"
                @close="resetForm"
              >
                <el-form ref="queryForm" :model="queryForm" label-width="40px">
                  <el-row>
                    <el-form-item label="别名">
                      <el-input v-model.trim="queryForm.sbx_alias"></el-input>
                    </el-form-item>
                  </el-row>
                  <el-row style="text-align: right">
                    <el-form-item>
                      <el-button type="primary" @click="resetForm">
                        取消
                      </el-button>
                      <el-button type="primary" @click="saveAlias">
                        保存
                      </el-button>
                    </el-form-item>
                  </el-row>
                </el-form>
              </el-dialog>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-trophy"></i>
                迭代版本
              </template>
              {{ detailInfo.version_code }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-time"></i>
                创建时间
              </template>
              {{ detailInfo.create_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                负责人
              </template>
              {{ detailInfo.owner }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-office-building"></i>
                环境类型
              </template>
              <el-tag size="small">{{ envType }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-link"></i>
                访问域名
              </template>
              <a :href="'https://' + binddomain + ''" target="_blank">
                {{ binddomain }}
              </a>
              <el-popover
                placement="bottom"
                trigger="click"
                width="500"
                @show="fetchBindData()"
              >
                <el-table :data="domainData">
                  <el-table-column
                    label="一级域名"
                    property="domain"
                    width="150"
                  ></el-table-column>
                  <el-table-column label="状态" show-overflow-tooltip>
                    <template #default="{ row }">
                      <el-tag :type="row.status | bindstatusType">
                        {{ row.status | bindstatusMap }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    label="当前绑定"
                    property="occupy_sbxid"
                  ></el-table-column>
                  <el-table-column
                    label="最后使用"
                    property="update_by"
                  ></el-table-column>

                  <el-table-column
                    label="操作"
                    show-overflow-tooltip
                    width="100"
                  >
                    <template #default="{ row }">
                      <el-button
                        :disabled="row.status !== 0"
                        type="text"
                        @click="handleBind(row)"
                      >
                        绑定
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button
                  v-if="!binded"
                  slot="reference"
                  size="mini"
                  type="success"
                >
                  绑定
                </el-button>
              </el-popover>
              <el-button
                v-if="binded"
                size="mini"
                type="warning"
                @click="handleUnBind()"
              >
                解绑
              </el-button>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                测试
              </template>
              {{ detailInfo.tester }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                产品
              </template>
              {{ detailInfo.product }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                运维
              </template>
              {{ detailInfo.operation }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                业务线
              </template>
              <el-tag size="medium">{{ detailInfo.line_name }}</el-tag>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-notebook-1"></i>
                质量评价
              </template>
              <el-rate
                v-model="quality"
                disabled
                show-score
                text-color="#ff9900"
              ></el-rate>
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <span>测试缺陷统计</span>
          </div>
          <vab-chart :option="buglist" auto-resize theme="vab-echarts-theme" />
        </el-card>
      </el-col>

      <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
        <el-card shadow="never">
          <el-alert show-icon title="Tips:" type="success">
            <template slot>
              <div class="ci-alert-list">
                1.通过服务管理选择本次迭代版本涉及的服务和分支。
              </div>
              <div class="ci-alert-list">
                2.系统测试结束后，需确认并同步新增配置后方可进入下一阶段。
              </div>
            </template>
          </el-alert>

          <el-tabs
            v-model="activeTab"
            type="border-card"
            @tab-click="handleTabchange"
          >
            <el-tab-pane name="services">
              <span slot="label">
                <i class="el-icon-setting"></i>
                <b>服务配置</b>
              </span>
              <vab-query-form>
                <vab-query-form-left-panel :span="12">
                  <el-button
                    icon="el-icon-edit"
                    type="primary"
                    @click="handleManage"
                  >
                    增加服务
                  </el-button>
                  <el-button
                    icon="el-icon-document-copy"
                    type="primary"
                    @click="handleCompare"
                  >
                    配置管理
                  </el-button>
                  <el-button
                    icon="el-icon-upload"
                    type="warning"
                    @click="handleBuild"
                  >
                    批量发布
                  </el-button>
                  <el-button
                    icon="el-icon-document-copy"
                    type="primary"
                    @click="handleWeak"
                  >
                    弱网测试
                  </el-button>
                  <el-dropdown v-if="false" @command="handleCommand">
                    <el-button icon="el-icon-plus" type="primary">
                      更多功能
                      <i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item
                        command="cc"
                        icon="el-icon-document-copy"
                      >
                        配置对比
                      </el-dropdown-item>
                      <el-dropdown-item command="ll" icon="el-icon-view">
                        构建日志
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </vab-query-form-left-panel>
                <vab-query-form-right-panel :span="12">
                  <el-form
                    :inline="true"
                    :model="queryForm"
                    @submit.native.prevent
                  >
                    <el-form-item>
                      <el-input
                        v-model.trim="queryForm.project"
                        clearable
                        placeholder="请输入查询关键字"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        icon="el-icon-search"
                        type="primary"
                        @click="queryData"
                      >
                        查询
                      </el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-right-panel>
              </vab-query-form>

              <el-table
                v-loading="listLoading"
                :data="list"
                :element-loading-text="elementLoadingText"
                @selection-change="setSelectRows"
              >
                <el-table-column
                  show-overflow-tooltip
                  type="selection"
                ></el-table-column>
                <el-table-column
                  label="服务名"
                  prop="name"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="项目名称"
                  prop="project"
                  show-overflow-tooltip
                ></el-table-column>

                <el-table-column label="状态" show-overflow-tooltip>
                  <template #default="{ row }">
                    <el-tag :type="row.status | statusType">
                      {{ row.status | statusMap }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column
                  label="分支"
                  prop="project_branch"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="构建时间"
                  prop="latest_build_time"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="构建次数"
                  prop="publish_count"
                  show-overflow-tooltip
                ></el-table-column>

                <el-table-column label="操作" show-overflow-tooltip width="260">
                  <template #default="{ row }">
                    <el-button type="text" @click="handleBuild(row)">
                      打包构建
                    </el-button>
                    <el-button
                      v-if="row.status > 1"
                      :disabled="!row.jenkins_buildno"
                      type="text"
                      @click="handleGetbuildlog(row)"
                    >
                      构建日志
                    </el-button>
                    <el-button type="text" @click="handleDelete(row)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                :current-page="queryForm.pageNo"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :total="total"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
              <edit ref="edit" @fetch-data="fetchData"></edit>
            </el-tab-pane>

            <el-tab-pane name="pods">
              <span slot="label">
                <i class="el-icon-ship"></i>
                <b>容器管理</b>
              </span>
              <vab-query-form>
                <vab-query-form-left-panel :span="12">
                  <el-button
                    icon="el-icon-upload"
                    type="primary"
                    @click="handleNotImp"
                  >
                    资源监控
                  </el-button>
                  <el-button
                    icon="el-icon-upload"
                    type="warning"
                    @click="handleNotImp"
                  >
                    销毁重建
                  </el-button>
                </vab-query-form-left-panel>
                <vab-query-form-right-panel :span="12">
                  <el-form
                    :inline="true"
                    :model="podForm"
                    @submit.native.prevent
                  >
                    <el-form-item>
                      <el-input
                        v-model.trim="podForm.keyword"
                        clearable
                        placeholder="请输入查询关键字"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        icon="el-icon-search"
                        type="primary"
                        @click="queryPods"
                      >
                        查询
                      </el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-right-panel>
              </vab-query-form>

              <el-table
                v-loading="podLoading"
                :data="podlist"
                :element-loading-text="elementLoadingText"
                @selection-change=""
              >
                <el-table-column
                  show-overflow-tooltip
                  type="selection"
                ></el-table-column>
                <el-table-column
                  label="实例名称"
                  prop="metadata.name"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="状态" show-overflow-tooltip width="120">
                  <template #default="{ row }">
                    <el-tag :type="row.status | podstatusType">
                      {{ row.status.phase }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="实例所在节点IP"
                  prop="status.hostIP"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="实例IP"
                  prop="status.podIP"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  :formatter="dateFormat"
                  label="创建时间"
                  prop="status.startTime"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="重启次数"
                  prop="status.containerStatuses[0].restartCount"
                  show-overflow-tooltip
                  width="60"
                ></el-table-column>

                <el-table-column
                  v-if="false"
                  prop="status.containerStatuses[0].name"
                ></el-table-column>

                <el-table-column label="操作" show-overflow-tooltip width="160">
                  <template #default="{ row }">
                    <el-button type="text" @click="handleRestart(row)">
                      重启
                    </el-button>
                    <el-button type="text" @click="handleRemote(row)">
                      远程
                    </el-button>
                    <el-button type="text" @click="handleRemoteLog(row)">
                      日志
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                :current-page="podForm.pageNum"
                :layout="layout"
                :page-size="podForm.pageSize"
                :total="podtotal"
                background
                @size-change="handlePodSizeChange"
                @current-change="handlePodCurrentChange"
              ></el-pagination>
            </el-tab-pane>

            <el-tab-pane
              v-if="queryForm.sbx_uuid === 'pretest'"
              name="deployments"
            >
              <span slot="label">
                <i class="el-icon-ship"></i>
                <b>Deployments</b>
              </span>
              <vab-query-form>
                <vab-query-form-right-panel :span="24">
                  <el-form
                    :inline="true"
                    :model="deploymentsForm"
                    @submit.native.prevent
                  >
                    <el-form-item>
                      <el-input
                        v-model.trim="deploymentsForm.keyword"
                        clearable
                        placeholder="请输入查询关键字"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button
                        icon="el-icon-search"
                        type="primary"
                        @click="queryDeployments"
                      >
                        查询
                      </el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-right-panel>
              </vab-query-form>

              <el-table
                v-loading="deploymentsLoading"
                :data="deploymentslist"
                :element-loading-text="elementLoadingText"
                @selection-change=""
              >
                <el-table-column
                  show-overflow-tooltip
                  type="selection"
                ></el-table-column>
                <el-table-column
                  label="名称"
                  prop="metadata.name"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="命名空间"
                  prop="metadata.namespace"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column label="状态" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span>
                      {{ row.status.replicas ? row.status.replicas : 0 }} /
                      {{ row.status.readyReplicas }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" show-overflow-tooltip width="160">
                  <template #default="{ row }">
                    <el-button type="text" @click="openDeploymentDialog(row)">
                      伸缩
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <el-dialog
                :visible.sync="dialogVisible"
                title="副本数"
                @close="resetReplicaCount"
              >
                <el-input
                  v-model="replicaCount"
                  placeholder="请输入副本数"
                  type="number"
                ></el-input>
                <span slot="footer" class="dialog-footer">
                  <el-button @click="cancelChange">取消</el-button>
                  <el-button type="primary" @click="confirmChange">
                    完成
                  </el-button>
                </span>
              </el-dialog>

              <el-pagination
                :current-page="deploymentsForm.pageNum"
                :layout="layout"
                :page-size="deploymentsForm.pageSize"
                :total="deploymentstotal"
                background
                @size-change="handleDeploymentsSizeChange"
                @current-change="handleDeploymentsCurrentChange"
              ></el-pagination>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import VabChart from '@/plugins/echarts'
  import Edit from './components/projectManagement'

  import {
    buildSandboxProject,
    deleteSandboxProject,
    doUpdateSandbox,
    getJksProgressiveText,
    getSandboxdetail,
    getSandboxProject,
    perfDeploymentslist,
    perfDeploymentsReplicas,
    perfPodDelete,
    perfPodlist,
    queryDomainBind,
    sandboxPodDelete,
    sandboxPodlist,
    updateDomainBind,
  } from '@/api/ciWorkflow'

  import { getBugs } from '@/api/tapd'

  export default {
    name: 'SandboxManagement',
    components: { VabChart, Edit },
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'info',
          1: 'success',
          2: 'warning',
          3: 'danger',
          4: 'warning',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '未发布',
          1: '运行中',
          2: '构建中',
          3: '构建失败',
          4: '部署中',
        }
        return statusMap[status]
      },
      bindstatusType(status) {
        const statusMap = {
          0: 'success',
          1: 'warning',
        }
        return statusMap[status]
      },
      bindstatusMap(status) {
        const statusMap = {
          0: '空闲中',
          1: '已绑定',
        }
        return statusMap[status]
      },
      podstatusType(row) {
        if (row.conditions) {
          if (
            row.conditions[0].status === 'True' &&
            row.conditions[1].status === 'True' &&
            row.conditions[2].status === 'True' &&
            row.conditions[3].status === 'True'
          ) {
            return 'success'
          } else {
            return 'danger'
          }
        } else {
          return 'danger'
        }
      },
    },
    data() {
      return {
        aliasDialogVisible: false,
        domainData: [],
        binded: false, //是否已经绑定域名
        bindid: 0, //绑定的域名id
        binddomain: '',
        timer: null,
        progressiveText: ['构建任务排队中……'],
        jenkins_drawer: false,
        reverse: true,
        logloading: true,
        getTapd: false,

        jenkinsInfo: {
          project: 'UAT-后端',
          buildno: 37,
          start: 0,
        },

        activeTab: 'services',
        listLoading: true,
        podLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        quality: 5.0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        detailInfo: {},
        envType: '系统测试',
        queryForm: {
          sbx_uuid: '',
          sbx_alias: '',
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        queryFormBackup: {},
        list: [],
        podtotal: 0,
        podForm: {
          namespace: '',
          pageNum: 1,
          pageSize: 10,
          keyword: '',
        },
        podlist: [],
        deploymentsLoading: true,
        deploymentstotal: 0,
        deploymentsForm: {
          namespace: 'pretest',
          pageNum: 1,
          pageSize: 10,
          keyword: '',
        },
        dialogVisible: false,
        replicaCount: null,
        currentRow: null,
        deploymentslist: [],
        buglist: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {},
          grid: {
            top: '0%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
          },
          yAxis: {
            type: 'category',
            data: ['致命缺陷数', '严重缺陷数', '一般缺陷数'],
          },
          series: [
            {
              type: 'bar',
              data: [
                {
                  value: 0,
                  itemStyle: {
                    color: '#a90000',
                  },
                },
                {
                  value: 0,
                  itemStyle: {
                    color: '#f1f1b1',
                  },
                },
                0,
              ],
            },
          ],
        },
      }
    },
    created() {
      this.listLoading = false
      this.queryForm.sbx_uuid = this.$route.params.sbx_uuid
      this.queryForm.sbx_alias = this.$route.params.sbx_alias
      this.podForm.namespace = this.$route.params.sbx_uuid
      this.queryFormBackup = JSON.parse(JSON.stringify(this.queryForm))
      this.fetchData()
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    mounted() {},
    methods: {
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },

      handlePodSizeChange(val) {
        this.podForm.pageSize = val
        this.fetchPodData()
      },
      handlePodCurrentChange(val) {
        this.podForm.pageNum = val
        this.fetchPodData()
      },

      handleCommand(command) {
        if (command === 'll') {
          this.jenkins_drawer = true
        }
        if (command === 'cc') {
          // console.log("cc");
        }
      },
      handleNotImp() {
        this.$baseMessage('尚未实现，敬请期待', 'warning')
      },
      //绑定域名
      async handleBind(row) {
        this.$baseConfirm(
          '确认绑定域名 ' + row.domain + ' 吗？',
          null,
          async () => {
            var data = {}
            data.status = 1
            data.occupy_sbxid = this.queryForm.sbx_uuid
            data.id = row.id
            const { msg } = await updateDomainBind(data)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
              this.fetchBindData()
            })
          }
        )
      },

      //解绑域名
      async handleUnBind() {
        this.$baseConfirm(
          '确认释放域名 ' + this.binddomain + ' 绑定关系吗？',
          null,
          async () => {
            var data = {
              status: 0,
              update_by: '',
              id: this.bindid,
              occupy_sbxid: '',
            }
            const { msg } = await updateDomainBind(data)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
              this.fetchBindData()
            })
          }
        )
      },

      handleProgressiveText() {
        this.logloading = false
      },

      handleGetbuildlog(row) {
        this.jenkinsInfo.project = row.jenkins_project
        this.jenkinsInfo.buildno = row.jenkins_buildno
        this.jenkins_drawer = true
      },

      dateFormat: function (row) {
        var date = new Date(row.status.startTime)
        var year = date.getFullYear()
        var month =
          date.getMonth() + 1 < 10
            ? '0' + (date.getMonth() + 1)
            : date.getMonth() + 1
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
        var hours =
          date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
        var minutes =
          date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
        var seconds =
          date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
        return (
          year +
          '-' +
          month +
          '-' +
          day +
          ' ' +
          hours +
          ':' +
          minutes +
          ':' +
          seconds
        )
      },

      async handleDelete(val) {
        this.$baseConfirm(
          '删除服务会将该服务恢复至初始状态并删除全部构建记录，确认吗？',
          null,
          async () => {
            this.$set(val, 'sbx_uuid', this.queryForm.sbx_uuid)
            const { msg } = await deleteSandboxProject(val)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
              this.fetchData()
            }, 300)
          }
        )
      },

      async handleBuild(val) {
        this.$baseConfirm(
          '构建服务可能会导致测试中断，确认继续吗？',
          null,
          async () => {
            if (val.project) {
              this.$set(val, 'sbx_uuid', this.queryForm.sbx_uuid)
              const { msg } = await buildSandboxProject(val)
              setTimeout(() => {
                this.$baseMessage(msg, 'success')
                this.fetchData()
              }, 300)
            } else {
              const projects = this.selectRows
                .map((item) => item.project)
                .join()
              var param = { sbx_uuid: this.queryForm.sbx_uuid }
              this.$set(param, 'project', projects)
              this.$set(
                param,
                'project_branch',
                this.selectRows[0].project_branch
              )
              const { msg } = await buildSandboxProject(param)
              setTimeout(() => {
                this.$baseMessage(msg, 'success')
                this.fetchData()
              }, 300)
            }
          }
        )
      },

      async handleRestart(row) {
        this.$baseConfirm(
          '重启实例可能会导致测试中断，确认继续吗？',
          null,
          async () => {
            const param =
              '/namespaces/' +
              this.podForm.namespace +
              '/pods/' +
              row.metadata.name
            if (this.envType === '压力测试') {
              var { metadata } = await perfPodDelete(param)
            } else {
              var { metadata } = await sandboxPodDelete(param)
            }
            setTimeout(() => {
              this.$baseMessage(
                `pod重启成功，新实例${metadata.name}已启动。`,
                'success'
              )
              this.fetchPodData()
            }, 500)
          }
        )
      },
      handleRemote(row) {
        var baseurl
        if (this.envType === '压力测试') {
          baseurl =
            '/kubepi/dashboard/terminal?cluster=perf-test&type=terminal&'
        } else {
          baseurl = '/kubepi/dashboard/terminal?cluster=dev-test&type=terminal&'
        }

        const param =
          'namespace=' +
          this.podForm.namespace +
          '&pod=' +
          row.metadata.name +
          '&container=' +
          row.status.containerStatuses[0].name
        window.open(baseurl + param)
      },
      ///kubepi/dashboard/terminal?cluster=dev-test&namespace=tp2p1qw0&pod=scs-694d7f57c-4bjtp&container=scs&type=log
      handleRemoteLog(row) {
        var baseurl
        if (this.envType === '压力测试') {
          baseurl = '/kubepi/dashboard/terminal?cluster=perf-test&type=log&'
        } else {
          baseurl = '/kubepi/dashboard/terminal?cluster=dev-test&type=log&'
        }
        const param =
          'namespace=' +
          this.podForm.namespace +
          '&pod=' +
          row.metadata.name +
          '&container=' +
          row.status.containerStatuses[0].name
        window.open(baseurl + param)
      },

      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      queryPods() {
        this.podForm.pageNum = 1
        this.fetchPodData()
      },
      setSelectRows(val) {
        this.selectRows = val
      },
      handleManage(row) {
        this.$refs['edit'].showEdit(this.queryForm.sbx_uuid)
      },
      handleCompare() {
        this.$router.push({
          name: 'configCenter',
          params: {
            sbx_uuid: this.queryForm.sbx_uuid,
          },
        })
      },
      handleWeak() {
        this.$router.push({
          name: 'weakNetTest',
          params: {
            sbx_uuid: this.queryForm.sbx_uuid,
          },
        })
      },
      handleTabchange() {
        if (this.podlist.length === 0) {
          if (this.activeTab === 'pods') {
            this.fetchPodData()
          }
        }
        if (this.deploymentslist.length === 0) {
          if (this.activeTab === 'deployments') {
            this.fetchDeploymentsData()
          }
        }
      },
      handleDrawerOpen() {
        this.fetchJksData()
        clearInterval(this.timer)
        this.timer = null
        this.setTimer()
        this.logloading = true
      },
      handleDrawerClose() {
        clearInterval(this.timer)
        this.logloading = false
        this.timer = null
        this.jenkinsInfo.start = 0
        this.progressiveText = ['构建任务排队中……']
      },

      setTimer() {
        if (this.timer == null) {
          this.timer = setInterval(() => {
            this.fetchJksData()
          }, 2500)
        }
      },
      scrollToBottom() {
        const container = this.$el.querySelector('.el-drawer__body')
        container.scrollTop = container.scrollHeight
      },

      async fetchJksData() {
        const param =
          'project=' +
          this.jenkinsInfo.project +
          '&buildno=' +
          this.jenkinsInfo.buildno +
          '&start=' +
          this.jenkinsInfo.start
        this.scrollToBottom()
        var resp = await getJksProgressiveText(param)
        if (resp) {
          this.progressiveText =
            this.progressiveText[0] === '构建任务排队中……'
              ? []
              : this.progressiveText
        }
        if (
          this.jenkinsInfo.start < parseInt(resp.headers['x-text-size']) ||
          resp.headers['x-more-data'] === 'true'
        ) {
          this.jenkinsInfo.start = parseInt(resp.headers['x-text-size'])
          this.progressiveText.push(resp.data)
        } else {
          clearInterval(this.timer)
          this.$baseNotify(
            '「' +
              this.jenkinsInfo.project +
              '」构建任务已结束，请查看日志结果。',
            '通知',
            'success',
            'bottom-right'
          )
          this.logloading = false
          this.timer = null
        }
        this.scrollToBottom()
      },

      async fetchBindData() {
        this.binddomain = this.queryForm.sbx_uuid + '.wegoab.com'
        this.bindid = 0
        this.binded = false
        const { data } = await queryDomainBind()
        this.domainData = data
        data.forEach((item) => {
          if (item.occupy_sbxid === this.queryForm.sbx_uuid) {
            this.binded = true
            this.bindid = item.id
            this.binddomain = item.domain
          }
        })
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },

      async fetchData() {
        this.listLoading = true
        var { data, totalCount } = await getSandboxProject(this.queryForm)
        this.list = data
        this.total = totalCount

        var { data } = await getSandboxdetail(this.queryForm)
        this.detailInfo = data[0]
        if (this.detailInfo.line_name == '测试') {
          this.envType = '压力测试'
        }
        setTimeout(() => {
          this.listLoading = false
        }, 300)

        this.fetchBindData()
        this.getTapd = true
        if (!this.getTapd && this.envType == '系统测试') {
          var param =
            'tapd_name=' +
            this.detailInfo.line_name +
            '&version_report=' +
            this.detailInfo.version_code
          var { data } = await getBugs(param)
          this.buglist.series[0].data[0].value = data.FATAL
          this.buglist.series[0].data[1].value = data.SERIOUS
          this.buglist.series[0].data[2] = data.NORMAL
          this.getTapd = true
        }
      },

      async fetchPodData() {
        this.podLoading = true
        const param =
          'search=true&pageNum=' +
          this.podForm.pageNum +
          '&pageSize=' +
          this.podForm.pageSize +
          '&namespace=' +
          this.podForm.namespace +
          '&keywords=' +
          this.podForm.keyword

        if (this.envType === '压力测试') {
          var { items, total } = await perfPodlist(param)
        } else {
          var { items, total } = await sandboxPodlist(param)
        }
        this.podlist = items
        this.podtotal = total
        setTimeout(() => {
          this.podLoading = false
        }, 500)
      },

      openDialog() {
        this.aliasDialogVisible = true
      },
      closeDialog() {
        this.aliasDialogVisible = false
      },
      resetForm() {
        this.queryForm = { ...this.queryFormBackup } // 还原表单数据
        this.aliasDialogVisible = false
      },
      saveAlias() {
        this.$refs['queryForm'].validate(async (valid) => {
          if (valid) {
            // console.log("queryForm: ", this.queryForm);
            const { sbx_uuid, sbx_alias } = this.queryForm
            const optimizedMergedData = { sbx_uuid, sbx_alias }
            // console.log("optimizedMergedData: ", optimizedMergedData);
            const { msg } = await doUpdateSandbox(optimizedMergedData)
            this.$baseMessage(msg, 'success')
            this.queryFormBackup = { ...this.queryForm }
            this.$emit('fetch-data')
            this.closeDialog()
          } else {
            return false
          }
        })
      },

      queryDeployments() {
        this.deploymentsForm.pageNum = 1
        this.fetchDeploymentsData()
      },

      async fetchDeploymentsData() {
        this.deploymentsLoading = true
        const param =
          'search=true&pageNum=' +
          this.deploymentsForm.pageNum +
          '&pageSize=' +
          this.deploymentsForm.pageSize +
          '&namespace=' +
          this.deploymentsForm.namespace +
          '&keywords=' +
          this.deploymentsForm.keyword

        var { items, total } = await perfDeploymentslist(param)

        this.deploymentslist = items
        this.deploymentstotal = total
        setTimeout(() => {
          this.deploymentsLoading = false
        }, 500)
      },

      cancelChange() {
        this.dialogVisible = false
        this.resetReplicaCount()
      },

      resetReplicaCount() {
        this.replicaCount = null
      },

      openDeploymentDialog(row) {
        this.currentRow = row
        this.dialogVisible = true
      },

      async confirmChange() {
        if (this.replicaCount !== null) {
          await this.handleChangeDeploymentsReplicas(
            this.currentRow,
            this.replicaCount
          )
          this.dialogVisible = false
          this.resetReplicaCount()
        }
      },

      async handleChangeDeploymentsReplicas(row, value) {
        const param = 'namespace=' + this.deploymentsForm.namespace
        const selfLink = row.metadata.selfLink
        const replicasValue = parseInt(value, 10)
        if (isNaN(replicasValue) || replicasValue < 0) {
          this.$baseMessage('副本数必须是一个非负整数。', 'error')
          return // 如果无效，则退出函数
        }

        row.spec.replicas = replicasValue
        var { metadata, spec } = await perfDeploymentsReplicas(
          selfLink,
          param,
          row
        )
        setTimeout(() => {
          this.$baseMessage(
            `Deployment: ${metadata.name},调整 replicas 为：${spec.replicas} 成功。`,
            'success'
          )
          this.fetchDeploymentsData()
        }, 500)
      },

      handleDeploymentsSizeChange(val) {
        this.deploymentsForm.pageSize = val
        this.fetchDeploymentsData()
      },
      handleDeploymentsCurrentChange(val) {
        this.deploymentsForm.pageNum = val
        this.fetchDeploymentsData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sandboxanagement-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }

    .ci-alert-list {
      margin-top: 5px;
    }
  }

  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0 0 1.85rem;
    margin: 0 0 var(--section-padding);
    padding: 0.8rem 1rem;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    background-color: var(--pre-background);
    color: #333 !important;
    color: var(--pre-color) !important;
    font-family: ui-monospace, SFMono-Regular, SF Mono, JetBrainsMono, Consolas,
      monospace !important;
    font-family: var(--font-family-mono) !important;
    font-weight: 300 !important;
    line-height: 1.66 !important;
  }
</style>
