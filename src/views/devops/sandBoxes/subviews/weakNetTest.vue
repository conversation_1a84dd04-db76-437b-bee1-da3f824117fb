<template>
  <div class="roleManagement-container">
    <el-tabs
      v-model="activeTab"
      type="border-card"
      @tab-click="handleTabchange"
    >
      <el-tab-pane name="test">
        <span slot="label">
          <i class="el-icon-setting"></i>
          <b>待测项目</b>
        </span>
        <vab-query-form>
          <vab-query-form-left-panel :span="12">
            <el-button
              icon="el-icon-plus"
              type="primary"
              :disabled="true"
              @click=""
            >
              提交测试
            </el-button>
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="12">
            <el-radio-group
              v-model="build_env"
              style="float: right"
              size="mini"
              :disabled="true"
            >
              <el-radio-button label="最新构建"></el-radio-button>
              <el-radio-button label="全部构建"></el-radio-button>
            </el-radio-group>
          </vab-query-form-right-panel>
        </vab-query-form>
        <el-table
          v-loading="listLoading"
          :data="list"
          :element-loading-text="elementLoadingText"
          @selection-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="project_name"
            label="项目名称"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="env_name"
            label="环境"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="version"
            label="版本号"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="build_size"
            label="包大小"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="tested_size"
            label="基线大小"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            label="变化率"
            prop="rate"
            :formatter="valueFormatter"
          >
            <template #default="{ row, column, $index }">
              <div
                v-html="column.formatter(row[column.property], row, $index)"
              ></div>
            </template>
          </el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="last_build_time"
            label="构建时间"
          ></el-table-column>
        </el-table>

        <el-pagination
          background
          :current-page="queryForm.pageNo"
          :page-size="queryForm.pageSize"
          :layout="layout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-tab-pane>
      <el-tab-pane name="prod">
        <span slot="label">
          <i class="el-icon-setting"></i>
          <b>基线设置</b>
        </span>

        <vab-query-form>
          <vab-query-form-left-panel :span="12">
            <p />
          </vab-query-form-left-panel>
          <vab-query-form-right-panel :span="12">
            <el-form :inline="true" :model="queryForm1" @submit.native.prevent>
              <el-form-item>
                <el-input
                  v-model.trim="queryForm1.keyword"
                  placeholder="项目名称或版本"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  type="primary"
                  @click="fetchData1"
                >
                  查询
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>

        <el-table
          v-loading="listLoading1"
          :data="prodlist"
          :element-loading-text="elementLoadingText"
          @selection-change="setSelectRows"
        >
          <el-table-column
            show-overflow-tooltip
            prop="project_name"
            label="项目名称"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="env_name"
            label="环境"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="version"
            label="版本号"
          ></el-table-column>
          <el-table-column
            show-overflow-tooltip
            prop="build_size"
            label="包大小"
          ></el-table-column>

          <el-table-column
            show-overflow-tooltip
            prop="last_build_time"
            label="构建时间"
          ></el-table-column>

          <el-table-column show-overflow-tooltip label="基线">
            <template #default="{ row }">
              <i
                :class="parseInt(row.tested) === 1 ? 'el-icon-star-on' : ''"
              ></i>
              <el-button
                v-if="parseInt(row.tested) !== 1"
                type="text"
                @click="handleEdit(row)"
              >
                设为基线
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page="queryForm1.pageNo"
          :page-size="queryForm1.pageSize"
          :layout="layout"
          :total="total1"
          @size-change="handleSizeChange1"
          @current-change="handleCurrentChange1"
        ></el-pagination>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import { getNssBuildInfo } from '@/api/ciWorkflow'

  export default {
    name: 'WeakNetTest',
    data() {
      return {
        list: [],
        prodlist: [],
        activeTab: 'test',
        build_env: '最新构建',
        listLoading: true,
        listLoading1: false,
        quota: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        total1: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          sbx_uuid: '',
          pageNo: 1,
          pageSize: 10,
          keyword: '',
          env_name: '',
        },
        queryForm1: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
          env_name: 'prod',
        },
      }
    },
    created() {
      this.queryForm.sbx_uuid = this.$route.params.sbx_uuid
      // console.log(this.queryForm.sbx_uuid)
      this.fetchData(this.queryForm.sbx_uuid)
      //this.listLoading = false
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit()
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      handleSizeChange1(val) {
        this.queryForm1.pageSize = val
        this.fetchData1()
      },
      handleCurrentChange1(val) {
        this.queryForm1.pageNo = val
        this.fetchData1()
      },
      handleTabchange() {
        if (this.activeTab === 'test') {
          this.fetchData()
        } else {
          this.fetchData1()
        }
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      valueFormatter(value) {
        if (parseFloat(value) > 0.8) {
          return (
            '<span style="color: red;">' +
            value +
            "<i class='el-icon-top'></span>"
          )
        } else if (parseFloat(value) > 0) {
          return (
            '<span style="color: green;">' +
            value +
            '<i class="el-icon-top"></span>'
          )
        } else {
          return (
            '<span style="color: green;">' +
            value +
            '<i class="el-icon-bottom"></span>'
          )
        }
      },

      async fetchData() {
        this.queryForm.env_name = 'test-' + this.queryForm.sbx_uuid
        const { data, total } = await getNssBuildInfo(this.queryForm)
        this.list = data
        this.total = total
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },

      async fetchData1() {
        const { data, total } = await getNssBuildInfo(this.queryForm1)
        this.prodlist = data
        this.total1 = total
        setTimeout(() => {
          this.listLoading1 = false
        }, 300)
      },
    },
  }
</script>
