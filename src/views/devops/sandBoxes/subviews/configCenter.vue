<template>
  <div class="config-container">
    <el-tabs v-model="activeSheet" type="border-card" @tab-click="">
      <el-tab-pane name="compare">
        <span slot="label">配置对比</span>
        <el-alert title="Tips:" type="success">
          <template slot>
            <div class="ci-alert-list">
              1.通过对比配置，可以列出当前沙盒环境与基线环境的配置差异。
            </div>
            <div class="ci-alert-list">
              2.人工确认无误后，可以将{{
                queryForm.sbx_uuid
              }}当前配置反向更新至测试基线。
            </div>
            <div class="ci-alert-list">
              2.比较结果可以保存起来作为修改线上配置的参考。
            </div>
            <div class="ci-alert-list">
              4.特殊配置可能会影响其他业务线测试环境，反向同步前请修改或删除。
            </div>
            <p />
          </template>
        </el-alert>
        <el-divider></el-divider>
        <el-card shadow="never">
          <el-container>
            <el-aside width="230px">
              <el-tabs
                v-model="activeTab"
                type="border-card"
                :stretch="true"
                @tab-click="handleTabchange"
              >
                <el-tab-pane name="nacos">
                  <span slot="label">
                    Nacos
                    <el-badge
                      v-if="diffcount > 0"
                      :value="diffcount"
                    ></el-badge>
                  </span>
                  <el-menu
                    default-active="1"
                    class="el-menu-vertical-demo"
                    background-color="#F5F7FA"
                    text-color="#696969"
                    active-text-color="#4bb98a"
                    :default-openeds="diffservices"
                    @select="handleMenuSelect"
                  >
                    <template v-for="(value, key, index) in menulist">
                      <el-submenu
                        v-if="value.length"
                        :index="key"
                        style="overflow: hidden"
                      >
                        <template slot="title">
                          <i style="color: #696969" class="el-icon-setting"></i>
                          {{ key }}
                        </template>
                        <el-menu-item
                          v-for="(f, vIdx) in value"
                          :key="vIdx"
                          :index="key + '/' + f"
                        >
                          <i
                            style="color: #696969"
                            class="el-icon-arrow-right"
                          ></i>
                          {{ f }}
                        </el-menu-item>
                      </el-submenu>
                    </template>
                  </el-menu>
                </el-tab-pane>

                <el-tab-pane name="apollo">
                  <span slot="label">
                    Apollo
                    <el-badge
                      v-if="apodiffcount > 0"
                      :value="apodiffcount"
                    ></el-badge>
                  </span>
                  <el-menu
                    default-active="1"
                    class="el-menu-vertical-demo"
                    background-color="#F5F7FA"
                    text-color="#696969"
                    active-text-color="#4bb98a"
                    :default-openeds="apodiffservices"
                    @select="handleMenuSelect"
                  >
                    <template v-for="(value, key, index) in apomenulist">
                      <el-submenu
                        v-if="value.length"
                        :index="key"
                        style="overflow: hidden"
                      >
                        <template slot="title">
                          <i style="color: #696969" class="el-icon-setting"></i>
                          {{ key }}
                        </template>
                        <el-menu-item
                          v-for="(f, vIdx) in value"
                          :key="vIdx"
                          :index="key + '/' + f"
                        >
                          <i
                            style="color: #696969"
                            class="el-icon-arrow-right"
                          ></i>
                          {{ f }}
                        </el-menu-item>
                      </el-submenu>
                    </template>
                  </el-menu>
                </el-tab-pane>
              </el-tabs>
            </el-aside>

            <el-container>
              <el-header style="text-align: right; font-size: 12px">
                <el-button
                  icon="el-icon-document-checked"
                  :loading="btnRefreshLoading"
                  type="primary"
                  @click="handleRefresh"
                >
                  重新检查
                </el-button>
                <el-button
                  :disabled="currentdiffs === ''"
                  icon="el-icon-upload"
                  type="warning"
                  @click="handleWriteBack"
                >
                  覆盖基线
                </el-button>
              </el-header>
              <el-main>
                <el-card shadow="never">
                  <div slot="header">
                    <span><b>对比方式：</b></span>
                    <el-switch
                      v-model="cmpMode"
                      active-color="#83DD7D"
                      inactive-color="#83DD7D"
                      active-value="line-by-line"
                      inactive-value="side-by-side"
                      active-text="逐行"
                      inactive-text="左右"
                    ></el-switch>
                  </div>
                  <span v-if="currentdiffs === ''">
                    请在左侧选择要对比的配置项
                  </span>
                  <div v-html="prettyHtml" />
                </el-card>
              </el-main>
            </el-container>
          </el-container>
        </el-card>
      </el-tab-pane>
      <el-tab-pane name="initial">
        <span slot="label">配置初始化</span>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import * as Diff2Html from 'diff2html'
  import 'diff2html/bundles/css/diff2html.min.css'

  import {
    getdiffDetail,
    getdiffList,
    writebackDiffence,
  } from '@/api/ciWorkflow'

  export default {
    name: 'ConfigCenter',
    data() {
      return {
        btnRefreshLoading: true,
        activeSheet: 'compare',
        activeTab: 'nacos',
        diffcount: 0,
        diffservices: [],
        currentdiffs: '',
        menulist: '',

        apodiffcount: 0,
        apodiffservices: [],
        apocurrentdiffs: '',
        apomenulist: '',

        queryForm: {
          sbx_uuid: '',
          type: 'nacos',
          filename: '',
          service: '',
        },
        cmpMode: 'side-by-side',
        matchMode: 'lines',
      }
    },
    computed: {
      prettyHtml() {
        return Diff2Html.html(this.currentdiffs, {
          drawFileList: false,
          matching: this.matchMode,
          fileContentToggle: true,
          outputFormat: this.cmpMode,
        })
      },
    },
    created() {
      this.queryForm.sbx_uuid = this.$route.params.sbx_uuid
      this.fetchData()
    },
    methods: {
      querySearch(queryString, cb) {
        var sandboxes = this.sandboxes
        for (var i = 0; i < sandboxes.length; i++) {
          sandboxes[i].value = sandboxes[i].sbx_uuid
        }
        var results = queryString
          ? sandboxes.filter(this.createFilter(queryString))
          : sandboxes
        cb(results)
      },
      createFilter(queryString) {
        return (sandbox) => {
          return (
            sandbox.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0
          )
        }
      },
      handleTabchange() {
        this.queryForm.type = this.activeTab
        this.currentdiffs = ''
        if (this.apodiffservices.length === 0 && this.activeTab === 'apollo') {
          this.fetchApolloData()
        }
      },
      handleRefresh() {
        this.btnRefreshLoading = true
        if (this.activeTab === 'nacos') {
          this.fetchData()
        } else {
          this.fetchApolloData()
        }
      },
      async handleWriteBack() {
        this.$baseConfirm(
          '确认将' +
            this.queryForm.sbx_uuid +
            '当前配置' +
            this.queryForm.filename +
            '覆盖到基线环境吗？',
          null,
          async () => {
            const { msg } = await writebackDiffence(this.queryForm)
            setTimeout(() => {
              this.currentdiffs = ''
              this.apocurrentdiffs = ''
              this.$baseMessage(msg, 'success')
            }, 300)
          }
        )
      },

      async handleMenuSelect(key, keyPath) {
        var kv = key.split('/')
        this.queryForm.service = kv[0]
        this.queryForm.filename = kv[1]
        const { data } = await getdiffDetail(this.queryForm)
        this.currentdiffs = data
        setTimeout(() => {}, 500)
      },

      async fetchData() {
        const { data, total } = await getdiffList(this.queryForm)
        this.menulist = data
        this.diffservices = Object.keys(data)
        this.diffcount = total.file_total
        setTimeout(() => {
          this.$baseNotify(
            '对比完成，找到' +
              this.diffcount +
              '个' +
              this.activeTab +
              '差异配置。',
            '提示',
            'success',
            'bottom-right'
          )
          this.btnRefreshLoading = false
        }, 1500)
      },

      async fetchApolloData() {
        const Loading = this.$baseLoading(8, '努力获取中...')
        let timeoutId
        try {
          // 设置超时关闭遮罩（1分钟后）
          timeoutId = setTimeout(() => {
            Loading.close()
            this.$baseNotify(
              '请求超时，请稍后重试',
              '错误',
              'error',
              'bottom-right'
            )
          }, 60000)

          const { data, total } = await getdiffList(this.queryForm)

          this.apomenulist = data
          this.apodiffservices = Object.keys(data)
          this.apodiffcount = total.file_total

          setTimeout(() => {
            this.$baseNotify(
              '对比完成，找到' +
                this.apodiffcount +
                '个' +
                this.activeTab +
                '差异配置。',
              '提示',
              'success',
              'bottom-right'
            )
            this.btnRefreshLoading = false
          }, 1500)
        } catch (error) {
          console.error('发生错误:', error)
          this.$baseNotify(
            '请求错误，请检查网络或服务器状态',
            '错误',
            'error',
            'bottom-right'
          )
        } finally {
          clearTimeout(timeoutId)
          Loading.close()
        }
      },
    },
  }
</script>

<style>
  .el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 200px;
    min-height: 400px;
  }

  .test {
    color: #edf8f2;
    color: #4bb98a;
    color: #f6f8f9;
  }
</style>
