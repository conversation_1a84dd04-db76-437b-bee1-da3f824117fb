<template></template>

<script>
  import { getPodFiles, openPodFiles, downloadPodFile } from '@/api/ciWorkflow'
  export default {
    name: 'PodFileExplorer',
    data() {
      return {
        dialogFormVisible: false,
        allfile: [],
        queryForm: {},
      }
    },
    created() {},
    methods: {
      showEdit() {
        this.dialogFormVisible = true
      },
      setSelectRows(val) {
        this.selectRows = val
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      async fetchData() {
        const { data } = await getPodFiles(this.queryForm)
        this.allfile = data
        setTimeout(() => {}, 500)
      },
    },
  }
</script>
