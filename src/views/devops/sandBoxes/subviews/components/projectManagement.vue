<template>
  <el-dialog
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="500px"
    @close="close"
  >
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="服务名称" prop="name">
        <el-select
          v-model="form.name"
          clearable
          filterable
          placeholder="请选择"
          @change="handleChange"
        >
          <el-option
            v-for="item in allproject"
            :key="item.appname"
            :label="`${item.name} ${item.appname}`"
            :value="item.appname + ',' + item.name + ',' + item.git_project"
          >
            <span style="float: left">{{ item.name }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ item.appname }}
            </span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="项目工程">
        <el-input v-model.trim="form.git_project" :readonly="true"></el-input>
      </el-form-item>
      <el-form-item label="项目分支" prop="branch">
        <el-select
          v-model="form.branch"
          autocomplete="on"
          clearable
          filterable
          placeholder="请先选择服务"
        >
          <el-option
            v-for="item in allbranch"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="doSave">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {
    createSandboxProject,
    getAllProject,
    getProjectBranch,
  } from '@/api/ciWorkflow'

  export default {
    name: 'AppManagement',
    data() {
      return {
        isedit: false,
        title: '',
        dialogFormVisible: false,
        form: {
          sbx_uuid: '',
          appname: '',
          project: '',
          git_project: '',
          name: '',
          branch: '',
        },
        allproject: [],
        allbranch: [],
      }
    },

    created() {
      this.fetchData()
    },
    methods: {
      showEdit(id, row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.form = { ...this.$options.data().from } // 重置表单
        } else {
          this.title = '编辑'
          this.isedit = true
          this.form = Object.assign({}, row)
        }
        this.form.sbx_uuid = id
        this.dialogFormVisible = true
      },

      close() {
        this.$refs['form'].resetFields()
        this.form = { ...this.$options.data().form }
        this.allbranch = [] // 清空分支数据
        this.dialogFormVisible = false
      },
      async doSave() {
        const { msg } = await createSandboxProject(this.form)
        this.$baseMessage(msg, 'success')
        this.$emit('fetch-data')
        this.close()
      },

      async handleChange(val) {
        if (val) {
          let array = val.split(',')
          this.form.appname = array[0]
          this.form.name = array[1]
          this.form.git_project = array[2]
          const { data } = await getProjectBranch({ git_project: array[2] })
          this.allbranch = data
          setTimeout(() => {}, 60000)
        } else {
          this.$refs['form'].resetFields()
          this.form = this.$options.data().form
          this.allbranch = []
        }
      },

      async fetchData() {
        const { data } = await getAllProject()
        this.allproject = data
        setTimeout(() => {}, 30000)
      },
    },
  }
</script>
