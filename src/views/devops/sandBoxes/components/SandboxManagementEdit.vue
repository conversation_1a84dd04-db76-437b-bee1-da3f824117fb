<template>
  <el-dialog
    :close-on-click-modal="isedit"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form
      v-if="isedit === false"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="80px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="业务线" prop="flow_uuid">
            <el-select
              v-model="form.bizline"
              filterable
              placeholder="请选择"
              @change="handleChange"
            >
              <el-option
                v-for="item in allBizlines"
                :key="item.tapd_id"
                :label="item.tapd_name"
                :value="item.tapd_name"
              >
                <span style="float: left">{{ item.tapd_name }}</span>
                <span style="float: right; color: #8492a6; font-size: 8px">
                  {{ item.tapd_id }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="迭代版本" prop="version_code">
            <el-select
              v-model="form.version_code"
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in allIteration"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="环境类型" prop="type">
            <el-select v-model="form.value" placeholder="请选择环境类型">
              <el-option
                v-for="item in typeOpions"
                :key="item.value"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item
            label="环境别名"
            placeholder="环境别名"
            prop="sbx_alias"
          >
            <el-input
              v-model.trim="form.sbx_alias"
              :readonly="true"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="基础域名" placeholder="wegoab.com" prop="domain">
            <el-input
              v-model.trim="form.domain"
              :readonly="true"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="有效期" prop="valid_time">
            <el-date-picker
              v-model="form.valid_time"
              :picker-options="pickerOptions"
              align="right"
              placeholder="计划截止时间"
              type="date"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="申请原因" prop="memo">
            <el-input
              v-model.trim="form.memo"
              :rows="4"
              autocomplete="off"
              spellcheck="false"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row style="text-align: right">
        <div class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button type="primary" @click="saveApply">提交申请</el-button>
        </div>
      </el-row>
    </el-form>

    <el-form
      v-if="isedit === true"
      ref="formEditor"
      :model="formEditor"
      :rules="rules"
      label-width="80px"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="环境标识" prop="sbx_uuid">
            <el-input
              v-model.trim="formEditor.sbx_uuid"
              :readonly="true"
              autocomplete="off"
              placeholder="请输入环境别名"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="环境别名" prop="sbx_alias">
            <el-input
              v-model.trim="formEditor.sbx_alias"
              autocomplete="off"
              placeholder="请输入环境别名"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row style="text-align: right">
        <el-button @click="editorClose">取 消</el-button>
        <el-button type="primary" @click="updateApply">提交变更</el-button>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
  import { getUserInfo } from '@/api/user'
  import { getBizlines, getIterations } from '@/api/tapd'
  import { doCreateSandbox, doUpdateSandbox } from '@/api/ciWorkflow'

  export default {
    name: 'SandboxManagementEdit',
    data() {
      return {
        form: {
          bizline: '',
          type: '',
          domain: 'wegoab.com',
          valid_time: '',
          memo: '',
          version_code: '',
          sbx_alias: '',
        },
        formEditor: {
          sbx_alias: '',
          sbx_uuid: '',
          flow_uuid: '',
          status: 0,
        },
        isedit: false,
        allBizlines: [],
        allIteration: [],
        typeOpions: [
          {
            value: '0',
            label: '开发联调',
            disabled: true,
          },
          {
            value: '1',
            label: '系统测试',
          },
          {
            value: '3',
            label: '灰度验证',
            disabled: true,
          },
        ],

        rules: {
          bizline: [
            { required: true, trigger: 'blur', message: '请选择业务线名称' },
          ],
          version_code: [
            {
              required: true,
              trigger: 'blur',
              message: '请选择或者填写迭代版本号',
            },
          ],
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now()
          },
          shortcuts: [
            {
              text: '三天',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 3)
                picker.$emit('pick', new Date())
              },
            },
            {
              text: '一周',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
                picker.$emit('pick', date)
              },
            },
            {
              text: '两周',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 14)
                picker.$emit('pick', date)
              },
            },
          ],
        },
        currentUser: '',
        isAdmin: false,
        title: '',
        dialogFormVisible: false,
      }
    },
    computed: {},
    mounted() {
      this.fetchData()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '申请新环境'
          this.isedit = false
        } else {
          this.title = '更新信息'
          this.isedit = true
          this.formEditor = Object.assign({}, row)
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      editorClose() {
        this.$refs['formEditor'].resetFields()
        this.formEditor = this.$options.data().formEditor
        this.dialogFormVisible = false
      },
      saveApply() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            const { msg } = await doCreateSandbox(this.form)
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },
      updateApply() {
        this.$refs['formEditor'].validate(async (valid) => {
          if (valid) {
            const { flow_uuid, sbx_uuid, status, sbx_alias } = this.formEditor
            const optimizedMergedData = {
              flow_uuid,
              sbx_uuid,
              status,
              sbx_alias,
            }
            const { msg } = await doUpdateSandbox(optimizedMergedData)
            this.$baseMessage('mergedData', 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },
      async handleChange() {
        const { data } = await getIterations({ line_name: this.form.bizline })
        this.allIteration = data
        setTimeout(() => {}, 600)
      },

      async fetchData() {
        if (this.isedit === false) {
          try {
            const { user, groups } = await getUserInfo()
            this.currentUser = user
            this.isAdmin = groups.indexOf('admin') !== -1
          } catch (error) {
            console.error('获取用户信息出错：', error)
          }
          try {
            const { data } = await getBizlines()
            this.allBizlines = data
          } catch (error) {
            console.error('获取业务线信息出错：', error)
          }
        } else {
          setTimeout(() => {}, 600)
        }
      },
    },
  }
</script>
