<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="true"
    width="800px"
    @close="close"
  >
    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
    >
      <el-table-column
        show-overflow-tooltip
        prop="step_name"
        label="进度"
      ></el-table-column>

      <el-table-column show-overflow-tooltip prop="step_result" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.step_result | statusType">
            <i v-if="row.step_result === 3" class="el-icon-loading"></i>
            {{ row.step_result | statusMap }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="create_time"
        label="开始时间"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="step_log"
        label="明细"
      ></el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
  import { getOperationlogs } from '@/api/ciWorkflow'

  export default {
    name: 'SandboxManagementLogs',
    filters: {
      statusMap(status) {
        const statusMap = {
          0: '完成',
          1: '失败',
          2: '部分失败',
          3: '进行中',
        }
        return statusMap[status]
      },
      statusType(status) {
        const statusMap = {
          0: 'success',
          1: 'danger',
          2: 'danger',
          3: 'warning',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        elementLoadingText: '正在获取日志...',
        listLoading: true,
        list: null,
      }
    },
    computed: {},
    created() {
      this.listLoading = true
      this.fetchData()
    },
    methods: {
      showlogs(sbx_uuid, type) {
        this.title = '初始化日志'
        this.fetchData(sbx_uuid, type)
        this.dialogFormVisible = true
      },
      close() {
        this.list = null
        this.dialogFormVisible = false
      },
      async fetchData(sbx_uuid, type) {
        const param = 'sbx_uuid=' + sbx_uuid + '&task_type=' + type
        const { data } = await getOperationlogs(param)
        this.list = data
        this.listLoading = false
        setTimeout(() => {}, 600)
      },
    },
  }
</script>
