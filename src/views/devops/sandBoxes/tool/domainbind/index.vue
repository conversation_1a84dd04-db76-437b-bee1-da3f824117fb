<template>
  <div class="portbindManagement-container">
    <el-alert title="Tips:" type="success">
      <template slot>
        <div class="ci-alert-list">解绑后才能再次绑定</div>
        <p />
      </template>
    </el-alert>
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <p />
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              placeholder="请输入关键字"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column
        show-overflow-tooltip
        prop="domain"
        label="测试域名"
      ></el-table-column>

      <el-table-column show-overflow-tooltip prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status | statusType">
            {{ row.status | statusMap }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="occupy_sbxid"
        label="沙盒id"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="update_by"
        label="使用人"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="update_time"
        label="绑定时间"
      ></el-table-column>

      <el-table-column show-overflow-tooltip label="操作" width="200">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">管理</el-button>
          <el-button type="text" color="#fffff" @click="handleVisit(row)">
            访问
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      :layout="layout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
  import { queryDomainBind } from '@/api/ciWorkflow'
  import qs from 'qs'

  export default {
    name: 'DomainbindManagement',
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'success',
          1: 'danger',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '空闲',
          1: '占用',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        list: null,
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    created() {
      this.fetchData()
      localStorage.setItem('domainbind', '1')
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.occupy_sbxid) {
          this.$router.push({
            name: 'sandboxManagement',
            params: {
              sbx_uuid: row.occupy_sbxid,
            },
          })
        }
      },
      handleVisit(row) {
        // console.log(row.domain)
        if (row.occupy_sbxid) {
          window.open('https://' + row.domain, '_blank')
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const { data, totalCount } = await queryDomainBind(this.queryForm)
        this.list = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>
