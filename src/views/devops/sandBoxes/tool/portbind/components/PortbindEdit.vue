<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="映射端口" prop="name">
            <el-input-number
              v-model.number="form.port"
              :min="20000"
              :max="20048"
              placeholder="映射端口，端口范围为20000-20048，不能重复。"
              :readonly="isedit"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="沙盒id" prop="sbxid">
            <el-input
              v-model.trim="form.sbxid"
              autocomplete="off"
              placeholder="中间件名称，如redis"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="映射服务" prop="service">
            <el-input
              v-model.trim="form.service"
              autocomplete="off"
              placeholder="中间件名称，如redis"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="服务端口">
            <el-input-number
              v-model.number="form.service_port"
              :min="1"
              :max="65535"
              placeholder="服务端口"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { addPortbind } from '@/api/ciWorkflow'

  export default {
    name: 'PortBindEdit',
    data() {
      return {
        form: {
          id: null,
          port: 20000,
          service_port: 22,
        },
        rules: {
          port: [
            { required: true, trigger: 'blur', message: '请输入映射端口号' },
            { type: 'number', message: '端口号必须为数字值' },
          ],
          sbxid: [{ required: true, trigger: 'blur', message: '请输入沙盒id' }],
          service: [
            { required: true, trigger: 'blur', message: '请输入服务名' },
          ],
          service_port: [
            { required: true, trigger: 'blur', message: '请输入服务端口' },
            { type: 'number', message: '端口号必须为数字值' },
          ],
        },
        title: '',
        isedit: false,
        dialogFormVisible: false,
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
        } else {
          this.title = '编辑'
          this.isedit = true
          this.form = Object.assign({}, row)
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            const { msg } = await addPortbind(this.form)
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },
      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>
