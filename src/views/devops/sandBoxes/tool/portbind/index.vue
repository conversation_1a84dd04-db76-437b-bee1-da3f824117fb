<template>
  <div class="portbindManagement-container">
    <el-alert title="Tips:" type="success">
      <template slot>
        <div class="ci-alert-list">1.可以绑定沙盒环境内微服务的tcp端口。</div>
        <div class="ci-alert-list">2.只允许删除自己添加的记录。</div>
        <div class="ci-alert-list">3.映射端口范围为20000-20048，不能重复。</div>
        <p />
      </template>
    </el-alert>
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          新增映射
        </el-button>
        <el-button icon="el-icon-upload" type="warning" @click="handleRefresh">
          刷新代理
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              placeholder="请输入关键字"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column
        show-overflow-tooltip
        prop="port"
        label="映射端口"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="service"
        label="服务名称"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="sbxid"
        label="沙盒id"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="service_port"
        label="服务端口"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="gateway_ip"
        label="代理ip"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="owner"
        label="创建人"
      ></el-table-column>

      <el-table-column show-overflow-tooltip label="操作" width="200">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">查看</el-button>
          <el-button type="text" color="#fffff" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      :layout="layout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <edit ref="edit" @fetch-data="fetchData"></edit>
  </div>
</template>

<script>
  import { getPortbind, deletePortbind } from '@/api/ciWorkflow'
  import Edit from './components/PortbindEdit'
  import qs from 'qs'

  export default {
    name: 'PortbindManagement',
    components: { Edit },
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'danger',
          1: 'success',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '下线',
          1: '正常',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        list: null,
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    created() {
      this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleRefresh() {
        this.$baseMessage('无刷新权限，请联系运维处理。', 'warning')
      },
      async handleDelete(row) {
        const { msg } = await deletePortbind(row)
        this.$baseMessage(msg, 'success')
        this.fetchData()
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const { data, totalCount } = await getPortbind(
          qs.stringify(this.queryForm)
        )
        this.list = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>
