<template>
  <div class="sandboxmain-container">
    <el-row :gutter="20">
      <el-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <span>环境统计</span>
          </div>
          <vab-chart :option="boxlist" auto-resize theme="vab-echarts-theme" />
        </el-card>
      </el-col>

      <el-col :lg="12" :md="12" :sm="24" :xl="12" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <span>业务线统计</span>
          </div>
          <vab-chart :option="linelist" auto-resize theme="vab-echarts-theme" />
        </el-card>
      </el-col>

      <el-col :lg="16" :md="24" :sm="24" :xl="16" :xs="24">
        <el-card shadow="never">
          <vab-query-form>
            <vab-query-form-left-panel :span="12">
              <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
                申请新环境
              </el-button>
            </vab-query-form-left-panel>
            <vab-query-form-right-panel :span="12">
              <el-form :inline="true" :model="queryForm" @submit.native.prevent>
                <el-form-item>
                  <el-input
                    v-model.trim="queryForm.keyword"
                    clearable
                    placeholder="环境ID/别名/迭代版本"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="queryData"
                  >
                    查询
                  </el-button>
                </el-form-item>
              </el-form>
            </vab-query-form-right-panel>
          </vab-query-form>

          <el-table
            v-loading="listLoading"
            :data="list"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <!--
            <el-table-column
              label="环境标识"
              prop="sbx_uuid"
              show-overflow-tooltip
            ></el-table-column>-->

            <el-table-column
              label="沙盒别名"
              prop="sbx_alias"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <a @click="handleView(row)">
                  <div style="display: flex; align-items: center">
                    <svg
                      class="icon"
                      height="20"
                      p-id="1646"
                      t="1702885712400"
                      version="1.1"
                      viewBox="0 0 1024 1024"
                      width="20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M682.666667 256h-128v81.066667h128a174.933333 174.933333 0 0 1 174.933333 174.933333 174.933333 174.933333 0 0 1-174.933333 174.933333h-128V768h128a256 256 0 0 0 256-256c0-141.653333-114.773333-256-256-256M166.4 512A174.933333 174.933333 0 0 1 341.333333 337.066667h128V256H341.333333a256 256 0 0 0-256 256 256 256 0 0 0 256 256h128v-81.066667H341.333333c-96.426667 0-174.933333-78.506667-174.933333-174.933333M341.333333 554.666667h341.333334v-85.333334H341.333333v85.333334z"
                        fill="#42A5F5"
                        p-id="1647"
                      ></path>
                    </svg>
                    {{ row.sbx_alias || row.sbx_uuid }}
                  </div>
                </a>
              </template>
            </el-table-column>
            <!--
            <el-table-column
              label="迭代版本"
              prop="version_code"
              show-overflow-tooltip
            ></el-table-column>-->

            <el-table-column label="环境类型" prop="type" show-overflow-tooltip>
              <template #default="{ row }">
                <el-tag :type="row.type | statusType">
                  {{ row.type | typeMap }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              label="申请人"
              prop="applicant"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column label="状态" prop="status" show-overflow-tooltip>
              <template #default="{ row }">
                <el-tag :type="row.status | statusType">
                  {{ row.status | statusMap }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" show-overflow-tooltip width="220">
              <template #default="{ row }">
                <el-button
                  v-if="row.status === 0"
                  :disabled="!isAdmin"
                  type="text"
                  @click="handleDeploy(row)"
                >
                  部署环境
                </el-button>
                <el-button
                  v-if="row.status === 5"
                  type="text"
                  @click="handleViewlog(row)"
                >
                  查看日志
                </el-button>
                <el-button
                  v-if="row.status === 5"
                  :disabled="!isAdmin"
                  type="text"
                  @click=""
                >
                  重新部署
                </el-button>
                <el-button
                  v-if="row.status === 1"
                  type="text"
                  @click="handleViewlog(row)"
                >
                  查看进度
                </el-button>
                <!--                <el-button v-if="row.status === 2" type="text" @click="handleEdit(row)">别名设置</el-button>-->
                <el-button
                  v-if="row.status === 2"
                  type="text"
                  @click="handleView(row)"
                >
                  管理服务
                </el-button>
                <el-button
                  v-if="row.status === 2"
                  :disabled="!isAdmin"
                  type="text"
                  @click="handleSeal(row)"
                >
                  封存环境
                </el-button>
                <el-button v-if="row.status === 3" type="text" @click="">
                  申请延期
                </el-button>
                <el-button
                  v-if="row.status === 3"
                  :disabled="!isAdmin"
                  type="text"
                  @click="handleDelete(row)"
                >
                  释放环境
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page="queryForm.pageNo"
            :layout="layout"
            :page-size="queryForm.pageSize"
            :total="total"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
        <el-card shadow="never">
          <el-alert title="说明:" type="success">
            <div class="ci-alert-list">○ 环境详细信息请点击：沙盒别名</div>
            <div class="ci-alert-list">○ 默认：沙盒别名为沙盒ID</div>
            <p />
          </el-alert>
        </el-card>

        <el-card shadow="never">
          <div slot="header">
            <span>沙盒工具箱</span>
          </div>
          <el-col
            v-for="(item, index) in iconList"
            :key="index"
            :lg="8"
            :md="8"
            :sm="8"
            :xl="8"
            :xs="8"
          >
            <router-link :to="item.link">
              <el-card :id="item.id" class="icon-panel" shadow="never">
                <vab-icon
                  :icon="['fas', item.icon]"
                  :style="{ color: item.color }"
                ></vab-icon>
                <p>{{ item.title }}</p>
              </el-card>
            </router-link>
          </el-col>
        </el-card>

        <el-card shadow="never">
          <div slot="header">
            <span>动态信息</span>
          </div>
          <el-timeline :reverse="reverse">
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :color="activity.color"
              :timestamp="activity.timestamp"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
    <edit ref="edit" @fetch-data="fetchData"></edit>
    <viewlog ref="viewlog"></viewlog>
  </div>
</template>

<script>
  import VabChart from '@/plugins/echarts'
  import Edit from './components/SandboxManagementEdit'
  import Viewlog from './components/SandboxManagementLogs'
  import {
    deploySandbox,
    earseSandbox,
    getSandboxlist,
    getSandboxstatistics,
    sealSandbox,
  } from '@/api/ciWorkflow'
  import { getUserInfo } from '@/api/user'
  import Driver from 'driver.js'
  import 'driver.js/dist/driver.min.css'
  import { getBizlines } from '@/api/tapd'

  export default {
    name: 'SandboxMain',
    components: { VabChart, Edit, Viewlog },
    filters: {
      typeMap(status) {
        const statusMap = {
          0: '开发联调',
          1: '系统测试',
          2: '灰度预发',
          4: '压力测试',
          5: '基线环境',
        }
        return statusMap[status]
      },

      statusMap(status) {
        const statusMap = {
          0: '申请中',
          1: '创建中',
          2: '使用中',
          3: '已封存',
          4: '已释放',
          5: '创建失败',
        }
        return statusMap[status]
      },
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'warning',
          2: 'success',
          3: 'danger',
          4: 'info',
          5: 'danger',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        iconList: [
          {
            id: 'portbind',
            icon: 'gift',
            title: '端口映射',
            link: '/devops/sandboxes/portbind',
            color: '#ffd666',
          },
          {
            id: 'domainbind',
            icon: 'bullhorn',
            title: '域名绑定',
            link: '/devops/sandboxes/domainbind',
            color: '#ffc069',
          },
        ],
        currentUser: '',
        isAdmin: '',
        total: 0,
        reverse: true,
        listLoading: true,
        selectRows: '',
        layout: 'total, sizes, prev, pager, next, jumper',
        elementLoadingText: '正在加载...',
        allList: [],
        list: null,
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        activities: [
          {
            content: '速度更快的2.8版本相册服务重构版本上线了！',
            timestamp: '2021-10-14',
          },
        ],
        timer: 0,
        boxlist: {
          tooltip: {
            trigger: 'item',
          },
          legend: {
            show: true,
            top: '5%',
            left: '2%',
            orient: 'vertical',
          },
          series: [
            {
              name: '环境列表',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '15',
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: true,
              },
              data: [],
            },
          ],
        },

        linelist: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {},
          grid: {
            top: '0%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
          },
          yAxis: {
            type: 'category',
            data: [],
          },
          series: [
            {
              type: 'bar',
              data: [],
            },
          ],
        },
      }
    },
    created() {
      this.fetchData()
      this.handleTour()
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    mounted() {},
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleView(row) {
        if (row.sbx_uuid) {
          this.$router.push({
            name: 'sandboxManagement',
            params: {
              sbx_uuid: row.sbx_uuid,
              sbx_alias: row.sbx_alias,
            },
          })
        }
      },
      handleTour() {
        if (localStorage.getItem('domainbind') !== '1') {
          const driver = new Driver()
          setTimeout(function () {
            driver.highlight({
              element: '#domainbind',
              popover: {
                title: '提示',
                description: '绑定/空闲的测试二级域名可以在这里查询  ',
              },
            })
          }, 500)
        }
      },
      handleEdit(row) {
        if (row.sbx_uuid) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleViewlog(row) {
        if (row.sbx_uuid) {
          this.$refs['viewlog'].showlogs(row.sbx_uuid, 0)
        }
      },

      async handleDeploy(row) {
        this.$baseConfirm(
          '部署前请确认资源容量，确认开始吗？',
          null,
          async () => {
            const { msg } = await deploySandbox(row)
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
          }
        )
      },
      async handleSeal(row) {
        this.$baseConfirm('确认封存环境吗？', null, async () => {
          const { msg } = await sealSandbox(row)
          this.$baseMessage('环境封存成功', 'success')
          this.$emit('fetch-data')
        })
      },
      async handleDelete(row) {
        this.$baseConfirm(
          '环境删除后数据将无法恢复，确认继续吗？',
          null,
          async () => {
            const { msg } = await earseSandbox(row)
            this.$baseMessage('环境删除成功', 'success')
            this.$emit('fetch-data')
          }
        )
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val

        // 基于已存储的allList进行分页数据切片操作
        const startIndex = (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        const endIndex = startIndex + this.queryForm.pageSize
        this.list = this.allList.slice(startIndex, endIndex)
        // 更新total值，确保总数量显示正确
        this.total = this.allList.length
        // this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        const startIndex = (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        const endIndex = startIndex + this.queryForm.pageSize
        this.list = this.allList.slice(startIndex, endIndex)
        this.total = this.allList.length
        // this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      async fetchData() {
        this.listLoading = true
        var { data, totalCount } = await getSandboxlist(this.queryForm)
        var filteredData = data.filter((item) => item.status >= 0)
        // 将完整列表数据存储到allList变量中
        this.allList = filteredData
        // 根据当前页码和每页显示条数进行首次分页数据设置
        const startIndex = (this.queryForm.pageNo - 1) * this.queryForm.pageSize
        const endIndex = startIndex + this.queryForm.pageSize

        this.list = this.allList.slice(startIndex, endIndex)
        this.total = this.allList.length

        setTimeout(() => {
          this.listLoading = false
        }, 500)

        const { user, groups } = await getUserInfo()
        this.currentUser = user
        this.isAdmin = groups.indexOf('admin') !== -1

        var { data } = await getSandboxstatistics()
        this.boxlist.series[0].data = data.status_data
        this.linelist.yAxis.data = data.line_name
        this.linelist.series[0].data = data.line_value

        var { data } = await getBizlines()
        console.log('index getBizlines：', data)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sandboxmain-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }
</style>
