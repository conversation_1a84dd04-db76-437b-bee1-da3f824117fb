<template>
  <el-container style="height: 100vh; overflow: auto">
    <el-form ref="form" :model="form" label-width="120px">
      <el-form-item label="发版的标题">
        <el-input v-model="form.title"></el-input>
      </el-form-item>

      <el-form-item label="功能变更">
        <el-table
          :data="form.changes"
          style="width: 100%"
          :show-header="false"
          @row-click="handleRowClick"
        >
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column
            prop="description"
            label="功能变更描述"
          ></el-table-column>
        </el-table>
        <el-button type="primary" @click="addChange">添加功能变更</el-button>
      </el-form-item>

      <el-form-item label="后端项目">
        <el-select
          v-model="form.backendProjects"
          multiple
          placeholder="请选择后端项目"
        >
          <el-option
            v-for="item in backendProjects"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="前端项目">
        <el-select
          v-model="form.frontendProjects"
          multiple
          placeholder="请选择前端项目"
        >
          <el-option
            v-for="item in frontendProjects"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="项目的版本号">
        <el-input v-model="form.version"></el-input>
      </el-form-item>

      <el-form-item label="相关责任人">
        <el-select
          v-model="form.responsiblePersons"
          multiple
          placeholder="请选择责任人"
        >
          <el-option
            v-for="item in responsiblePersons"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="备注信息">
        <el-input v-model="form.remarks" type="textarea"></el-input>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">重置</el-button>
      </el-form-item>
    </el-form>
  </el-container>
</template>

<script>
  export default {
    data() {
      return {
        form: {
          title: '',
          changes: [],
          backendProjects: [],
          frontendProjects: [],
          version: '',
          responsiblePersons: [],
          remarks: '',
        },
        backendProjects: [
          { value: 'backend_project_1', label: '后端项目1' },
          { value: 'backend_project_2', label: '后端项目2' },
          { value: 'backend_project_3', label: '后端项目3' },
        ],
        frontendProjects: [
          { value: 'frontend_project_1', label: '前端项目1' },
          { value: 'frontend_project_2', label: '前端项目2' },
          { value: 'frontend_project_3', label: '前端项目3' },
        ],
        responsiblePersons: [
          { value: 'tester', label: '测试' },
          { value: 'developer', label: '研发' },
          { value: 'product', label: '产品' },
          { value: 'operation', label: '运维' },
        ],
      }
    },
    methods: {
      submitForm(formName) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            alert('提交成功')
          } else {
            // console.log("error submit!!");
            return false
          }
        })
      },
      resetForm(formName) {
        this.$refs[formName].resetFields()
      },
      addChange() {
        this.form.changes.push({ description: '' })
      },
      handleRowClick(row) {
        const index = this.form.changes.indexOf(row)
        this.form.changes.splice(index, 1)
      },
    },
  }
</script>
