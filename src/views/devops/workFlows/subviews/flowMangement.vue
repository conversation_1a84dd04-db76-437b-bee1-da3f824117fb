<template>
  <div class="sandboxanagement-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card shadow="never">
          <el-steps
            :active="5"
            finish-status="success"
            process-status="process"
          >
            <el-step title="项目启动"></el-step>
            <el-step title="开发联调"></el-step>
            <el-step title="自动化测试"></el-step>
            <el-step title="系统测试"></el-step>
            <el-step title="灰度预发"></el-step>
            <el-step title="上线自检"></el-step>
            <el-step title="现网发布"></el-step>
            <el-step title="结束"></el-step>
          </el-steps>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card shadow="never">
          <el-alert title="Tips:" type="success" show-icon>
            <template slot>
              <div class="ci-alert-list">1.先列一下相关的按钮</div>
              <div class="ci-alert-list">2.交互逻辑</div>
            </template>
          </el-alert>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" type="flex">
      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-card shadow="never">
          <el-descriptions
            class="margin-top"
            title="基本信息"
            :column="3"
            size="medium"
            border
          >
            <template slot="extra">
              <el-button type="primary" size="small">修改</el-button>
            </template>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-guide"></i>
                工作流ID
              </template>
              {{ flow_uuid }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-trophy"></i>
                迭代版本
              </template>
              V114
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class=""></i>
                所属业务
              </template>
              增长与会员
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                创建人
              </template>
              dongxin
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-time"></i>
                创建时间
              </template>
              2022-04-14 17:42
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                业务负责人
              </template>
              jiyelin
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                测试负责人
              </template>
              huangjiefang
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                产品负责人
              </template>
              zhangqingliang
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                运维负责人
              </template>
              wuxuan
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        <projects></projects>
        <story></story>
      </el-col>

      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <Sandboxes></Sandboxes>
        <el-card shadow="never">
          <div slot="header">
            <span>流程日志</span>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in activities"
              :key="index"
              :timestamp="activity.timestamp"
              :color="activity.color"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import VabChart from '@/plugins/echarts'
  import { getSandboxProject } from '@/api/ciWorkflow'
  import Story from './components/TapdStory'
  import Projects from './components/ProjectsInfo'
  import Sandboxes from './components/SandboxInfo'

  export default {
    name: 'FlowManagement',
    components: { VabChart, Story, Projects, Sandboxes },
    filters: {},
    data() {
      return {
        reverse: true,
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        quality: 4.7,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          sbx_uuid: '',
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        list: [],
        activities: [
          {
            timestamp: '2022-04-27 16:29',
            content: '工作流页面设计',
          },
          {
            timestamp: '2022-04-20 14:22',
            content: '灰度失败，返回系统测试。',
            color: '#f34d37',
          },
          {
            timestamp: '2022-04-14 10:29',
            content: '沙盒交互设计',
          },
          {
            timestamp: '2022-04-13 14:22',
            content: '版本提交测试成功',
            color: '#47ba80',
          },
          {
            timestamp: '2022-04-11 07:39',
            content: '沙盒页面设计',
          },
          {
            timestamp: '2022-04-01 12:39',
            content: '项目启动',
          },
        ],
      }
    },
    created() {
      this.listLoading = false
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    mounted() {},
    methods: {
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      setSelectRows(val) {
        this.selectRows = val
      },

      handleManage(row) {
        this.$refs['edit'].showEdit(this.queryForm.sbx_uuid)
      },

      async fetchData() {
        this.listLoading = true
        const { data, totalCount } = await getSandboxProject(this.queryForm)
        this.list = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sandboxanagement-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
    .ci-alert-list {
      margin-top: 5px;
    }
  }
</style>
