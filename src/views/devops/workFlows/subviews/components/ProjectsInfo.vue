<template>
  <el-card shadow="never">
    <template #header>
      <vab-icon icon="send-plane-2-line" />
      制品信息
      <el-tag class="card-header-tag" type="success">
        自动构建于2022-05-01 12:46
      </el-tag>
      <el-button style="float: right" type="primary">自动构建设置</el-button>
    </template>
    <el-table :data="tableData" row-key="title">
      <el-table-column prop="name" label="服务名称" />
      <el-table-column prop="image_name" label="仓库名称" width="220px" />
      <el-table-column prop="tag" label="TAG" />
      <el-table-column prop="type" label="类型" />
      <el-table-column prop="create_time" label="创建时间" width="150px" />
    </el-table>
  </el-card>
</template>
<script>
  export default {
    data() {
      return {
        tableData: [
          {
            name: '老订单',
            image_name: 'wego/wego-order',
            tag: 'prod.325',
            type: 2,
            create_time: '2022-05-01 12:46',
          },
          {
            name: '老相册',
            image_name: 'wego/wego-legacy-album',
            tag: 'prod.231',
            type: 2,
            create_time: '2022-05-01 12:44',
          },
        ],
      }
    },
    mounted() {},
  }
</script>
