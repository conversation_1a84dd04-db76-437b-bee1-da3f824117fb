<template>
  <el-card shadow="never">
    <template #header>
      <vab-icon icon="send-plane-2-line" />
      版本更新说明
      <el-tag class="card-header-tag" type="success">
        更新于2022-04-28 15:10
      </el-tag>
      <el-button style="float: right" type="primary">同步TAPD信息</el-button>
    </template>
    <el-table :data="tableData" row-key="title">
      <el-table-column prop="title" label="标题" width="320px" />
      <el-table-column prop="priority" label="优先级" />
      <el-table-column prop="owner" label="处理人" />
      <el-table-column prop="status" label="状态" />
    </el-table>
  </el-card>
</template>
<script>
  export default {
    data() {
      return {
        tableData: [
          {
            title: '会员中心-支付流程优化',
            priority: '--',
            owner: '邹涵东',
            status: 2,
          },
          {
            title: '【APP 首页获取手机号弹窗】',
            priority: '--',
            owner: '袁航;刘光;',
            status: 2,
          },
          {
            title: '【微信服务】',
            priority: '1',
            owner: '袁航;',
            status: 2,
          },
          {
            title: '【会员续费强弹窗】',
            priority: '2',
            owner: '袁航;',
            status: 2,
          },
          {
            title: '【广告位配置】 -增加小程序跳转方式',
            priority: '2',
            owner: '袁航;刘光',
            status: 1,
          },
          {
            title: '【客户助手】侧边栏需求文档',
            priority: '--',
            owner: '刘光;袁航',
            status: 0,
          },
        ],
      }
    },
    mounted() {},
  }
</script>
