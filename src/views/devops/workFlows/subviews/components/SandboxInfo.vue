<template>
  <el-card shadow="never">
    <template #header>
      <vab-icon icon="send-plane-2-line" />
      沙盒信息
    </template>
    <el-table :data="tableData">
      <el-table-column prop="sbx_uuid" label="沙盒ID" />
      <el-table-column prop="type" label="类型" />
      <el-table-column show-overflow-tooltip label="操作">
        <template #default="{ row }">
          <el-button type="text" @click="">管理</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>
<script>
  export default {
    data() {
      return {
        tableData: [
          {
            sbx_uuid: 't3khks0o',
            type: 0,
          },
        ],
      }
    },
    mounted() {},
  }
</script>
