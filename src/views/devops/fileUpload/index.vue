<template>
  <div class="roleManagement-container">
    <vab-query-form>
      <el-statistic title="CDN刷新配额剩余">
        <template slot="formatter">
          {{ quota }}
        </template>
      </el-statistic>

      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          上传文件
        </el-button>

        <el-button
          icon="el-icon-refresh"
          :disabled="selectRows.length === 0"
          type="success"
          @click="handleRefresh()"
        >
          刷新CDN
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              placeholder="请输入查询条件"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="fetchData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column show-overflow-tooltip type="selection"></el-table-column>
      <el-table-column show-overflow-tooltip label="URL">
        <template #default="{ row }">
          https://{{ row.domain }}{{ row.path }}{{ row.targetname }}
        </template>
      </el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="filesize"
        label="文件大小"
        width="100"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="owner"
        label="上传人"
        width="100"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="create_time"
        label="上传时间"
        width="200"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="expire_time"
        label="过期时间"
        width="150"
      ></el-table-column>

      <el-table-column show-overflow-tooltip label="操作" width="250">
        <template #default="{ row }">
          <el-button type="text" @click="handleLink(row)">打开链接</el-button>
          <el-dropdown trigger="click" @command="handleDropdown">
            <el-button type="text" style="margin-left: 10px">
              更多
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                icon="el-icon-link"
                :command="beforeHandleCommand('src', row)"
              >
                源站链接
              </el-dropdown-item>
              <el-dropdown-item
                icon="el-icon-delete"
                :command="beforeHandleCommand('del', row)"
                :disabled="true"
              >
                删除文件
              </el-dropdown-item>
              <el-dropdown-item
                icon="el-icon-question"
                :command="beforeHandleCommand('check', row)"
              >
                一致性检查
              </el-dropdown-item>
              <el-dropdown-item
                icon="el-icon-refresh"
                :command="beforeHandleCommand('refresh', row)"
                divided
              >
                刷新缓存
              </el-dropdown-item>
              <el-dropdown-item
                icon="el-icon-key"
                :command="beforeHandleCommand('sign', row)"
              >
                签名下载
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      :layout="layout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <edit ref="edit" @fetch-data="fetchData"></edit>
  </div>
</template>

<script>
  import {
    getUploadRecord,
    getConsistency,
    getRefreshQuota,
    setRefreshCDN,
    getSignUrl,
  } from '@/api/txCloud'
  import Edit from './components/FileSelector.vue'

  export default {
    name: 'FileUpload',
    components: { Edit },
    data() {
      return {
        list: null,
        listLoading: true,
        quota: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    created() {
      this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit()
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      handleLink(row) {
        window.open('https://' + row.domain + row.path + row.targetname)
      },
      beforeHandleCommand(item, row) {
        return {
          command: item,
          row: row,
        }
      },
      handleDropdown(command) {
        switch (command.command) {
          case 'refresh':
            this.handleRefresh(command.row)
            break
          case 'check':
            this.checkConsistency(command.row)
            break
          case 'src':
            var url =
              'https://xc-static-1251632793.cos.ap-guangzhou.myqcloud.com' +
              command.row.path +
              command.row.filename
            this.copyToClipboard(url)
            this.$baseMessage('对象存储URL已经复制到剪贴板', 'success')
            break
          case 'sign':
            this.getSignUrl(command.row)
            break
        }
      },
      copyToClipboard(content) {
        if (window.clipboardData) {
          window.clipboardData.setData('text', content)
        } else {
          ;(function (content) {
            document.oncopy = function (e) {
              e.clipboardData.setData('text', content)
              e.preventDefault() //取消事件的默认动作
              document.oncopy = null
            }
          })(content)
          document.execCommand('Copy')
        }
      },
      async checkConsistency(row) {
        const { result } = await getConsistency(row)
        if (result) {
          this.$baseMessage('对象存储和CDN文件一致。', 'success')
        } else {
          this.$baseMessage('对象存储和CDN文件不一致，请手工刷新。', 'warning')
        }
      },
      async getSignUrl(row) {
        console.log({
          url: 'https://' + row.domain + row.path + row.targetname,
        })
        const { data } = await getSignUrl(
          'https://' + row.domain + row.path + row.targetname
        )
        if (data) {
          this.copyToClipboard(data)
          this.$baseMessage('对象存储URL已经复制到剪贴板', 'success')
        }
      },
      async handleRefresh(row) {
        this.$baseConfirm(
          '确认刷新所选文件CDN缓存吗？请关注配额。',
          null,
          async () => {
            var urls = ''
            if (row) {
              urls = 'https://' + row.domain + row.path + row.targetname
            } else if (this.selectRows.length > 0) {
              for (var item of this.selectRows) {
                // console.log(item)
                urls =
                  urls +
                  'https://' +
                  item.domain +
                  item.path +
                  item.targetname +
                  ','
              }
            }
            if (urls.slice(-1) === ',') {
              urls = urls.slice(0, -1)
            }
            const { UrlPurge } = await setRefreshCDN({ url: urls })
            this.quota = UrlPurge[0].Available
            this.$baseNotify('任务已经提交，约5分钟内生效。', '通知', 'success')
          }
        )
      },
      async fetchData() {
        const { data, totalCount } = await getUploadRecord(this.queryForm)
        this.list = data
        this.total = totalCount
        const { UrlPurge } = await getRefreshQuota()
        this.quota = UrlPurge[0].Available
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>
