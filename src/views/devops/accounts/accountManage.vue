<template>
  <div class="roleManagement-container">
    <vab-query-form>
      <el-row :gutter="20">
        <el-col :span="6">
          <div>
            <el-statistic
              group-separator=","
              :precision="0"
              :value="apiStat.accountCount"
              title="云账号个数"
            ></el-statistic>
          </div>
        </el-col>
        <el-col :span="4">
          <div>
            <el-statistic title="阿里云调用次数">
              <template slot="formatter">{{ apiStat.alicallCount }}</template>
            </el-statistic>
          </div>
        </el-col>
        <el-col :span="4">
          <div>
            <el-statistic
              group-separator=","
              :precision="2"
              decimal-separator="."
              :value="apiStat.aliRate"
              title="阿里云成功率"
            >
              <template slot="prefix">
                <i class="el-icon-s-flag" style="color: green"></i>
              </template>
              <template slot="suffix">%</template>
            </el-statistic>
          </div>
        </el-col>

        <el-col :span="4">
          <div>
            <el-statistic title="腾讯云调用次数">
              <template slot="formatter">{{ apiStat.txcallCount }}</template>
            </el-statistic>
          </div>
        </el-col>
        <el-col :span="4">
          <div>
            <el-statistic
              group-separator=","
              :precision="2"
              decimal-separator="."
              :value="apiStat.txRate"
              title="腾讯云成功率"
            >
              <template slot="prefix">
                <i class="el-icon-s-flag" style="color: green"></i>
              </template>
              <template slot="suffix">%</template>
            </el-statistic>
          </div>
        </el-col>
      </el-row>
      <p />
      <el-divider></el-divider>
      <p />
      <vab-query-form-left-panel :span="12">
        <p />
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              placeholder="请输入查询条件"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="fetchData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
    >
      <el-table-column
        show-overflow-tooltip
        prop="account_name"
        label="云账号"
        width="180"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="secretId"
        label="AccessKey"
        width="350"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="sk"
        label="SecretKey"
        width="100"
      >
        <template #default="{ row }">
          <el-button
            v-if="row.sk !== null"
            type="text"
            class="el-icon-view"
            @click="handleViewSk(row)"
          ></el-button>
          {{ row.sk }}
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="platform"
        label="厂商"
        width="100"
      ></el-table-column>
      <el-table-column show-overflow-tooltip label="权限列表">
        <template slot-scope="scope">
          <div class="cell-wrapper">{{ scope.row.privilege_desc }}</div>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="scenario"
        label="业务场景"
        width="150"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="apply_user"
        label="责任人"
        width="150"
      ></el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      :layout="layout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <el-dialog
      :visible.sync="dialogVisible"
      title="验证码"
      custom-class="customWidth"
      @close="resetVerify"
    >
      <div class="verify-wrapper">
        <drag-verify-img-chip
          v-if="dialogVisible"
          ref="dragVerify"
          :imgsrc="img"
          :is-passing.sync="isPassing"
          :width="305"
          text="请按住滑块拖动"
          success-text="验证通过"
          handler-icon="el-icon-d-arrow-right"
          success-icon="el-icon-circle-check"
          radius="4px"
          @refresh="resetVerify"
          @passcallback="handleSuccess"
        ></drag-verify-img-chip>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import dragVerifyImgChip from 'vue-drag-verify-img-chip'
  import { queryCloudUser, queryCloudSecretKey } from '@/api/ciWorkflow'

  export default {
    name: 'AccountManagement',
    components: { dragVerifyImgChip },
    data() {
      return {
        list: [],
        listLoading: true,
        isPassing: false,
        quota: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        dialogVisible: false,
        apiStat: {
          accountCount: 0,
          alicallCount: 360454,
          aliRate: 99.27,
          txcallCount: 199484,
          txRate: 95.54,
        },
      }
    },
    created() {
      //临时页面权限管控
      let ug = getCookie('groups').split(',')
      if (ug.indexOf('dev') >= 0 || ug.indexOf('admins') >= 0) {
      } else {
        this.$router.push('/401')
      }
      this.fetchData()
      var rnd = Math.floor(Math.random() * 2) + 1
      this.img = '/static/wego' + rnd + '.png'
      this.listLoading = false
    },

    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      handleViewSk(row) {
        if (row) {
          this.dialogVisible = true
          this.selectRows = row
        }
      },
      async handleSuccess() {
        this.$message.success('验证通过！')
        this.dialogVisible = false
        var rnd = Math.floor(Math.random() * 2) + 1
        this.img = '/static/wego' + rnd + '.png'
        // const row2Update = this.list.find(obj => obj.ak === this.selectRows.ak);
        // if (row2Update) {
        //   row2Update.sk = 'iCST11JVVIs333PDoCQTJw22VR8bppaGhvv'
        // }
        var { data } = await queryCloudSecretKey(this.selectRows.account_id)
        // console.log(data)
        var rt, rb
        rt = 'secretKey获取成功'
        rb =
          '<p align="left"><b>明文</b>:</p><p>' +
          data.secretKey +
          '</p>' +
          '<p align="left"><b>nacos</b>:</p><p>[[' +
          data.en_key +
          ']]</p><p align="left"><b>apollo</b>:</p><p>{{' +
          data.en_key2 +
          '}}</p>'
        rb =
          '<table>\n' +
          '  <tr>\n' +
          '    <th>明文</th>\n' +
          '  </tr>\n' +
          '  <tr>\n' +
          '    <td align="center" style="font-size: 12px;">' +
          data.secretKey +
          '</td>\n' +
          '  </tr>\n' +
          '  <tr>\n' +
          '    <th>nacos</th>\n' +
          '  </tr>\n' +
          '  <tr>\n' +
          '    <td align="center" style="font-size: 11px;"> secretKey: [[' +
          data.en_key +
          ']]</td>\n' +
          '  </tr>\n' +
          '  <tr>\n' +
          '    <th>apollo</th>\n' +
          '  </tr>\n' +
          '  <tr>\n' +
          '    <td align="center" style="font-size: 11px;"> secretKey: {{' +
          data.ESK +
          '}}</td>\n' +
          '  </tr>\n' +
          '</table>'

        this.$swal({
          title: '<strong>' + rt + '</strong>',
          icon: 'info',
          html: rb,
          showCloseButton: true,
          focusConfirm: false,
          confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
          confirmButtonAriaLabel: 'Thumbs up, great!',
        })
      },
      handleError() {
        this.$message.error('验证失败，请重试！')
        this.$refs.dragVerify.reset()
      },
      resetVerify() {
        this.$refs.dragVerify.reset()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      async fetchData() {
        this.listLoading = true
        var { data, total } = await queryCloudUser(this.queryForm)
        this.list = data
        this.total = total
        if (this.queryForm.keyword === '') {
          this.apiStat.accountCount = total
        }
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }

  export function getCookie(name) {
    var arr,
      reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
    if ((arr = document.cookie.match(reg))) {
      return '' + arr[2]
    } else {
      return ''
    }
  }
</script>
<style>
  .el-table__body td .cell-wrapper {
    height: 40px;
    white-space: pre-wrap;
    word-break: break-all;
    word-wrap: break-word;
  }
  .verify-wrapper {
    width: 350px;
    height: 175px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .customWidth {
    width: 380px !important;
    height: 310px !important;
  }

  .td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid #ddd;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
