<template>
  <el-dialog
    title="上传文件"
    :visible.sync="dialogFormVisible"
    width="580px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-alert title="提示:" type="success" :closable="false">
          <div class="ci-alert-list">1. 仅限非隐私文件。</div>
          <div class="ci-alert-list">
            2. 上传后的文件链接为「 https://static.szwego.com/etc/
            <b>自定义前缀</b>
            /
            <b>文件名</b>
            」
          </div>
          <div class="ci-alert-list">
            3. 上传同名同路径将会覆盖原有文件，覆盖后需要刷新CDN才生效。
          </div>
          <div class="ci-alert-list">
            4. 如文件无需长期保存，请指定过期时间。
          </div>
          <div class="ci-alert-list">5. 文件大小不能超过200MB。</div>
        </el-alert>
        <el-divider></el-divider>
        <el-form-item label="路径前缀：" prop="path">
          <el-autocomplete
            v-model="form.path"
            popper-class="my-autocomplete"
            :clearable="true"
            :fetch-suggestions="querySearch"
            placeholder="路径前缀"
            @select=""
          >
            <i slot="suffix" class="el-icon-edit el-input__icon" @click=""></i>
            <template slot-scope="{ item }">
              <div class="name">{{ item.value }}</div>
              <span class="addr">{{ item.description }}</span>
            </template>
          </el-autocomplete>
        </el-form-item>

        <el-form-item label="过期时间：">
          <el-date-picker
            v-model="form.expiretime"
            type="date"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
            placeholder="留空文件将永久保存"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="文件列表：">
          <el-upload
            ref="upload"
            class="upload-demo"
            :data="form"
            action="/file/api/v1/cos/push"
            :on-preview="handlePreview"
            :before-upload="handleUpload"
            :file-list="fileList"
            :auto-upload="false"
            :multiple="true"
            :drag="true"
          >
            <el-button slot="trigger" size="small" type="primary">
              选取或拖拽文件
            </el-button>
            <el-button
              style="margin-left: 10px"
              size="small"
              type="success"
              @click="submitUpload"
            >
              上传到服务器
            </el-button>
          </el-upload>
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="close">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getUploadPathlist } from '@/api/txCloud'

  export default {
    name: 'FileSelector',
    data() {
      return {
        fileList: [],
        title: '',
        dialogFormVisible: false,
        queryForm: {
          keyword: '',
        },
        form: {
          path: '',
          expiretime: '',
        },
        pathlist: [
          { value: 'tutorial/', description: '用户教程文件' },
          { value: 'ime/', description: '输入法文件' },
          { value: 'appdl/', description: '应用程序下载' },
          { value: 'agreement/', description: '用户协议类文件' },
          { value: 'cms/', description: '官网页面资源等' },
        ],
        rules: {
          path: [{ required: true, message: '请选择存放路径' }],
        },

        pickerOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now()
          },
          shortcuts: [
            {
              text: '一个月',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 30)
                picker.$emit('pick', date)
              },
            },
            {
              text: '一年',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 365)
                picker.$emit('pick', date)
              },
            },
          ],
        },
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      showEdit() {
        this.dialogFormVisible = true
      },
      close() {
        this.fileList = []
        this.path = ''
        this.form = this.$options.data().form
        this.dialogFormVisible = false
        this.$emit('fetch-data')
      },
      handleUpload(file) {
        const fn = file.name
        if (escape(fn).indexOf('%u') >= 0) {
          this.$baseMessage('文件名中包含中文字符，上传失败。', 'error')
          return false
        }
      },
      submitUpload() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            this.$refs.upload.submit()
          } else {
            return false
          }
        })
      },
      handleRemove(file, fileList) {
        // console.log(file, fileList);
      },
      handlePreview(file) {
        // console.log(file);
      },
      handlePath() {
        var path = this.form.path
        // console.log (path.substr(0,1) ,path.substr(0,1) === "/")
        var startpos = path.substr(0, 1) === '/' ? 1 : 0
        var endstr = path.substr(path.length - 1, 1) === '/' ? '' : '/'
        // console.log("str:",path.substring(startpos))
        // console.log("end:",endstr)
      },
      querySearch(queryString, cb) {
        var pathlist = this.pathlist
        var results = queryString
          ? pathlist.filter(this.createFilter(queryString))
          : pathlist
        // 调用 callback 返回建议列表的数据
        cb(results)
      },
      createFilter(queryString) {
        return (pathlist) => {
          return (
            pathlist.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
            0
          )
        }
      },
      async fetchData() {
        const { data } = await getUploadPathlist(this.queryForm)
        this.pathlist = data
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>

<style lang="scss">
  .my-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .addr {
        font-size: 12px;
        color: #b4b4b4;
      }

      .highlighted .addr {
        color: #ddd;
      }
    }
    .ci-alert-list {
      margin-top: 5px;
    }
  }
</style>
