<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="服务名称" prop="name">
            <el-input
              v-model.trim="form.name"
              :readonly="isedit"
              autocomplete="off"
              placeholder="微服务名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部署名称" prop="appname">
            <el-input
              v-model.trim="form.appname"
              :readonly="isedit"
              autocomplete="off"
              placeholder="集群内工作负载名称"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="别名">
            <el-input
              v-model.trim="form.alianame"
              :readonly="isedit"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工程名称" prop="project">
            <el-input
              v-model.trim="form.project"
              :readonly="isedit"
              autocomplete="off"
              placeholder="工程名"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="仓库地址">
            <el-input
              v-model.trim="form.repo_url"
              :readonly="isedit"
              autocomplete="off"
              placeholder="工蜂repo地址"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="仓库名称">
            <el-input
              v-model.trim="form.git_project"
              :readonly="isedit"
              autocomplete="off"
              placeholder="仓库名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实例数">
            <el-input-number
              v-model.trim="form.replicas"
              :max="100"
              :min="1"
              :readonly="isedit"
              placeholder="工作负载实例数"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目类型" prop="type">
            <el-select
              v-model="form.type"
              :readonly="isedit"
              clearable
              placeholder="请选择项目类型"
            >
              <el-option
                v-for="item in type_option"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.value }}</span>
                <span style="float: right; color: #8492a6; font-size: 11px">
                  {{ item.label }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="是否上线">
            <el-switch
              v-model="form.online"
              active-value="1"
              inactive-value="0"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :readonly="isedit" type="primary" @click="save">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { addProject, editProject } from '@/api/ciWorkflow'

  export default {
    name: 'UserManagementEdit',
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'danger',
          1: 'success',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '下线',
          1: '正常',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        form: {
          name: '',
          appname: '',
          alianame: '',
          project: '',
          repo_url: '',
          git_project: '',
          replicas: '',
          online: 1,
          owner: '',
          health_url: '',
          type: '',
        },
        rules: {
          name: [
            { required: true, trigger: 'blur', message: '请输入微服务名称' },
          ],
          appname: [
            { required: true, trigger: 'blur', message: '请输入部署名称' },
          ],
          project: [
            { required: true, trigger: 'blur', message: '请输入工程名' },
          ],
          type: [
            { required: true, trigger: 'blur', message: '请选择项目类型' },
          ],
        },
        title: '',
        isedit: false,
        dialogFormVisible: false,
        type_option: [
          { value: 0, label: '后端' },
          { value: 1, label: '前端' },
          { value: 2, label: '后端其他' },
          { value: 3, label: '前端其他' },
        ],
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
        } else {
          this.title = '编辑'
          this.isedit = true
          this.form = Object.assign({}, row)
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      async save() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            // 添加前缀校验逻辑
            const validPrefixes = ['toolbox', 'wsxc', 'wsxch5']
            const prefix = this.form.git_project.split('/')[0] // 获取前缀部分
            if (!validPrefixes.includes(prefix)) {
              this.$message.error(
                '仓库名称的前缀必须为 toolbox、wsxc 或 wsxch5'
              )
              return false
            }

            let msg
            if (!this.isedit) {
              const response = await addProject(this.form)
              msg = response.msg
            } else {
              const response = await editProject(this.form)
              msg = response.msg
            }
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },
      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>
