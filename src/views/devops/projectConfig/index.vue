<template>
  <div class="projectManagement-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          新增服务
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              clearable
              placeholder="请输入关键字"
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column
        label="服务名称"
        prop="name"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        label="部署名称"
        prop="appname"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="工程名称"
        prop="project"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column label="状态" prop="online" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag :type="row.online | statusType">
            {{ row.online | statusMap }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="线上副本数量"
        prop="replicas"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="项目类型" prop="type" show-overflow-tooltip>
        <template #default="{ row }">
          {{ type_option[row.type] }}
        </template>
      </el-table-column>

      <el-table-column label="操作" show-overflow-tooltip width="200">
        <template #default="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" @click="handleUrl(row)">内网访问</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <edit ref="edit" @fetch-data="fetchData"></edit>
  </div>
</template>

<script>
  import { getProject } from '@/api/ciWorkflow'
  import Edit from './components/ProjectConfigEdit'

  export default {
    name: 'ProjectManagement',
    components: { Edit },
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'danger',
          1: 'success',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '下线',
          1: '正常',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        list: null,
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        type_option: {},
      }
    },
    created() {
      this.fetchData()
      const array = Edit.data().type_option
      this.type_option = this.transformTypeOption(array)
    },
    methods: {
      transformTypeOption(array) {
        const obj = {}
        array.forEach((item) => {
          obj[item.value] = item.label
        })
        return obj
      },
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleUrl(row) {
        if (row.appname) {
          const url = 'http://' + row.appname + '.prod.cluster.szwego.com'
          window.open(url, '_blank')
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const { data, totalCount } = await getProject(this.queryForm)
        this.list = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>
