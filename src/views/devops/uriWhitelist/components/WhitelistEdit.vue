<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    :close-on-click-modal="isedit"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="isedit" label="申请流水号">
            <el-input
              v-model.trim="form.apply_ci_appid"
              :readonly="true"
              placeholder="自动生成"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="isedit" label="申请时间">
            <el-input
              v-model.trim="form.create_time"
              :readonly="true"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="项目代号" prop="version_code">
            <el-input
              v-model.trim="form.version_code"
              autocomplete="off"
              placeholder="项目代号，如V101"
              :readonly="isedit"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请人">
            <el-input
              v-model.trim="form.applicant"
              autocomplete="off"
              :readonly="true"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item v-if="isedit" label="审核状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :label="0">申请中</el-radio>
              <el-radio :label="1">已审核</el-radio>
              <el-radio :label="2">已禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item v-if="isedit" label="审核人" prop="status">
            <el-input
              v-model.trim="form.approver"
              autocomplete="off"
              :readonly="true"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="URI列表 （支持多条）" prop="uri">
            <el-input
              v-model="form.uri"
              type="textarea"
              :readonly="isedit"
              placeholder="多条记录请用换行隔开"
              :rows="8"
              spellcheck="false"
              @input="getUricount"
            ></el-input>
            <span
              class="text"
              style="float: right; color: #909399; margin-right: 10px"
            >
              共有 {{ uricount }} 行
            </span>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注信息">
            <el-input
              v-model="form.memo"
              type="textarea"
              :readonly="isedit"
              :rows="2"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-alert
        v-if="form.status === 1 && isAdmin"
        title="警告:"
        type="warning"
        :closable="false"
      >
        <div class="ci-alert-list">
          禁用操作会将当前批次全部url，如果只禁用单条记录请到数据库操作。
        </div>
      </el-alert>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="info" @click="close">关 闭</el-button>
      <el-button
        v-if="!isedit"
        type="success"
        icon="el-icon-check"
        @click="saveApply"
      >
        提 交
      </el-button>
      <el-button
        v-if="form.status !== 1 && isAdmin && isedit"
        type="success"
        icon="el-icon-check"
        @click="updateStatus(1)"
      >
        启 用
      </el-button>
      <el-button
        v-if="form.status === 1 && isAdmin"
        type="danger"
        icon="el-icon-error"
        @click="updateStatus(2)"
      >
        禁 用
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getUserInfo } from '@/api/user'
  import { doSave, doUpdate } from '@/api/uriWhitelist'

  export default {
    name: 'WhitelistEdit',
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '申请中',
          1: '已审核',
          2: '已禁用',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        form: {
          apply_ci_appid: '',
          version_code: '',
          uri: '',
          applicant: '',
          approver: '',
          memo: '',
          create_time: '',
          status: '0',
        },
        rules: {
          version_code: [
            { required: true, trigger: 'blur', message: '请输入项目代号' },
          ],
          uri: [
            { required: true, trigger: 'blur', message: '请输入白名单列表' },
          ],
        },
        title: '',
        isedit: false,
        currentUser: '',
        isAdmin: false,
        dialogFormVisible: false,
        uricount: 0,
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增申请'
          this.isedit = false
          this.form.applicant = this.currentUser
        } else {
          this.title = '查看详情'
          this.isedit = true
          this.form = Object.assign({}, row)
          this.form.uri = row.uri.join('\n')
          this.getUricount()
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      getUricount() {
        this.uricount = this.form.uri.split('\n').length
      },
      updateStatus(newStatus) {
        var msg =
          newStatus === 1 ? '确认启用这些URL吗？' : '确认禁用这些URL吗？'
        this.$baseConfirm(msg, null, async () => {
          var statusData = {
            status: newStatus,
            apply_ci_appid: this.form.apply_ci_appid,
            approver: this.currentUser,
          }
          const { msg } = await doUpdate(statusData)
          this.$baseMessage(msg, 'success')
          this.$emit('fetch-data')
          this.close()
        })
      },
      saveApply() {
        var confirmmsg =
          this.uricount === 1
            ? 'url支持一次提交多条，确认本次只提交一条记录吗？'
            : `确认提交当前${this.uricount}条记录吗？`
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            this.$baseConfirm(confirmmsg, null, async () => {
              const { msg } = await doSave(this.form)
              this.$baseMessage(msg, 'success')
              this.$emit('fetch-data')
              this.close()
            })
          } else {
            return false
          }
        })
      },
      async fetchData() {
        const { user, groups } = await getUserInfo()
        this.currentUser = user
        this.isAdmin = groups.indexOf('admin') !== -1
        setTimeout(() => {}, 300)
      },
    },
  }
</script>
