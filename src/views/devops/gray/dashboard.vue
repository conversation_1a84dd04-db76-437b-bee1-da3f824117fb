<template>
  <div class="grayManagement-container">
    <el-card>
      <i class="el-icon-moon"><b>灰度任务管理</b></i>
      <p />
      <el-alert title="说明:" type="success">
        <template slot>
          <div class="ci-alert-list">○ 灰度任务按优先级顺序生效。</div>
          <div class="ci-alert-list">
            ○ 多个下发Cookie的任务会相互影响，请勿同时开启。
          </div>
          <div class="ci-alert-list">○ 未设置规则任务会匹配全部用户。</div>
          <p />
        </template>
      </el-alert>
      <p />
      <EventConfig></EventConfig>
    </el-card>
    <el-card>
      <i class="el-icon-collection-tag"><b>静态资源服务（前端）</b></i>
      <p />
      <el-alert title="说明:" type="success">
        <template slot>
          <div class="ci-alert-list">○ 配置各标签用户返回的项目和版本</div>
          <div class="ci-alert-list">○ 更新配置后约1分钟生效</div>
          <p />
        </template>
      </el-alert>
      <p />
      <NssConfig></NssConfig>
    </el-card>
  </div>
</template>
<script>
  import NssConfig from './components/NssConfig'
  import EventConfig from './components/EventConfig'

  export default {
    name: 'GrayDashboard',
    components: { NssConfig, EventConfig },
    data() {
      return {}
    },
    created() {},
    methods: {},
  }
</script>

<style lang="scss" scoped>
  .grayManagement-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }
</style>
