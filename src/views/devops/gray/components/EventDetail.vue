<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    :close-on-click-modal="isedit"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item v-if="isedit" label="任务ID">
            <el-input
              v-model.trim="form.id"
              :readonly="true"
              placeholder="自动生成"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="isedit" label="申请时间">
            <el-input
              v-model.trim="form.create_time"
              :readonly="true"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="任务名称">
            <el-input
              v-model.trim="form.eventname"
              placeholder="请输入灰度任务名称"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="标签类型" prop="tag">
            <el-radio-group v-model="form.tag" size="mini">
              <el-radio-button label="stg">stg</el-radio-button>
              <el-radio-button label="canary">canary</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务类型">
            <el-radio-group
              v-model="form.tag_type"
              size="mini"
              @change="handleTypeChange($event)"
            >
              <el-radio-button label="0">规则模式</el-radio-button>
              <el-radio-button label="1">cookie模式</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="任务优先级">
            <el-tooltip
              class="item"
              effect="light"
              content="0-100，数字越小优先级越高"
              placement="bottom-end"
            >
              <el-input-number
                v-model="form.priority"
                :min="0"
                :max="100"
                label="描述文字"
              ></el-input-number>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划结束时间">
            <el-date-picker
              v-model="form.expire_time"
              type="datetime"
              placeholder="选择日期时间"
              align="right"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item v-if="isedit" label="任务状态" prop="status">
            <el-tag :type="form.status | statusType">
              {{ form.status | statusMap }}
            </el-tag>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="规则列表" prop="rule">
            <el-card shadow="never">
              <el-table
                :data="form.ruleData"
                style="width: 100%"
                size="mini"
                :row-class-name="tableRowClassName"
              >
                <el-table-column prop="rule_type" label="规则类型" width="150">
                  <template slot="header" slot-scope="scope">
                    <el-select
                      v-model="newruletype"
                      placeholder="请选择"
                      @change="handleNewtype($event)"
                    >
                      <el-option
                        v-for="item in typeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        :disabled="item.disabled"
                      ></el-option>
                    </el-select>
                  </template>
                  <template #default="{ row }">
                    {{ row.rule_type | typeMap }}
                  </template>
                </el-table-column>
                <el-table-column prop="rule_value" label="任务类型">
                  <template slot="header" slot-scope="scope">
                    <el-input
                      v-model="newruleValue"
                      size="mini"
                      placeholder="输入规则取值"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="操作">
                  <template slot="header" slot-scope="scope">
                    <el-button type="plain" size="mini" @click="handleAdd()">
                      {{ newbutton }}
                    </el-button>
                    <el-tooltip class="item" effect="light" placement="top-end">
                      <div slot="content">
                        ○ 同规则多个数值用逗号隔开
                        <br />
                        ○
                        同规则多个数值条件关系为「OR」；不同规则间关系为「AND」
                        <br />
                        ○ 未设置任何规则将匹配全部用户
                        <br />
                        ○ ip匹配规则可以使用「office-sz」表示深圳办公区IP
                      </div>
                      <i class="el-icon-warning"></i>
                    </el-tooltip>
                  </template>
                  <template #default="{ row }">
                    <el-button type="text" size="mini" @click="handleEdit(row)">
                      修改
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      @click="handleDelete(row)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="任务说明">
            <el-input v-model="form.memo" type="textarea" :rows="2"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="info" @click="close">取 消</el-button>
      <el-button
        v-if="form.status !== 1 && isAdmin && isedit"
        type="warning"
        icon="el-icon-check"
        @click="updateStatus(1)"
      >
        启 用
      </el-button>
      <el-button
        v-if="form.status === 1 && isAdmin"
        type="warning"
        icon="el-icon-error"
        @click="updateStatus(0)"
      >
        暂 停
      </el-button>
      <el-button
        v-if="form.status === 1 && isAdmin"
        type="danger"
        icon="el-icon-error"
        @click="updateStatus(2)"
      >
        禁 用
      </el-button>
      <el-button type="success" icon="el-icon-check" @click="saveApply">
        保 存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getUserInfo } from '@/api/user'
  import { updateGrayEvent, updateGrayStatus } from '@/api/ciWorkflow'

  export default {
    name: 'EventDetail',
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'info',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '配置中',
          1: '已生效',
          2: '已结束',
        }
        return statusMap[status]
      },
      typeMap(status) {
        const typeMap = {
          1: 'IP匹配',
          2: 'albumid',
          3: '归属地',
          4: '客户端类型',
          5: '客户端版本',
          101: '按比例随机',
          102: '按总数量',
        }
        return typeMap[status]
      },
      ruleMap(status) {
        const ruleMap = {
          0: '规则模式',
          1: 'cookie模式',
        }
        return ruleMap[status]
      },
    },
    data() {
      return {
        newbutton: '增加',
        newruletype: '',
        newruleValue: '',
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() <= Date.now()
          },
          shortcuts: [
            {
              text: '一天后',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24)
                picker.$emit('pick', new Date())
              },
            },
            {
              text: '一周后',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 7)
                picker.$emit('pick', date)
              },
            },
            {
              text: '两周后',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3600 * 1000 * 24 * 14)
                picker.$emit('pick', date)
              },
            },
          ],
        },
        typeOptions: [
          {
            value: '1',
            label: 'IP匹配',
          },
          {
            value: '2',
            label: 'albumid',
            disabled: true,
          },
          {
            value: '3',
            label: '归属地',
          },
          {
            value: '4',
            label: '客户端类型',
          },
          {
            value: '5',
            label: '客户端版本',
          },
        ],
        form: {
          id: '',
          eventname: '',
          status: 0,
          tag: 'stg',
          tag_type: '规则模式',
          priority: 50,
          owner: '',
          create_time: '',
          expire_time: '',
          memo: '',
          ruleData: [],
        },
        rules: {
          version_code: [
            { required: true, trigger: 'blur', message: '请输入项目代号' },
          ],
          uri: [
            { required: true, trigger: 'blur', message: '请输入白名单列表' },
          ],
        },
        title: '',
        isedit: false,
        currentUser: '',
        isAdmin: false,
        dialogFormVisible: false,
        uricount: 0,
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      tableRowClassName({ row, rowIndex }) {
        if (row.rule_type.toString() === this.newruletype.toString()) {
          return 'warning-row'
        }
        return ''
      },
      showEdit(row) {
        if (!row) {
          this.title = '创建活动'
          this.isedit = false
          //this.form.owner = this.currentUser
        } else {
          this.title = '查看详情'
          this.isedit = true
          this.form = Object.assign({}, row)
          if (row.tag_type === 'cookie模式') {
            this.typeOptions = [
              {
                value: '101',
                label: '比例随机',
              },
              {
                value: '102',
                label: '灰度总数',
              },
            ]
          }
        }
        this.dialogFormVisible = true
      },
      handleNewtype(key) {
        this.newruleid = key
        this.newbutton = '增加'
        this.newruleValue = ''
        for (var item in this.form.ruleData) {
          if (
            this.form.ruleData[item].rule_type.toString() === key.toString()
          ) {
            this.newruleValue = this.form.ruleData[item].rule_value
            this.newbutton = '更新'
          }
        }
      },
      handleTypeChange(key) {
        if (key !== '1') {
          this.typeOptions = [
            {
              value: '1',
              label: 'IP匹配',
            },
            {
              value: '2',
              label: 'albumid',
              disabled: true,
            },
            {
              value: '3',
              label: '归属地',
            },
            {
              value: '4',
              label: '客户端类型',
            },
            {
              value: '5',
              label: '客户端版本',
            },
          ]
        } else {
          this.typeOptions = [
            {
              value: '101',
              label: '比例随机',
            },
            {
              value: '102',
              label: '灰度总数',
            },
          ]
        }
      },
      handleAdd() {
        this.form.ruleData.forEach((item, index, obj) => {
          if (item.rule_type.toString() === this.newruletype.toString()) {
            obj.splice(index, 1)
          }
        })
        const newRule = {
          event_id: this.form.id,
          rule_type: this.newruletype.toString(),
          rule_value: this.newruleValue.toString(),
        }
        this.form.ruleData.push(newRule)
        this.newruleValue = ''
      },
      handleEdit(row) {
        this.$set(this, 'newruletype', row.rule_type.toString())
        this.newruleValue = row.rule_value
        this.newbutton = '更新'
      },
      handleDelete(row) {
        this.form.ruleData.forEach((item, index, obj) => {
          if (item.rule_type.toString() === row.rule_type.toString()) {
            obj.splice(index, 1)
          }
        })
      },
      close() {
        this.newruletype = ''
        this.newvalue = ''
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.typeOptions = [
          {
            value: '1',
            label: 'IP匹配',
          },
          {
            value: '2',
            label: 'albumid',
            disabled: true,
          },
          {
            value: '3',
            label: '归属地',
          },
          {
            value: '4',
            label: '客户端类型',
          },
          {
            value: '5',
            label: '客户端版本',
          },
        ]
        this.dialogFormVisible = false
      },
      async updateStatus(newStatus) {
        this.form.status = newStatus
        const { msg } = await updateGrayStatus(this.form)
        setTimeout(() => {
          this.$baseMessage(msg, 'success')
          this.$emit('fetch-data')
          this.close()
        }, 300)
      },
      async saveApply() {
        const { msg } = await updateGrayEvent(this.form)
        setTimeout(() => {
          this.$baseMessage(msg, 'success')
          this.$emit('fetch-data')
          this.close()
        }, 300)
      },
      async fetchData() {
        const { user, groups } = await getUserInfo()
        this.currentUser = user
        this.isAdmin = groups.indexOf('admin') !== -1
        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<style>
  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }
</style>
