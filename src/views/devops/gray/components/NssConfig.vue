<template>
  <div class="roleManagement-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="hover">
          <div slot="header">
            <el-tag type="danger">prd (生产发版)</el-tag>
            <el-button
              style="float: right"
              type="primary"
              :disabled="!isPrdChanging"
              size="mini"
              @click="updatePrdCfg"
            >
              更新配置
            </el-button>
          </div>
          <div>
            <el-form :model="prdForm" @submit.native.prevent>
              <!--                  <el-form-item label="H5项目： ">
                    <el-radio-group v-model="prdForm.project" size="mini" @change="prdChanged">
                      <el-radio-button label="wsxc_portal">老框架</el-radio-button>
                      <el-radio-button label="wsxc_entry">新框架</el-radio-button>
                    </el-radio-group>
                  </el-form-item>-->

              <el-form-item
                v-if="prdForm.project !== 'wsxc_portal'"
                label="wsxc_entry版本： "
              >
                <el-select
                  v-model="prdForm.version_entry"
                  placeholder="请选择"
                  @change="prdChanged"
                >
                  <el-option
                    v-for="item in WsxcEntryList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="wsxc_portal版本： ">
                <el-select
                  v-model="prdForm.version_portal"
                  placeholder="请选择"
                  @change="prdChanged"
                >
                  <el-option
                    v-for="item in WsxcPortalList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="increase版本： ">
                <el-select
                  v-model="prdForm.version_increase"
                  placeholder="请选择"
                  @change="prdChanged"
                >
                  <el-option
                    v-for="item in WsxcIncreaseList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="silentLogin版本： ">
                <el-select
                  v-model="prdForm.version_login"
                  placeholder="请选择"
                  @change="prdChanged"
                >
                  <el-option
                    v-for="item in WsxcLoginList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="article版本： ">
                <el-select
                  v-model="prdForm.version_article"
                  placeholder="请选择"
                  @change="prdChanged"
                >
                  <el-option
                    v-for="item in WsxcArticleList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="weshop版本： ">
                <el-select
                  v-model="prdForm.version_weshop"
                  placeholder="请选择"
                  @change="prdChanged"
                >
                  <el-option
                    v-for="item in WsxcWeshopList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="更新时间： ">
                <span>{{ prdForm.updatetime }}</span>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="hover">
          <div slot="header">
            <el-tag type="warning">stg (灰度发版)</el-tag>
            <el-button
              style="float: right"
              type="primary"
              :disabled="!isStgChanging"
              size="mini"
              @click="updateStgCfg"
            >
              更新配置
            </el-button>
          </div>
          <div>
            <el-form :model="stgForm" @submit.native.prevent>
              <!--                  <el-form-item label="H5项目： ">
                    <el-radio-group v-model="stgForm.project" size="mini"  @change="stgChanged">
                      <el-radio-button label="wsxc_portal">老框架</el-radio-button>
                      <el-radio-button label="wsxc_entry">新框架</el-radio-button>
                    </el-radio-group>
                  </el-form-item>-->

              <el-form-item
                v-if="stgForm.project !== 'wsxc_portal'"
                label="wsxc_entry版本： "
              >
                <el-select
                  v-model="stgForm.version_entry"
                  clearable
                  placeholder="请选择"
                  @change="stgChanged"
                >
                  <el-option
                    v-for="item in WsxcEntryList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="wsxc_portal版本： ">
                <el-select
                  v-model="stgForm.version_portal"
                  clearable
                  placeholder="请选择"
                  :loading="listLoading"
                  @change="stgChanged"
                >
                  <el-option
                    v-for="item in WsxcPortalList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="increase版本： ">
                <el-select
                  v-model="stgForm.version_increase"
                  clearable
                  placeholder="请选择"
                  :loading="listLoading"
                  @change="stgChanged"
                >
                  <el-option
                    v-for="item in WsxcIncreaseList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="silentLogin版本： ">
                <el-select
                  v-model="stgForm.version_login"
                  placeholder="请选择"
                  @change="stgChanged"
                >
                  <el-option
                    v-for="item in WsxcLoginList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="article版本： ">
                <el-select
                  v-model="stgForm.version_article"
                  placeholder="请选择"
                  @change="stgChanged"
                >
                  <el-option
                    v-for="item in WsxcArticleList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="weshop版本： ">
                <el-select
                  v-model="stgForm.version_weshop"
                  placeholder="请选择"
                  @change="stgChanged"
                >
                  <el-option
                    v-for="item in WsxcWeshopList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="更新时间： ">
                <span>{{ stgForm.updatetime }}</span>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8">
        <el-card shadow="hover">
          <div slot="header">
            <el-tag type="primary">canary (验收发版)</el-tag>
            <el-button
              style="float: right"
              type="success"
              :disabled="!isCanaryChanging"
              size="mini"
              @click="updatCanaryCfg"
            >
              更新配置
            </el-button>
          </div>
          <div>
            <el-form :model="canaryForm" @submit.native.prevent>
              <!--                  <el-form-item label="H5项目： ">
                    <el-radio-group v-model="canaryForm.project" size="mini" @change="canaryChanged">
                      <el-radio-button label="wsxc_portal">老框架</el-radio-button>
                      <el-radio-button label="wsxc_entry">新框架</el-radio-button>
                    </el-radio-group>
                  </el-form-item>-->

              <el-form-item
                v-if="canaryForm.project !== 'wsxc_portal'"
                label="wsxc_entry版本： "
              >
                <el-select
                  v-model="canaryForm.version_entry"
                  clearable
                  placeholder="请选择"
                  @change="canaryChanged"
                >
                  <el-option
                    v-for="item in WsxcEntryList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="wsxc_portal版本： ">
                <el-select
                  v-model="canaryForm.version_portal"
                  clearable
                  placeholder="请选择"
                  @change="canaryChanged"
                >
                  <el-option
                    v-for="item in WsxcPortalList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="increase版本： ">
                <el-select
                  v-model="canaryForm.version_increase"
                  clearable
                  placeholder="请选择"
                  :loading="listLoading"
                  @change="canaryChanged"
                >
                  <el-option
                    v-for="item in WsxcIncreaseList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="silentLogin版本： ">
                <el-select
                  v-model="canaryForm.version_login"
                  placeholder="请选择"
                  @change="canaryChanged"
                >
                  <el-option
                    v-for="item in WsxcLoginList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="article版本： ">
                <el-select
                  v-model="canaryForm.version_article"
                  placeholder="请选择"
                  @change="canaryChanged"
                >
                  <el-option
                    v-for="item in WsxcArticleList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="weshop版本： ">
                <el-select
                  v-model="canaryForm.version_weshop"
                  placeholder="请选择"
                  @change="canaryChanged"
                >
                  <el-option
                    v-for="item in WsxcWeshopList"
                    :key="item.id"
                    :label="item.version"
                    :value="item.version"
                  >
                    <span style="float: left">{{ item.version }}</span>
                    <span style="float: right; color: #8492a6; font-size: 11px">
                      {{ item.last_build_time }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="更新时间： ">
                <span>{{ canaryForm.updatetime }}</span>
              </el-form-item>
            </el-form>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import {
    getNssProjectCfg,
    getNssProjectVersionList,
    getVersioncfg,
    updateNssCanary,
    updateNssPrd,
    updateNssStg,
  } from '@/api/ciWorkflow'

  export default {
    name: 'NssVersionCfg',
    components: {},
    data() {
      return {
        activeTab: 'envA',
        list: null,
        listLoading: false,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        isPrdChanging: false,
        isStgChanging: false,
        isCanaryChanging: false,
        prdForm: {
          project: '',
          version_entry: '',
          version_portal: '',
          version_increase: '',
          version_login: '',
          version_article: '',
          version_weshop: '',
          updatetime: '未设置',
        },

        stgForm: {
          project: '',
          version_entry: 'last_build',
          version_portal: 'last_build',
          version_increase: 'last_build',
          version_login: 'last_build',
          version_article: 'last_build',
          version_weshop: 'last_build',
          updatetime: '未设置',
        },

        canaryForm: {
          project: '',
          version_entry: 'last_build',
          version_portal: 'last_build',
          version_increase: 'last_build',
          version_login: 'last_build',
          version_article: 'last_build',
          version_weshop: 'last_build',
          updatetime: '未设置',
        },

        WsxcPortalList: [],
        WsxcEntryList: [],
        WsxcIncreaseList: [],
        WsxcLoginList: [],
        WsxcArticleList: [],
        WsxcWeshopList: [],

        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    computed: {
      /*    WsxcEntryList() {
      return this.vesionlist.filter((item) => {
        return (
          item.env === 0 && item.project_name.toLocaleLowerCase() === "wsxc_entry"
        )
      })
    },
    WsxcPortalList() {
      return this.vesionlist.filter((item) => {
        return (
          item.env === 0 && item.project_name.toLocaleLowerCase() === "wsxc_portal"
        )
      })
    },*/
    },
    created() {
      this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit()
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      prdChanged() {
        this.isPrdChanging = true
      },
      stgChanged() {
        this.isStgChanging = true
      },
      canaryChanged() {
        this.isCanaryChanging = true
      },

      async updatePrdCfg() {
        this.$baseConfirm(
          '本次更新将影响「全部用户」，确认要更新prd版本吗？',
          '警告',
          async () => {
            const { msg } = await updateNssPrd(this.prdForm)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
            }, 200)
          }
        )
      },

      async updateStgCfg() {
        this.$baseConfirm(
          '本次更新将影响「灰度用户」，确认要更新stg版本吗？',
          '警告',
          async () => {
            const { msg } = await updateNssStg(this.stgForm)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
            }, 200)
          }
        )
      },

      async updatCanaryCfg() {
        this.$baseConfirm(
          '本次更新将影响「验收用户」，确认要更新canary版本吗？',
          '警告',
          async () => {
            const { msg } = await updateNssCanary(this.canaryForm)
            setTimeout(() => {
              this.$baseMessage(msg, 'success')
            }, 200)
          }
        )
      },

      async fetchData() {
        var { data } = await getNssProjectCfg()
        for (var key in data) {
          var item = data[key]
          if (item.type === 'prd') {
            this.prdForm.project = item.project_name
          }
          if (item.type === 'stg') {
            this.stgForm.project = item.project_name
          }
          if (item.type === 'canary') {
            this.canaryForm.project = item.project_name
          }
        }

        var { data } = await getVersioncfg()
        for (var key in data) {
          var item = data[key]
          if (item.type === 'prd') {
            this.prdForm.updatetime = item.approve_time
            this.prdForm.version_entry =
              item.project_name === 'wsxc_entry'
                ? item.version
                : this.prdForm.version_entry
            this.prdForm.version_portal =
              item.project_name === 'wsxc_portal'
                ? item.version
                : this.prdForm.version_portal
            this.prdForm.version_increase =
              item.project_name === 'increase_performance'
                ? item.version
                : this.prdForm.version_increase
            this.prdForm.version_login =
              item.project_name === 'wsxc_silentLogin'
                ? item.version
                : this.prdForm.version_login
            this.prdForm.version_article =
              item.project_name === 'wsxc_article'
                ? item.version
                : this.prdForm.version_article
            this.prdForm.version_weshop =
              item.project_name === 'wsxc_weshop'
                ? item.version
                : this.prdForm.version_weshop
            this.isPrdChanging = false
          }
          if (item.type === 'stg') {
            this.stgForm.updatetime = item.approve_time
            this.stgForm.version_entry =
              item.project_name === 'wsxc_entry'
                ? item.version
                : this.stgForm.version_entry
            this.stgForm.version_portal =
              item.project_name === 'wsxc_portal'
                ? item.version
                : this.stgForm.version_portal
            this.stgForm.version_increase =
              item.project_name === 'increase_performance'
                ? item.version
                : this.stgForm.version_increase
            this.stgForm.version_login =
              item.project_name === 'wsxc_silentLogin'
                ? item.version
                : this.stgForm.version_login
            this.stgForm.version_article =
              item.project_name === 'wsxc_article'
                ? item.version
                : this.stgForm.version_article
            this.stgForm.version_weshop =
              item.project_name === 'wsxc_weshop'
                ? item.version
                : this.stgForm.version_weshop
            this.isStgChanging = false
          }
          if (item.type === 'canary') {
            this.canaryForm.updatetime = item.approve_time
            this.canaryForm.version_entry =
              item.project_name === 'wsxc_entry'
                ? item.version
                : this.canaryForm.version_entry
            this.canaryForm.version_portal =
              item.project_name === 'wsxc_portal'
                ? item.version
                : this.canaryForm.version_portal
            this.canaryForm.version_increase =
              item.project_name === 'increase_performance'
                ? item.version
                : this.canaryForm.version_increase
            this.canaryForm.version_login =
              item.project_name === 'wsxc_silentLogin'
                ? item.version
                : this.canaryForm.version_login
            this.canaryForm.version_article =
              item.project_name === 'wsxc_article'
                ? item.version
                : this.canaryForm.version_article
            this.canaryForm.version_weshop =
              item.project_name === 'wsxc_weshop'
                ? item.version
                : this.canaryForm.version_weshop
            this.isCanaryChanging = false
          }
        }

        var { data } = await getNssProjectVersionList('wsxc_portal', '')
        this.WsxcPortalList = data

        var { data } = await getNssProjectVersionList('wsxc_entry', '')
        this.WsxcEntryList = data

        var { data } = await getNssProjectVersionList(
          'increase_performance',
          ''
        )
        this.WsxcIncreaseList = data

        var { data } = await getNssProjectVersionList('wsxc_silentLogin', '')
        this.WsxcLoginList = data

        var { data } = await getNssProjectVersionList('wsxc_article', '')
        this.WsxcArticleList = data

        var { data } = await getNssProjectVersionList('wsxc_weshop', '')
        this.WsxcWeshopList = data
      },
    },
  }
</script>

<style></style>
