<template>
  <div class="eventConfig-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          新增任务
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-radio-group v-model="eventFilter" size="mini">
            <el-radio-button label="当前任务"></el-radio-button>
            <el-radio-button label="历史任务"></el-radio-button>
          </el-radio-group>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column show-overflow-tooltip type="selection"></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="id"
        label="ID"
        width="50"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="eventname"
        label="任务名称"
      ></el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="priority"
        label="优先级"
        width="80"
      ></el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="tag"
        label="任务标签"
        width="100"
      >
        <template #default="{ row }">
          <el-tag :type="row.tag | tagType">
            {{ row.tag }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column show-overflow-tooltip prop="tag_type" label="任务类型">
        <template #default="{ row }">
          {{ row.tag_type | ruleMap }}
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="create_time"
        label="创建时间"
      ></el-table-column>

      <el-table-column show-overflow-tooltip prop="status" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.status | statusType">
            {{ row.status | statusMap }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        show-overflow-tooltip
        prop="owner"
        label="创建人"
      ></el-table-column>

      <el-table-column show-overflow-tooltip label="操作" width="100">
        <template #default="{ row }">
          <el-button type="success" @click="handleEdit(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="queryForm.pageNo"
      :page-size="queryForm.pageSize"
      :layout="layout"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <edit ref="edit" @fetch-data="fetchData"></edit>
  </div>
</template>

<script>
  import { getGrayEvent } from '@/api/ciWorkflow'
  import Edit from './EventDetail.vue'

  export default {
    name: 'NssVersionCfg',
    components: { Edit },
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'info',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '配置中',
          1: '已生效',
          2: '已结束',
        }
        return statusMap[status]
      },
      tagType(status) {
        const statusMap = {
          stg: 'warning',
          canary: 'success',
          prd: 'info',
        }
        return statusMap[status]
      },
      ruleMap(status) {
        const ruleMap = {
          0: '规则模式',
          1: 'cookie模式',
        }
        return ruleMap[status]
      },
    },
    data() {
      return {
        eventFilter: '当前任务',
        list: null,
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',

        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    computed: {},
    created() {
      this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      async fetchData() {
        this.listLoading = true
        const { data } = await getGrayEvent()
        this.list = data
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>

<style></style>
