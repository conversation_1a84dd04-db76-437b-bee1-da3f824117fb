<template>
  <div class="index-container">
    <!-- 其他功能代码保持不变 -->
    <el-row :gutter="20">
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>PinPoint Transaction查询</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="queryTxid"
            ></el-button>
          </div>
          <el-form
            :model="txidForm"
            style="display: flex"
            @submit.native.prevent
          >
            <el-form-item style="flex: 1">
              <el-input
                v-model.trim="txidForm.txid"
                clearable
                placeholder="txid in http headers"
                prefix-icon="el-icon-search"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>腾讯云资源信息查询</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="queryip"
            ></el-button>
          </div>
          <el-form style="display: flex" @submit.native.prevent>
            <el-form-item style="flex: 1">
              <el-input
                v-model.trim="ip2find"
                clearable
                placeholder="IP / 环境 / 服务名"
                prefix-icon="el-icon-search"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>办公室IP查询</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="queryofficeip"
            ></el-button>
          </div>
          <el-form style="display: flex" @submit.native.prevent>
            <el-form-item style="flex: 1">
              <span>查询深圳1305、1307办公区的IP。</span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>二维码识别</span>
          </div>
          <div>
            <qrcode-drop-zone
              @detect="onDetect"
              @dragover="onDragOver"
              @init="logErrors"
            >
              <div :class="{ dragover: dragover }" class="drop-area">
                将二维码图片文件拖动到此区域
              </div>
            </qrcode-drop-zone>
          </div>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>apollo数据加密</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="base64Func"
            ></el-button>
          </div>
          <el-form style="display: flex" @submit.native.prevent>
            <el-form-item style="flex: 1">
              <el-input
                v-model.trim="decodeStr"
                clearable
                placeholder="请输入你需要的数据"
                prefix-icon="el-icon-search"
              />
            </el-form-item>
          </el-form>
          <el-dialog :visible.sync="dialogVisible" title="加密结果">
            <p>
              <b>加密字符</b>
              : {{ decodeStr }}
            </p>
            <p>
              <b>加密结果</b>
              : {{ encryptedResult }}
            </p>
          </el-dialog>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>容器IP推断</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="handleGetIps"
            ></el-button>
          </div>
          <div>
            <el-input v-model="ips" :rows="1" type="textarea"></el-input>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>albumID解密 (支持批量)</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="decodeID"
            ></el-button>
          </div>
          <el-form style="display: flex" @submit.native.prevent>
            <el-form-item style="flex: 1">
              <el-input
                v-model="encryptedID"
                :rows="3"
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>IP归属查询 (支持批量)</span>
            <el-button
              icon="el-icon-search"
              size="mini"
              style="float: right"
              type="primary"
              @click="handleIps2City"
            ></el-button>
          </div>
          <div>
            <el-input v-model="ips1" :rows="3" type="textarea"></el-input>
          </div>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>投诉信息推送数据下载</span>
            <el-button
              icon="el-icon-download"
              size="mini"
              style="float: right"
              type="primary"
              @click="downloadComplainInfo"
            ></el-button>
          </div>
          <span>时间选择(大于等于)：</span>
          <el-date-picker
            v-model="ComplaintInfoTime"
            placeholder="选择日期时间"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" align="buttom">
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>Base64 编/解码</span>
          </div>
          <el-form @submit.native.prevent>
            <el-form-item>
              <el-input
                v-model.trim="base64Input"
                clearable
                placeholder="请输入文本"
                rows="3"
                type="textarea"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button size="mini" type="primary" @click="encodeBase64">
                编码
              </el-button>
              <el-button size="mini" type="primary" @click="decodeBase64">
                解码
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="base64Output"
                :rows="3"
                placeholder="输出结果"
                readonly
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>URL 编/解码</span>
          </div>
          <el-form @submit.native.prevent>
            <el-form-item>
              <el-input
                v-model.trim="urlInput"
                clearable
                placeholder="请输入URL"
                rows="3"
                type="textarea"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button size="mini" type="primary" @click="encodeUrl">
                编码
              </el-button>
              <el-button size="mini" type="primary" @click="decodeUrl">
                解码
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model="urlOutput"
                :rows="3"
                placeholder="输出结果"
                readonly
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>JSON数据可视化工具</span>
            <el-button
              icon="el-icon-data-analysis"
              size="mini"
              style="float: right"
              type="primary"
              @click="openJsonVisualization"
            ></el-button>
          </div>
          <el-form @submit.native.prevent>
            <el-form-item style="flex: 1">
              <span>
                可视化JSON数据，轻松生成折线图、柱状图和面积图。支持任意数组结构的JSON数据分析。
              </span>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>

    <!-- JSON可视化弹窗 -->
    <el-dialog
      :visible.sync="jsonDialogVisible"
      fullscreen
      custom-class="json-visualization-dialog"
      append-to-body
      title="JSON数据可视化工具"
    >
      <div class="data-visualization-dashboard">
        <!-- 头部和主题切换 -->
        <div class="dashboard-header">
          <h1>JSON数据可视化</h1>
        </div>

        <div class="dashboard-container">
          <!-- JSON输入和数据配置部分仅在图表未生成时显示 -->
          <div v-if="!chartRendered">
            <!-- JSON输入卡片 -->
            <div class="card json-input-card">
              <div class="card-header">
                <h2>输入 JSON 数据</h2>
              </div>
              <div class="card-body">
                <textarea
                  v-model="jsonInput"
                  placeholder="请在此处粘贴您的 JSON 数据..."
                  @input="debouncedParseJson"
                ></textarea>
                <div class="button-container">
                  <button class="btn secondary" @click="parseJson">
                    解析 JSON
                  </button>
                  <button class="btn secondary" @click="clearJson">清空</button>
                </div>
              </div>
            </div>

            <!-- 数据配置卡片 -->
            <div v-if="jsonData" class="card config-card">
              <div class="card-header">
                <h2>数据配置</h2>
              </div>
              <div class="card-body">
                <div class="config-section">
                  <label for="array-select">数据数组：</label>
                  <div class="select-wrapper">
                    <select id="array-select" v-model="selectedArrayKey">
                      <option v-for="key in arrayKeys" :key="key" :value="key">
                        {{ key }}
                      </option>
                    </select>
                    <div class="select-arrow"></div>
                  </div>
                </div>

                <div v-if="selectedArray.length > 0" class="config-section">
                  <label for="time-field">时间轴字段：</label>
                  <div class="select-wrapper">
                    <select id="time-field" v-model="selectedTimeField">
                      <option
                        v-for="field in objectKeys(selectedArray[0])"
                        :key="field"
                        :value="field"
                      >
                        {{ field }}
                      </option>
                    </select>
                    <div class="select-arrow"></div>
                  </div>
                </div>

                <div v-if="selectedArray.length > 0" class="config-section">
                  <label for="value-field">数值字段：</label>
                  <div class="select-wrapper">
                    <select id="value-field" v-model="selectedValueField">
                      <option
                        v-for="field in numericFields(selectedArray[0])"
                        :key="field"
                        :value="field"
                      >
                        {{ field }}
                      </option>
                    </select>
                    <div class="select-arrow"></div>
                  </div>
                </div>

                <div
                  v-if="selectedArray.length > 0"
                  class="config-section chart-type-section"
                >
                  <label>图表类型：</label>
                  <div class="chart-type-buttons">
                    <button
                      class="chart-type-btn"
                      :class="{ active: chartType === 'line' }"
                      @click="chartType = 'line'"
                    >
                      <span class="chart-icon">📈</span>
                      折线图
                    </button>
                    <button
                      class="chart-type-btn"
                      :class="{ active: chartType === 'bar' }"
                      @click="chartType = 'bar'"
                    >
                      <span class="chart-icon">📊</span>
                      柱状图
                    </button>
                    <button
                      class="chart-type-btn"
                      :class="{ active: chartType === 'area' }"
                      @click="chartType = 'area'"
                    >
                      <span class="chart-icon">🌊</span>
                      面积图
                    </button>
                  </div>
                </div>

                <button
                  v-if="selectedArray.length > 0"
                  class="btn primary btn-generate"
                  @click="renderChart"
                >
                  生成图表
                  <span class="icon">📊</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 图表卡片 -->
          <div v-show="chartRendered" class="card chart-card">
            <div class="card-header">
              <h2>数据可视化</h2>
              <div class="card-actions">
                <button
                  class="btn icon-btn"
                  title="下载图表"
                  @click="downloadChart"
                >
                  <span class="icon">💾</span>
                </button>
              </div>
            </div>
            <div class="card-body">
              <div
                id="chartContainer"
                ref="chartContainer"
                class="chart-container"
              ></div>
            </div>
          </div>
        </div>

        <!-- 状态提示 -->
        <div
          class="toast"
          :class="{ show: toast.show, success: toast.type === 'success' }"
        >
          {{ toast.message }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    aesDecrypt,
    cleanMaven,
    cnfTypeService,
    ComplaintInfo,
    decodeAlbumID,
    decodeAlbumIDs,
    getIPsCity,
    getIPsService,
    getNginxroute,
    getQueryip,
    getSkywalkingSample,
    OpsReport,
    saveExclude,
    setSkywalkingSample,
    getOfficeIp,
  } from '@/api/toolbox'
  import { QrcodeDropZone } from 'vue-qrcode-reader'
  import { Base64 } from 'js-base64'
  import { httpGet } from '@/api/test'
  import * as echarts from 'echarts'

  export default {
    name: 'Toolbox',
    components: { QrcodeDropZone },
    data() {
      return {
        base64Input: '',
        base64Output: '',
        urlInput: '',
        urlOutput: '',
        ips: '',
        ips1: '',
        domain: '',
        txidForm: {
          txid: '',
        },
        encryptedID: '',
        ip2find: '',
        uri2find: '',
        projects: [
          { value: 'content' },
          { value: 'content-check' },
          { value: 'datanalysis' },
          { value: 'lookbook' },
          { value: 'lookbook-admin' },
          { value: 'lookbook-common' },
          { value: 'lookbook-da' },
          { value: 'lookbook-parent' },
          { value: 'lookbook-scheduler' },
          { value: 'lookbook-support' },
          { value: 'lookbook-test' },
          { value: 'script' },
          { value: 'starter' },
          { value: 'toolbox' },
          { value: 'wego-ai' },
          { value: 'wego-album' },
          { value: 'wego-album-api' },
          { value: 'wego-album-common' },
          { value: 'wego-album-service' },
          { value: 'wego-basic-common' },
          { value: 'wego-commodity' },
          { value: 'wego-commodity-api' },
          { value: 'wego-commodity-common' },
          { value: 'wego-commodity-service' },
          { value: 'wego-common' },
          { value: 'wego-data-mongodb-starter' },
          { value: 'wego-eswordcount' },
          { value: 'wego-eswordcount-service' },
          { value: 'wego-logistics' },
          { value: 'wego-logistics-api' },
          { value: 'wego-logistics-common' },
          { value: 'wego-logistics-service' },
          { value: 'wego-order' },
          { value: 'wego-order-api' },
          { value: 'wego-order-common' },
          { value: 'wego-order-service' },
          { value: 'wego-partner' },
          { value: 'wego-payment' },
          { value: 'wego-payment-api' },
          { value: 'wego-payment-common' },
          { value: 'wego-payment-service' },
          { value: 'wego-root' },
          { value: 'wego-searching' },
          { value: 'wego-starter' },
          { value: 'wgf' },
          { value: 'xway' },
          { value: 'basic' },
          { value: 'bifrost' },
          { value: 'bigdata' },
          { value: 'commodity' },
          { value: 'dss' },
          { value: 'flygeese' },
          { value: 'portal' },
          { value: 'searching' },
          { value: 'share' },
          { value: 'starter' },
          { value: 'xway' },
        ],
        mavenForm: {
          service: '',
        },
        possibeService: [],
        skywalkingform: {
          prdA: '10',
          prdB: '10',
          prdCS: '10',
        },
        rules: {
          service: [
            {
              required: true,
              trigger: 'blur',
              message: '请输入要清理的工程目录',
            },
          ],
        },
        decodeStr: '',
        encryptedResult: '',
        dialogVisible: false,
        cnf_options: [
          { value: 'nacos', label: 'nacos' },
          { value: 'apollo', label: 'apollo' },
        ],
        cnf_services: { services: [] },
        cnf_type: {
          type: '',
          service: '',
          file: '',
        },
        labelPosition: 'right',
        formLabelAlign: {
          name: '',
          region: '',
          type: '',
        },
        message: '',
        result: null,
        error: null,
        dragover: false,
        ComplaintInfoTime: '',
        jsonDialogVisible: false,
        jsonInput: '',
        jsonData: null,
        selectedArrayKey: '',
        selectedArray: [],
        selectedTimeField: '',
        selectedValueField: '',
        chartInstance: null,
        chartType: 'line',
        chartRendered: false,
        isDarkMode: false,
        toast: {
          show: false,
          message: '',
          type: 'error',
        },
        chartData: null,
        debouncedParseJson: null,
      }
    },
    computed: {
      arrayKeys() {
        if (!this.jsonData) return []
        return Object.keys(this.jsonData).filter(
          (key) =>
            Array.isArray(this.jsonData[key]) && this.jsonData[key].length > 0
        )
      },
    },
    watch: {
      selectedArrayKey(newKey) {
        if (newKey && this.jsonData) {
          this.selectedArray = this.jsonData[newKey]
          this.selectedTimeField = ''
          this.selectedValueField = ''
        }
      },
    },
    created() {
      this.fetchData()
      this.debouncedParseJson = this.debounce(() => {
        if (this.jsonInput.trim()) {
          this.parseJson()
        }
      }, 1000)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.resizeChart)
      if (this.chartInstance) {
        this.chartInstance.dispose()
        this.chartInstance = null
      }
    },
    methods: {
      encodeBase64() {
        this.base64Output = btoa(this.base64Input)
      },
      decodeBase64() {
        try {
          const base64Decoded = atob(this.base64Input)
          const bytes = new Uint8Array(base64Decoded.length)
          for (let i = 0; i < base64Decoded.length; i++) {
            bytes[i] = base64Decoded.charCodeAt(i)
          }
          const decoder = new TextDecoder('utf-8')
          this.base64Output = decoder.decode(bytes)
        } catch (e) {
          this.base64Output =
            '解码失败：输入的字符串不是有效的Base64编码或包含不正确的字符编码。'
        }
      },
      encodeUrl() {
        this.urlOutput = encodeURIComponent(this.urlInput)
      },
      decodeUrl() {
        try {
          this.urlOutput = decodeURIComponent(this.urlInput)
        } catch (e) {
          this.urlOutput = '解码失败：输入的字符串不是有效的URL编码。'
        }
      },
      querySearch(queryString, cb) {
        const projects = this.projects
        const results = queryString
          ? projects.filter(this.createFilter(queryString))
          : projects
        cb(results)
      },
      createFilter(queryString) {
        return (project) => {
          return (
            project.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
          )
        }
      },
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      queryTxid() {
        let txid = this.txidForm.txid
        txid = txid.indexOf('^') < 0 ? decodeURI(txid) : txid
        const link =
          'https://pinpoint.in.szwego.com/transactionDetail?transactionInfo='
        const param =
          '{"agentId":"' +
          txid.split('%')[0] +
          '","spanId":"-1","traceId":"' +
          txid +
          '","collectorAcceptTime":"0"}'
        if (this.txidForm.txid) {
          window.open(link + encodeURI(param), '_blank')
        }
      },
      async queryofficeip() {
        try {
          const data = await getOfficeIp()
          const htmlContent = data
            .map(
              (item) => `
      <div>
        <strong>IP 信息:</strong> ${item.ip || '无数据'} <br>
        <strong>更新时间:</strong> ${item.update_time || '无数据'} <br>
      </div>
    `
            )
            .join('<br>')
          this.$swal({
            title: '<strong>查询结果</strong>',
            icon: 'info',
            html: htmlContent,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> 确定',
            confirmButtonAriaLabel: '确定',
          })
        } catch (error) {
          this.$swal({
            title: '<strong>错误</strong>',
            icon: 'error',
            html: `<div>查询时发生错误：${error.message || '未知错误'}</div>`,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> 确定',
            confirmButtonAriaLabel: '确定',
          })
        }
      },
      updateSkywalking() {
        this.$baseConfirm('确认修改Skywalking设置吗？', '', async () => {
          const { msg } = await setSkywalkingSample(this.skywalkingform)
          this.$baseMessage(msg, 'success')
        })
      },
      handleCleancache() {
        this.$refs['mavenForm'].validate(async (valid) => {
          if (valid) {
            this.$baseConfirm(
              '确认清除项目「' + this.mavenForm.service + '」的缓存吗？',
              '',
              async () => {
                const { msg } = await cleanMaven(this.mavenForm)
                this.$baseMessage('清理已完成。', 'success')
                this.mavenForm.service = ''
              }
            )
          }
        })
      },
      async handleGetIps() {
        this.possibeService = []
        const { data } = await getIPsService(this.ips)
        data.forEach((item) => {
          if (
            item.type === 'services' &&
            this.possibeService.indexOf(item.name) === -1
          ) {
            this.possibeService.push(item.name)
          }
        })
        this.$swal({
          title: '<strong>possible service</strong>',
          icon: 'info',
          html: this.possibeService,
          showCloseButton: true,
          focusConfirm: false,
          confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
          confirmButtonAriaLabel: 'Thumbs up, great!',
        })
      },
      async handleIps2City() {
        if (this.ips1) {
          let ips = this.ips1.replace(/[\n;，\s\t]+/g, ',')
          ips = ips.replace(/[^\x00-\x7F]+/g, '')
          let ipArray = ips.split(',')
          let validIpArray = ipArray.filter((ip) => {
            let ipRegex =
              /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
            return ipRegex.test(ip)
          })
          ips = validIpArray.join(',')
          const headers = [
            'IP',
            'Continent',
            'Country',
            'Province',
            'City',
            'Owner',
          ]
          const translations = {
            IP: 'IP',
            Continent: '洲',
            Country: '国家',
            Province: '省份',
            Owner: '所有者',
            City: '城市',
          }
          const { data } = await getIPsCity('ips1=' + ips)
          let html =
            '<table style="font-size:0.8em; border-collapse:collapse; margin:auto;text-align: center">'
          html += '<thead><tr>'
          headers.forEach((header) => {
            html += `<th style="border:1px solid #000;">${translations[header]}</th>`
          })
          html += '</tr></thead>'
          html += '<tbody>'
          data.forEach((item) => {
            html += '<tr>'
            headers.forEach((header) => {
              let key = header.toLowerCase().replace(' ', '')
              let value = item[key]
              html += `<td style="border:1px solid #000;padding:10px;">${
                value !== undefined ? value : '-'
              }</td>`
            })
            html += '</tr>'
          })
          html += '</tbody></table>'
          this.$swal({
            title: '<strong>查询结果</strong>',
            icon: 'info',
            html: html,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
            confirmButtonAriaLabel: 'Thumbs up, great!',
          })
        }
      },
      async testxxx() {
        const res = await httpGet('/static/index.html')
      },
      async fetchData() {
        const { data } = await getSkywalkingSample()
        if (data) {
          this.skywalkingform = data
        }
      },
      async decodeIDold() {
        if (this.encryptedID) {
          const res = await decodeAlbumID(this.encryptedID)
          let rt, rb
          if (res.code === 200) {
            rt = '解密成功'
            rb = `<p align="left"><b>密文</b>:  ${this.encryptedID}</p><p align="left"><b>明文</b>:  ${res.data}</p>`
          } else {
            rt = '解密失败'
            rb = '请确认密文是否完整'
          }
          this.$swal({
            title: `<strong>${rt}</strong>`,
            icon: 'info',
            html: rb,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
            confirmButtonAriaLabel: 'Thumbs up, great!',
          })
        }
      },
      async decodeID() {
        if (this.encryptedID) {
          let ids = this.encryptedID.replace(/[\n;，\s\t]+/g, ',')
          ids = ids.replace(/[^\x00-\x7F]+/g, '')
          let idArray = ids.split(',')
          let validIpArray = idArray.filter((ip) => {
            let ipRegex = /^_[0-9A-Za-z\-_]+/
            return ipRegex.test(ip)
          })
          ids = validIpArray.join(',')
          const { data } = await decodeAlbumIDs('ids=' + ids)
          const headers = ['encode', 'decode']
          const translations = {
            encode: '密文',
            decode: '明文',
          }
          let html =
            '<table style="font-size:0.8em; border-collapse:collapse; margin:auto;text-align: center">'
          html += '<thead><tr>'
          headers.forEach((header) => {
            html += `<th style="border:1px solid #000;">${translations[header]}</th>`
          })
          html += '</tr></thead>'
          html += '<tbody>'
          data.forEach((item) => {
            html += '<tr>'
            headers.forEach((header) => {
              let key = header.toLowerCase().replace(' ', '')
              let value = item[key]
              html += `<td style="border:1px solid #000;padding:10px;">${
                value !== undefined ? value : '-'
              }</td>`
            })
            html += '</tr>'
          })
          html += '</tbody></table>'
          this.$swal({
            title: '<strong>解密完成</strong>',
            icon: 'info',
            html: html,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
            confirmButtonAriaLabel: 'Thumbs up, great!',
          })
        }
      },
      async queryip() {
        if (this.ip2find) {
          const { data } = await getQueryip(this.ip2find)
          this.$swal({
            title: '<strong>查询结果</strong>',
            icon: 'info',
            html: data,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
            confirmButtonAriaLabel: 'Thumbs up, great!',
          })
        }
      },
      async getQueryroute() {
        if (this.uri2find) {
          const { data } = await getNginxroute(this.uri2find)
          this.$swal({
            title: '<strong>路由匹配结果</strong>',
            icon: 'info',
            html: data,
            showCloseButton: true,
            focusConfirm: false,
            confirmButtonText: '<i class="fa fa-thumbs-up"></i> OK',
            confirmButtonAriaLabel: 'Thumbs up, great!',
          })
        }
      },
      async downloadComplainInfo() {
        if (this.ComplaintInfoTime) {
          try {
            const response = await ComplaintInfo({
              t_create_time: this.ComplaintInfoTime,
            })
            const currentDate = new Date()
            const formattedDate =
              currentDate.getFullYear().toString().padStart(4, '0') +
              (currentDate.getMonth() + 1).toString().padStart(2, '0') +
              currentDate.getDate().toString().padStart(2, '0') +
              currentDate.getHours().toString().padStart(2, '0') +
              currentDate.getMinutes().toString().padStart(2, '0') +
              currentDate.getSeconds().toString().padStart(2, '0')
            const filename = `complaint_info_${formattedDate}.csv`
            const blob = new Blob([response], { type: 'text/csv' })
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', `${filename}.csv`)
            document.body.appendChild(link)
            link.click()
            window.URL.revokeObjectURL(url)
            document.body.removeChild(link)
          } catch (err) {}
        }
      },
      async onDetect(promise) {
        try {
          const { content } = await promise
          this.$baseAlert(content, '识别结果')
          const report = { action: 'qrcode', body: content }
          await OpsReport(Base64.encode(JSON.stringify(report)))
          this.error = null
        } catch (error) {
          if (error.name === 'DropImageFetchError') {
            this.error = '不支持跨域图片'
          } else if (error.name === 'DropImageDecodeError') {
            this.error = '图片格式不正确，识别失败'
          } else {
            this.error = '未知错误，' + error.message
          }
        }
      },
      async base64Func() {
        if (this.decodeStr) {
          const res = await aesDecrypt(this.decodeStr)
          if (res) {
            this.encryptedResult = res.encrypt_str
            this.dialogVisible = true
          } else {
            this.encryptedResult = '加密失败'
            this.dialogVisible = true
          }
        }
      },
      onTypeChange() {
        this.queryConfService(this.cnf_type.type, (result) => {
          this.cnf_services.services = result
        })
      },
      async queryConfService(queryString, cb) {
        try {
          let type_str = { queryString }
          let response = await cnfTypeService(type_str)
          cb(response.typeServices)
        } catch (error) {
          this.$baseMessage('获取服务名失败：500', 'failed')
        }
      },
      async confsaveExclude() {
        if (this.cnf_type) {
          const res = await saveExclude(this.cnf_type)
          if (res) {
            this.$baseMessage(res.msg, 'success')
          } else {
            this.$baseMessage(res.msg, 'failed')
          }
        }
      },
      logErrors(promise) {
        promise.catch(console.error)
      },
      onDragOver(isDraggingOver) {
        this.dragover = isDraggingOver
      },
      openJsonVisualization() {
        this.jsonDialogVisible = true
        this.$nextTick(() => {
          if (this.chartInstance && this.chartRendered) {
            this.chartInstance.resize()
          }
        })
      },
      debounce(fn, delay) {
        let timeout
        return function () {
          const context = this
          const args = arguments
          clearTimeout(timeout)
          timeout = setTimeout(() => fn.apply(context, args), delay)
        }
      },
      parseJson() {
        if (!this.jsonInput.trim()) {
          this.showToast('请输入JSON数据')
          return
        }
        try {
          this.jsonData = JSON.parse(this.jsonInput)
          if (this.arrayKeys.length > 0) {
            this.selectedArrayKey = this.arrayKeys[0]
            this.selectedArray = this.jsonData[this.selectedArrayKey]
            this.showToast('JSON解析成功!', 'success')
          } else {
            this.showToast('未发现可用的数据数组')
          }
        } catch (err) {
          this.showToast('JSON解析失败，请检查格式')
          console.error('JSON解析错误:', err)
        }
      },
      clearJson() {
        this.jsonInput = ''
        this.jsonData = null
        this.selectedArrayKey = ''
        this.selectedArray = []
        this.selectedTimeField = ''
        this.selectedValueField = ''
        this.chartRendered = false
        this.chartData = null
        if (this.chartInstance) {
          this.chartInstance.dispose()
          this.chartInstance = null
        }
      },
      objectKeys(obj) {
        return Object.keys(obj)
      },
      numericFields(obj) {
        return Object.keys(obj).filter((key) => typeof obj[key] === 'number')
      },
      prepareChartData() {
        if (!this.selectedTimeField || !this.selectedValueField) {
          this.showToast('请选择时间轴和数值字段')
          return false
        }
        try {
          this.chartData = this.selectedArray
            .map((item) => ({
              time: item[this.selectedTimeField],
              value: item[this.selectedValueField],
            }))
            .sort((a, b) => {
              try {
                return new Date(a.time) - new Date(b.time)
              } catch (e) {
                return String(a.time).localeCompare(String(b.time))
              }
            })
          this.chartTitle = `${this.selectedValueField} 随 ${this.selectedTimeField} 变化趋势`
          return true
        } catch (err) {
          this.showToast('数据处理失败')
          console.error('数据处理错误:', err)
          return false
        }
      },
      renderChart() {
        if (!this.prepareChartData()) {
          return
        }
        this.chartRendered = true
        this.$nextTick(() => {
          try {
            const chartContainer = document.getElementById('chartContainer')
            if (!chartContainer) {
              throw new Error('图表容器DOM不存在')
            }
            if (this.chartInstance) {
              this.chartInstance.dispose()
            }
            this.chartInstance = echarts.init(
              chartContainer,
              this.isDarkMode ? 'dark' : null
            )
            const option = this.createChartOption(this.chartData)
            this.chartInstance.setOption(option)
            chartContainer.classList.add('chart-animated')
            setTimeout(() => {
              chartContainer.classList.remove('chart-animated')
            }, 1000)
            window.addEventListener('resize', this.resizeChart)
          } catch (err) {
            this.chartRendered = false
            this.showToast('图表渲染失败: ' + err.message)
            console.error('图表渲染错误:', err)
          }
        })
      },
      createChartOption(data) {
        const colors = this.isDarkMode
          ? ['#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622']
          : ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
        const option = {
          title: {
            text: this.chartTitle,
            left: 'center',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 16,
            },
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: colors[0],
              },
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: data.map((d) => d.time),
            boundaryGap: this.chartType === 'bar',
            axisLabel: {
              rotate: 45,
              formatter: function (value) {
                const date = new Date(value)
                if (!isNaN(date.getTime())) {
                  return date.toLocaleDateString()
                }
                return value
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          series: [
            {
              name: this.selectedValueField,
              data: data.map((d) => d.value),
              type: this.chartType === 'area' ? 'line' : this.chartType,
              smooth: true,
              itemStyle: {
                color: colors[0],
              },
              areaStyle:
                this.chartType === 'area'
                  ? {
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: this.isDarkMode
                              ? 'rgba(80, 141, 255, 0.8)'
                              : 'rgba(84, 112, 198, 0.8)',
                          },
                          {
                            offset: 1,
                            color: this.isDarkMode
                              ? 'rgba(80, 141, 255, 0.1)'
                              : 'rgba(84, 112, 198, 0.1)',
                          },
                        ],
                      },
                    }
                  : null,
              animationDuration: 1500,
              animationEasing: 'elasticOut',
            },
          ],
          toolbox: {
            feature: {
              saveAsImage: {
                title: '保存为图片',
                name: 'chart_' + new Date().getTime(),
              },
              dataZoom: {
                title: {
                  zoom: '区域缩放',
                  back: '区域缩放还原',
                },
              },
              dataView: {
                title: '数据视图',
                readOnly: true,
                lang: ['数据视图', '关闭', '刷新'],
              },
            },
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100,
            },
            {
              start: 0,
              end: 100,
            },
          ],
        }
        return option
      },
      resizeChart() {
        if (this.chartInstance) {
          this.chartInstance.resize()
        }
      },
      toggleTheme() {
        this.isDarkMode = !this.isDarkMode
        document.body.classList.toggle('dark-theme', this.isDarkMode)
        if (this.chartRendered) {
          this.renderChart()
        }
      },
      showToast(message, type = 'error') {
        this.toast.message = message
        this.toast.show = true
        this.toast.type = type
        setTimeout(() => {
          this.toast.show = false
        }, 3000)
      },
      downloadChart() {
        if (this.chartInstance) {
          const url = this.chartInstance.getDataURL()
          const link = document.createElement('a')
          link.download = `chart_${new Date().getTime()}.png`
          link.href = url
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          this.showToast('图表已下载', 'success')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .index-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 115px;
        }
      }
    }

    .el-autocomplete {
      width: 100%;
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 205px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }

  .drop-area {
    height: 30px;
    color: #434141;
    text-align: center;
    font-weight: bold;
    padding: 10px;
    background-color: rgba(210, 207, 207, 0.5);
  }

  .dragover {
    background-color: rgba(98, 97, 97, 0.8);
  }

  .drop-error {
    color: red;
    font-weight: bold;
  }

  /* JSON 可视化弹窗样式调整 */
  .json-visualization-dialog {
    .el-dialog__body {
      padding: 1rem;
      /* 使用card背景色保证内部内容对比度足够 */
      background-color: var(--card-bg) !important;
      color: var(--text-color) !important;
    }
  }

  /* 基础样式与主题变量 */
  :root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --error-color: #f72585;
    --warning-color: #f8961e;
    --bg-color: #f9f9f9;
    --card-bg: #ffffff;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --input-bg: #ffffff;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --border-radius: 8px;
  }

  .dark-theme {
    --primary-color: #4895ef;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --error-color: #f72585;
    --warning-color: #f8961e;
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --text-color: #f0f0f0;
    --border-color: #333333;
    --input-bg: #2d2d2d;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .data-visualization-dashboard {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: var(--bg-color);
    min-height: 100vh;
    padding: 2rem;
    transition: var(--transition);
  }

  /* 头部样式 */
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .dashboard-header h1 {
    font-weight: 600;
    font-size: 1.8rem;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--secondary-color)
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 3s ease infinite;
    background-size: 200% 200%;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .theme-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
  }

  .theme-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .theme-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* 卡片容器 */
  .dashboard-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  @media (min-width: 992px) {
    .dashboard-container {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    .chart-card {
      grid-column: 1 / -1;
    }
  }

  /* 卡片样式 */
  .card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    transform: translateY(0);
  }

  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .dark-theme .card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  }

  .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
  }

  .card-body {
    padding: 1.5rem;
  }

  /* JSON输入区域 */
  .json-input-card textarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-family: monospace;
    resize: vertical;
    transition: var(--transition);
  }

  .json-input-card textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  }

  .dark-theme .json-input-card textarea:focus {
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.4);
  }

  .button-container {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    justify-content: flex-end;
  }

  /* 按钮样式 */
  .btn {
    padding: 0.6rem 1.25rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.9rem;
  }

  .btn .icon {
    margin-left: 0.5rem;
  }

  .btn.primary {
    background-color: var(--primary-color);
    color: white;
  }

  .btn.primary:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .dark-theme .btn.primary:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .btn.secondary {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .btn.secondary:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .btn.secondary:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .btn-generate {
    margin-top: 1.5rem;
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
  }

  .icon-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    font-size: 1rem;
    border-radius: 50%;
    background-color: transparent;
  }

  .icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .icon-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .card-actions {
    display: flex;
    gap: 0.5rem;
  }

  /* 配置区域样式 */
  .config-section {
    margin-bottom: 1.25rem;
  }

  .config-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
  }

  .select-wrapper {
    position: relative;
    width: 100%;
  }

  .select-wrapper select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-bg);
    color: var(--text-color);
    appearance: none;
    cursor: pointer;
    transition: var(--transition);
  }

  .select-wrapper select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  }

  .dark-theme .select-wrapper select:focus {
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.4);
  }

  .select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--text-color);
    pointer-events: none;
  }

  /* 图表类型选择 */
  .chart-type-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .chart-type-btn {
    flex: 1;
    min-width: 80px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-bg);
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
  }

  .chart-type-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .chart-type-btn:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .chart-type-btn.active {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.1);
  }

  .chart-icon {
    font-size: 1.2rem;
  }

  /* 图表容器 */
  .chart-container {
    width: 100%;
    height: 400px;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
  }

  .chart-animated {
    animation: chartFadeIn 1s ease;
  }

  @keyframes chartFadeIn {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 提示框 */
  .toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    background-color: var(--error-color);
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    opacity: 0;
    transform: translateY(100px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    z-index: 1000;
    pointer-events: none;
  }

  .toast.show {
    opacity: 1;
    transform: translateY(0);
  }

  .toast.success {
    background-color: var(--success-color);
  }

  /* 限制配置卡片宽度 */
  .config-card {
    max-width: 400px;
    margin: 0 auto;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .data-visualization-dashboard {
      padding: 1rem;
    }
    .chart-container {
      height: 300px;
    }
    .btn-generate {
      padding: 0.7rem;
    }
    .dashboard-header h1 {
      font-size: 1.5rem;
    }
  }
</style>
