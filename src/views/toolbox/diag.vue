<template>
  <div class="diagnosis-analyzer">
    <!-- 头部区域 -->
    <header>
      <div class="header-content">
        <h1>网络诊断结果分析</h1>
        <p>输入诊断ID查看详细分析报告</p>
      </div>
    </header>

    <!-- 搜索区域 -->
    <div class="search-container">
      <div class="search-box">
        <input
          v-model="diagnosisId"
          placeholder="请输入诊断ID (例如: Diag-1234567890-1234)"
          class="search-input"
        />
        <button
          class="search-button"
          :disabled="loading"
          @click="fetchDiagnosisResult"
        >
          <span v-if="loading">
            <i class="fas fa-spinner fa-spin"></i>
          </span>
          <span v-else>
            <i class="fas fa-search"></i>
            查询
          </span>
        </button>
      </div>
    </div>

    <!-- 加载中提示 -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>正在加载诊断结果...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
    </div>

    <!-- 主要结果区域 -->
    <div v-if="diagnosisResult && !loading" class="results-container">
      <!-- 概览卡片 -->
      <div class="overview-card">
        <div class="overview-header">
          <h2>诊断概览</h2>
          <span class="diagnosis-id">ID: {{ diagnosisId }}</span>
        </div>
        <div class="overview-stats">
          <div class="stat-item" :class="getHealthClass(overallHealth.score)">
            <div class="stat-value">{{ overallHealth.score }}%</div>
            <div class="stat-label">总体健康度</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ successCount }}</div>
            <div class="stat-label">成功项</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ failureCount }}</div>
            <div class="stat-label">失败项</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ diagnosisTime }}</div>
            <div class="stat-label">检测时间</div>
          </div>
        </div>
        <div class="overall-conclusion">
          <h3>总体结论</h3>
          <p>{{ overallHealth.conclusion }}</p>
        </div>
      </div>

      <!-- 各个步骤详情 -->
      <div class="steps-container">
        <h2>详细诊断报告</h2>

        <!-- 终端信息 -->
        <div class="step-card">
          <div class="step-header">
            <h3>
              <i class="fas fa-laptop"></i>
              终端信息收集
              <span class="result-badge" :class="getStepHealthClass('step1')">
                {{ getStepHealth('step1') }}
              </span>
            </h3>
          </div>
          <div class="step-content">
            <div v-if="terminalInfo.ip" class="detail-item">
              <div class="detail-label">IP信息:</div>
              <div class="detail-value">
                {{ terminalInfo.ip }}
                <span
                  v-if="ipLocations[terminalInfo.ip]"
                  class="ip-location"
                  :title="formatLocationInfo(ipLocations[terminalInfo.ip])"
                >
                  ({{ ipLocations[terminalInfo.ip].province }}
                  {{ ipLocations[terminalInfo.ip].city }}
                  {{ ipLocations[terminalInfo.ip].isp }})
                </span>
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(terminalInfo.ipScore)"
              >
                {{ terminalInfo.ipConclusion }}
              </div>
            </div>

            <div class="detail-item browser-info">
              <div class="detail-label">浏览器信息:</div>
              <div class="detail-value">
                {{ terminalInfo.browser }} {{ terminalInfo.browserVersion }}
                <span v-if="terminalInfo.mobile" class="device-tag">
                  移动设备
                </span>
                <span v-else class="device-tag">桌面设备</span>
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(terminalInfo.browserScore)"
              >
                {{ terminalInfo.browserConclusion }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">操作系统:</div>
              <div class="detail-value">
                {{ terminalInfo.os }} {{ terminalInfo.osVersion }}
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(terminalInfo.osScore)"
              >
                {{ terminalInfo.osConclusion }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">设备信息:</div>
              <div class="detail-value">
                <div>屏幕分辨率: {{ terminalInfo.screen }}</div>
                <div>CPU核心数: {{ terminalInfo.cpuCores }}</div>
                <div>设备内存: {{ terminalInfo.memory }}</div>
                <div>时区: {{ terminalInfo.timeZone }}</div>
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(terminalInfo.deviceScore)"
              >
                {{ terminalInfo.deviceConclusion }}
              </div>
            </div>
          </div>
        </div>

        <!-- CDN信息 -->
        <div class="step-card">
          <div class="step-header">
            <h3>
              <i class="fas fa-cloud"></i>
              CDN信息收集
              <span class="result-badge" :class="getStepHealthClass('step2')">
                {{ getStepHealth('step2') }}
              </span>
            </h3>
          </div>
          <div class="step-content">
            <!-- IP比较检测 -->
            <div v-if="ipComparison.show" class="detail-item">
              <div class="detail-label">IP一致性检测:</div>
              <div class="detail-value">
                <div>
                  连接主站的IP: {{ ipComparison.userIp }}
                  <span
                    v-if="ipLocations[ipComparison.userIp]"
                    class="ip-location"
                    :title="
                      formatLocationInfo(ipLocations[ipComparison.userIp])
                    "
                  >
                    ({{ ipLocations[ipComparison.userIp].province }}
                    {{ ipLocations[ipComparison.userIp].city }}
                    {{ ipLocations[ipComparison.userIp].isp }}))
                  </span>
                </div>
                <div>
                  连接CDN的IP: {{ ipComparison.cdnclientIp }}
                  <span
                    v-if="ipLocations[ipComparison.cdnclientIp]"
                    class="ip-location"
                    :title="
                      formatLocationInfo(ipLocations[ipComparison.cdnclientIp])
                    "
                  >
                    ({{ ipLocations[ipComparison.cdnclientIp].province }}
                    {{ ipLocations[ipComparison.cdnclientIp].city }}
                    {{ ipLocations[ipComparison.cdnclientIp].isp }}))
                  </span>
                </div>
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(ipComparison.score)"
              >
                {{ ipComparison.conclusion }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">CDN供应商:</div>
              <div class="detail-value">
                {{ cdnInfo.provider || '获取失败' }}
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(cdnInfo.providerScore)"
              >
                {{ cdnInfo.providerConclusion }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">CDN节点:</div>
              <div class="detail-value">
                {{ cdnInfo.node || '获取失败' }}
                <span
                  v-if="ipLocations[cdnInfo.node]"
                  class="ip-location"
                  :title="formatLocationInfo(ipLocations[cdnInfo.node])"
                >
                  ({{ ipLocations[cdnInfo.node].province }}
                  {{ ipLocations[cdnInfo.node].city }}
                  {{ ipLocations[cdnInfo.node].isp }})
                </span>
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(cdnInfo.nodeScore)"
              >
                {{ cdnInfo.nodeConclusion }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">视频下载速度测试:</div>
              <div v-if="cdnInfo.speedDetails" class="detail-value speed-test">
                <div class="speed-item">
                  <span class="speed-label">总耗时:</span>
                  <span class="speed-value">
                    {{ cdnInfo.speedDetails.totalTime }}
                  </span>
                </div>
                <div class="speed-item">
                  <span class="speed-label">下载速度:</span>
                  <span class="speed-value">
                    {{ cdnInfo.speedDetails.speed }}
                  </span>
                </div>
                <div class="speed-item">
                  <span class="speed-label">DNS查询时间:</span>
                  <span class="speed-value">
                    {{ cdnInfo.speedDetails.dnsTime }}
                  </span>
                </div>
                <div class="speed-item">
                  <span class="speed-label">TCP建联时间:</span>
                  <span class="speed-value">
                    {{ cdnInfo.speedDetails.tcpTime }}
                  </span>
                </div>
                <div class="speed-item">
                  <span class="speed-label">首字节时间:</span>
                  <span class="speed-value">
                    {{ cdnInfo.speedDetails.ttfbTime }}
                  </span>
                </div>
              </div>
              <div v-else class="detail-value">测试失败</div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(cdnInfo.speedScore)"
              >
                {{ cdnInfo.speedConclusion }}
              </div>
            </div>

            <div class="detail-item">
              <div class="detail-label">静态资源下载测试:</div>
              <div v-if="cdnInfo.staticDetails" class="detail-value speed-test">
                <div class="speed-item">
                  <span class="speed-label">总耗时:</span>
                  <span class="speed-value">
                    {{ cdnInfo.staticDetails.totalTime }}
                  </span>
                </div>
                <div class="speed-item">
                  <span class="speed-label">下载速度:</span>
                  <span class="speed-value">
                    {{ cdnInfo.staticDetails.speed }}
                  </span>
                </div>
              </div>
              <div v-else class="detail-value">测试失败</div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(cdnInfo.staticScore)"
              >
                {{ cdnInfo.staticConclusion }}
              </div>
            </div>
          </div>
        </div>

        <!-- 其他检测 -->
        <div class="step-card">
          <div class="step-header">
            <h3>
              <i class="fas fa-cogs"></i>
              其他检测
              <span class="result-badge" :class="getStepHealthClass('step3')">
                {{ getStepHealth('step3') }}
              </span>
            </h3>
          </div>
          <div class="step-content">
            <div class="detail-item">
              <div class="detail-label">图片格式支持:</div>
              <div class="detail-value format-support">
                <div class="format-item">
                  <span class="format-name">WebP:</span>
                  <span
                    class="format-status"
                    :class="otherTests.webp ? 'supported' : 'not-supported'"
                  >
                    {{ otherTests.webp ? '支持' : '不支持' }}
                  </span>
                </div>
                <div class="format-item">
                  <span class="format-name">AVIF:</span>
                  <span
                    class="format-status"
                    :class="otherTests.avif ? 'supported' : 'not-supported'"
                  >
                    {{ otherTests.avif ? '支持' : '不支持' }}
                  </span>
                </div>
              </div>
              <div
                class="detail-conclusion"
                :class="getHealthClass(otherTests.formatScore)"
              >
                {{ otherTests.formatConclusion }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 建议和解决方案区域 -->
      <div v-if="recommendations.length > 0" class="recommendations-card">
        <h2>建议和解决方案</h2>
        <ul class="recommendations-list">
          <li v-for="(rec, index) in recommendations" :key="index">
            <i :class="rec.icon"></i>
            <div class="recommendation-content">
              <h4>{{ rec.title }}</h4>
              <p>{{ rec.content }}</p>
            </div>
          </li>
        </ul>
      </div>

      <!-- 原始JSON数据展示 -->
      <div class="raw-data-container">
        <div class="raw-data-header" @click="toggleRawData">
          <h3>原始诊断数据</h3>
          <i
            :class="showRawData ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"
          ></i>
        </div>
        <div v-if="showRawData" class="raw-data">
          <pre>{{ JSON.stringify(rawDiagnosisData, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getIPsCity, getDiagData } from '@/api/toolbox'

  export default {
    name: 'DiagnosisAnalyzer',
    data() {
      return {
        diagnosisId: '',
        loading: false,
        error: '',
        rawDiagnosisData: null,
        diagnosisResult: null,
        showRawData: false,

        // 新增IP位置信息存储
        ipLocations: {},

        // 格式化的诊断结果
        terminalInfo: {
          ip: '',
          ipScore: 100,
          ipConclusion: '出口IP正常',

          browser: '',
          browserVersion: '',
          mobile: false,
          browserScore: 100,
          browserConclusion: '浏览器正常',

          os: '',
          osVersion: '',
          osScore: 100,
          osConclusion: '操作系统正常',

          screen: '',
          cpuCores: '',
          memory: '',
          timeZone: '',
          deviceScore: 100,
          deviceConclusion: '设备配置正常',
        },

        // IP比较检测
        ipComparison: {
          show: false,
          cdnclientIp: '',
          userIp: '',
          match: true,
          score: 100,
          conclusion: 'IP一致性正常',
        },

        cdnInfo: {
          provider: '',
          providerScore: 0,
          providerConclusion: '无法获取CDN供应商信息',

          node: '',
          nodeScore: 0,
          nodeConclusion: '无法获取CDN节点信息',

          speedDetails: null,
          speedScore: 0,
          speedConclusion: '网络速度测试失败',

          staticDetails: null,
          staticScore: 0,
          staticConclusion: '资源下载测试失败',
        },

        otherTests: {
          webp: false,
          avif: false,
          formatScore: 50,
          formatConclusion: '部分图片格式不受支持',
        },

        recommendations: [],
      }
    },

    computed: {
      successCount() {
        if (!this.diagnosisResult) return 0
        return this.diagnosisResult.filter((item) => item.success).length
      },

      failureCount() {
        if (!this.diagnosisResult) return 0
        return this.diagnosisResult.filter((item) => !item.success).length
      },

      diagnosisTime() {
        // 模拟从ID中提取时间戳，实际可能需要从服务器获取或从其他地方计算
        if (!this.diagnosisId) return '未知'
        const parts = this.diagnosisId.split('-')
        if (parts.length >= 2) {
          const timestamp = parseInt(parts[1], 10)
          if (!isNaN(timestamp)) {
            return new Date(timestamp).toLocaleString()
          }
        }
        return '未知'
      },

      overallHealth() {
        if (!this.diagnosisResult) {
          return { score: 0, conclusion: '未获取诊断结果' }
        }

        // 计算总体健康度得分 (0-100)
        const total = this.successCount + this.failureCount
        const score = Math.round((this.successCount / total) * 100)

        // 根据得分生成总结论
        let conclusion = ''
        if (score >= 90) {
          conclusion =
            '网络状况良好，各项指标正常，可以正常访问网站和使用服务。'
        } else if (score >= 70) {
          conclusion =
            '网络状况基本正常，部分指标有轻微异常，但不影响主要功能使用。'
        } else if (score >= 50) {
          conclusion =
            '网络状况存在一定问题，可能会导致某些功能无法正常使用，建议查看具体失败项并采取相应措施。'
        } else {
          conclusion =
            '网络状况严重异常，多数功能可能无法正常使用，请查看详细报告并联系网络管理员或客服人员解决。'
        }

        return { score, conclusion }
      },
    },

    methods: {
      async fetchDiagnosisResult() {
        if (!this.diagnosisId) {
          this.error = '请输入有效的诊断ID'
          return
        }

        this.loading = true
        this.error = ''

        try {
          // 调用真实API接口
          const { data } = await getDiagData(this.diagnosisId)
          const responseData = data

          if (!data) {
            throw new Error(responseData.error || '未找到诊断记录')
          }

          // 保存原始数据
          this.rawDiagnosisData = data

          // 获取诊断详情
          const diagnosisDetails = data.details

          // 去除重复项
          this.diagnosisResult = this.removeDuplicates(diagnosisDetails)

          // 分析诊断结果
          this.analyzeDiagnosisResult()

          // 获取IP位置信息
          await this.fetchIPLocations()

          // 生成建议
          this.generateRecommendations()
        } catch (err) {
          this.error = `获取诊断结果失败: ${err.message}`
          this.diagnosisResult = null
        } finally {
          this.loading = false
        }
      },

      // 新增方法：获取IP位置信息
      async fetchIPLocations() {
        // 收集所有需要查询的IP
        const ips = []

        // 添加终端IP
        if (this.terminalInfo.ip && this.terminalInfo.ip !== '获取失败') {
          ips.push(this.terminalInfo.ip)
        }

        // 添加IP比较中的IP
        if (this.ipComparison.show) {
          if (this.ipComparison.userIp) ips.push(this.ipComparison.userIp)
          if (this.ipComparison.cdnclientIp)
            ips.push(this.ipComparison.cdnclientIp)
        }

        // 添加CDN节点IP
        if (this.cdnInfo.node && this.cdnInfo.node !== '获取失败') {
          ips.push(this.cdnInfo.node)
        }

        // 去重
        const uniqueIps = [...new Set(ips)]

        // 如果有IP需要查询
        if (uniqueIps.length > 0) {
          try {
            // 用逗号连接IP
            const ipsParam = uniqueIps.join(',')
            // 根据正确的调用方式调用API获取IP位置信息
            const { data } = await getIPsCity('ips1=' + ipsParam)
            console.log(data)
            if (data) {
              // 创建IP到位置信息的映射
              data.forEach((location) => {
                this.ipLocations[location.ip] = location
              })
            }
          } catch (error) {
            console.error('获取IP位置信息失败:', error)
          }
        }
      },

      // 格式化位置信息以便显示在tooltip中
      formatLocationInfo(location) {
        if (!location) return ''

        return `国家/地区: ${location.country || '未知'}
省份: ${location.province || '未知'}
城市: ${location.city || '未知'}
ISP: ${location.isp || '未知'}
所有者: ${location.owner || '未知'}
经纬度: ${location.lngwgs || '未知'}, ${location.latwgs || '未知'}
时区: ${location.timezone || '未知'}
AS号: ${location.asnumber || '未知'}`
      },

      // 去除重复的诊断项
      removeDuplicates(data) {
        const seen = new Set()
        return data.filter((item) => {
          const key = `${item.stepId}-${item.subStepId}`
          if (seen.has(key)) return false
          seen.add(key)
          return true
        })
      },

      // 分析诊断结果，填充各个部分的数据
      analyzeDiagnosisResult() {
        if (!this.diagnosisResult) return

        // 首先提取IP信息，用于后续比较
        const terminalIp = this.getTerminalIp()
        const cdnIp = this.getCdnIp()
        const cdnClientIp = this.getCdnClientIp()

        this.diagnosisResult.forEach((item) => {
          if (item.stepId === 'step1') {
            // 终端信息收集
            this.analyzeTerminalInfo(item)
          } else if (item.stepId === 'step2') {
            // CDN信息收集
            this.analyzeCdnInfo(item)
          } else if (item.stepId === 'step3') {
            // 其他检测
            this.analyzeOtherTests(item)
          }
        })

        // 在所有信息都分析完后，比较IP一致性
        this.compareIps(terminalIp, cdnClientIp)
      },

      // 获取终端IP
      getTerminalIp() {
        const ipItem = this.diagnosisResult.find(
          (item) =>
            item.stepId === 'step1' &&
            item.subStepId === 'step1-1' &&
            item.success
        )

        if (ipItem && ipItem.result && ipItem.result.status === 'success') {
          return ipItem.result.ip
        }
        return null
      },

      // 获取CDN探测IP
      getCdnIp() {
        const cdnItem = this.diagnosisResult.find(
          (item) =>
            item.stepId === 'step2' &&
            item.subStepId === 'step2-2' &&
            item.success
        )

        if (cdnItem && cdnItem.result && cdnItem.result.status === 'success') {
          return cdnItem.result.ip
        }
        return null
      },

      getCdnClientIp() {
        const cdnItem = this.diagnosisResult.find(
          (item) =>
            item.stepId === 'step2' &&
            item.subStepId === 'step2-2' &&
            item.success
        )

        if (cdnItem && cdnItem.result && cdnItem.result.status === 'success') {
          return cdnItem.result.eoClientIp
        }
        return null
      },

      // 比较IP一致性
      compareIps(terminalIp, cdnClientIp) {
        if (terminalIp && cdnClientIp) {
          this.ipComparison.show = true
          this.ipComparison.userIp = terminalIp
          this.ipComparison.cdnclientIp = cdnClientIp

          if (terminalIp === cdnClientIp) {
            this.ipComparison.match = true
            this.ipComparison.score = 100
            this.ipComparison.conclusion = 'IP一致性正常，网络连接直接'
          } else {
            this.ipComparison.match = false
            this.ipComparison.score = 60
            this.ipComparison.conclusion =
              '检测到IP不一致，您访问CDN时可能存在代理或VPN'

            // 添加到建议中
            this.recommendations.push({
              icon: 'fas fa-shield-alt',
              title: '检测到网络代理',
              content:
                '您的出口IP与CDN探测到的IP不一致，表明您可能正在使用代理或VPN。这可能导致网络访问速度下降或连接不稳定，建议尝试直接连接网络。',
            })
          }
        }
      },

      // 分析终端信息
      analyzeTerminalInfo(item) {
        if (item.subStepId === 'step1-1' && item.success) {
          // IP信息
          this.terminalInfo.ip =
            item.result.status === 'success' ? item.result.ip : '获取失败'

          // 这里可以添加IP相关的判断逻辑
          // 例如: 检查IP是否在特定区域、是否是已知的问题IP等
          if (this.terminalInfo.ip === '获取失败') {
            this.terminalInfo.ipScore = 0
            this.terminalInfo.ipConclusion =
              '无法获取出口IP，可能存在网络连接问题'
          } else {
            this.terminalInfo.ipScore = 100
            this.terminalInfo.ipConclusion = '出口IP正常'
          }
        } else if (item.subStepId === 'step1-2' && item.success) {
          // 浏览器信息
          const result = item.result
          if (result.status === 'success') {
            this.terminalInfo.browser = result.browser
            this.terminalInfo.browserVersion = result.browserVersion
            this.terminalInfo.mobile = result.mobile
            this.terminalInfo.screen = result.screen
            this.terminalInfo.os = result.os
            this.terminalInfo.osVersion = result.osVersion

            // 浏览器兼容性检查逻辑可以在这里添加
            // 例如: 检查浏览器是否过旧、是否存在已知兼容性问题等
            if (result.browser === 'Internet Explorer') {
              this.terminalInfo.browserScore = 50
              this.terminalInfo.browserConclusion =
                'Internet Explorer可能存在兼容性问题，建议使用Chrome或Edge'
            } else {
              this.terminalInfo.browserScore = 100
              this.terminalInfo.browserConclusion = '浏览器正常，兼容性良好'
            }

            // 操作系统检查逻辑
            if (
              result.os === 'Windows' &&
              result.osVersion &&
              parseFloat(result.osVersion) < 6.1
            ) {
              this.terminalInfo.osScore = 70
              this.terminalInfo.osConclusion =
                '操作系统版本较旧，可能影响部分功能'
            } else {
              this.terminalInfo.osScore = 100
              this.terminalInfo.osConclusion = '操作系统正常'
            }
          }
        } else if (item.subStepId === 'step1-3' && item.success) {
          // 设备信息
          this.terminalInfo.cpuCores = item.result.cpuCores
          this.terminalInfo.memory = item.result.memory
          this.terminalInfo.timeZone = item.result.timeZone

          // 设备性能检查逻辑
          const cpuCoresNum = parseInt(item.result.cpuCores, 10)
          if (!isNaN(cpuCoresNum) && cpuCoresNum < 2) {
            this.terminalInfo.deviceScore = 70
            this.terminalInfo.deviceConclusion =
              '设备性能较弱，可能导致体验下降'
          } else {
            this.terminalInfo.deviceScore = 100
            this.terminalInfo.deviceConclusion = '设备配置正常'
          }
        }
      },

      // 分析CDN信息
      analyzeCdnInfo(item) {
        if (item.subStepId === 'step2-1') {
          // CDN供应商
          if (item.success && item.result.status === 'success') {
            this.cdnInfo.provider = item.result.cdnProvider
            this.cdnInfo.providerScore = 100
            this.cdnInfo.providerConclusion = 'CDN供应商正常'
          } else {
            this.cdnInfo.provider = '获取失败'
            this.cdnInfo.providerScore = 0
            this.cdnInfo.providerConclusion =
              '无法获取CDN供应商信息，可能存在网络连接问题'
          }
        } else if (item.subStepId === 'step2-2') {
          // CDN节点
          if (item.success && item.result.status === 'success') {
            this.cdnInfo.node = item.result.ip || '未知'
            this.cdnInfo.nodeScore = 100
            this.cdnInfo.nodeConclusion = 'CDN节点连接正常'
          } else {
            this.cdnInfo.node = '获取失败'
            this.cdnInfo.nodeScore = 0
            this.cdnInfo.nodeConclusion =
              '无法获取CDN节点信息，可能存在网络连接问题'
          }
        } else if (item.subStepId === 'step2-3') {
          // 网络速度测试
          if (item.success && item.result.status === 'success') {
            this.cdnInfo.speedDetails = item.result.details

            // 根据下载速度判断网络质量
            const speedMatch =
              this.cdnInfo.speedDetails.speed.match(/(\d+\.\d+)/)
            const speedValue = speedMatch ? parseFloat(speedMatch[1]) : 0

            if (speedValue >= 10) {
              this.cdnInfo.speedScore = 100
              this.cdnInfo.speedConclusion =
                '网络速度良好，满足高清视频和大文件下载需求'
            } else if (speedValue >= 5) {
              this.cdnInfo.speedScore = 80
              this.cdnInfo.speedConclusion = '网络速度正常，可流畅观看视频'
            } else if (speedValue >= 2) {
              this.cdnInfo.speedScore = 60
              this.cdnInfo.speedConclusion =
                '网络速度较慢，可能影响视频播放和大文件下载'
            } else {
              this.cdnInfo.speedScore = 30
              this.cdnInfo.speedConclusion =
                '网络速度非常慢，可能导致加载时间过长'
            }
          } else {
            this.cdnInfo.speedDetails = null
            this.cdnInfo.speedScore = 0
            this.cdnInfo.speedConclusion =
              '网络速度测试失败，可能存在网络连接问题'
          }
        } else if (item.subStepId === 'step2-4') {
          // 资源下载测试
          if (item.success && item.result.status === 'success') {
            this.cdnInfo.staticDetails = item.result.details

            // 根据资源下载时间判断
            const timeMatch =
              this.cdnInfo.staticDetails.totalTime.match(/(\d+\.\d+)/)
            const timeValue = timeMatch ? parseFloat(timeMatch[1]) : 9999

            if (timeValue < 500) {
              this.cdnInfo.staticScore = 100
              this.cdnInfo.staticConclusion = '资源加载速度极快，用户体验优秀'
            } else if (timeValue < 1000) {
              this.cdnInfo.staticScore = 90
              this.cdnInfo.staticConclusion = '资源加载速度快，用户体验良好'
            } else if (timeValue < 2000) {
              this.cdnInfo.staticScore = 70
              this.cdnInfo.staticConclusion = '资源加载速度一般，用户体验可接受'
            } else {
              this.cdnInfo.staticScore = 50
              this.cdnInfo.staticConclusion = '资源加载速度慢，可能影响用户体验'
            }
          } else {
            this.cdnInfo.staticDetails = null
            this.cdnInfo.staticScore = 0
            this.cdnInfo.staticConclusion =
              '资源下载测试失败，可能存在网络连接问题'
          }
        }
      },

      // 分析其他测试
      analyzeOtherTests(item) {
        if (item.subStepId === 'step3-1' && item.success) {
          // 图片格式支持
          if (item.result.status === 'success') {
            this.otherTests.webp = item.result.webp
            this.otherTests.avif = item.result.avif

            // 根据支持的格式计算得分和结论
            if (this.otherTests.webp && this.otherTests.avif) {
              this.otherTests.formatScore = 100
              this.otherTests.formatConclusion =
                '支持所有现代图片格式，可获得最佳图片加载体验'
            } else if (this.otherTests.webp) {
              this.otherTests.formatScore = 80
              this.otherTests.formatConclusion =
                '支持WebP但不支持AVIF，图片加载体验良好'
            } else {
              this.otherTests.formatScore = 50
              this.otherTests.formatConclusion =
                '不支持现代图片格式，图片加载可能较慢'
            }
          }
        }
      },

      // 生成建议
      generateRecommendations() {
        this.recommendations = []

        // 根据各项检测结果生成建议
        // 浏览器建议
        if (this.terminalInfo.browserScore < 100) {
          this.recommendations.push({
            icon: 'fas fa-globe',
            title: '更新浏览器',
            content: `建议使用最新版本的Chrome、Edge或Firefox浏览器，以获得最佳体验。当前使用的是${this.terminalInfo.browser} ${this.terminalInfo.browserVersion}。`,
          })
        }

        // 网络速度建议
        if (this.cdnInfo.speedScore < 60) {
          this.recommendations.push({
            icon: 'fas fa-tachometer-alt',
            title: '改善网络连接',
            content:
              '您的网络速度较慢，建议尝试连接更稳定的网络，或联系您的网络服务提供商。',
          })
        }

        // CDN连接问题建议
        if (this.cdnInfo.providerScore === 0 || this.cdnInfo.nodeScore === 0) {
          this.recommendations.push({
            icon: 'fas fa-cloud',
            title: '检查CDN连接',
            content:
              '无法连接到内容分发网络，可能是由于网络限制或代理设置。建议检查您的网络连接和防火墙设置。',
          })
        }

        // 图片格式支持建议
        if (!this.otherTests.webp) {
          this.recommendations.push({
            icon: 'fas fa-image',
            title: '更新浏览器以支持现代图片格式',
            content:
              '您的浏览器不支持WebP图片格式，可能导致图片加载较慢。建议更新到最新版本的Chrome或Edge浏览器。',
          })
        }

        // 检查IP位置信息，如果有不同地区的IP，加入建议
        if (
          this.ipComparison.show &&
          this.ipLocations[this.ipComparison.userIp] &&
          this.ipLocations[this.ipComparison.cdnclientIp]
        ) {
          const userProvince =
            this.ipLocations[this.ipComparison.userIp].province
          const cdnProvince =
            this.ipLocations[this.ipComparison.cdnclientIp].province

          if (userProvince !== cdnProvince) {
            this.recommendations.push({
              icon: 'fas fa-map-marker-alt',
              title: 'IP地区不一致',
              content: `您的IP（${userProvince}）与CDN探测到的IP（${cdnProvince}）属于不同地区，这可能表明您正在使用跨地区的代理或VPN，可能导致访问延迟增加。`,
            })
          }
        }
      },

      // 获取步骤健康状态
      getStepHealth(stepId) {
        if (!this.diagnosisResult) return '未检测'

        const stepItems = this.diagnosisResult.filter(
          (item) => item.stepId === stepId
        )
        const successCount = stepItems.filter((item) => item.success).length
        const total = stepItems.length

        if (successCount === total) return '全部正常'
        if (successCount === 0) return '全部异常'
        return `部分异常 (${successCount}/${total})`
      },

      // 获取步骤健康状态的CSS类
      getStepHealthClass(stepId) {
        const health = this.getStepHealth(stepId)
        if (health === '全部正常') return 'success'
        if (health === '全部异常') return 'error'
        return 'warning'
      },

      // 根据得分获取健康状态CSS类
      getHealthClass(score) {
        if (score >= 80) return 'success'
        if (score >= 60) return 'warning'
        return 'error'
      },

      // 切换显示原始数据
      toggleRawData() {
        this.showRawData = !this.showRawData
      },
    },
  }
</script>

<style>
  /* 现有样式保持不变 */
  @import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css');

  :root {
    --primary-color: #4a6fa5;
    --secondary-color: #f5f7fa;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #dc3545;
    --text-color: #333;
    --light-text: #666;
    --card-bg: #fff;
    --border-color: #e0e0e0;
    --header-bg: linear-gradient(135deg, #4a6fa5, #6983b3);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--secondary-color);
  }

  .diagnosis-analyzer {
    max-width: 1200px;
    margin: 0 auto;
  }

  /* 头部样式 */
  header {
    background: var(--header-bg);
    color: white;
    padding: 2rem 0;
    text-align: center;
    border-radius: 0 0 10px 10px;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
  }

  .header-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  header h1 {
    margin: 0;
    font-size: 2.2rem;
    font-weight: 700;
  }

  header p {
    margin-top: 0.5rem;
    font-size: 1.1rem;
    opacity: 0.9;
  }

  /* 搜索区域样式 */
  .search-container {
    max-width: 800px;
    margin: 0 auto 2rem;
    padding: 0 1rem;
  }

  .search-box {
    display: flex;
    gap: 10px;
    background: white;
    border-radius: 8px;
    padding: 10px;
    box-shadow: var(--shadow);
  }

  .search-input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
  }

  .search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 111, 165, 0.2);
  }

  .search-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0 20px;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
  }

  .search-button:hover {
    background-color: #395781;
  }

  .search-button:disabled {
    background-color: #b0b0b0;
    cursor: not-allowed;
  }

  /* 加载和错误提示样式 */
  .loading-container,
  .error-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    text-align: center;
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
  }

  .loading-container p,
  .error-container p {
    margin-top: 1rem;
    font-size: 1.1rem;
  }

  .spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(74, 111, 165, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .error-container {
    color: var(--error-color);
  }

  .error-container i {
    font-size: 2rem;
  }

  /* 结果容器样式 */
  .results-container {
    max-width: 800px;
    margin: 0 auto 3rem;
    padding: 0 1rem;
  }

  /* 概览卡片样式 */
  .overview-card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .overview-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
  }

  .diagnosis-id {
    font-size: 0.9rem;
    color: var(--light-text);
    background: var(--secondary-color);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
  }

  .overview-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .stat-item {
    text-align: center;
    padding: 1rem;
    background: var(--secondary-color);
    border-radius: 8px;
    transition: var(--transition);
  }

  .stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .stat-item.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
  }

  .stat-item.warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
  }

  .stat-item.error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
  }

  .stat-value {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
  }

  .stat-label {
    font-size: 0.9rem;
    color: var(--light-text);
  }

  .overall-conclusion {
    background: var(--secondary-color);
    padding: 1rem;
    border-radius: 8px;
  }

  .overall-conclusion h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
  }

  .overall-conclusion p {
    color: var(--text-color);
    line-height: 1.5;
  }

  /* 步骤卡片样式 */
  .steps-container {
    margin-bottom: 2rem;
  }

  .steps-container > h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--primary-color);
  }

  .step-card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
  }

  .step-header {
    padding: 1rem 1.5rem;
    background: #f0f4f9;
  }

  .step-header h3 {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    color: var(--primary-color);
  }

  .step-header h3 i {
    margin-right: 0.8rem;
  }

  .result-badge {
    margin-left: auto;
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.3rem 0.6rem;
    border-radius: 20px;
    background: var(--secondary-color);
  }

  .result-badge.success {
    background: var(--success-color);
    color: white;
  }

  .result-badge.warning {
    background: var(--warning-color);
    color: #333;
  }

  .result-badge.error {
    background: var(--error-color);
    color: white;
  }

  .step-content {
    padding: 1.5rem;
  }

  .detail-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
  }

  .detail-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .detail-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
  }

  .detail-value {
    margin-bottom: 0.8rem;
    line-height: 1.6;
  }

  .browser-info .device-tag {
    display: inline-block;
    font-size: 0.75rem;
    padding: 0.15rem 0.4rem;
    margin-left: 0.5rem;
    border-radius: 4px;
    background: #e0e0e0;
    color: #333;
  }

  .speed-test {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 0.8rem;
  }

  .speed-item {
    background: var(--secondary-color);
    padding: 0.5rem 0.8rem;
    border-radius: 6px;
  }

  .speed-label {
    font-size: 0.85rem;
    color: var(--light-text);
  }

  .speed-value {
    font-weight: 600;
  }

  .format-support {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.8rem;
  }

  .format-item {
    display: flex;
    align-items: center;
    background: var(--secondary-color);
    padding: 0.5rem 0.8rem;
    border-radius: 6px;
  }

  .format-name {
    font-weight: 600;
    margin-right: 0.5rem;
  }

  .format-status {
    padding: 0.15rem 0.4rem;
    border-radius: 4px;
    font-size: 0.75rem;
  }

  .format-status.supported {
    background: var(--success-color);
    color: white;
  }

  .format-status.not-supported {
    background: var(--error-color);
    color: white;
  }

  .detail-conclusion {
    font-weight: 500;
    padding: 0.5rem 0.8rem;
    border-radius: 6px;
    line-height: 1.5;
  }

  .detail-conclusion.success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
  }

  .detail-conclusion.warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
  }

  .detail-conclusion.error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--error-color);
  }

  /* 建议卡片样式 */
  .recommendations-card {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .recommendations-card h2 {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--primary-color);
  }

  .recommendations-list {
    list-style: none;
  }

  .recommendations-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.2rem;
    padding-bottom: 1.2rem;
    border-bottom: 1px solid var(--border-color);
  }

  .recommendations-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .recommendations-list li i {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-right: 1rem;
    margin-top: 0.2rem;
  }

  .recommendation-content {
    flex: 1;
  }

  .recommendation-content h4 {
    font-weight: 600;
    margin-bottom: 0.3rem;
    color: var(--text-color);
  }

  .recommendation-content p {
    color: var(--light-text);
    line-height: 1.5;
  }

  /* 原始数据显示样式 */
  .raw-data-container {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    overflow: hidden;
  }

  .raw-data-header {
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f0f4f9;
    cursor: pointer;
    transition: var(--transition);
  }

  .raw-data-header:hover {
    background: #e0e7f0;
  }

  .raw-data-header h3 {
    font-size: 1.2rem;
    color: var(--primary-color);
  }

  .raw-data {
    padding: 1.5rem;
    overflow-x: auto;
  }

  .raw-data pre {
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    white-space: pre-wrap;
  }

  /* 新增IP位置信息样式 */
  .ip-location {
    display: inline-block;
    margin-left: 5px;
    font-size: 0.9rem;
    color: #666;
    background: #f0f4f9;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    cursor: help;
    transition: var(--transition);
  }

  .ip-location:hover {
    background: #e0e7f0;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .overview-stats {
      grid-template-columns: repeat(2, 1fr);
    }

    .speed-test,
    .format-support {
      grid-template-columns: 1fr;
    }

    .step-header h3 {
      flex-direction: column;
      align-items: flex-start;
    }

    .result-badge {
      margin-left: 0;
      margin-top: 0.5rem;
    }
  }

  @media (max-width: 480px) {
    .overview-stats {
      grid-template-columns: 1fr;
    }

    .search-box {
      flex-direction: column;
    }

    .search-button {
      width: 100%;
      padding: 12px;
    }

    .recommendations-list li {
      flex-direction: column;
    }

    .recommendations-list li i {
      margin-bottom: 0.8rem;
    }
  }
</style>
