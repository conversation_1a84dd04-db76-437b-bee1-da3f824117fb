<template>
  <div class="index-container">
    <el-row :gutter="20">
      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>Maven缓存清理</span>
            <el-button
              icon="el-icon-delete"
              size="mini"
              style="float: right"
              type="primary"
              @click="handleCleancache"
            ></el-button>
          </div>
          <el-form
            ref="mavenForm"
            :model="mavenForm"
            :rules="rules"
            @submit.native.prevent
          >
            <el-form-item prop="env">
              <el-autocomplete
                v-model.trim="mavenForm.env"
                :fetch-suggestions="queryEnv"
                :popper-class="'env-autocomplete'"
                clearable
                placeholder="如：沙盒环境或非沙盒环境(local)"
                prefix-icon="el-icon-delete"
                value-key="value"
                @select="handleEnvSelect"
              >
                <template slot-scope="{ item }">
                  <div class="env-item">
                    <span :class="['env-type', item.type]">
                      {{ item.typeLabel }}
                    </span>
                    <span class="env-value">{{ item.value }}</span>
                    <span class="env-value">{{ item.alias }}</span>
                  </div>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item prop="service">
              <el-autocomplete
                v-model.trim="mavenForm.service"
                :fetch-suggestions="querySearchForProject"
                clearable
                placeholder="如：wego-album-api"
                prefix-icon="el-icon-delete"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>SkyWalking事件采样设置</span>
            <el-button
              size="mini"
              style="float: right"
              type="primary"
              @click="updateSkywalking"
            >
              更新设置
            </el-button>
          </div>
          <el-form :model="skywalkingform" @submit.native.prevent>
            <el-form-item label="A-环境：  ">
              <el-radio-group v-model="skywalkingform.prdA" size="mini">
                <el-radio-button :label="10">精简模式</el-radio-button>
                <el-radio-button :label="60">观察模式</el-radio-button>
                <el-radio-button :label="100">全量模式</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="B-环境：  ">
              <el-radio-group v-model="skywalkingform.prdB" size="mini">
                <el-radio-button :label="10">精简模式</el-radio-button>
                <el-radio-button :label="60">观察模式</el-radio-button>
                <el-radio-button :label="100">全量模式</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="长沙环境： ">
              <el-radio-group v-model="skywalkingform.prdCS" size="mini">
                <el-radio-button :label="10">精简模式</el-radio-button>
                <el-radio-button :label="60">观察模式</el-radio-button>
                <el-radio-button :label="100">全量模式</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>配置同步文件排除选择</span>
            <el-button
              icon="el-icon-delete"
              size="mini"
              style="float: right"
              type="primary"
              @click="confsaveExclude"
            ></el-button>
          </div>
          <el-form
            :label-position="labelPosition"
            :model="cnf_type"
            label-width="80px"
          >
            <el-form-item label="环境名： ">
              <el-select
                v-model="cnf_type.type"
                clearable
                placeholder="请选择"
                @change="onTypeChange"
              >
                <el-option
                  v-for="item in cnf_options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="服务名： ">
              <el-select
                v-if="cnf_services.services.length >= 0"
                v-model="cnf_type.service"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="service in cnf_services.services"
                  :key="service"
                  :fetch-suggestions="querySearch"
                  :label="service"
                  :value="service"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="文件名：">
              <el-input
                v-model="cnf_type.file"
                clearable
                placeholder=",号分隔,文件名"
              ></el-input>
            </el-form-item>
          </el-form>
          <div>
            <p>{{ message }}</p>
          </div>
        </el-card>
      </el-col>

      <el-col :lg="8" :md="8" :sm="12" :span="24" :xl="8" :xs="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>Mongo分片实例重启</span>
            <el-button
              icon="el-icon-refresh"
              size="mini"
              style="float: right"
              type="primary"
              @click="submitReloadMongoDB"
            ></el-button>
          </div>
          <el-form
            ref="mongoDbFrom"
            :model="mongoForm"
            :rules="mongorules"
            style="display: flex"
            @submit.native.prevent
          >
            <el-form-item prop="nodeParams" style="flex: 1">
              <el-input
                v-model.trim="mongoForm.nodeParams"
                clearable
                placeholder="实例节点ID,多个用','分隔 example：cmgo-p5hylb4z_0-node-primary"
                prefix-icon="el-icon-refresh"
              />
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import {
    cleanMaven,
    cnfTypeService,
    getJenkinsMaven,
    getSkywalkingSample,
    PostMongoReloadNode,
    saveExclude,
    setSkywalkingSample,
  } from '@/api/toolbox'
  import { getSandboxlist } from '@/api/ciWorkflow'
  import { getEnvs } from '@/api/presm'

  export default {
    name: 'Toolbox',
    data() {
      return {
        defaultProjects: [
          { value: 'content' },
          { value: 'content-check' },
          { value: 'datanalysis' },
          { value: 'lookbook' },
          { value: 'lookbook-admin' },
          { value: 'lookbook-common' },
          { value: 'lookbook-da' },
          { value: 'lookbook-parent' },
          { value: 'lookbook-scheduler' },
          { value: 'lookbook-support' },
          { value: 'lookbook-test' },
          { value: 'script' },
          { value: 'starter' },
          { value: 'toolbox' },
          { value: 'wego-ai' },
          { value: 'wego-album' },
          { value: 'wego-album-api' },
          { value: 'wego-album-common' },
          { value: 'wego-album-service' },
          { value: 'wego-basic-common' },
          { value: 'wego-commodity' },
          { value: 'wego-commodity-api' },
          { value: 'wego-commodity-common' },
          { value: 'wego-commodity-service' },
          { value: 'wego-common' },
          { value: 'wego-data-mongodb-starter' },
          { value: 'wego-eswordcount' },
          { value: 'wego-eswordcount-service' },
          { value: 'wego-logistics' },
          { value: 'wego-logistics-api' },
          { value: 'wego-logistics-common' },
          { value: 'wego-logistics-service' },
          { value: 'wego-order' },
          { value: 'wego-order-api' },
          { value: 'wego-order-common' },
          { value: 'wego-order-service' },
          { value: 'wego-partner' },
          { value: 'wego-payment' },
          { value: 'wego-payment-api' },
          { value: 'wego-payment-common' },
          { value: 'wego-payment-service' },
          { value: 'wego-root' },
          { value: 'wego-searching' },
          { value: 'wego-starter' },
          { value: 'wgf' },
          { value: 'xway' },
          { value: 'basic' },
          { value: 'bifrost' },
          { value: 'bigdata' },
          { value: 'commodity' },
          { value: 'dss' },
          { value: 'flygeese' },
          { value: 'portal' },
          { value: 'searching' },
          { value: 'share' },
          { value: 'starter' },
          { value: 'xway' },
        ],
        projects: [],
        mavenForm: {
          env: '',
          service: '',
        },

        mongoForm: {
          nodeParams: '',
        },

        skywalkingform: {
          prdA: '10',
          prdB: '10',
          prdCS: '10',
        },

        rules: {
          service: [
            {
              required: true,
              trigger: 'blur',
              message: '请选择要清理的工程',
            },
          ],
          env: [
            {
              required: true,
              trigger: 'blur',
              message: '请选择要清理的环境',
            },
          ],
        },

        mongorules: {
          nodeParams: [
            {
              required: true,
              trigger: 'blur',
              message: '分片示例节点ID不能为空',
            },
            {
              pattern: /^\S+$/,
              message: '分片示例节点ID不能为空白字符',
              trigger: 'blur',
            },
          ],
        },

        cnf_options: [
          { value: 'nacos', label: 'nacos' },
          { value: 'apollo', label: 'apollo' },
        ],
        cnf_services: { services: [] },
        cnf_type: {
          type: '',
          service: '',
          file: '',
        },
        labelPosition: 'right',
        message: '',
        result: null,
        error: null,
        dragover: false,
      }
    },

    created() {
      this.fetchData()
    },
    methods: {
      handleEnvSelect(item) {
        this.mavenForm.env = item.value
      },

      async queryEnv(queryString, cb) {
        try {
          const queryForm = {
            pageNo: 1,
            pageSize: 100,
            keyword: '',
          }

          const { data: sandboxData } = await getSandboxlist(queryForm)
          const sandboxList = sandboxData.map((item) => ({
            value: item.sbx_uuid,
            alias: item.sbx_alias,
            version_code: item.version_code,
            type: 'sandbox',
            typeLabel: '沙盒',
          }))

          const presmQueryForm = {
            ...queryForm,
            status: 2,
          }
          const { data: presmData } = await getEnvs(presmQueryForm)
          const presmList = presmData.map((item) => ({
            value: item.pre_uuid,
            alias: item.pre_alias,
            version_code: item.version_code,
            type: 'presm',
            typeLabel: '预发布',
          }))

          const allEnvs = [
            ...sandboxList,
            ...presmList,
            {
              value: 'local',
              alias: 'local',
              type: 'local',
              typeLabel: 'LOCAL',
            },
          ]

          const results = queryString
            ? allEnvs.filter((item) =>
                item.value.toLowerCase().includes(queryString.toLowerCase())
              )
            : allEnvs

          cb(results)
        } catch (error) {
          console.error('Error fetching environments:', error)
          cb([
            {
              value: 'local',
              alias: 'local',
              type: 'local',
              typeLabel: 'LOCAL',
            },
          ])
        }
      },

      async querySearchForProject(queryString, cb) {
        if (this.mavenForm.env === '') {
          this.$baseMessage('环境选择异常', 'failed')
        } else {
          try {
            const queryForm = {
              env: this.mavenForm.env,
            }
            const { data } = await getJenkinsMaven(queryForm)
            this.projects = data
          } catch (error) {
            console.error('Error fetching projects:', error)
            this.projects = []
          }
        }
        this.querySearch(queryString, (results) => {
          cb(results)
        })
      },

      querySearch(queryString, cb) {
        const projects = this.projects
        const results = queryString
          ? projects.filter(this.createFilter(queryString))
          : projects
        cb(results)
      },

      createFilter(queryString) {
        return (project) => {
          return (
            project.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
          )
        }
      },

      updateSkywalking() {
        this.$baseConfirm('确认修改Skywalking设置吗？', '', async () => {
          const { msg } = await setSkywalkingSample(this.skywalkingform)
          this.$baseMessage(msg, 'success')
        })
      },

      handleCleancache() {
        this.$refs['mavenForm'].validate(async (valid) => {
          if (valid) {
            this.$baseConfirm(
              '确认清除项目「' +
                this.mavenForm.env +
                ' : ' +
                this.mavenForm.service +
                '」的缓存吗？',
              '',
              async () => {
                const { msg } = await cleanMaven(this.mavenForm)
                this.$baseMessage('清理已完成。', 'success')
                this.mavenForm.env = ''
                this.mavenForm.service = ''
              }
            )
          }
        })
      },

      async fetchData() {
        var { data } = await getSkywalkingSample()
        if (data) {
          this.skywalkingform = data
        }
      },

      onTypeChange() {
        this.queryConfService(this.cnf_type.type, (result) => {
          this.cnf_services.services = result
        })
      },

      async queryConfService(queryString, cb) {
        try {
          let type_str = { queryString: queryString }
          let response = await cnfTypeService(type_str)
          cb(response.typeServices)
        } catch (error) {
          this.$baseMessage('获取服务名失败：500', 'failed')
        }
      },

      async confsaveExclude() {
        if (this.cnf_type) {
          const res = await saveExclude(this.cnf_type)
          if (res) {
            this.$baseMessage(res.msg, 'success')
          } else {
            this.$baseMessage(res.msg, 'failed')
          }
        }
      },

      submitReloadMongoDB() {
        this.$refs.mongoDbFrom.validate((valid) => {
          if (valid) {
            this.handleReloadMongoDB()
          } else {
            this.$baseMessage('表单验证失败', 'error')
            return false
          }
        })
      },

      async handleReloadMongoDB() {
        try {
          console.log(this.mongoForm)
          const { msg } = await PostMongoReloadNode(this.mongoForm)
          this.$baseMessage(msg, 'success')
        } catch (error) {
          console.error('MongoDB重启失败:', error)
          this.$baseMessage(error, 'error')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .index-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 115px;
        }
      }
    }

    .el-autocomplete {
      width: 100%;
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 205px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }

  .drop-area {
    height: 30px;
    color: #434141;
    text-align: center;
    font-weight: bold;
    padding: 10px;

    background-color: rgba(210, 207, 207, 0.5);
  }

  .dragover {
    background-color: rgba(98, 97, 97, 0.8);
  }

  .drop-error {
    color: red;
    font-weight: bold;
  }

  .env-item {
    display: flex;
    align-items: center;

    .env-type {
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 12px;
      margin-right: 8px;

      &.sandbox {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.presm {
        background-color: #f6ffed;
        color: #52c41a;
      }

      &.local {
        background-color: #f9f0ff;
        color: #722ed1;
      }
    }

    .env-value {
      flex: 1;
    }
  }

  ::v-deep .env-autocomplete {
    .el-autocomplete-suggestion__wrap {
      max-height: 280px;
    }
  }
</style>
