.container {
    width: 1280px;
    margin: 50px auto 0;
    min-height: calc(100% - 122px);
    min-height: -moz-calc(100% - 122px);
    min-height: -webkit-calc(100% - 110px);
    position: relative;
    z-index: 1;
}

.warp {
    height: 150px;
    margin: 0 auto;
    overflow: hidden;
    ul {
        list-style: none;
        padding: 0;
        margin: 0 auto;
        li,
        a {
            display: block;
            height: 30px;
            line-height: 30px;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }
        date {
            color: #979ba5;
        }
    }
}
.top {
    clear: both;
    margin-bottom: 35px;
    display: flex;
    .datatime {
        width: 58px;
        margin-right: 18px;
        .month {
            height: 24px;
            line-height: 24px;
            background: #3a84ff;
            font-size: 12px;
            text-align: center;
            color: #fff;
        }
        .day {
            height: 36px;
            line-height: 36px;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            color: #000;
            background: #fff;
            box-shadow: 0 2px 4px 0 rgba(0,0,0,.1);
        }
    }
    .welcome-text {
        font-size: 20px;
        text-align: left;
        color: #313238;
        line-height: 26px;
        margin-bottom: 12px;
        white-space:nowrap;
        overflow:hidden;
    }
    .tip {
        font-size: 14px;
        color: #979ba5;
        line-height: 19px;
        overflow:hidden;
    }
}

.main-left {
    width: 885px;
    float: left;
}
.main-right {
    margin-top: 32px;
    margin-left: 10px;
    float: left;
    width: 382px;
}
.main-panel {
    width: 382px;
    background: #FFF;
    border-radius: 2px;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,.1);
    box-sizing: border-box;
    padding: 16px;
    .tip {
        font-size: 14px;
        color: #979ba5;
        line-height: 19px;
    }
}
.right-title {
    padding-bottom: 12px;
    margin-bottom: 3px;
    font-size: 16px;
    color: #313238;
    width: 360px;
    border-bottom: 1px solid #dcdee5;
}
.title {
    overflow:hidden;text-overflow:ellipsis;white-space:nowrap;
    width:280px;
}
.date {
    overflow:hidden;
}

.list.special {
    margin-top: 20px;
}
.list.special li {
    margin-bottom: 25px;
}

.footer {
    height: 70px;
    text-align: center;
    font-size: 12px;
    line-height: 24px;
    padding: 15px 0;
    background: #f0f1f5;
    color: #63656e;
}

.search-box {
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .search-wrapper {
        position: relative;
        height: 40px;
        label {
            position: absolute;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.5);
            top: 8px;
            left: 12px;
            z-index: -1;
            transition: .15s all ease-in-out;
        }
        input {
            padding: 4px 12px;
            color: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(0, 0, 0, 0.12);
            transition: .15s all ease-in-out;
            background: white;
            &:focus {
                outline: none;
                transform: scale(1.05);
                & + label {
                    font-size: 10px;
                    transform: translateY(-24px) translateX(-12px);
                }
            }
            &::-webkit-input-placeholder {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.5);
                font-weight: 100;
            }
        }
    }
    .wrapper {
       width: 100%;
        flex-wrap: wrap;
        padding: 20px;
        left: -25px;
        position: relative;
        .card {
            float: left;
            box-shadow: rgba(0, 0, 0, 0.11765) 0px 1px 6px, rgba(0, 0, 0, 0.11765) 0px 1px 4px;
            width: calc((100% - 100px)/4);
            margin: 10px;
            transition: .15s all ease-in-out;
            a {
                text-decoration: none;
                padding: 12px;
                color: #03A9F4;
                font-size: 24px;
                display: flex;
                flex-direction: column;
                align-items: center;
                img {
                    height: 100px;
                }
                small {
                    font-size: 12px;
                    padding: 4px;
                }
            }
            &:hover {
                transform: scale(1.05);
                background: #dbe4f5;
            }
        }
    }


}