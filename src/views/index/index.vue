<template>
  <div class="container">
    <div class="top">
      <div class="datatime">
        <div id="month" class="month">{{ nowWeek }}</div>
        <div id="day" class="day">{{ nowMonth }}.{{ nowDay }}</div>
      </div>
      <div class="text-wrapper">
        <p class="welcome-text">
          Hi {{ performanceConsent }}，欢迎使用微购科技运维工作台!
        </p>
        <p class="tip">{{ chosenWelcomeMessage }}</p>
      </div>
    </div>
    <el-row :gutter="5">
      <el-col :lg="17" :md="17" :sm="24" :xl="17" :xs="24">
        <el-card>
          <vab-query-form>
            <vab-query-form-left-panel :span="18">
              <el-radio-group v-model="appfilter" size="medium">
                <el-radio-button label="全部"></el-radio-button>
                <el-radio-button label="监控告警"></el-radio-button>
                <el-radio-button label="环境配置"></el-radio-button>
                <el-radio-button label="研发流程"></el-radio-button>
                <el-radio-button label="运营后台"></el-radio-button>
              </el-radio-group>
            </vab-query-form-left-panel>
            <vab-query-form-right-panel :span="6">
              <el-input
                v-model.trim="keyword"
                clearable
                placeholder="关键字过滤..."
              />
            </vab-query-form-right-panel>
          </vab-query-form>
          <div class="search-box">
            <div class="wrapper">
              <div v-for="post in filteredList" class="card">
                <a target="_blank" :href="post.link">
                  <img :src="post.img" />
                  <small>{{ post.desc }}</small>
                  {{ post.title }}
                </a>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="7" :md="7" :sm="24" :xl="7" :xs="24">
        <el-card>
          <template #header>
            <vab-icon icon="send-plane-2-line" />
            最新动态
          </template>
          <template>
            <vue-seamless-scroll
              :class-option="classOption"
              :data="messagelist"
              class="warp"
            >
              <ul class="item">
                <li v-for="(item, index) in messagelist" :key="index">
                  <span
                    :title="item.title"
                    class="title"
                    v-text="item.title"
                  ></span>
                  <span class="date" v-text="item.date"></span>
                </li>
              </ul>
            </vue-seamless-scroll>
          </template>
        </el-card>
        <el-card>
          <template #header>
            <vab-icon icon="send-plane-2-line" />
            常用链接
          </template>

          <ul class="list special">
            <li class="list-item">
              <a href="https://cloud.tencent.com" target="_blank">
                腾讯云控制台
              </a>
            </li>
            <li class="list-item">
              <a
                href="https://slowisfast.feishu.cn/drive/shared/"
                target="_blank"
              >
                飞书文档
              </a>
            </li>
            <li class="list-item">
              <a href="http://www.bejson.com/" target="_blank">JSON格式化</a>
            </li>
            <li class="list-item">
              <a href="https://regex101.com/" target="_blank">正则表达式测试</a>
            </li>
            <li class="list-item">
              <a href="https://zhangzongchu.szwego.com/" target="_blank">
                长研所VPN管理
              </a>
            </li>
            <li class="list-item">
              <a href="https://check.szwego.com/" target="_blank">
                微购科技网络诊断
              </a>
            </li>
            <li class="list-item">
              <a href="https://proxy.szwego.com/guide.htm" target="_blank">
                加速代理设置指导
              </a>
            </li>
            <li class="list-item">
              <a
                href="https://admin.szwego.com/files/vpn-guide.htm"
                target="_blank"
              >
                VPN接入指导
              </a>
            </li>
          </ul>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
  import { getApplist } from '@/api/user'
  import axios from 'axios'

  class Post {
    constructor(title, link, desc, img, groups, type) {
      this.title = title
      this.link = link
      this.desc = desc
      this.img = img
      this.groups = groups
      this.type = type
    }
  }

  export default {
    data() {
      return {
        welcomeMessages: [
          'Winter is coming.',
          'The things I do for love.',
          'The night is dark and full of terrors.',
          'I will take what is mine with fire and blood.',
          'What is dead may never die.',
          'The seed is strong.',
          'Unbowed, Unbent, Unbroken',
          'A mind needs books as a sword needs a whetstone, if it is to keep its edge. ',
          'The man who fears losing has already lost.',
          'Valar morghulis.',
          'Every flight begins with a fall.',
          'A true man does what he will, not what he must.',
          'Power resides when men believe it resides.',
          'Everyone is your enemy. Everyone is your friend.',
          'Without question? No, I ask how much. ',
          'Do not go gentle into that good night.',
          'Rage, rage against the dying of the light.',
          'Stand firm, even when the storm rages.',
          'Face the darkness, and let your courage shine.',
          'Life be beautiful like summer flowers and death like autumn leaves',
        ],
        chosenWelcomeMessage: '',
        classOption: {
          step: 1,
          singleHeight: 60,
          waitTime: 3000,
        },
        messagelist: [
          {
            title: '暂无动态',
            date: '+1s',
          },
        ],
        performanceConsent: '',
        usergroups: [],
        timer: {},
        nowMonth: '',
        nowDay: '',
        nowWeek: '',
        txid: '',
        ip2find: '',
        appfilter: '全部',
        keyword: '',
        appList: [],
      }
    },
    computed: {
      filteredList() {
        return this.appList.filter((post) => {
          return (
            post.title.toLowerCase().includes(this.keyword.toLowerCase()) &&
            auth(post.groups) &&
            (post.type === this.appfilter || this.appfilter === '全部')
          )
        })
      },
    },
    mounted() {
      this.chooseRandomWelcomeMessage()
    },
    created() {
      this.setNowTimes(), (this.performanceConsent = getCookie('nickname'))
      ;(this.usergroups = getCookie('groups').split(',')),
        this.getTickets(),
        (this.timer = setInterval(() => {
          this.getTickets()
        }, 10000))
      this.getApplications()
    },
    beforeDestroy() {
      clearInterval(this.timer)
    },
    methods: {
      chooseRandomWelcomeMessage() {
        const randomIndex = Math.floor(
          Math.random() * this.welcomeMessages.length
        )
        this.chosenWelcomeMessage = this.welcomeMessages[randomIndex]
      },
      async getApplications() {
        const { data } = await getApplist()
        this.appList = data
      },
      toggleOnOff() {
        this.onOff = !this.onOff
      },
      gopinpoint() {
        window.open(
          'https://pinpoint.in.szwego.com/transactionDetail/' +
            this.txid +
            '/0/' +
            this.txid.split('%')[0] +
            '/-1',
          '_blank'
        )
      },
      queryip() {},
      setNowTimes() {
        let myDate = new Date()
        let wk = myDate.getDay()
        let mm = myDate.getMonth() + 1
        let dd = String(
          myDate.getDate() < 10 ? '0' + myDate.getDate() : myDate.getDate()
        )
        let weeks = [
          '星期日',
          '星期一',
          '星期二',
          '星期三',
          '星期四',
          '星期五',
          '星期六',
        ]
        let week = weeks[wk]
        this.nowMonth = mm
        this.nowDay = dd
        this.nowWeek = week
      },
      getTickets() {
        //请求api接口
        axios.defaults.withCredentials = true
        axios.get('/user/api/v1/getnotices').then(({ data }) => {
          //console.log(data)
          if (data) {
            this.messagelist = data
            //console.log(this.messagelist)
          } else {
            //console.log(data.status)
            // console.log("获取消息列表失败");
          }
        })
      },
    },
  }

  export function getCookie(name) {
    var arr,
      reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
    if ((arr = document.cookie.match(reg))) {
      return '' + arr[2]
    } else {
      return ''
    }
  }

  export function auth(ag) {
    //console.log("user group:",ug,", app group:",ag)
    let result = false
    let ug = getCookie('groups').split(',')
    for (var g of ag.split(',')) {
      if (ug.indexOf(g.trim()) >= 0 || ug.indexOf('admins') >= 0) {
        //console.log("user group:",ug,", app group:",ag,", match group:",g)
        result = true
      }
    }
    return result
  }
</script>

<style lang="scss" scoped>
  .container {
    width: 1280px;
    margin: 50px auto 0;
    min-height: calc(100% - 122px);
    min-height: -moz-calc(100% - 122px);
    min-height: -webkit-calc(100% - 110px);
    position: relative;
    z-index: 1;
  }

  .warp {
    height: 300px;
    margin: 0 auto;
    overflow: hidden;

    ul {
      list-style: none;
      padding: 0;
      margin: 0 auto;

      li,
      a {
        display: block;
        height: 30px;
        line-height: 30px;
        display: flex;
        justify-content: space-between;
        font-size: 12px;
      }

      date {
        color: #979ba5;
      }
    }
  }

  .top {
    clear: both;
    margin-bottom: 35px;
    display: flex;

    .datatime {
      width: 58px;
      margin-right: 18px;

      .month {
        height: 24px;
        line-height: 24px;
        background: #3a84ff;
        font-size: 12px;
        text-align: center;
        color: #fff;
      }

      .day {
        height: 36px;
        line-height: 36px;
        text-align: center;
        font-size: 18px;
        font-weight: 700;
        color: #000;
        background: #fff;
        box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
      }
    }

    .welcome-text {
      font-size: 20px;
      text-align: left;
      color: #313238;
      line-height: 26px;
      margin-bottom: 12px;
      white-space: nowrap;
      overflow: hidden;
    }

    .tip {
      font-size: 14px;
      color: #979ba5;
      line-height: 19px;
      overflow: hidden;
    }
  }

  .main-left {
    width: 885px;
    float: left;
  }

  .main-right {
    margin-top: 32px;
    margin-left: 10px;
    float: left;
    width: 382px;
  }

  .main-panel {
    width: 382px;
    background: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
    padding: 16px;

    .tip {
      font-size: 14px;
      color: #979ba5;
      line-height: 19px;
    }
  }

  .right-title {
    padding-bottom: 12px;
    margin-bottom: 3px;
    font-size: 16px;
    color: #313238;
    width: 360px;
    border-bottom: 1px solid #dcdee5;
  }

  .title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 280px;
  }

  .date {
    overflow: hidden;
  }

  .list.special {
    margin-top: 20px;
  }

  .list.special li {
    margin-bottom: 25px;
  }

  .footer {
    height: 70px;
    text-align: center;
    font-size: 12px;
    line-height: 24px;
    padding: 15px 0;
    background: #f0f1f5;
    color: #63656e;
  }

  .search-box {
    align-items: center;
    justify-content: center;
    flex-direction: column;

    .search-wrapper {
      position: relative;
      height: 40px;

      label {
        position: absolute;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.5);
        top: 8px;
        left: 12px;
        z-index: -1;
        transition: 0.15s all ease-in-out;
      }

      input {
        padding: 4px 12px;
        color: rgba(0, 0, 0, 0.7);
        border: 1px solid rgba(0, 0, 0, 0.12);
        transition: 0.15s all ease-in-out;
        background: white;

        &:focus {
          outline: none;
          transform: scale(1.05);

          & + label {
            font-size: 10px;
            transform: translateY(-24px) translateX(-12px);
          }
        }

        &::-webkit-input-placeholder {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.5);
          font-weight: 100;
        }
      }
    }

    .wrapper {
      width: 100%;
      flex-wrap: wrap;
      padding: 20px;
      left: -25px;
      position: relative;

      .card {
        float: left;
        box-shadow: rgba(0, 0, 0, 0.11765) 0px 1px 6px,
          rgba(0, 0, 0, 0.11765) 0px 1px 4px;
        width: calc((100% - 100px) / 4);
        margin: 10px;
        transition: 0.15s all ease-in-out;

        a {
          text-decoration: none;
          padding: 12px;
          color: #03a9f4;
          font-size: 24px;
          display: flex;
          flex-direction: column;
          align-items: center;

          img {
            height: 100px;
          }

          small {
            font-size: 12px;
            padding: 4px;
          }
        }

        &:hover {
          transform: scale(1.05);
          background: #dbe4f5;
        }
      }
    }
  }
</style>
