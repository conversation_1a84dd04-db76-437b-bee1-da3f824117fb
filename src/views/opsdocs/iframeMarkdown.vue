<template>
  <div class="iframe-container">
    <template v-if="isLoading" class="loading-indicator">
      <iframe
        ref="vuepressFrame"
        v-loading="iframeLoaded"
        :src="devopsDocs"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-spinner="el-icon-loading"
        element-loading-text="拼命加载中"
      ></iframe>
    </template>
  </div>
</template>

<script>
  import { baseURL } from '@/config/setting.config.js'

  export default {
    name: 'Test',
    data() {
      return {
        isLoading: true,
      }
    },
    computed: {
      isLocal() {
        return baseURL.includes('127.0.0.1')
      },
      devopsDocs() {
        let devopsDocsHost

        if (this.isLocal) {
          devopsDocsHost =
            'https://affine.in.szwego.com/workspace/191eba4b-7816-4095-8b6d-f4879e2777e0/all'
        } else {
          devopsDocsHost =
            'https://affine.in.szwego.com/workspace/191eba4b-7816-4095-8b6d-f4879e2777e0/all'
        }

        return devopsDocsHost
      },
    },
    methods: {
      iframeLoaded() {
        this.isLoading = false
      },
    },
  }
</script>

<style>
  .iframe-container {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  .iframe-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
  }
</style>
