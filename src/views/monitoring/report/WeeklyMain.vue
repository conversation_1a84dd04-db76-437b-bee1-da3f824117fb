<script>
  import DatabaseCard from '@/views/monitoring/report/componets/DatabaseCard.vue'
  import NetworkCard from '@/views/monitoring/report/componets/NetworkCard.vue'
  import ComputerCard from '@/views/monitoring/report/componets/ComputerCard.vue'
  import CosCard from '@/views/monitoring/report/componets/CosCard.vue'
  import OwnerCard from '@/views/monitoring/report/componets/OwnerCard.vue'
  import TopCard from '@/views/monitoring/report/componets/TopCard.vue'
  import { WeeklyOpsReport } from '@/api/itsm'
  import auto from 'chart.js/auto'
  import { commonEChartsOptions } from '@/views/monitoring/report/componets/commonEchartsOptions'
  import moment from 'moment/moment'
  import NssConfig from '@/views/devops/gray/components/NssConfig.vue'

  export default {
    name: 'WeeklyMain',
    components: {
      NssConfig,
      DatabaseCard,
      NetworkCard,
      ComputerCard,
      CosCard,
      OwnerCard,
      TopCard,
    },

    data() {
      return {
        tabs: [
          { label: '总览', name: 'owner' },
          { label: '存储', name: 'cos' },
          { label: '数据库', name: 'database' },
          { label: '网络', name: 'network' },
          { label: '计算', name: 'computer' },
          { label: 'top20', name: 'top' },
        ],
        tabPosition: 'left',
        activeName: 'owner',
        isStripe: true,
        computer_id: ['mongo', 'mysql', 'es', 'redis'],
        owner_id: ['qiniu', 'tx'],
        type_id: ['service', 'user'],
        start_time: '',
        end_time: '',
        analysis_weekly: {},
        analysis_all: [],
        cosData: [],
        cosApi: [],
        computerData: [],
        ownerCost: [],
        owner_cost_all: [],
        timeOptions: [
          { label: '当周', value: 'week' },
          { label: '近7周', value: 'month' },
          { label: '近半年', value: 'halfYear' },
          { label: '当年', value: 'year' },
        ],
        timevalue: 'month',
        Loading: true,
      }
    },

    computed: {
      cos_qiniu_cost_all() {
        return (
          this.analysis_weekly.cos_qiniu_cost +
          this.analysis_weekly.cos_qiniu_api_cost
        ).toFixed(0)
      },

      cos_qiniu_api() {
        return (this.analysis_weekly.cos_qiniu_api / 1000).toFixed(1)
      },

      net_traffic() {
        return (obj) => {
          return (obj / 10000).toFixed(2)
        }
      },

      netTotalCost() {
        let cost = 0

        for (const owner of this.owner_id) {
          cost += this.analysis_weekly[`net_cdn_${owner}_cost`]
        }
        for (const type_ of this.type_id) {
          cost += this.analysis_weekly[`net_${type_}_traffic_cost`]
        }

        return cost
      },

      WeeklyOption() {
        return this.OwnerCostOptions()
      },

      otherOption() {
        return (str) => {
          return this.CostOtherOptions(str)
        }
      },

      auto() {
        return auto
      },
    },
    created() {
      this.getWeeklyReport().then(() => {
        this.dataReady = true
        this.tabs.forEach((tab) => {
          tab.dataReady = true
        })
      })
    },

    methods: {
      getWeeks(year, week) {
        const date = new Date(year, 0, 1)
        const dayOfWeek = (date.getDay() + 6) % 7
        const firstMondayOfYear = new Date(year, 0, 1 + ((8 - dayOfWeek) % 7))
        const startDate = new Date(
          firstMondayOfYear.getTime() + (week - 1) * 7 * 24 * 60 * 60 * 1000
        )
        const endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000)

        const formattedStartDate = startDate
          .toISOString()
          .split('T')[0]
          .replace(/-/g, '/')

        const formattedEndDate = endDate
          .toISOString()
          .split('T')[0]
          .replace(/-/g, '/')

        return `${formattedStartDate} - ${formattedEndDate}`
      },
      getComputerData() {
        this.computerData = []
        if (this.analysis_weekly) {
          for (const owner of this.computer_id) {
            const item = {}
            let column = `cc_db_${owner}_tx`
            let column_cost = `cc_db_${owner}_tx_cost`
            item.memory = this.analysis_weekly[column].memory
            item.capacity = this.analysis_weekly[column].capacity
            item.cost = this.analysis_weekly[column_cost]
            item.type = owner
            item.title = owner.toUpperCase()
            if (owner === 'mongo') {
              item.cpu = this.analysis_weekly[column].cpu
              item.used = this.analysis_weekly[column].used
            } else if (owner === 'es') {
              item.cpu = this.analysis_weekly[column].cpu
              item.nodeCount = this.analysis_weekly[column].nodeCount
            } else if (owner === 'mysql') {
              item.cpu = this.analysis_weekly[column].cpu
            } else if (owner === 'redis') {
              item.usage = this.analysis_weekly[column].usage
            }
            this.computerData.push(item)
          }
        }
      },

      getCosData() {
        this.cosData = []
        if (this.analysis_weekly) {
          for (const owner of this.owner_id) {
            const item = {}
            item.file = this.cos_file(owner)
            item.size = this.cos_size(owner)
            item.dailyNum = this.cos_daily_inc_num(owner)
            item.dailySize = this.cos_daily_inc_size(owner)
            item.type = owner
            if (owner === 'qiniu') {
              item.title = '七牛'
              item.cost = this.analysis_weekly.cos_qiniu_cost
              item.api = this.cos_qiniu_api
            } else {
              item.title = '腾讯'
              item.cost = this.analysis_weekly.cos_tx_cost
            }
            this.cosData.push(item)
          }
        }
        if (this.analysis_weekly) {
          this.cosApi = Object.keys(this.analysis_weekly)
            .filter((key) => key.includes('cos_qiniu_api'))
            .map((key) => ({ [key]: this.analysis_weekly[key] }))
        } else {
          this.cosApi = []
        }
      },

      cos_size(type) {
        if (type === 'qiniu') {
          return (this.analysis_weekly.cos_qiniu_size / 1000).toFixed(0)
        } else if (type === 'tx') {
          return (this.analysis_weekly.cos_tx_size / 1000).toFixed(1)
        }
        return 0
      },

      cos_file(type) {
        if (type === 'qiniu') {
          return (this.analysis_weekly.cos_qiniu_files / 100000000).toFixed(1)
        } else if (type === 'tx') {
          return (this.analysis_weekly.cos_tx_files / 100000000).toFixed(2)
        }
        return 0
      },

      cos_daily_inc_num(type) {
        if (type === 'qiniu') {
          return (this.analysis_weekly.cos_qiniu_daily_inc_num / 1e4).toFixed(0)
        } else if (type === 'tx') {
          return (this.analysis_weekly.cos_tx_daily_inc_num / 1e4).toFixed(0)
        }
        return 0
      },

      cos_daily_inc_size(type) {
        if (type === 'qiniu') {
          return this.analysis_weekly.cos_qiniu_daily_inc_size
        } else if (type === 'tx') {
          return this.analysis_weekly.cos_tx_daily_inc_size
        }
        return 0
      },

      OwnerCostOptions() {
        const ownerCostDataDesc = this.owner_cost_all.sort((a, b) => {
          if (a.year !== b.year) {
            return a.year - b.year
          }

          return a.weekly - b.weekly
        })

        for (let i = 0; i < ownerCostDataDesc.length; i++) {
          const keys = ['ali', 'tx', 'qiniu', 'costAll']
          if (i === 0) {
            const currentWeek = ownerCostDataDesc[i]
            keys.forEach((key) => {
              currentWeek[`${key}Percent`] = 0 + '%'
              currentWeek['costAll'] =
                currentWeek['ali'] + currentWeek['tx'] + currentWeek['qiniu']
            })
          } else {
            const currentWeek = ownerCostDataDesc[i]
            const previousWeek = ownerCostDataDesc[i - 1]
            keys.forEach((key) => {
              const percentKey = `${key}Percent`
              const percentValue =
                (
                  ((currentWeek[key] - previousWeek[key]) / previousWeek[key]) *
                  100
                ).toFixed(2) + '%'
              currentWeek[percentKey] = percentValue || 0
              currentWeek['costAll'] =
                currentWeek['ali'] + currentWeek['tx'] + currentWeek['qiniu']
            })
          }
        }

        const ownerCostData = ownerCostDataDesc

        const latestSevenWeeks = ownerCostData.map((item) => item.weekly)

        const seriesData = [
          {
            name: '阿里云',
            type: 'bar',
            stack: 'total',
            data: ownerCostData.map((item) => item.ali),
            percentData: ownerCostData.map((item) => item.aliPercent),
          },
          {
            name: '腾讯云',
            type: 'bar',
            stack: 'total',
            data: ownerCostData.map((item) => item.tx),
            percentData: ownerCostData.map((item) => item.txPercent),
          },
          {
            name: '七牛云',
            type: 'bar',
            stack: 'total',
            data: ownerCostData.map((item) => item.qiniu),
            percentData: ownerCostData.map((item) => item.qiniuPercent),
          },
          {
            name: '总计',
            type: 'bar',
            stack: 'total',
            data: ownerCostData.map((item) => item.costAll),
            percentData: ownerCostData.map((item) => item.costAllPercent),
          },
        ]
        const legendData = seriesData.map((item) => item.name)

        return {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
            formatter: (params) => {
              const costAll = ownerCostData.find(
                (item) => item.weekly === Number(params[0].axisValue)
              )?.costAll
              const week = params[0].name
              const year = ownerCostData.find(
                (item) => item.weekly === Number(params[0].axisValue)
              ).year
              const weekdays = this.getWeeks(year, week)
              let content = `${weekdays}<br>`

              params.forEach((param) => {
                const seriesName = param.seriesName
                const value = param.value
                const percentData = seriesData.find(
                  (series) => series.name === seriesName
                ).percentData
                const percent = percentData[param.dataIndex]
                content += `${seriesName} ¥ ${value} &nbsp;&nbsp;&nbsp; ${percent}<br>`
              })

              return content
            },
          },
          toolbox: {
            right: '10%',
            ...commonEChartsOptions.toolbox,
          },
          legend: {
            dataL: legendData,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: latestSevenWeeks,
          },
          yAxis: {
            type: 'value',
          },
          series: seriesData,
        }
      },

      CostOtherOptions(owner) {
        const latestSevenWeeks = [...this.analysis_all]
          .sort((a, b) => {
            if (a.year !== b.year) {
              return a.year - b.year
            }

            return a.week - b.week
          })
          .map((item) => item.week)

        const seriesDataMap = {}
        this.analysis_all.forEach((item) => {
          if (latestSevenWeeks.includes(item.week)) {
            const ccSaasData = item[`cc_saas_${owner}`]
            if (ccSaasData && ccSaasData.length > 0) {
              ccSaasData.forEach((data) => {
                const { name, value } = data
                if (!seriesDataMap[name]) {
                  seriesDataMap[name] = new Array(latestSevenWeeks.length).fill(
                    null
                  )
                }
                const weekIndex = latestSevenWeeks.indexOf(item.week)
                if (weekIndex !== -1) {
                  seriesDataMap[name][weekIndex] = value
                }
              })
            }
          }
        })

        const seriesData = []
        for (const name in seriesDataMap) {
          if (seriesDataMap.hasOwnProperty(name)) {
            seriesData.push({
              name: name,
              value: seriesDataMap[name],
            })
          }
        }

        return {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },

            extraCssText: 'width:auto; min-width:150px;',
            formatter: (params) => {
              const weekNumber = Number(params[0].axisValue)
              const currentItem = this.analysis_all.find(
                (item) => item.week === weekNumber
              )
              const currentYear = currentItem
                ? currentItem.year
                : new Date().getFullYear()
              const weekDateRange = this.getWeeks(currentYear, weekNumber)

              let tooltipContent = `${weekDateRange}<table>`
              params.forEach((param) => {
                const displayValue =
                  param.value !== undefined ? param.value : '–'
                tooltipContent += `
                <tr>
                  <td style="text-align: left;">${param.marker}${param.seriesName}:</td>
                  <td style="text-align: right;">${displayValue}</td>
                </tr>`
              })
              tooltipContent += `</table>`
              return tooltipContent
            },
          },
          toolbox: {
            left: 'left',
            top: 'middle',
            orient: 'vertical',
            showTitle: false,
            ...commonEChartsOptions.toolbox,
          },
          textStyle: {
            fontFamily: 'sans-serif',
            fontSize: 12,
          },

          legend: {
            itemGap: 1,
            scroll: true,
            selected: {},
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
            top: 80,
          },
          xAxis: {
            type: 'category',
            data: latestSevenWeeks,
          },
          yAxis: {
            type: 'value',
          },
          series: seriesData.map((item) => ({
            name: item.name,
            type: 'bar',
            stack: 'total',
            label: {
              show: false,
            },

            emphasis: {
              focus: 'series',
              label: {
                show: false,
              },
            },
            data: item.value,
          })),
        }
      },

      CostOptions(column_cost) {
        const data_series = [...this.analysis_all]
          .sort((a, b) => {
            if (a.year !== b.year) {
              return a.year - b.year
            }

            return a.week - b.week
          })
          .map((item) => ({
            week: item.week,
            value: item[column_cost],
          }))

        const series_array = [
          {
            data: data_series.map((item) => item.value),
            type: 'line',
            smooth: true,
          },
        ]
        return {
          tooltip: {
            show: true,
            trigger: 'item',
            tooltip: { axisPointer: { snap: true } },
          },
          xAxis: {
            type: 'category',
            data: data_series.map((item) => item.week),
          },
          yAxis: {
            type: 'value',
            ...commonEChartsOptions.yAxis,
          },
          series: series_array,
        }
      },

      calculateTotalCost(data) {
        const totalCost = data.reduce((acc, item) => acc + Number(item.cost), 0)
        return totalCost
      },

      handleClick(tab, event) {
        this.activeName = tab.name
      },
      getComponent(name) {
        const components = {
          owner: 'OwnerCard',
          cos: 'CosCard',
          database: 'DatabaseCard',
          network: 'NetworkCard',
          computer: 'ComputerCard',
          top: 'TopCard',
        }
        return components[name]
      },
      handleTimeLabelChange(refresh) {
        let s, e
        switch (this.timevalue) {
          case 'week':
            s = moment().subtract(1, 'week').startOf('isoWeek')
            e = moment().subtract(1, 'week').endOf('isoWeek')
            break
          case 'month':
            s = moment().subtract(7, 'week').startOf('isoWeek')
            e = moment().subtract(1, 'week').endOf('isoWeek')
            break
          case 'halfYear':
            s = moment().startOf('month').subtract(5, 'month')
            e = moment().endOf('month')
            break
          case 'year':
            s = moment().startOf('year')
            e = moment().endOf('year')
            break
        }
        if (s && e) {
          this.start_time = s.format('YYYY-MM-DD')
          this.end_time = e.format('YYYY-MM-DD')
        }

        if (this.timevalue === '上月') {
          this.end_time = moment().startOf('month').format('YYYY-MM-DD')
        }

        if (refresh) {
          this.refreshPage()
        }
      },

      refreshPage() {
        this.analysis_weekly = {}
        this.analysis_all = []
        this.cosData = []
        this.cosApi = []
        this.computerData = []
        this.ownerCost = []
        this.owner_cost_all = []
        this.getWeeklyReport(this.start_time, this.end_time)
      },

      async getWeeklyReport(start_time = null, end_time = null) {
        this.Loading = true
        const { data, owner_cost, owner_cost_all } = await WeeklyOpsReport(
          start_time,
          end_time
        )
        if (data && data.length > 0) {
          this.analysis_all = data
          const firstData = data[0]
          for (const key in firstData) {
            this.$set(this.analysis_weekly, key, firstData[key])
          }

          const totalCost = Object.values(owner_cost).reduce(
            (acc, value) => acc + value,
            0
          )
          const previousWeekly = owner_cost.weekly - 1
          const previousData = owner_cost_all.find(
            (data) =>
              data.weekly === previousWeekly && data.year === owner_cost.year
          )

          const allItem = {
            name: 'all',
            cost: totalCost,
            percent: '',
          }

          if (previousData) {
            const previousTotalCost = Object.values(previousData).reduce(
              (acc, value) => acc + value,
              0
            )
            const percentChange =
              ((totalCost - previousTotalCost) / previousTotalCost) * 100
            allItem.percent = percentChange.toFixed(2) + '%'
          }
          this.ownerCost.push(allItem)

          for (const key in owner_cost) {
            if (key === 'weekly' || key === 'year') {
              continue
            }

            const item = {
              name: key,
              cost: owner_cost[key],
              percent: '',
            }

            const previousWeekly = owner_cost.weekly - 1
            const previousData = owner_cost_all.find(
              (data) =>
                data.weekly === previousWeekly && data.year === owner_cost.year
            )

            if (previousData) {
              const previousCost = previousData[key]
              const percentChange =
                ((owner_cost[key] - previousCost) / previousCost) * 100
              item.percent = percentChange.toFixed(2) + '%'
            }
            this.ownerCost.push(item)
          }

          this.owner_cost_all = owner_cost_all
          this.getCosData()
          this.getComputerData()
        } else {
          this.$baseMessage('获取周报费用数据失败', 'failed')
        }
        setTimeout(() => {
          this.Loading = false
        }, 300)
      },
    },
  }
</script>

<template>
  <div class="report-container">
    <el-row :gutter="24">
      <el-card>
        <div>
          <el-tabs
            v-model="activeName"
            v-loading="Loading"
            tab-position="top"
            @tab-click="handleClick"
          >
            <el-tab-pane
              v-for="item in tabs"
              :key="item.name"
              :label="item.label"
              :name="item.name"
            >
              <template v-if="activeName === item.name">
                <component
                  :is="getComponent(item.name)"
                  :analysis-all="analysis_all"
                  :analysis-weekly="analysis_weekly"
                  :calculate-total-cost="calculateTotalCost"
                  :computer-data="computerData"
                  :cos-data="cosData"
                  :cost-options="CostOptions"
                  :net-total-cost="netTotalCost"
                  :net-traffic="net_traffic"
                  :other-option="otherOption"
                  :owner-cost="ownerCost"
                  :weekly-option="WeeklyOption"
                ></component>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="el-inline-block hidden-xs-only">
          <el-radio-group
            v-model="timevalue"
            class="select-container"
            @change="handleTimeLabelChange(true)"
          >
            <el-radio-button
              v-for="option in timeOptions"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </el-card>
    </el-row>
  </div>
</template>

<style>
  .report-container {
    padding: 10px !important;
    margin: 3px !important;
    background: #f5f7f8 !important;
  }

  .select-container {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .el-inline-block {
    display: inline-block;
  }

  .custom-tab-pane {
    color: #1890ff;
    height: 50px;
    line-height: 50px;
    font-size: 15px;
    padding: 0 15px;
  }
</style>
