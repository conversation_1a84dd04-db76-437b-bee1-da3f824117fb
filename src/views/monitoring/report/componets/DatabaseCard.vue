<script>
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'

  export default {
    name: 'DatabaseCard',
    components: { CardC<PERSON> },
    props: {
      titleH2: String,
      ComputerData: Array,
      CalculateTotalCost: Function,
      CostOptions: Function,
    },
  }
</script>

<template>
  <div class="sandbox-container__content">
    <el-row class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="bold-black">
            <div style="flex: 1; box-sizing: border-box">总计</div>
          </div>
          <div style="display: flex; align-items: center">
            <div class="red-number" style="font-size: 30px !important">
              ¥
              {{
                CalculateTotalCost(ComputerData)
                  ? CalculateTotalCost(ComputerData).toLocaleString()
                  : '0'
              }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-for="item in ComputerData" :key="item" class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="bold-black">
            <div style="flex: 1; box-sizing: border-box">{{ item.title }}</div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              ¥ {{ item.cost ? item.cost.toLocaleString() : '0' }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="flex-row mb15" style="flex-direction: column">
      <div style="flex: 1 1 auto">
        <el-col
          v-for="item in ComputerData"
          :key="item"
          :span="12"
          style="padding-right: -8px; padding-left: 8px"
        >
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ item.title }}
                </div>
                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span v-if="item.type !== 'redis'" class="bold-black_span">
                    CPU: {{ item.cpu }}
                  </span>
                  <span v-if="item.type === 'mongo'" class="bold-black_span">
                    使用: {{ item.used }}
                  </span>
                  <span class="bold-black_span">
                    {{
                      item.type === 'redis'
                        ? '已使用 ' + item.usage
                        : '内存 ' + item.memory
                    }}
                  </span>
                  <span class="bold-black_span">容量: {{ item.capacity }}</span>
                  <span v-if="item.type === 'es'" class="bold-black_span">
                    节点数: {{ item.nodeCount }}
                  </span>
                </span>
              </div>
            </div>
            <card-chart
              :option="CostOptions(`cc_db_${item.type}_tx_cost`)"
            ></card-chart>
          </el-card>
        </el-col>
      </div>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .bold-black {
    font-weight: bold;
    color: black;
  }

  .bold-black_span {
    font-weight: bold;
    color: black;
    margin-left: 10px;
  }

  .sandbox-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    &__content {
      h2 {
        font-size: 16px;
      }
    }
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .red-number {
    font-size: 30px !important;
    color: #ffa39e;
    font-weight: bold;
  }

  .base-el-col {
    padding-left: 8px;
    padding-right: 8px;
    boder: 1px solid #f1f2f3;
  }

  .mb15 {
    margin-left: 8px !important;
    margin-right: -8px !important;
    margin-top: 15px !important;
    margin-buttom: 15px !important;
  }
</style>
