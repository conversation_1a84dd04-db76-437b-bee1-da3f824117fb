<script>
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'

  export default {
    name: 'OwnerCard',
    components: { Card<PERSON>hart },
    props: {
      OwnerCost: Array,
      CalculateTotalCost: Function,
      WeeklyOption: Object,
      AnalysisWeekly: Object,
    },
    data() {
      return {
        cloudProviderMap: {
          all: '总计',
          ali: '阿里云',
          tx: '腾讯云',
          qiniu: '七牛云',
        },
      }
    },

    methods: {
      tableRowColor({ rowIndex }) {
        if (rowIndex % 2 === 0) {
          return 'success-row'
        }
      },
      formatCloudProviderName(row) {
        return this.cloudProviderMap[row.name] || row.name
      },
      getPercentageClass(percent) {
        if (percent.includes('-')) {
          return 'green-number'
        } else {
          return 'red-number'
        }
      },
    },
  }
</script>

<template>
  <div class="sandbox-container__content">
    <div style="flex: 0 0 auto">
      <el-row class="mb15" style="margin-left: 8px; margin-right: 8px">
        <el-col
          v-for="(item, index) in OwnerCost"
          :key="index"
          :lg="6"
          :md="12"
          :sm="24"
          :xl="6"
          :xs="24"
          class="base-el-col"
        >
          <el-card
            :body-style="{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }"
            shadow="hover"
            style="height: 130px"
          >
            <div slot="header" class="clearfix">
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ formatCloudProviderName(item) }}
                </div>
                <span>
                  <el-tag class="header_tag" size="mini">
                    {{ item.name }}
                  </el-tag>
                </span>
              </div>
            </div>
            <div style="display: flex; align-items: center">
              <div
                :class="{ 'red-number': item.name === 'all' }"
                style="font-size: 30px !important"
              >
                ¥ {{ item.cost ? item.cost.toLocaleString() : '0' }}
              </div>
              <span :class="getPercentageClass(item.percent)">
                {{ item.percent }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row class="mb15" style="!important;">
        <el-col style="padding-right: -8px; padding-left: 8px">
          <el-card
            :body-style="{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }"
            class="echarts-card"
            shadow="hover"
            style="overflow-y: auto; overflow-x: hidden"
          >
            <card-chart :option="WeeklyOption"></card-chart>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .mb15 {
    margin-left: 8px !important;
    margin-right: -8px !important;
    margin-top: 15px !important;
    margin-buttom: 15px !important;
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .flex-col {
    flex-grow: 1;
    flex-basis: 0;
  }

  .card-container {
    display: flex;
    justify-content: space-between;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .red-number {
    color: rgb(255, 100, 98);
    font-weight: bold;
    font-size: 15px !important;
    margin-left: 10px;
  }

  .green-number {
    color: rgb(102, 144, 249);
    font-weight: bold;
    font-size: 15px !important;
    margin-left: 5px;
  }

  .base-el-col {
    padding-left: 8px;
    padding-right: 8px;
    boder: 1px solid #f1f2f3;
  }

  .el-tag.header_tag {
    padding: 0 5px;
    line-height: 19px;
    height: 20px;
    background-color: #fff1f0;
    border-color: #ffa39e;
    color: #f5222d;
    border-width: 1px;
    border-style: solid;
    border-radius: 4px;
    box-sizing: border-box;
    white-space: nowrap;
  }
</style>
