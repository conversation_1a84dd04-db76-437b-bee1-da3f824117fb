<template>
  <vab-chart
    :option="option"
    auto-resize
    class="box-vabchart"
    theme="vab-echarts-theme"
  ></vab-chart>
</template>

<script>
  import VabChart from '@/plugins/echarts'

  export default {
    name: 'ModuleE<PERSON>row',
    components: { VabChart },
    props: {
      option: {
        type: Object,
        required: true,
      },
    },
  }
</script>

<style>
  .box-vabchart {
    height: 300px;
    overflow: visible;
  }
</style>
