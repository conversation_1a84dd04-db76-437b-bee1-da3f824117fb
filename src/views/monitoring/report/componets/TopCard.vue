<script>
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'
  import 'element-ui/lib/theme-chalk/index.css'
  import { commonEChartsOptions } from '@/views/monitoring/report/componets/commonEchartsOptions'

  export default {
    name: 'TopCard',
    components: { CardChart },
    props: {
      AnalysisWeekly: Object,
      AnalysisAll: Array,
    },
    data() {
      return {
        activeName: 'PROJECT',
        userType: {
          USER: 'userupload_stat',
          URL: 'url_stat',
          PROJECT: 'cc_project_info',
        },
        columnType: {
          businessLine: 'cc_project_info',
          albumId: 'userupload_stat',
          url: 'url_stat',
        },
        dialogVisible: false,
        dialogOption: null,
        currentColumnProp: null,
      }
    },

    computed: {
      tagItems() {
        return (value) => {
          return this.formatTopNum(value)
        }
      },
      tableData() {
        return (column_name) => {
          if (this.AnalysisWeekly && this.AnalysisWeekly[column_name]) {
            return this.AnalysisWeekly[column_name]
          }
          return []
        }
      },

      columns() {
        return (column_name) => {
          let labelMappings = {}
          if (column_name === 'userupload_stat') {
            labelMappings = {
              albumId: '相册ID',
              uploadedFiles: '文件上传数',
              totalFileSize: '消耗空间/GB',
              topNum: '变化',
            }
          } else if (column_name === 'url_stat') {
            labelMappings = {
              url: '请求url',
              traffic: '流量/GB',
              responseTime: '响应时间',
              requestCount: '请求量',
              topNum: '变化',
            }
          } else if (column_name === 'cc_project_info') {
            labelMappings = {
              businessLine: '服务',
              serviceCount: '服务数',
              consumerCount: '消费者',
              PodsCount: 'Pods',
              cpu: 'cpu/C',
              memory: '内存/GB',
            }
          }
          if (this.AnalysisWeekly[column_name]) {
            const dataKeys = Object.keys(this.AnalysisWeekly[column_name][0])
            dataKeys.sort((a, b) => {
              return (
                Object.keys(labelMappings).indexOf(a) -
                Object.keys(labelMappings).indexOf(b)
              )
            })
            return dataKeys.map((key) => ({
              label: labelMappings[key] || key,
              prop: key,
            }))
          }
          return []
        }
      },

      dynamicDialogTitle() {
        if (this.currentColumnProp) {
          return `${this.currentColumnProp} 变化趋势`
        }
        return ''
      },
    },
    methods: {
      tableRowColor({ rowIndex }) {
        if (rowIndex % 2 === 0) {
          return 'even-row'
        }
      },

      openDiaLog(columnProp, line_name) {
        this.currentColumnProp = `${columnProp.label}:${line_name}`
        console.log(this.currentColumnProp)
        const column_name = this.columnType[columnProp.prop]
        const lineData = [...this.AnalysisAll]
          .sort((a, b) => {
            if (a.year !== b.year) {
              return a.year - b.year
            }

            return a.week - b.week
          })
          .map((item) => ({
            week: item.week,
            value: item[column_name],
          }))
          .reduce((accumulator, currentItem) => {
            const filteredValue = currentItem.value.find(
              (data) => data[columnProp.prop] === line_name
            )
            if (filteredValue) {
              accumulator.push({
                week: currentItem.week,
                ...filteredValue,
              })
            }
            return accumulator
          }, [])

        const fields = Object.keys(lineData[0]).filter(
          (key) => key !== 'week' && key !== columnProp.prop
        )
        const legendData = []
        fields.forEach((field) => {
          legendData.push(field)
        })
        const series = fields.map((field) => {
          return {
            name: field,
            type: 'line',
            stack: 'Total',
            data: lineData.map((item) => item[field]),
            label: {
              show: false,
            },

            emphasis: {
              focus: 'series',
              label: {
                show: false,
              },
            },
          }
        })
        const rowDataSeries = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          toolbox: {
            left: 'left',
            top: 'middle',
            orient: 'vertical',
            showTitle: false,
            ...commonEChartsOptions.toolbox,
          },
          textStyle: {
            fontFamily: 'sans-serif',
            fontSize: 12,
          },

          legend: {
            itemGap: 1,
            scroll: true,
            data: legendData,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
            top: 80,
          },
          xAxis: {
            type: 'category',
            data: lineData.map((item) => item.week),
          },
          yAxis: {
            type: 'value',
          },
          series: series,
        }
        this.dialogOption = rowDataSeries

        this.dialogVisible = true
      },

      closeDialog() {
        this.dialogVisible = false
        this.currentColumnProp = null
      },

      formatTopNum(value) {
        const svgHeight = '20'
        if (value === 0) {
          return '<svg t="1698830400093" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" p-id="1832" id="mx_n_1698830400094" width="25" height="25"><path d="M512 64C264.58 64 64 264.57 64 512s200.************** 448-200.58 448-448S759.42 64 512 64z m214.63 470.62L568.24 693A32 32 0 0 1 523 647.76L626.75 544H320a32 32 0 0 1 0-64h306.75L523 376.23A32 32 0 0 1 568.24 331l158.39 158.37c0.06 0.06 0.07 0.14 0.13 0.19a31.94 31.94 0 0 1 6.77 34.67 31.79 31.79 0 0 1-6.77 10.2c-0.06 0.07-0.07 0.14-0.13 0.19z" fill="#bfbfbf" p-id="1833"></path></svg>'
        } else if (value === 'nano') {
          return `<svg t="1698830734880" class="icon" viewBox="0 0 1536 1024" xmlns="http://www.w3.org/2000/svg" p-id="25066" width="25" height="25"><path d="M729.6 443.744c19.104 0 34.144-15.008 34.144-34.144s-15.008-34.144-34.144-34.144h-136.544c-19.104 0-34.144 15.008-34.144 34.144v273.056c0 19.104 15.008 34.144 34.144 34.144H729.6c19.104 0 34.144-15.008 34.144-34.144s-15.008-34.144-34.144-34.144h-102.4v-68.256h102.4c19.104 0 34.144-15.008 34.144-34.144s-15.008-34.144-34.144-34.144h-102.4v-68.256h102.4z m-273.056-66.24c-19.104 0-34.144 15.008-34.144 33.44v169.312l-143.36-189.088c-8.864-12.288-23.904-17.056-38.24-12.288a34.464 34.464 0 0 0-23.2 32.096v271.712c0 18.432 15.008 34.144 34.144 34.144s34.144-15.712 34.144-34.144v-169.984l143.36 189.792c6.816 8.864 17.056 14.336 27.296 14.336a30.496 30.496 0 0 0 10.912-2.048c13.664-4.768 23.2-17.76 23.2-32.096v-271.712c0-18.432-15.008-33.44-34.144-33.44z m921.6-206.848h-1228.8A136.96 136.96 0 0 0 12.8 307.2v477.856A136.96 136.96 0 0 0 149.344 921.6h1228.8a136.96 136.96 0 0 0 136.544-136.544V307.2a136.96 136.96 0 0 0-136.544-136.544z m68.256 614.4a68.48 68.48 0 0 1-68.256 68.256h-1228.8a68.48 68.48 0 0 1-68.256-68.256V307.2a68.48 68.48 0 0 1 68.256-68.256h1228.8A68.48 68.48 0 0 1 1446.4 307.2v477.856z m-167.264-409.6c-17.056-6.144-36.864 2.048-43.008 19.808l-69.632 184.32-69.632-184.32c-9.568-25.952-53.248-25.952-62.816 0l-69.632 184.32-69.632-184.32c-6.144-17.76-25.248-26.624-43.008-19.808s-25.952 25.952-19.808 43.008l101.024 267.616c9.568 25.952 53.248 25.952 62.816 0l69.632-184.32 69.632 184.32c4.768 12.96 17.76 21.856 31.392 21.856 14.336 0 26.624-8.864 31.392-21.856l101.024-267.616a32.576 32.576 0 0 0-19.808-43.008z" fill="#1296db" p-id="25067"></path></svg>`
        } else if (value > 0) {
          return `<div style="display: flex; align-items:center;"><svg t="1698830134958" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" p-id="951" width="25" height="25"><path d="M512 64C264.58 64 64 264.57 64 512s200.************** 448-200.58 448-448S759.42 64 512 64z m202 523.65a32 32 0 0 1-45.25 0L510.4 429.32 352.08 587.65a32 32 0 0 1-45.25-45.26L487.4 361.82c0.13-0.12 0.16-0.31 0.3-0.45a32.21 32.21 0 0 1 45.4 0c0.14 0.14 0.17 0.33 0.3 0.45L714 542.39a32 32 0 0 1 0 45.26z" fill="#d81e06" p-id="952"></path></svg><span style="margin-left: 5px;font-size: ${svgHeight};">${value}</span></div>`
        } else {
          return `<div style="display: flex; align-items:center;"><svg t="1698830163278" class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" p-id="1213" width="25" height="25"><path d="M512 64C264.58 64 64 264.57 64 512s200.************** 448-200.58 448-448S759.42 64 512 64z m203.53 417.61L535 662.17c-0.13 0.14-0.17 0.33-0.3 0.45a32.21 32.21 0 0 1-45.4 0c-0.14-0.12-0.18-0.31-0.31-0.45l-180.6-180.56a32 32 0 0 1 45.25-45.25L512 594.67l158.27-158.31a32 32 0 0 1 45.26 45.25z" fill="#1296db" p-id="1214"></path></svg><span style="margin-left: 5px;font-size: ${svgHeight};">${Math.abs(
            value
          )}</span></div>`
        }
      },

      calculateColumnWidth(column) {
        if (['businessLine', 'albumId', 'url'].includes(column.prop)) {
          return '150px'
        } else {
          return ''
        }
      },
    },
  }
</script>

<template>
  <div class="sandbox-container__content">
    <el-row
      v-for="name in ['URL', 'USER', 'PROJECT']"
      :key="name"
      :gutter="20"
      class="flex-row"
      style="flex-direction: column"
    >
      <el-col class="flex-col">
        <el-card class="bold-black" style="width: 100%">
          <div slot="header" class="clearfix">
            <span>
              {{ name !== 'PROJECT' ? name : 'Project Info' }}
            </span>
          </div>

          <el-table
            :data="tableData(userType[name])"
            :default-sort="{ prop: 'date', order: 'descending' }"
            :row-class-name="tableRowColor"
            stripe
            style="width: 100%"
            width="360"
          >
            <el-table-column
              v-for="column in columns(userType[name])"
              v-if="column.prop != 'topRank'"
              :key="column.prop"
              :label="column.label"
              :min-width="calculateColumnWidth(column)"
              :prop="column.prop"
              sortable
            >
              <template #default="scope">
                <div
                  v-if="
                    ['businessLine', 'albumId', 'url'].includes(column.prop)
                  "
                >
                  <a @click="openDiaLog(column, scope.row[column.prop])">
                    {{ scope.row[column.prop] }}
                  </a>
                  <el-dialog
                    :modal="false"
                    :modal-append-to-body="false"
                    :title="dynamicDialogTitle"
                    :visible="dialogVisible"
                    @close="closeDialog()"
                  >
                    <template #default>
                      <card-chart :option="dialogOption"></card-chart>
                    </template>
                  </el-dialog>
                </div>

                <div
                  v-else-if="column.prop === 'topNum'"
                  class="tag-group"
                  v-html="tagItems(scope.row[column.prop])"
                ></div>

                <div v-else>{{ scope.row[column.prop] }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .bold-black {
    font-weight: bold;
    color: black;
  }

  .sandbox-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .flex-col {
    flex-grow: 1;
    flex-basis: 0;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .el-table .even-row {
    background: rgb(249, 227, 226);
  }
</style>
