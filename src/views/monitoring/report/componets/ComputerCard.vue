<script>
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'

  export default {
    name: 'ComputerCard',
    components: { Card<PERSON><PERSON> },
    props: {
      AnalysisWeekly: Object,
      OwnerCost: Array,
      CostOptions: Function,
      OtherOption: Function,
    },
    data() {
      return {
        owner_id: ['qiniu', 'tx'],
        type_id: ['service', 'user'],
        cloudProviderMap: {
          ali: '阿里云',
          tx: '腾讯云',
        },
      }
    },
    methods: {
      formatCloudProviderName(row) {
        return this.cloudProviderMap[row.name] || row.name
      },
      getPercentageClass(percent) {
        if (percent.includes('-')) {
          return 'green-number'
        } else {
          return 'red-number'
        }
      },
      tableRowClassName({ row, rowIndex, data }) {
        const totalRows = data ? data.length : 0
        const warningRowIndex = 1
        const successRowIndex = 3
        if (totalRows === 0) {
          if (rowIndex === warningRowIndex) {
            return 'warning-row'
          } else if (rowIndex === successRowIndex) {
            return 'success-row'
          }
        } else {
          if (rowIndex % (totalRows + 1) === warningRowIndex) {
            return 'warning-row'
          } else if (rowIndex % (totalRows + 1) === successRowIndex) {
            return 'success-row'
          }
        }
        return ''
      },
      tableRowColor({ rowIndex }) {
        if (rowIndex % 2 === 0) {
          return 'success-row'
        }
      },
    },
  }
</script>

<template>
  <div class="sandbox-container__content">
    <el-row class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix">
            <div style="display: flex">
              <div style="flex: 1; box-sizing: border-box">CVM</div>
              <span>
                <el-tag class="header_tag" size="mini">cvm</el-tag>
              </span>
            </div>
          </div>
          <div style="display: flex; align-items: center">
            <div class="red-number-span" style="font-size: 30px !important">
              ¥
              {{
                AnalysisWeekly.cc_vm_tx_cost
                  ? AnalysisWeekly.cc_vm_tx_cost.toLocaleString()
                  : '0'
              }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col
        v-for="(item, index) in OwnerCost"
        v-if="item.name != 'all' && item.name != 'qiniu'"
        :key="index"
        class="base-el-col"
      >
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix">
            <div style="display: flex">
              <div style="flex: 1; box-sizing: border-box">
                {{ formatCloudProviderName(item) }}
              </div>
              <span>
                <el-tag class="header_tag" size="mini">{{ item.name }}</el-tag>
              </span>
            </div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              ¥ {{ item.cost ? item.cost.toLocaleString() : '0' }}
            </div>
            <span :class="getPercentageClass(item.percent)">
              {{ item.percent }}
            </span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="flex-row mb15" style="flex-direction: column">
      <div style="flex: 1 1 auto">
        <el-col v-for="item in ['cpu', 'cost', 'ram']" :key="item" :span="8">
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ item }}
                </div>
                <span
                  class="clearfix bold-black_span"
                  style="display: flex; justify-content: space-between"
                >
                  <template v-if="item === 'cost'">
                    ¥{{ AnalysisWeekly[`cc_vm_tx_${item}`] }}
                  </template>
                  <template v-else-if="item === 'ram'">
                    {{ AnalysisWeekly[`cc_vm_tx_${item}`] }}GB
                  </template>
                  <template v-else>
                    {{ AnalysisWeekly[`cc_vm_tx_${item}`] }}
                  </template>
                </span>
              </div>
            </div>
            <card-chart :option="CostOptions(`cc_vm_tx_${item}`)"></card-chart>
          </el-card>
        </el-col>
      </div>
    </el-row>

    <el-row
      v-for="owner in ['tx', 'ali']"
      :key="owner"
      :gutter="20"
      class="flex-row mb15"
      style="flex-direction: column"
    >
      <div style="flex: 1 1 auto">
        <el-col :span="6" style="padding-right: 8px; padding-left: 8px">
          <el-card class="table-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ owner === 'tx' ? '腾讯云排行' : '阿里云排行' }}
                </div>
              </div>
            </div>
            <el-table
              :data="AnalysisWeekly[`cc_saas_${owner}`]"
              :row-class-name="tableRowColor"
              max-height="300"
              style="width: 100%"
              width="360"
            >
              <el-table-column
                label="产品"
                prop="name"
                width="150"
              ></el-table-column>
              <el-table-column
                label="费用"
                prop="value"
                width="150"
              ></el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="18" style="padding-right: 8px; padding-left: 8px">
          <card-chart :option="OtherOption(`${owner}`)"></card-chart>
        </el-col>
      </div>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .sandbox-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    &__content {
      h2 {
        font-size: 16px;
      }
    }
  }

  .bold-black {
    font-weight: bold;
    color: black;
  }

  .bold-black_span {
    font-weight: bold;
    color: black;
    margin-left: 10px;
  }

  .base-el-col {
    padding-left: 8px;
    padding-right: 8px;
    boder: 1px solid #f1f2f3;
  }

  .mb15 {
    margin-left: 8px !important;
    margin-right: -8px !important;
    margin-top: 15px !important;
    margin-buttom: 15px !important;
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: space-between;
  }

  .table-card {
    flex: 2;
    margin-right: 20px;
    max-width: 360px;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .red-number-span {
    font-size: 30px !important;
    color: #ffa39e;
    font-weight: bold;
  }

  .green-number {
    color: rgb(102, 144, 249);
    font-weight: bold;
    font-size: 15px !important;
    margin-left: 5px;
  }

  .red-number {
    color: rgb(255, 100, 98);
    font-weight: bold;
    font-size: 15px !important;
    margin-left: 10px;
  }
</style>
