// echartsConfig.js

export const commonEChartsOptions = {
  yAxis: {
    axisLabel: {
      fontSize: 10,
      margin: 8,
    },
  },
  toolbox: {
    show: true,
    feature: {
      dataZoom: {
        yAxisIndex: 'none',
      },
      dataView: { readOnly: false },
      magicType: { type: ['line', 'bar', 'stack'] },
      saveAsImage: {},
    },
  },
  textStyle: {
    fontFamily: 'sans-serif',
    fontSize: 12,
  },
}
