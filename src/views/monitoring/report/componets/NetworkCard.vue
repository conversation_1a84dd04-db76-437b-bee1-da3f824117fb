<script>
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'

  export default {
    components: { CardChart },
    props: {
      AnalysisWeekly: Object,
      NetTotalCost: Number,
      CostOptions: Function,
      NetTraffic: Function,
    },
    data() {
      return {
        owner_id: ['qiniu', 'tx'],
        type_id: ['service', 'user'],
      }
    },
  }
</script>

<template>
  <div class="sandbox-container__content">
    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="bold-black">
            <div style="flex: 1; box-sizing: border-box">总计</div>
          </div>
          <div style="display: flex; align-items: center">
            <div class="red-number" style="font-size: 30px !important">
              ¥ {{ NetTotalCost ? NetTotalCost.toLocaleString() : '0' }}
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col v-for="owner in owner_id" :key="owner" class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix bold-black">
            <div style="flex: 1; box-sizing: border-box">
              {{ owner === 'qiniu' ? '七牛CDN' : '腾讯CDN' }}
            </div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              ¥
              {{
                AnalysisWeekly[`net_cdn_${owner}_cost`]
                  ? AnalysisWeekly[`net_cdn_${owner}_cost`].toLocaleString()
                  : '0'
              }}
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col v-for="owner in type_id" :key="owner" class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix bold-black">
            <div style="flex: 1; box-sizing: border-box">
              {{ owner === 'service' ? '服务流量' : '用户流量' }}
            </div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              ¥
              {{
                AnalysisWeekly[`net_${owner}_traffic_cost`]
                  ? AnalysisWeekly[`net_${owner}_traffic_cost`].toLocaleString()
                  : '0'
              }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row
      :gutter="20"
      class="flex-row mb15"
      style="flex-direction: column; !important;"
    >
      <div style="flex: 1 1 auto">
        <el-col
          v-for="owner in owner_id"
          :key="owner"
          :span="12"
          style="padding-right: -8px; padding-left: 8px"
        >
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ owner === 'qiniu' ? '七牛 CDN' : '腾讯 CDN' }}
                </div>
                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span class="bold-black_span">
                    容量: {{ AnalysisWeekly[`net_cdn_${owner}`] }} GB
                  </span>
                </span>
              </div>
            </div>
            <card-chart
              :option="CostOptions(`net_cdn_${owner}_cost`)"
            ></card-chart>
          </el-card>
        </el-col>

        <el-col
          v-for="owner in type_id"
          :key="owner"
          :span="12"
          style="padding-right: -8px; padding-left: 8px"
        >
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ owner === 'service' ? '服务流量' : '用户流量' }}
                </div>
                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span class="bold-black_span">
                    {{ NetTraffic(AnalysisWeekly[`net_${owner}_traffic`]) }} TB
                  </span>
                </span>
              </div>
            </div>
            <card-chart
              :option="CostOptions(`net_${owner}_traffic_cost`)"
            ></card-chart>
          </el-card>
        </el-col>

        <el-col :span="12" style="padding-right: -8px; padding-left: 8px">
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">日QPS</div>
                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span class="bold-black_span">
                    {{ AnalysisWeekly.net_daily_qps }}
                  </span>
                </span>
              </div>
            </div>
            <card-chart :option="CostOptions('net_daily_qps')"></card-chart>
          </el-card>
        </el-col>

        <el-col :span="12" style="padding-right: -8px; padding-left: 8px">
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">用户带宽</div>
                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span class="bold-black_span">
                    {{ AnalysisWeekly.net_user_bandwidth }} GB
                  </span>
                </span>
              </div>
            </div>
            <card-chart
              :option="CostOptions('net_user_bandwidth')"
            ></card-chart>
          </el-card>
        </el-col>
      </div>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .bold-black {
    font-weight: bold;
    color: black;
  }

  .bold-black_span {
    font-weight: bold;
    color: black;
    margin-left: 10px;
  }

  .sandbox-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    &__content {
      h2 {
        font-size: 16px;
      }
    }
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .red-number {
    font-size: 30px !important;
    color: #ffa39e;
    font-weight: bold;
  }

  .mb15 {
    margin-left: 8px !important;
    margin-right: -8px !important;
    margin-top: 15px !important;
    margin-buttom: 15px !important;
  }

  .base-el-col {
    padding-left: 8px;
    padding-right: 8px;
    boder: 1px solid #f1f2f3;
  }
</style>
