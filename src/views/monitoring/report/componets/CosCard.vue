<script>
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'

  export default {
    name: 'CosCard',
    components: { Card<PERSON><PERSON> },
    props: {
      CosData: Array,
      CostOptions: Function,
      AnalysisWeekly: Object,
    },
    computed: {
      cos_qiniu_api() {
        return (this.AnalysisWeekly.cos_qiniu_api / 1000).toFixed(1)
      },
      cos_all() {
        const cos_cost = this.CosData.reduce(
          (acc, item) => acc + Number(item.cost),
          0
        )
        return cos_cost + this.AnalysisWeekly.cos_qiniu_api_cost
      },
    },
  }
</script>

<template>
  <div class="sandbox-container__content">
    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="bold-black">
            <div style="flex: 1; box-sizing: border-box">总计</div>
          </div>
          <div style="display: flex; align-items: center">
            <div class="red-number" style="font-size: 30px !important">
              ¥{{ cos_all ? cos_all.toLocaleString() : '0' }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-for="(item, index) in CosData" :key="index" class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix bold-black">
            <div style="flex: 1; box-sizing: border-box">
              {{ item.title }}
            </div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              ¥ {{ item.cost ? item.cost.toLocaleString() : '0' }}
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix bold-black">
            <div style="flex: 1; box-sizing: border-box">回源流量</div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              {{ cos_qiniu_api ? cos_qiniu_api.toLocaleString() : 0 }} TB
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col class="base-el-col">
        <el-card
          :body-style="{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }"
          shadow="hover"
          style="height: 130px"
        >
          <div slot="header" class="clearfix bold-black">
            <div style="flex: 1; box-sizing: border-box">回源花费</div>
          </div>
          <div style="display: flex; align-items: center">
            <div style="font-size: 30px !important">
              ¥
              {{
                AnalysisWeekly.cos_qiniu_api_cost
                  ? AnalysisWeekly.cos_qiniu_api_cost.toLocaleString()
                  : '0'
              }}
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row
      :gutter="20"
      class="flex-row mb15"
      style="flex-direction: column; !important;"
    >
      <div style="flex: 1 1 auto">
        <el-col
          v-for="item in CosData"
          :key="item"
          :span="12"
          style="padding-right: -8px; padding-left: 8px"
        >
          <el-card class="box-card">
            <div
              slot="header"
              class="bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">
                  {{ item.title }}
                </div>

                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span class="bold-black_span">
                    存量： {{ item.file }} 亿 {{ item.size }} TB
                  </span>
                  <span class="bold-black_span">
                    日增：{{ item.dailyNum }} 万 {{ item.dailySize }} GB
                  </span>
                  <span v-if="item.type === 'qiniu'" class="bold-black_span">
                    回源：{{ item.api }} TB
                  </span>
                </span>
              </div>
            </div>
            <card-chart
              :option="CostOptions(`cos_${item.type}_cost`)"
            ></card-chart>
          </el-card>
        </el-col>
        <el-col :span="12" style="padding-right: -8px; padding-left: 8px">
          <el-card class="box-card">
            <div
              slot="header"
              class="clearfix bold-black"
              style="
                display: flex;
                justify-content: space-between;
                flex-direction: column;
              "
            >
              <div style="display: flex">
                <div style="flex: 1; box-sizing: border-box">读写回源</div>
                <span
                  class="clearfix"
                  style="display: flex; justify-content: space-between"
                >
                  <span class="bold-black_span">
                    用量：{{ cos_qiniu_api }} TB
                  </span>
                  <span class="bold-black_span">
                    Cost：¥{{ AnalysisWeekly.cos_qiniu_api_cost }}
                  </span>
                </span>
              </div>
            </div>
            <card-chart
              :option="CostOptions(`cos_qiniu_api_cost`)"
            ></card-chart>
          </el-card>
        </el-col>
      </div>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .bold-black {
    font-weight: bold;
    color: black;
  }

  .bold-black_span {
    font-weight: bold;
    color: black;
    margin-left: 10px;
  }

  .flex-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .el-table .warning-row {
    background: oldlace;
  }

  .el-table .success-row {
    background: #f0f9eb;
  }

  .red-number {
    font-size: 30px !important;
    color: #ffa39e;
    font-weight: bold;
  }

  .mb15 {
    margin-left: 8px !important;
    margin-right: -8px !important;
    margin-top: 15px !important;
    margin-buttom: 15px !important;
  }

  .base-el-col {
    padding-left: 8px;
    padding-right: 8px;
    boder: 1px solid #f1f2f3;
  }
</style>
