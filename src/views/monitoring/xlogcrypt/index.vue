<template>
  <div class="roleManagement-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          上传xlog 文件
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              clearable
              placeholder="请输入查询条件"
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="fetchData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="xloglist"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column
        label="ID"
        prop="id"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="文件名"
        prop="filename"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="上传人"
        prop="owner"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="上传时间"
        prop="create_time"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="状态" prop="status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag :type="statusType(row.status)">
            {{ statusMap(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" show-overflow-tooltip>
        <template #default="{ row }">
          <el-button
            v-if="row.status === 1"
            type="text"
            @click="handleView(row)"
          >
            详情
          </el-button>

          <el-button
            v-if="row.status === 1"
            type="text"
            @click="XLogFileDownload(row)"
          >
            下载
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <edit ref="edit" @fetch-data="fetchData"></edit>
  </div>
</template>

<script>
  import Edit from './components/xlogUpload.vue'
  import { getXlogList, postDownloadXLogFile } from '@/api/xlog'

  export default {
    name: 'FileUpload',
    components: { Edit },
    data() {
      return {
        xloglist: [],
        listLoading: true,
        quota: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        refreshInterval: null,
      }
    },
    created() {
      this.fetchData()
      this.startAutoRefresh()
    },
    beforeDestroy() {
      this.stopAutoRefresh()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit()
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      async fetchData() {
        const { data, totalCount } = await getXlogList(this.queryForm)
        this.xloglist = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },

      statusType(status) {
        return status === 0 ? 'danger' : status === 1 ? 'success' : 'info'
      },

      statusMap(status) {
        return status === 0 ? '解析中' : status === 1 ? '解析完成' : '未知状态'
      },

      startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
          this.fetchData()
        }, 10000)
      },

      stopAutoRefresh() {
        clearInterval(this.refreshInterval)
      },

      XLogFileDownload(row) {
        this.$baseConfirm('确认下载文件吗？', null, async () => {
          const response = await postDownloadXLogFile(row.id, row.filename)
          // 创建下载链接
          const blob = new Blob([response], { type: 'text/plain' })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', row.filename)
          document.body.appendChild(link)
          link.click()
          window.URL.revokeObjectURL(url) // 释放
          document.body.removeChild(link)
          this.$baseMessage('文件下载成功！', 'success') // 上传成功提示
        })
      },
      async handleView(row) {
        console.log('xlog index: ', row)
        this.$router.push({
          name: 'xLogCryptInfo',
          params: {
            file_id: row.id,
          },
        })
      },
    },
  }
</script>
