<template>
  <el-dialog
    :visible.sync="dialogFormVisible"
    title="上传文件"
    width="580px"
    @close="close"
  >
    <el-form ref="form" v-loading="Loading" :model="form" label-width="100px">
      <el-row>
        <el-divider></el-divider>
        <el-form-item label="文件列表：">
          <el-upload
            ref="upload"
            :auto-upload="false"
            :before-upload="handleUpload"
            :data="form"
            :drag="true"
            :file-list="fileList"
            :multiple="false"
            :on-change="handleFileChange"
            :on-preview="handlePreview"
            action=""
            class="upload-demo"
          >
            <el-button slot="trigger" size="small" type="primary">
              选取或拖拽文件
            </el-button>
            <el-button
              size="small"
              style="margin-left: 10px"
              type="success"
              @click="submitUpload"
            >
              上传到服务器
            </el-button>
          </el-upload>
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="close">关 闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { postUploadXlogFile } from '@/api/xlog'

  export default {
    name: 'XlogUpload',
    data() {
      return {
        fileList: [],
        Loading: false,
        dialogFormVisible: false,
        form: {
          file: null,
        },
      }
    },
    methods: {
      showEdit() {
        this.dialogFormVisible = true
      },
      close() {
        this.fileList = []
        this.form = this.$options.data().form
        this.dialogFormVisible = false
        this.$emit('fetch-data')
      },
      handlePreview(file) {},
      handleFileChange(file) {
        const hasChineseCharacters = /[\u4e00-\u9fa5]/.test(file.name)
        if (hasChineseCharacters) {
          this.$baseMessage('文件名不能包含中文字符！', 'error')
          return false
        }

        this.fileList = [file]
        this.form.file = file.raw
      },

      handleUpload(file) {
        return true
      },
      async submitUpload() {
        if (!this.form.file) {
          this.$baseMessage('请先选择文件！', 'error')
          return
        }
        this.Loading = true
        const formData = new FormData()
        formData.append('file', this.form.file)
        try {
          await postUploadXlogFile(formData)
          this.$baseMessage('文件上传成功！', 'success')
          this.Loading = false
          this.close()
        } catch (error) {
          this.Loading = false
          this.$baseMessage('文件上传失败，请重试！', 'error')
          console.error(error)
        }
      },
    },
  }
</script>

<style lang="scss">
  .my-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .addr {
        font-size: 12px;
        color: #b4b4b4;
      }

      .highlighted .addr {
        color: #ddd;
      }
    }

    .ci-alert-list {
      margin-top: 5px;
    }
  }
</style>
