<template>
  <div class="log-table-container">
    <el-form
      :inline="true"
      :model="formFilters"
      @submit.native.prevent="applyFilters"
    >
      <el-row :gutter="24">
        <el-col v-for="column in columns" :key="column.prop" :span="4">
          <el-form-item :label="column.label" :prop="column.prop">
            <el-select
              v-model="formFilters[column.prop]"
              :placeholder="`请选择${column.label}`"
              collapse-tags
              filterable
              multiple
            >
              <el-option
                v-for="option in filters[column.prop]"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row
        :gutter="24"
        justify="space-between"
        style="margin-top: 0px"
        type="flex"
      >
        <el-col :span="6">
          <el-input
            v-model="searchKeyword"
            clearable
            placeholder="请输入关键字进行搜索"
            @input="onSearch"
          />
        </el-col>
        <el-col>
          <el-button type="primary" @click="applyFilters">应用筛选</el-button>
          <el-button type="warning" @click="refreshSearch">重载</el-button>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-table
        v-el-table-infinite-scroll="loadMoreData"
        :data="tableData"
        :height="tableHeight"
        :infinite-scroll-disabled="isLoading"
      >
        <el-table-column label="时间" min-width="100px" prop="log_time" />

        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :label="column.label"
          :min-width="column.width"
          :prop="column.prop"
        />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="text" @click="showLogDetail(scope.row)">
              详情
            </el-button>
            <el-button
              v-if="scope.row.kibanaUrl"
              type="text"
              @click="navigateToKibana(scope.row.kibanaUrl)"
            >
              Kibana查询
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <p>共 {{ totalRecords }} 条</p>
    </el-row>
    <el-drawer
      :visible.sync="isDrawerVisible"
      direction="rtl"
      size="800px"
      @close="closeDrawer"
    >
      <template #title>
        <strong>日志详情</strong>
      </template>
      <div>
        <div v-if="isJsonFormat">
          <pre class="json-content">{{ formattedLogDetails }}</pre>
        </div>
        <div v-else class="html-content" v-html="formattedLogDetails"></div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import ElTableInfiniteScroll from 'el-table-infinite-scroll'
  import { getXLogDetailsList, getXLogFilters } from '@/api/xlog'

  export default {
    name: 'XLogDetailsInfo',
    directives: {
      'el-table-infinite-scroll': ElTableInfiniteScroll,
    },
    data() {
      return {
        tableData: [],
        originalData: [],
        isLoading: false,
        isDrawerVisible: false,
        selectedLog: {},
        page: 1,
        pageSize: 10,
        file_id: null,
        searchKeyword: '',
        totalRecords: 0,
        tableHeight: `${window.innerHeight - 300}px`,
        columns: [
          { label: '级别', prop: 'log_level', filters: [] },
          { label: '类型', prop: 'log_type', filters: [] },
          { label: '方法', prop: 'method_name', filters: [], width: '160' },
          { label: '模块', prop: 'module', filters: [], width: '160' },
          { label: '进程', prop: 'process', filters: [] },
          { label: '线程', prop: 'thread', filters: [] },
        ],
        filters: {
          log_level: [],
          log_type: [],
          method_name: [],
          module: [],
          process: [],
          thread: [],
        },
        formFilters: {},
        formattedLogDetails: '',
        isJsonFormat: true,
      }
    },

    created() {
      this.isLoading = false
      this.file_id = this.$route.params.file_id
      if (this.$route.meta.title) {
        document.title = `${this.$route.meta.title}-${
          this.$route.params.file_id || 0
        }`
      }
    },

    mounted() {
      this.fetchFilters()
    },

    methods: {
      async fetchFilters() {
        const { data } = await getXLogFilters(this.file_id)
        if (data) {
          this.filters = data
          await this.loadMoreData()
        }
      },

      async loadMoreData() {
        if (this.isLoading) return
        this.isLoading = true
        const params = {
          pageNo: this.page,
          pageSize: this.pageSize,
        }

        if (this.searchKeyword || this.searchKeyword !== '') {
          params.keywords = this.searchKeyword
        }
        const filters = this.getSelectedFilters()

        try {
          const response = await getXLogDetailsList(
            params,
            filters,
            this.file_id
          )

          if (this.page === 1) {
            this.originalData = [...response.data]
          } else {
            this.originalData = [...this.originalData, ...response.data]
          }

          this.tableData = [...this.originalData]
          this.totalRecords = response.totalCount || 0

          if (response.data.length < this.pageSize) {
            this.isLoading = true
          } else {
            this.page++
            this.isLoading = false
          }
        } catch (error) {
          console.error('加载数据失败', error)
        } finally {
          this.isLoading = false
        }
      },

      getSelectedFilters() {
        return this.formFilters
      },

      applyFilters() {
        this.page = 1
        this.pageSize = 20
        this.loadMoreData()
      },

      showLogDetail(log) {
        this.selectedLog = log
        this.isDrawerVisible = true

        try {
          let parsedLogDetails = JSON.parse(this.selectedLog.log_details)

          if (typeof parsedLogDetails.data === 'string') {
            let cleanedData = parsedLogDetails.data
              .replace(/\\n/g, '')
              .replace(/\\t/g, '\t')
              .replace(/\"/g, '"')

            try {
              parsedLogDetails.data = JSON.parse(cleanedData)
            } catch (nestedError) {
              parsedLogDetails.data = cleanedData
            }
          }

          this.formattedLogDetails = JSON.stringify(parsedLogDetails, null, 2)
          this.formattedLogDetails = this.formattedLogDetails.replace(
            /\\"/g,
            "'"
          )

          this.isJsonFormat = true
        } catch (error) {
          console.error('无法解析 log_details 数据', error)

          let rawData = this.selectedLog.log_details
            .replace(/\\n/g, '<br>')
            .replace(/\\/g, '')

          this.formattedLogDetails = rawData
          this.isJsonFormat = false
        }
      },

      closeDrawer() {
        this.selectedLog = null
        this.formattedLogDetails = ''
        this.isDrawerVisible = false
      },

      onSearch() {
        this.page = 1
        ;(this.pageSize = 20), (this.tableData = [])
        this.originalData = []
        this.tableHeight = `${window.innerHeight - 300}px`
        this.loadMoreData()
      },

      refreshSearch() {
        this.page = 1
        ;(this.pageSize = 20), (this.searchKeyword = '')
        this.formFilters = {}
        this.tableHeight = `${window.innerHeight - 300}px`
        this.tableData = []
        this.originalData = []
        this.isLoading = false
        this.loadMoreData()
      },

      navigateToKibana(url) {
        window.open(url, '_blank')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .log-table-container {
    margin-top: 0px;

    ::v-deep {
      .el-table {
        height: 100%;
        overflow-y: auto;
      }
    }
  }

  .json-content {
    background-color: #333;
    color: #fff;
    margin: 10px 20px 0;
    padding: 10px;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: auto;
    border-radius: 4px;
  }

  .html-content {
    background-color: #2f2f2f;
    color: white;
    padding: 10px;
    margin: 10px 20px;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
</style>
