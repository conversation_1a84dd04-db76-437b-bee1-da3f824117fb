<template>
  <div class="kafka-container">
    <el-row :gutter="20" class="statistics-cards">
      <el-col :span="6">
        <el-card
          :body-style="{ padding: '15px' }"
          class="stat-card"
          shadow="hover"
        >
          <div class="stat-content">
            <div class="stat-icon error">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">错误总数</div>
              <div class="stat-value">{{ countError }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          :body-style="{ padding: '15px' }"
          class="stat-card"
          shadow="hover"
        >
          <div class="stat-content">
            <div class="stat-icon ignore">
              <i class="el-icon-turn-off"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">忽略错误</div>
              <div class="stat-value">{{ ignoreRulesData.termsCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          :body-style="{ padding: '15px' }"
          class="stat-card"
          shadow="hover"
        >
          <div class="stat-content">
            <div class="stat-icon history">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">历史告警</div>
              <div class="stat-value">{{ historicalAlarms }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card
          :body-style="{ padding: '15px' }"
          class="stat-card"
          shadow="hover"
        >
          <div class="stat-content">
            <div class="stat-icon active">
              <i class="el-icon-bell"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">正在告警</div>
              <div class="stat-value">{{ nowAlarms }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-tabs v-model="activeTabName" class="tabs-container">
      <el-tab-pane label="错误统计" name="error">
        <code-error
          @update:countError="updatecountError"
          @update:allErrCode="updateallErrCode"
        ></code-error>
      </el-tab-pane>

      <el-tab-pane label="告警详情" name="alert">
        <kafka-alert-fire
          @update:nowAlarms="updatenowAlarms"
          @update:historicalAlarms="updatehistoricalAlarms"
        ></kafka-alert-fire>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import NginxError from '@/views/monitoring/components/kafkaNginxError.vue'
  import CodeError from '@/views/monitoring/kafka/components/kafkaCodeError.vue'
  import kafkaAlertFire from '@/views/monitoring/kafka/components/kafkaAlertfire.vue'
  import VabChart from '@/plugins/echarts'
  import countTo from 'vue-count-to'
  import { kafkaIgnoreRules } from '@/api/monitoring'

  export default {
    name: 'KafkaError',
    components: { countTo, VabChart, NginxError, CodeError, kafkaAlertFire },
    data() {
      return {
        activeTabName: 'error',
        countError: 0,
        ignoreError: 0,
        historicalAlarms: 0,
        nowAlarms: 0,
        isDataLoaded: false,
        allErrCode: [],
        ignoreRulesData: {
          data: [],
          termsCount: 0,
        },
      }
    },
    mounted() {
      this.fetchData()
    },
    methods: {
      checkDataLoaded() {
        if (this.nowAlarms && this.historicalAlarms) {
          this.isDataLoaded = true
          this.fetchData()
        }
      },
      updatenowAlarms(val) {
        this.nowAlarms = val
        this.checkDataLoaded()
      },
      updateignoreRulesData(val) {
        this.ignoreRulesData = val
        this.checkDataLoaded()
      },
      updatehistoricalAlarms(val) {
        this.historicalAlarms = val
        this.checkDataLoaded()
      },
      updatecountError(val) {
        this.countError = val
        this.checkDataLoaded()
      },
      updateallErrCode(val) {
        this.allErrCode = val
        this.checkDataLoaded()
      },

      async fetchData() {
        try {
          const ignoreRulesResponse = await kafkaIgnoreRules()
          if (ignoreRulesResponse && ignoreRulesResponse.data) {
            this.ignoreRulesData = Object.assign(
              {},
              {
                data: ignoreRulesResponse.data,
                termsCount: ignoreRulesResponse.data.length,
              }
            )
          }
        } catch (error) {
          console.error('Error fetching data:', error)
        }
        setTimeout(() => {
          this.tableLoading = false
        }, 60)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      transition: all 0.3s;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }
    }

    .stat-content {
      display: flex;
      align-items: center;
    }

    .stat-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 24px;
        color: white;
      }

      &.error {
        background-color: #f56c6c;
      }

      &.ignore {
        background-color: #67c23a;
      }

      &.history {
        background-color: #409eff;
      }

      &.active {
        background-color: #e6a23c;
      }
    }

    .stat-info {
      flex: 1;
    }

    .stat-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 5px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #303133;
    }

    @media (max-width: 1200px) {
      .stat-content {
        .stat-icon {
          width: 40px;
          height: 40px;

          i {
            font-size: 20px;
          }
        }

        .stat-title {
          font-size: 13px;
        }

        .stat-value {
          font-size: 20px;
        }
      }
    }
  }

  .kafka-container {
    .tabs-container {
      margin-top: 10px;

      ::v-deep .el-tabs__content {
        padding: 10px 0;
      }
    }
  }
</style>
