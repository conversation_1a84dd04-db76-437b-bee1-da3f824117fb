<template>
  <div class="kafka-container">
    <el-tabs
      v-model="activeTab"
      type="border-card"
      class="custom-tabs"
      style="margin-top: 10px"
    >
      <el-tab-pane label="告警策略" name="alert">
        <kafka-alert-rule></kafka-alert-rule>
      </el-tab-pane>

      <el-tab-pane label="忽略列表" name="ignore">
        <kafka-ignore></kafka-ignore>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import kafkaIgnore from './components/kafkaIgnoreRules.vue'
  import kafkaAlertRule from './components/kafkaAlertRule.vue'

  export default {
    name: 'ErrorConfig',
    components: { kafkaIgnore, kafkaAlertRule },
    data() {
      return {
        activeTab: 'alert',
      }
    },
  }
</script>

<style lang="scss" scoped>
  .kafka-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #d6d7d8 !important;

    ::v-deep .custom-tabs {
      .el-tabs__header {
        margin: 0;
        background-color: #fff;
        padding: 5px 15px 0;
        border-bottom: 1px solid #e4e7ed;
      }

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__item {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        color: #666;
        border: none;
        margin: 0 10px;
        padding: 0 15px;

        &.is-active {
          color: #409eff;
          background-color: #fff;
          border-bottom: 2px solid #409eff;
        }

        &:hover {
          color: #409eff;
        }
      }
    }

    ::v-deep .el-tabs__content {
      padding: 20px;
      background-color: #fff;
    }

    ::v-deep .el-card {
      margin-top: 10px;
    }

    ::v-deep .el-tabs--border-card {
      box-shadow: none;
      border: none;
    }
  }
</style>
