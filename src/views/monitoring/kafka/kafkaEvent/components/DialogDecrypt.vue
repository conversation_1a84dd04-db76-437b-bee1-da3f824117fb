<template>
  <el-dialog
    ref="decryptDialog"
    :title="title"
    :visible="dialogVisible"
    heiht="400px"
    width="80%"
    @close="$emit('close')"
  >
    <template v-if="decryptedData">
      <el-row>
        <el-table :data="decryptedData">
          <el-table-column
            label="密文"
            min-width="120"
            prop="encode"
          ></el-table-column>
          <el-table-column
            label="明文"
            min-width="120"
            prop="decode"
          ></el-table-column>
        </el-table>
      </el-row>
    </template>

    <template v-if="queryData.data.length">
      <el-row>
        <el-table
          v-loading="tableLoading"
          :border="false"
          :data="queryData.data"
          :stripe="true"
          class="custom-table"
          element-loading-text="加载中...."
          size="mini"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <pre
                class="expand-content"
                v-html="formatDetails(scope.row.details)"
              ></pre>
            </template>
          </el-table-column>

          <el-table-column
            label="事件"
            prop="event"
            width="120"
          ></el-table-column>

          <el-table-column
            label="相册ID"
            min-width="100"
            prop="albumId"
          ></el-table-column>

          <el-table-column
            label="源IP"
            prop="source_ip"
            width="130"
          ></el-table-column>

          <el-table-column
            label="时间"
            prop="create_time"
            width="160"
          ></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageIndex"
          :page-size="pageSize"
          :total="queryData.totalCount"
          background
          small
          style="margin-top: 20px"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-row>
    </template>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="queryAlbum">查询</el-button>
      <el-button type="info" @click="$emit('close')">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { getKafkaEventNotifyQuery } from '@/api/monitoring'

  export default {
    props: {
      title: String,
      dialogVisible: Boolean,
      decryptedData: Array,
      queryForm: Object,
      selectedAlbumId: String,
      formatDetails: Function,
    },
    data() {
      return {
        queryData: {
          data: [],
          totalCount: null,
        },
        tableLoading: false,
        pageIndex: 1,
        pageSize: 10,
      }
    },
    methods: {
      resetQueryData() {
        this.queryData = {
          data: [],
          totalCount: null,
        }
        this.pageIndex = 1
        this.pageSize = 10
      },
      async queryAlbum(timeoutMs = 5000) {
        this.tableLoading = true
        const params = {
          startTime: this.queryForm.start_time,
          endTime: this.queryForm.end_time,
          keyword: this.selectedAlbumId,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
        }

        try {
          const { data, totalCount } = await getKafkaEventNotifyQuery(params, {
            timeout: timeoutMs,
          })

          if (data && totalCount) {
            this.queryData.data = data
            this.queryData.totalCount = totalCount
          } else {
            this.$baseMessage('查询结果为空', 'error')
          }
        } catch (error) {
          console.error('获取数据异常')
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        } finally {
          this.tableLoading = false
        }
      },
      handleSizeChange(val) {
        this.pageSize = val
        this.pageIndex = 1
        this.queryAlbum()
      },

      handleCurrentChange(val) {
        this.pageIndex = val
        this.queryAlbum()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .expand-content {
    white-space: pre-wrap;
    word-break: break-all;
    overflow-x: auto;
    max-height: 200px;
    background-color: #f9f9f9;
    padding: 20px;
  }
</style>
