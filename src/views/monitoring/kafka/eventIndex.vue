<template>
  <div class="security-dashboard">
    <el-row :gutter="24" class="mb15">
      <el-col class="base-el-col" style="margin-bottom: 20px; margin-top: 20px">
        <div class="header">
          <div class="timeSelect">
            <el-form ref="queryForm" :model="queryForm" @submit="changeData">
              <el-radio-group
                v-model="queryForm.timevalue"
                size="mini"
                style="float: left; margin-right: -1px"
                @change="handleTimeLabelChange(true)"
              >
                <el-radio-button
                  v-for="option in timeOptions"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-model="DateTimePicker"
                end-placeholder="结束日期"
                range-separator="至"
                size="mini"
                start-placeholder="开始日期"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                @change="changeFormData"
              ></el-date-picker>
            </el-form>
          </div>
          <div class="inputButtonGroup">
            <el-input
              v-model="queryForm.keyword"
              class="search-input"
              clearable
              placeholder="请输入查询事件关键字"
              @change="refreshPage()"
            />
            <el-button
              circle
              icon="el-icon-search"
              size="mini"
              style="margin-left: 20px"
              @click="changeData"
            ></el-button>
          </div>
        </div>
      </el-col>
      <el-col style="margin-bottom: 20px; margin-top: -20px">
        <el-radio-group
          v-model="selectedOption"
          size="mini"
          style="margin-top: 5px"
          @change="handleOptionChange"
        >
          <el-radio-button
            v-for="event in filterEvent"
            :key="event.value"
            :label="event.value"
            :name="event.text"
          ></el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <el-row :gutter="24" class="mb15">
      <el-col :span="18" class="content-col">
        <el-table
          v-loading="tableLoading"
          :border="false"
          :data="eventNotifyQueryData.data"
          :stripe="true"
          class="custom-table"
          element-loading-text="加载中...."
          size="mini"
        >
          <el-table-column type="expand">
            <template #default="scope">
              <pre
                class="expand-content"
                v-html="formatDetails(scope.row.details)"
              ></pre>
            </template>
          </el-table-column>

          <el-table-column
            label="事件"
            prop="event"
            width="140"
          ></el-table-column>

          <el-table-column label="相册ID" min-width="100" prop="albumId">
            <template #default="scope">
              <template v-if="!scope.row.event.includes('Error')">
                <el-col :span="18">
                  {{ scope.row.albumId }}
                </el-col>
                <el-col :span="2">
                  <el-button
                    plain
                    size="mini"
                    type="info"
                    @click="
                      decodeAlbumId(scope.row.albumId)
                      selectedAlbumId = scope.row.albumId
                    "
                  >
                    解密查询
                  </el-button>
                </el-col>
              </template>
              <template v-else></template>
            </template>
          </el-table-column>

          <el-table-column
            label="源IP"
            prop="source_ip"
            width="130"
          ></el-table-column>

          <el-table-column
            label="时间"
            prop="create_time"
            width="160"
          ></el-table-column>
        </el-table>

        <dialog-decrypt
          ref="decryptDialog"
          :decrypted-data="decryptedData"
          :dialog-visible.sync="decryptDialogVisible"
          :format-details="formatDetails"
          :query-form="queryForm"
          :selected-album-id="selectedAlbumId"
          :title="decryptionDialogTitle"
          @close="dialogClose"
        ></dialog-decrypt>

        <el-pagination
          :current-page="queryForm.pageIndex"
          :layout="layout"
          :page-size="queryForm.pageSize"
          :total="eventNotifyQueryData.totalCount"
          background
          small
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>

      <el-col
        :span="6"
        class="base-el-col"
        style="display: flex; flex-direction: column"
      >
        <el-table
          v-loading="tableLoading"
          :border="false"
          :data="eventNotifyGroup"
          :stripe="true"
          class="custom-table"
          element-loading-text="加载中...."
        >
          <el-table-column label="事件" prop="event">
            <template #default="{ row }">
              <el-button style="color: #3f9eff" type="text">
                <el-link :underline="false" type="primary">
                  {{ row.event }}
                </el-link>
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="数量" prop="count"></el-table-column>
        </el-table>

        <div v-if="eventNotifyGroup" style="flex: 1">
          <e-chart :option="initPieOption()" style="height: 300px"></e-chart>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import DialogDecrypt from '@/views/monitoring/kafka/kafkaEvent/components/DialogDecrypt.vue'
  import EChart from '@/views/security/component/echartsModel.vue'
  import moment from 'moment/moment'

  import {
    getKafkaEventNotify,
    getKafkaEventNotifyQuery,
  } from '@/api/monitoring'
  import { reject } from 'lodash'
  import { decodeAlbumIDs } from '@/api/toolbox'

  export default {
    name: 'KafkaEvent',
    components: { EChart, DialogDecrypt },
    data() {
      return {
        selectedOption: null,
        filterEvent: [],
        tableLoading: false,
        queryForm: {
          timevalue: 'day',
          keyword: '',
          start_time: '',
          end_time: '',
          pageIndex: 1,
          pageSize: 10,
          event_type: '',
        },
        DateTimePicker: [],
        timeOptions: [
          { label: '今天', value: 'day' },
          { label: '近7天', value: 'week' },
          { label: '近30天', value: 'month' },
          { label: '半年', value: 'halfYear' },
        ],
        layout: 'total, sizes, prev, pager, next, jumper',

        eventNotifyGroup: [],
        eventNotifyQueryData: {
          data: [],
          totalCount: null,
        },
        selectedAlbumId: null,
        decryptDialogVisible: false,
        decryptionDialogTitle: '解密结果',
        decryptedData: [],
      }
    },

    computed: {},
    mounted() {
      this.handleTimeLabelChange(false)
      this.refreshPage()
    },

    methods: {
      dialogClose() {
        this.decryptDialogVisible = false

        this.$refs.decryptDialog.resetQueryData()
      },
      formatDetails(text) {
        if (!text) return ''
        let formattedText = text.replace(/(.*?)(=|：)(.*?)\s*,\s*/g, '$1$2$3\n')
        formattedText = formattedText.replace(
          /,\s*token\s*=(.*)/g,
          '\ntoken=$1'
        )

        return formattedText.trim()
      },

      handleTimeLabelChange(refresh) {
        let s, e
        switch (this.queryForm.timevalue) {
          case 'day':
            s = moment().startOf('day')
            e = moment().endOf('day')
            break
          case 'week':
            s = moment().subtract(6, 'days').startOf('day')
            e = moment().endOf('day')
            break
          case 'month':
            s = moment().subtract(29, 'days').startOf('day')
            e = moment().endOf('day')
            break
          case 'halfYear':
            s = moment().startOf('month').subtract(5, 'month')
            e = moment().endOf('month')
            break
        }
        if (s && e) {
          this.queryForm.start_time = s.format('yyyy-MM-DD HH:mm:ss')
          this.queryForm.end_time = e.format('yyyy-MM-DD HH:mm:ss')
          this.DateTimePicker = [
            this.queryForm.start_time,
            this.queryForm.end_time,
          ]
        }
        if (refresh) {
          this.refreshPage()
        }
      },
      async changeFormData() {
        this.queryForm.start_time = this.DateTimePicker[0]
        this.queryForm.end_time = this.DateTimePicker[1]
        this.queryForm.timevalue = ''
        this.refreshPage()
      },

      async changeData() {
        this.refreshPage()
      },

      async decodeAlbumId(albumId) {
        try {
          const { data } = await decodeAlbumIDs('ids=' + albumId)
          this.decryptedData = [
            {
              encode: data[0].encode,
              decode: data[0].decode,
            },
          ]

          this.decryptDialogVisible = true
        } catch (error) {
          console.error('解密失败:', error)
          console.error('解密失败:', error)
          this.$baseMessage('解密失败，请重试', 'error')
        }
      },

      async eventGroup(timeoutMs = 5000) {
        try {
          const { data, filterEvent } = await getKafkaEventNotify({
            startTime: this.queryForm.start_time,
            endTime: this.queryForm.end_time,
          })
          this.eventNotifyGroup = data
          this.filterEvent = filterEvent
        } catch (error) {
          console.error('获取数据异常')
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
      },

      async getEventQuery(timeoutMs = 5000) {
        try {
          const { data, totalCount } = await getKafkaEventNotifyQuery({
            startTime: this.queryForm.start_time,
            endTime: this.queryForm.end_time,
            pageIndex: this.queryForm.pageIndex,
            pageSize: this.queryForm.pageSize,
            keyword: this.queryForm.keyword,
            event_type: this.queryForm.event_type,
          })
          this.eventNotifyQueryData.data = data
          this.eventNotifyQueryData.totalCount = totalCount
        } catch (error) {
          console.error('获取数据异常')
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
      },

      async fetchData() {
        this.queryForm.pageIndex = 1
        this.queryForm.pageSize = 10
        this.queryForm.event_type = ''
        this.selectedOption = ''
        this.tableLoading = true
        await this.eventGroup()
        await this.getEventQuery()
        this.tableLoading = false
      },

      async handleOptionChange() {
        if (this.selectedOption) {
          this.queryForm.event_type = this.selectedOption
          await this.getEventQuery()
        }
      },

      initPieOption() {
        if (this.eventNotifyGroup) {
          const options = {
            tooltip: {
              trigger: 'item',
              formatter: '{b} : {c} ({d}%)',
            },

            legend: {
              orient: 'vertical',
              left: 'right',
              top: 'middle',
              data: this.eventNotifyGroup.map((item) => item.event),
            },
            series: [
              {
                name: '事件类型',
                type: 'pie',
                radius: '60%',
                center: ['40%', '60%'],
                data: this.eventNotifyGroup.map((item) => ({
                  value: item.count,
                  name: item.event,
                })),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            ],
          }
          return options
        } else {
          return null
        }
      },

      refreshPage() {
        this.fetchData()
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.queryForm.pageIndex = 1
        this.getEventQuery()
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
        this.getEventQuery()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb15 {
    margin-top: 8px !important;
    margin-buttom: 15px !important;
    margin-left: 8px !important;
    margin-right: 8px !important;
  }

  .content-col {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .base-el-col {
    padding-left: 8px;
    padding-right: -8px;
    boder: 1px solid #f1f2f3;
  }

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
  }

  .timeSelect {
    flex: 1;
  }

  .inputButtonGroup {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .search-input {
    width: 400px;
  }

  .expand-content {
    white-space: pre-wrap;
    word-break: break-all;
    overflow-x: auto;
    max-height: 200px;
    background-color: #f9f9f9;
    padding: 20px;
  }

  .el-pagination {
    margin-top: 35px !important;
    margin-bottom: 35px !important;
  }
</style>
