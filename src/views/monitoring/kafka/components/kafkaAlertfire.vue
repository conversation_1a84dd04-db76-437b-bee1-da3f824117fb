<script>
  import Card<PERSON>hart from '@/views/monitoring/report/componets/ModuleElrow.vue'
  import Vab<PERSON>hart from '@/plugins/echarts'
  import { kafkaAlertFire, postKafkaAlertFire } from '@/api/monitoring'

  export default {
    name: 'KafkaAlertFire',
    components: {
      Vab<PERSON><PERSON>,
      CardChart,
    },

    filters: {
      statusMap(status) {
        const statusMap = {
          1: '已恢复',
          0: '告警中',
          2: '暂停告警',
        }
        return statusMap[status]
      },
    },

    data() {
      return {
        currentFilterStatus: 0,
        inputKeyword: '',
        elementLoadingText: '正在加载中...',
        tableLoading: true,
        currentPage: 1,
        pageSize: 10,
        nowAlarm: 'true',
        layout: 'total, sizes, prev, pager, next, jumper',
        historicalAlarms: 0,
        nowAlarms: 0,
        alertFirData: {
          data: [],
          termsCount: 0,
        },
      }
    },

    computed: {
      totalPages() {
        return Math.ceil(this.alertFirData.termsCount / this.pageSize)
      },
    },

    created() {},

    mounted() {
      this.fetchData()
      this.refreshTimer = setInterval(() => {
        this.queryData(true)
      }, 300000)
    },

    beforeDestroy() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
      }
    },

    methods: {
      formattedContent(content) {
        let formatted = content.trim()
        formatted = formatted.replace(/\t/g, '    ')
        formatted = formatted
          .split('\n')
          .map((line) => line.trimStart())
          .join('\n')
        formatted = formatted.replace(/\s\s+/g, ' ')
        return formatted
      },

      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger',
          P0: 'danger',
          P1: 'warning',
          P2: 'primary',
          P3: 'info',
        }
        return statusMap[status]
      },
      getStatusType(level) {
        const levelStr = this.levelToStatus(level)
        return this.statusType(levelStr) || 'info'
      },
      levelToStatus(level) {
        const statusMap = {
          0: 'P0',
          1: 'P1',
          2: 'P2',
          3: 'P3',
        }
        return statusMap[level]
      },

      setSelectRows(val) {
        this.selectRows = val
      },

      queryData(refresh) {
        if (refresh) {
          this.currentPage = 1

          this.fetchData()
        }
      },

      async fetchData() {
        const requestData = {
          BeginTime: this.BeginTime,
          ...(this.inputKeyword
            ? {
                inputKeyword: this.inputKeyword,
              }
            : {}),
          nowAlarm: this.nowAlarm,
          pageNo: this.currentPage,
          pageSize: this.pageSize,
        }

        console.log(requestData)

        this.tableLoading = true
        try {
          const alertFirResponse = await kafkaAlertFire(requestData)
          if (alertFirResponse) {
            let filteredData = alertFirResponse.data

            this.historicalAlarms = alertFirResponse.statusCounts.hisAlarms
            this.nowAlarms = alertFirResponse.statusCounts.nowAlarms
            this.$emit('update:nowAlarms', this.nowAlarms)
            this.$emit('update:historicalAlarms', this.historicalAlarms)

            this.alertFirData = Object.assign(
              {},
              {
                data: filteredData,
                termsCount: alertFirResponse.totalCount,
              }
            )
          }
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.tableLoading = false
        }
        setTimeout(() => {
          this.tableLoading = false
        }, 60)
      },

      handleSizeChange(val) {
        this.pageSize = val
        this.fetchData()
      },

      handleCurrentChange(page) {
        this.currentPage = page
        this.fetchData()
      },

      handleRadioChange() {
        this.queryData(true)
      },

      setFilterStatus(status) {
        this.tableLoading = true
        setTimeout(() => {
          this.alertFirData = null
          this.currentFilterStatus = status
          if (this.currentFilterStatus === 0) {
            this.nowAlarm = 'true'
          } else {
            this.nowAlarm = 'false'
          }
          this.fetchData()

          this.tableLoading = false
        }, 250)
      },

      async updateAlertFire(row, newStatus) {
        const payload = {
          id: row.id,
          status: newStatus,
        }
        try {
          const response = await postKafkaAlertFire(payload)
          this.$baseMessage(`${response.msg}`, 'success')
          await this.fetchData()
        } catch (error) {
          this.$baseMessage(`操作失败: ${error.response.msg}`, 'error')
        }
      },

      handleStart(row) {
        this.updateAlertFire(row, 0)
      },
      handleRecover(row) {
        this.updateAlertFire(row, 1)
      },

      handlePause(row) {
        this.updateAlertFire(row, 2)
      },
    },
  }
</script>

<template>
  <el-row :gutter="20" class="flex-row" style="flex-direction: column">
    <el-card shadow="never">
      <div slot="header" class="header-container">
        <span class="header-title">
          <el-radio-group
            v-model="currentFilterStatus"
            size="mini"
            @change="setFilterStatus"
          >
            <el-radio-button :label="0">当前</el-radio-button>
            <el-radio-button :label="1">历史</el-radio-button>
          </el-radio-group>
        </span>

        <vab-query-form class="query-form">
          <vab-query-form-right-panel :span="24">
            <el-form :inline="true" @submit.native.prevent>
              <el-form-item class="form-item">
                <el-input
                  v-model="inputKeyword"
                  clearable
                  placeholder="请输入查询关键字"
                  size="mini"
                  @change="queryData(true)"
                />
              </el-form-item>
              <el-form-item class="form-item">
                <el-button
                  circle
                  icon="el-icon-search"
                  size="mini"
                  @click="queryData(true)"
                ></el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>
      </div>

      <el-table
        :data="alertFirData.data"
        :element-loading-text="elementLoadingText"
        :loading="tableLoading"
        style="width: 100%"
        @selection-change="setSelectRows"
      >
        <el-table-column min-width="20" type="expand">
          <template #default="props">
            <el-form class="demo-table-expand" inline label-position="left">
              <el-form-item label="内容："></el-form-item>
              <el-form-item>
                <pre class="formatted-content">{{
                  formattedContent(props.row.content)
                }}</pre>
              </el-form-item>
              <el-form-item label="时间：">
                <span>{{ props.row.last_fire_at }}</span>
              </el-form-item>
              <el-form-item label="通知：">
                <span>{{ props.row.displayname }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column
          label="告警标题"
          min-width="130"
          prop="title"
        ></el-table-column>
        <el-table-column
          label="告警状态"
          min-width="80"
          prop="status"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag :type="statusType(row.status)">
              {{ row.status | statusMap }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="级别"
          prop="level"
          show-overflow-tooltip
          width="80"
        >
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.level)">
              {{ levelToStatus(row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          v-if="currentFilterStatus === 0"
          label="操作"
          min-width="140"
          show-overflow-tooltip
        >
          <template #header="{ column }">
            <span class="centered-header">{{ column.label }}</span>
          </template>

          <template #default="scope">
            <template v-if="scope.row.status === 0">
              <el-button
                size="mini"
                type="success"
                @click="handleRecover(scope.row)"
              >
                关闭告警
              </el-button>
              <el-button
                size="mini"
                type="warning"
                @click="handlePause(scope.row)"
              >
                暂停告警
              </el-button>
            </template>

            <template v-else-if="scope.row.status === 1"></template>

            <template v-else-if="scope.row.status === 2">
              <el-button
                size="mini"
                type="success"
                @click="handleRecover(scope.row)"
              >
                关闭告警
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="totalPages > 0"
        :current-page="currentPage"
        :layout="layout"
        :page-size="pageSize"
        :total="alertFirData.termsCount"
        style="margin-top: 10px"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </el-card>
  </el-row>
</template>

<style lang="scss" scoped>
  ::v-deep {
    .el-card__header {
      border-top: 0px solid #fff;
      border-bottom: 0px solid #f4f4f4;
    }
  }

  .demo-table-expand {
    font-size: 0px;
  }

  .demo-table-expand label {
    width: 10px;
    color: #99a9bf;
  }

  .demo-table-expand .el-form-item {
    margin-left: 20px;
    margin-bottom: 0;
    width: 50%;
  }

  .formatted-content {
    font-family: 'Fira Code', 'Source Code Pro', monospace;
    word-wrap: break-word;
    font-family: monospace;
    overflow: auto;
    background-color: #f5f5f5;
    /* 轻微的背景色可以增加对比度 */
    width: 100vh;
    padding: 10px;
    border-radius: 5px;
    font-size: 10px;
    margin: 0;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 20px;
  }

  .header-title {
    margin-right: auto;
    line-height: 20px;
    font-size: 14px;
    font-weight: bold;
  }

  .query-form {
    margin-right: 0;
    margin-top: 0.2%;
    margin-bottom: 0.2%;
  }

  .form-item {
    margin-bottom: 0;
  }

  .vab-query-form-right-panel .el-form-item {
    margin-bottom: 0;
  }

  .vab-query-form-right-panel .el-input__inner,
  .vab-query-form-right-panel .el-form-item .el-button {
    height: 18px;
    line-height: 18px;
    padding: 0;
    font-size: 12px;
  }

  .centered-header {
    text-align: center;
    display: block;
    width: 80%;
  }
</style>
