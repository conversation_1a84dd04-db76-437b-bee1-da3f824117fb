<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form
      ref="vForm"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="alert-form"
    >
      <el-form-item class="label-right-align" label="标题" prop="rule_name">
        <el-input
          v-model.trim="formData.rule_name"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>

      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="优先级"
            prop="priority"
          >
            <el-input-number
              v-model.trim="formData.priority"
              :max="100"
              :min="1"
              clearable
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="接收人"
            prop="receiver"
          >
            <el-select
              v-model.trim="formData.receiver"
              :filter-method="customFilter"
              class="full-width-input"
              clearable
              filterable
              multiple
            >
              <el-option
                v-for="item in filteredUsersOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="级别"
            prop="fire_level"
          >
            <el-select
              v-model.trim="formData.fire_level"
              class="full-width-input"
              clearable
            >
              <el-option
                v-for="item in selectLevel"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12" class="grid-cell"></el-col>

        <el-col :span="12" class="grid-cell">
          <el-form-item class="label-right-align" label="阈值" prop="threshold">
            <el-input
              v-model.trim="formData.threshold"
              clearable
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item class="label-right-align" label="频率" prop="fire_rate">
            <el-input
              v-model.trim="formData.fire_rate"
              clearable
              placeholder="单位：分钟"
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item class="label-right-align" label="错误码" prop="err_code">
        <el-input
          v-model.trim="formData.err_code"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="label-right-align"
        label="波动变化"
        prop="percentage_change"
      >
        <el-input
          v-model.trim="formData.percentage_change"
          clearable
          placeholder="同比波动百分比，超过该值时触发告警。如果为空表示按阈值告警"
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="label-right-align"
        label="URL模式"
        prop="url_pattern"
      >
        <el-input
          v-model.trim="formData.url_pattern"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="label-right-align"
        label="详细描述"
        prop="description"
      >
        <el-input
          v-model.trim="formData.description"
          clearable
          type="textarea"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-alert effect="light" title="说明:" type="info" class="alert-info">
      <div class="ci-alert-list">
        ○ AlertRule告警策略
        <br />
      </div>
    </el-alert>
    <div slot="footer" class="dialog-footer">
      <el-button class="dialog-btn" size="small" @click="close">
        取 消
      </el-button>
      <el-button
        class="dialog-btn"
        :readonly="isedit"
        size="small"
        type="primary"
        @click="save"
      >
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { postKafkaAlertRule } from '@/api/monitoring'

  export default {
    name: 'AlertRuleEditor',
    props: ['UsersOptions'],
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          rule_name: '',
          err_code: '',
          url_pattern: '',
          priority: 100,
          description: '',
          threshold: '',
          percentage_change: '',
          fire_level: 3,
          fire_rate: '',
          receiver: '',
        },
        rules: {
          rule_name: [
            {
              required: true,
              message: '必填项',
            },
          ],
          priority: [
            {
              required: true,
              pattern: /^[-]?\d+(\.\d+)?$/,
              trigger: ['blur', 'change'],
              message: '必须为数值',
            },
          ],
          threshold: [
            {
              pattern: /^[-]?\d+(\.\d+)?$/,
              trigger: ['blur', 'change'],
              message: '必须为数值',
            },
          ],
          fire_level: [
            {
              required: true,
              message: '必填项',
            },
          ],
        },
        selectLevel: [
          {
            label: '致命',
            value: 0,
          },
          {
            label: '严重',
            value: 1,
          },
          {
            label: '告警',
            value: 2,
          },
          {
            label: '一般',
            value: 3,
          },
        ],
        searchQuery: '',
        queryForm: {
          pageNo: 1,
          pageSize: 1000,
          userName: '',
        },
      }
    },
    computed: {
      filteredUsersOptions() {
        if (this.searchQuery.length === 0) {
          return this.UsersOptions
        } else {
          return this.UsersOptions.filter((item) => {
            return (
              item.label
                .toLowerCase()
                .includes(this.searchQuery.toLowerCase()) ||
              item.value.toLowerCase().includes(this.searchQuery.toLowerCase())
            )
          })
        }
      },
    },
    mounted() {
      this.getUserList()
    },

    methods: {
      async getUserList() {},

      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true

          const receiverUsernames = row.receiver ? row.receiver.split(',') : []
          const receiverValues = receiverUsernames
            .map(
              (username) =>
                this.UsersOptions.find((user) => user.value === username)?.value
            )
            .filter(Boolean)

          this.formData = {
            ...row,
            receiver: receiverValues,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            const formDataToSend = {
              ...this.formData,
            }

            const response = await postKafkaAlertRule(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      customFilter(queryString) {
        this.searchQuery = queryString
      },
    },
  }
</script>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .alert-form {
    position: relative;
    padding-bottom: 5px;
    margin-bottom: 5px;
  }

  .alert-info {
    padding: 10px 20px;
    margin: 0 5px 10px;
    border: none;

    ::v-deep .el-alert__title {
      font-size: 13px;
      line-height: 18px;
    }

    ::v-deep .el-alert__content {
      padding: 0;
    }

    .ci-alert-list {
      color: #666;
      font-size: 13px;
      line-height: 1.5;
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }

  .dialog-footer {
    padding: 0px 0px;
    text-align: right;
    background-color: #f8f9fa;
    margin-top: 4px;

    .dialog-btn {
      padding: 8px 20px;
      margin-left: 10px;
      border-radius: 4px;
      font-size: 13px;

      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
        transition: all 0.2s;
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding-bottom: 0;
  }
</style>
