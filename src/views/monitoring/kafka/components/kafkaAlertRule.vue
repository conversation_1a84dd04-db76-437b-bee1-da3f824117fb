<script>
  import { kafkaAlertRule } from '@/api/monitoring'
  import Edit from '@/views/monitoring/kafka/components/AlertruleEditor.vue'
  import BaseKafkaLayout from './BaseKafkaLayout.vue'
  import { getList } from '@/api/userManagement'

  export default {
    name: 'KafkaAlertRule',
    components: { Edit, BaseKafkaLayout },
    filters: {
      statusMap(status) {
        const statusMap = {
          1: '已恢复',
          0: '告警中',
          2: '暂停告警',
        }
        return statusMap[status]
      },
    },

    data() {
      return {
        inputKeyword: '',
        elementLoadingText: '正在加载...',
        tableLoading: true,
        currentPage: 1,
        pageSize: 10,
        filteredData: [],
        UsersOptions: [],
      }
    },

    computed: {
      paginatedData() {
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        return this.filteredData.slice(start, end)
      },

      totalPages() {
        return Math.ceil(this.filteredData.length / this.pageSize)
      },
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },

    watch: {
      alertFirData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
      inputKeyword() {
        this.queryData(true)
      },
    },

    created() {
      this.fetchData()
    },
    mounted() {
      this.refreshTimer = setInterval(() => {
        this.queryData(true)
      }, 300000)
    },

    methods: {
      statusType(status) {
        const statusMap = {
          致命: 'danger',
          严重: 'warning',
          警告: 'primary',
          一般: 'info',
        }
        return statusMap[status]
      },
      levelToStatus(level) {
        const statusMap = {
          0: '致命',
          1: '严重',
          2: '警告',
          3: '一般',
        }
        return statusMap[level]
      },

      getStatusType(level) {
        const levelStr = this.levelToStatus(level)
        return this.statusType(levelStr) || 'info'
      },

      queryData(refresh) {
        if (refresh) {
          this.currentPage = 1
          this.fetchData()
        }
      },
      filterData() {
        if (this.inputKeyword) {
          this.filteredData = this.alertFirData.data.filter(
            (item) =>
              (item.err_code
                ? item.err_code.includes(this.inputKeyword)
                : false) ||
              (item.rule_name
                ? item.rule_name.includes(this.inputKeyword)
                : false) ||
              (item.description
                ? item.description.includes(this.inputKeyword)
                : false)
          )
        } else {
          this.filteredData = this.alertFirData.data
        }
      },

      async fetchData() {
        this.tableLoading = true
        try {
          const alertRuleResponse = await kafkaAlertRule()
          if (alertRuleResponse && alertRuleResponse.data) {
            let filteredData = alertRuleResponse.data

            this.alertFirData = Object.assign(
              {},
              {
                data: filteredData,
                termsCount: filteredData.length,
              }
            )
          }
          this.filterData()

          const params = {
            pageNo: 1,
            pageSize: 1000,
            keyword: '',
          }
          const { data: userData } = await getList(params)
          const filteredUserData = userData.filter(
            (item) =>
              item.status &&
              !(item.access.length === 1 && item.access[0] === '') &&
              !(item.role.length === 1 && item.role[0] === '')
          )

          this.UsersOptions = filteredUserData.map((item) => ({
            label: item.displayname,
            value: item.username,
          }))

          this.UsersOptions.unshift({ label: '所有人', value: 'all' })
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.tableLoading = false
        }
        setTimeout(() => {
          this.tableLoading = false
        }, 60)
      },

      handlePageChange(page) {
        this.currentPage = page
      },

      handleSizeChange(val) {
        this.pageSize = val
        this.currentPage = 1
        this.fetchData()
      },

      handleEdit(row) {
        if (!this.$refs.edit) {
          console.error('Edit component reference not found')
          return
        }
        if (row && row.id) {
          this.$refs.edit.showEdit(row)
        } else {
          this.$refs.edit.showEdit()
        }
      },
    },
  }
</script>

<template>
  <base-kafka-layout
    :table-data="paginatedData"
    :table-loading="tableLoading"
    :element-loading-text="elementLoadingText"
    :current-page.sync="currentPage"
    :page-size="pageSize"
    :total="filteredData.length"
    :input-keyword.sync="inputKeyword"
    @add="handleEdit"
    @search="queryData(true)"
    @size-change="handleSizeChange"
    @page-change="handlePageChange"
  >
    <template #alert-content>
      ○ 告警规则表，定义了基于错误码和URL的告警规则
      <br />
      ○ 包括数量阈值、同比波动值以及时间段范围
    </template>

    <template #table-columns>
      <el-table-column
        label="告警标题"
        prop="rule_name"
        min-width="100"
      ></el-table-column>
      <el-table-column
        label="错误码"
        min-width="100"
        prop="err_code"
      ></el-table-column>
      <el-table-column
        label="优先级"
        min-width="40"
        prop="priority"
      ></el-table-column>

      <el-table-column
        label="级别"
        min-width="60"
        prop="fire_level"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.fire_level)">
            {{ levelToStatus(row.fire_level) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="通知"
        prop="displayname"
        min-width="70"
      ></el-table-column>
      <el-table-column
        label="描述"
        prop="description"
        min-width="70"
      ></el-table-column>
      <el-table-column label="操作" min-width="40" show-overflow-tooltip>
        <template #default="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </template>

    <template #editor>
      <edit
        ref="edit"
        :users-options="UsersOptions"
        @fetch-data="fetchData"
      ></edit>
    </template>
  </base-kafka-layout>
</template>

<style lang="scss" scoped>
  ::v-deep .el-table {
    th {
      background-color: #f0f6ff !important;
      color: #606266;
      font-size: 14px;
      font-weight: bold;
    }

    .cell {
      margin-left: 10px;
      font-weight: normal;
      font-size: 14px;
      color: #606266;
    }
  }

  .expand-content {
    padding: 10px 0;
  }

  .expand-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      width: 60px;
      color: #99a9bf;
      font-weight: bold;
    }

    .value {
      flex: 1;
      color: #606266;
    }
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    height: 20px;
  }

  .left-section {
    display: flex;
    align-items: center;
  }

  .right-section {
    display: flex;
    align-items: center;
  }

  .header-title {
    margin-right: auto;
    line-height: 20px;
    font-size: 14px;
    font-weight: bold;
  }

  .query-form {
    margin-right: 0;
    margin-top: 0.2%;
    margin-bottom: 0.2%;
  }

  .form-item {
    margin-bottom: 0;
  }

  .vab-query-form-right-panel .el-form-item {
    margin-bottom: 0;
  }

  .vab-query-form-right-panel .el-input__inner,
  .vab-query-form-right-panel .el-form-item .el-button {
    height: 18px;
    line-height: 18px;
    padding: 0;
    font-size: 12px;
  }

  .centered-header {
    text-align: center;
    display: block;
    width: 80%;
  }
</style>
