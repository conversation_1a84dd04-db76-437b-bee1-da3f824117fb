<script>
  import Card<PERSON>hart from '@/views/monitoring/report/componets/ModuleElrow.vue'
  import moment from 'moment'
  import V<PERSON><PERSON><PERSON> from '@/plugins/echarts'
  import {
    ExcludeIgnoreList,
    kafkaCodeError,
    kafkaCodeErrorQuery,
  } from '@/api/monitoring'
  import { Loading } from 'element-ui'
  import { commonEChartsOptions } from '@/views/monitoring/report/componets/commonEchartsOptions'

  export default {
    name: 'KafkaCodeError',

    components: { VabChart, CardChart },

    filters: {
      statusType(status) {
        const statusMap = {
          2: 'danger',
          1: 'success',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          2: '关闭',
          1: '开启',
        }
        return statusMap[status]
      },
    },

    data() {
      return {
        legendShow: false,
        popoverDisable: false,
        buttonText: '时间选择',
        formData: {
          timevalue: '',
          BeginTime: '',
          EndTime: '',
        },
        timeOptions: [
          { label: '近7天', value: 'week' },
          { label: '近30天', value: 'month' },
          { label: '半年', value: 'halfYear' },
        ],
        DateTimePicker: [],
        inputKeyword: '',
        granularity: '5m',
        BeginTime: '',
        EndTime: '',
        codeErrorData: {
          data: [],
          termsCount: 0,
          countError: 0,
        },
        elementLoadingText: '正在加载...',
        tableLoading: true,
        currentPage: 1,
        pageSize: 10,
        pageSizes: [10, 30, 50, 100],
        dialogVisible: false,
        chartLoading: false,
        dislogOption: null,
        currentRow: null,
        codeFilterData: [],
        IgnoreStatus: false,
        layout: 'sizes, prev, pager, next, jumper, total',
      }
    },

    computed: {
      paginatedData() {
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        return this.codeErrorData.data.slice(start, end)
      },

      totalPages() {
        return Math.ceil(this.codeErrorData.termsCount / this.pageSize)
      },

      getDialogTitleHtml() {
        if (!this.currentRow || !this.granularity) return ''
        const errorCode = `错误码：${this.currentRow.err_code}`
        const errorMessage = `错误消息：${this.currentRow.err_msg}`
        return `
      <div>
        <div style="font-size: 16px; font-weight: bold;">${errorCode}</div>
        <div style="font-size: 12px; margin-top: 5px;">${errorMessage}</div>
      </div>
    `
      },
    },

    watch: {
      '$route.query': {
        immediate: true,
        handler(newQuery) {
          const newKeywordParam = newQuery.keyword
            ? decodeURIComponent(newQuery.keyword)
            : ''
          const newGranularity = newQuery.granularity || ''
          if (
            newKeywordParam !== this.inputKeyword ||
            newGranularity !== this.granularity
          ) {
            this.inputKeyword = newKeywordParam
            this.granularity = newGranularity
            this.fetchData()
          }
        },
      },
    },

    mounted() {
      const { keyword, granularity } = this.$route.query
      this.inputKeyword = keyword ? decodeURIComponent(keyword) : ''
      this.granularity =
        granularity || localStorage.getItem('granularity') || '5m'
      this.fetchData()
    },

    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },

      deleteGra(refresh) {
        if (refresh) {
          this.granularity = ''
        }
      },

      queryData(refresh) {
        if (refresh) {
          this.currentPage = 1
          const query = {
            ...(this.inputKeyword
              ? { keyword: encodeURIComponent(this.inputKeyword) }
              : {}),
            ...(this.granularity ? { granularity: this.granularity } : {}),
          }
          this.$router
            .push({ path: this.$route.path, query })
            .catch((err) => {})
          this.fetchData()
        }
      },

      async fetchData() {
        const requestData = {
          BeginTime: this.BeginTime,
          ...(this.inputKeyword ? { inputKeyword: this.inputKeyword } : {}),
          ...(this.granularity ? { granularity: this.granularity } : {}),
        }

        this.tableLoading = true
        try {
          const codeErrorResponse = await kafkaCodeError(requestData)
          if (codeErrorResponse && codeErrorResponse.data) {
            this.codeErrorData = Object.assign(
              {},
              {
                data: codeErrorResponse.data,
                termsCount: codeErrorResponse.termsCount,
                countError: codeErrorResponse.countError,
                allErrCode: codeErrorResponse.allCodeError,
              }
            )
            this.$emit('update:countError', this.codeErrorData.countError)
            this.$emit('update:allErrCode', this.codeErrorData.allErrCode)
            this.currentPage = 1
            this.$nextTick(() => {
              if (this.IgnoreStatus) {
                this.excludeIgnore()
              }

              this.handlePageChange(this.currentPage)
            })
          }
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.tableLoading = false
        }
        setTimeout(() => {
          this.tableLoading = false
        }, 60)
      },

      handlePageChange(page) {
        this.currentPage = page
      },

      handleSizeChange(val) {
        this.pageSize = val
      },

      handleRadioChange() {
        this.queryData(true)
      },

      handleKeyChange(value) {
        if (value) {
          this.granularity = value
          localStorage.setItem('granularity', value)
          this.queryData(true)
        }
      },

      async excludeIgnore() {
        if (this.IgnoreStatus) {
          const requestData = this.codeErrorData
          this.tableLoading = true
          try {
            const filteredData = await ExcludeIgnoreList(requestData)
            this.codeErrorData = Object.assign(
              {},
              this.codeErrorData,
              filteredData
            )
            this.$emit('update:countError', this.codeErrorData.countError)
          } catch (error) {
            console.error('获取过滤数据时出错：', error)
          } finally {
            this.tableLoading = false
          }
        } else {
          await this.fetchData()
        }
      },

      async openDialog(row) {
        this.dislogOption = {}

        let urls = row.urls.map((url) => url.url).join(',')

        const currentTime = moment()
        const yesterdaySameTime = currentTime.clone().subtract(1, 'day')

        this.currentRow = row
        this.formData = {
          BeginTime: yesterdaySameTime.format('YYYY-MM-DD HH:mm:ss'),
          EndTime: currentTime.format('YYYY-MM-DD HH:mm:ss'),
          urls: urls,
          ...(row.err_code ? { inputKeyword: row.err_code } : {}),
          ...(this.granularity ? { granularity: this.granularity } : {}),
        }

        this.dialogVisible = true
        this.DateTimePicker = [this.formData.BeginTime, this.formData.EndTime]
        this.buttonText = `${this.formData.BeginTime} 至 ${this.formData.EndTime}`
        await this.performAsyncOperation(this.chartData(this.formData), 5000)
      },

      async handleTimeLabelChange(refresh) {
        this.dislogOption = {}
        let s, e
        switch (this.formData.timevalue) {
          case 'week':
            s = moment().subtract(6, 'days').set({
              hour: moment().hour(),
              minute: moment().minute(),
              second: moment().second(),
              millisecond: 0,
            })
            e = moment().endOf('day')
            break
          case 'month':
            s = moment().subtract(29, 'days').set({
              hour: moment().hour(),
              minute: moment().minute(),
              second: moment().second(),
              millisecond: 0,
            })
            e = moment().endOf('day')
            break
          case 'halfYear':
            s = moment().startOf('month').subtract(5, 'month').set({
              hour: moment().hour(),
              minute: moment().minute(),
              second: moment().second(),
              millisecond: 0,
            })
            e = moment().endOf('month')
            break
        }
        this.formData.BeginTime = s.format('YYYY-MM-DD HH:mm:ss')
        this.formData.EndTime = e.format('YYYY-MM-DD HH:mm:ss')

        if (refresh) {
          await this.performAsyncOperation(this.chartData(this.formData), 5000)
        }

        this.DateTimePicker = [this.formData.BeginTime, this.formData.EndTime]
        this.buttonText = `${this.formData.BeginTime} 至 ${this.formData.EndTime}`
      },

      async changeFormData() {
        this.dislogOption = {}

        this.formData.BeginTime = this.DateTimePicker[0]
        this.formData.EndTime = this.DateTimePicker[1]

        this.buttonText = `${this.formData.BeginTime} 至 ${this.formData.EndTime}`

        this.dialogVisible = true
        await this.performAsyncOperation(this.chartData(this.formData), 5000)

        this.formData.timevalue = ''
        this.popoverDisable = true
      },

      async performAsyncOperation(operation, timeoutMs = 5000) {
        this.chartLoading = true
        let loadingInstance = Loading.service({
          fullscreen: true,
          text: '拼命加载中...',
        })

        const timeout = new Promise((resolve, reject) => {
          setTimeout(() => reject(new Error('请求超时')), timeoutMs)
        })

        try {
          return Promise.race([operation, timeout])
        } catch (error) {
          loadingInstance.close()
          console.error('Error fetching data:', error)
        } finally {
          loadingInstance.close()
        }
      },

      async chartData(requestData) {
        try {
          const codeErrorResponse = await kafkaCodeErrorQuery(requestData)
          if (codeErrorResponse && codeErrorResponse.data) {
            this.codeFilterData = codeErrorResponse.data
            this.updateChartOptions()
          }
        } catch (error) {
          console.error('Error fetching data:', error)
        }
      },

      updateChartOptions() {
        const uniqueUrls = Array.from(
          new Set(this.codeFilterData.map((item) => item.url))
        )
        const allTimes = Array.from(
          new Set(this.codeFilterData.map((item) => item.time))
        ).sort((a, b) => new Date(a) - new Date(b))
        const urlsLength = uniqueUrls.length
        const series = uniqueUrls.map((url) => {
          const filteredData = this.codeFilterData.filter(
            (item) => item.url === url
          )
          const data = allTimes.map((time) => {
            const found = filteredData.find((item) => item.time === time)
            return {
              value: found ? found.count : 0,
              name: time,
            }
          })

          let minValue = Number.MAX_VALUE
          let maxValue = Number.MIN_VALUE
          let minItem = null
          let maxItem = null

          filteredData.forEach((item) => {
            if (item.value < minValue) {
              minValue = item.value
              minItem = item
            }
            if (item.value > maxValue) {
              maxValue = item.value
              maxItem = item
            }
          })
          const sessions = {
            name: url,
            type: 'line',
            smooth: true,
            stack: 'Total',
            data: data,
          }

          this.legendShow = urlsLength <= 4 ? true : false

          let markLineData = []
          if (minItem && maxItem && urlsLength <= 4) {
            const isReverse = new Date(minItem.name) > new Date(maxItem.name)
            markLineData = [
              {
                type: 'custom',
                yAxis: isReverse ? maxItem.value : minItem.value,
                xAxis: isReverse ? maxItem.name : minItem.name,
              },
              {
                type: 'custom',
                yAxis: isReverse ? minItem.value : maxItem.value,
                xAxis: isReverse ? minItem.name : maxItem.name,
              },
            ]
            sessions['markLine'] = {
              label: {
                show: true,
                position: 'middle',
                formatter: '{b}',
              },
              lineStyle: {
                type: 'dashed',
              },
              data: [markLineData],
            }
          }
          return sessions
        })

        this.dislogOption = {
          tooltip: {
            trigger: 'axis',
            confine: true,
            axisPointer: {
              type: 'line',
            },
          },

          toolbox: {
            left: 'right',
            top: 'top',
            bottom: 'top',
            orient: 'vertical',
            height: 'auto',
            width: 'auto',
            showTitle: false,
            ...commonEChartsOptions.toolbox,
          },
          textStyle: {
            fontFamily: 'sans-serif',
            fontSize: 12,
          },
          legend: {
            show: this.legendShow,
            bottom: 'auto',
            padding: 0,
            itemGap: 2,
            type: 'scroll',
            orient: 'vertical',
            scroll: false,
            data: uniqueUrls,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
            top: 20,
          },
          xAxis: {
            type: 'category',
            data: allTimes,

            axisLabel: {
              interval: 'auto',
              inside: false,
              rotate: -10,
              formatter: function (value) {
                const date = new Date(value)
                const year = date.getFullYear()
                const month = ('0' + (date.getMonth() + 1)).slice(-2)
                const day = ('0' + date.getDate()).slice(-2)
                const hour = ('0' + date.getHours()).slice(-2)
                const minute = ('0' + date.getMinutes()).slice(-2)
                return `${year}-${month}-${day} ${hour}:${minute}`
              },
            },
          },
          yAxis: {
            type: 'value',
          },
          series: series,
        }
        this.chartLoading = false
      },

      closeDialog() {
        this.dialogVisible = false
        this.chartLoading = false
        this.dislogOption = {}
        this.formData = {
          timevalue: '',
          BeginTime: '',
          EndTime: '',
          urls: '',
          ...(this.currentRow
            ? { inputKeyword: this.currentRow.err_code }
            : {}),
          ...(this.granularity ? { granularity: this.granularity } : {}),
        }
        this.DateTimePicker = []
        this.buttonText = '时间选择'
      },

      openPopover() {
        this.popoverDisable = false
      },
    },
  }
</script>

<template>
  <el-row :gutter="20" class="flex-row" style="flex-direction: column">
    <el-card shadow="never">
      <div slot="header" class="header-container">
        <vab-query-form class="query-form">
          <vab-query-form-left-panel :span="8">
            <el-radio-group
              v-model="granularity"
              size="mini"
              style="float: right"
              @change="handleRadioChange"
            >
              <el-radio-button label="5m">5m</el-radio-button>
              <el-radio-button label="10m">10m</el-radio-button>
              <el-radio-button label="30m">30m</el-radio-button>
            </el-radio-group>
          </vab-query-form-left-panel>

          <vab-query-form-left-panel :span="6">
            <el-checkbox
              v-model="IgnoreStatus"
              border
              size="mini"
              @change="excludeIgnore"
            >
              过滤忽略
            </el-checkbox>
          </vab-query-form-left-panel>

          <vab-query-form-right-panel :span="12">
            <el-form :inline="true" @submit.native.prevent>
              <el-form-item class="form-item">
                <el-input
                  v-model="inputKeyword"
                  clearable
                  placeholder="请输入查询关键字"
                  size="mini"
                  @change="handleKeyChange(granularity)"
                />
              </el-form-item>
              <el-form-item class="form-item">
                <el-button
                  circle
                  icon="el-icon-search"
                  size="mini"
                  @click="queryData(true)"
                ></el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-right-panel>
        </vab-query-form>
      </div>

      <el-table
        v-loading="tableLoading"
        :data="paginatedData"
        :element-loading-text="elementLoadingText"
        style="width: 100%"
        @selection-change="setSelectRows"
      >
        <el-table-column type="expand" width="20">
          <template #default="scope">
            <el-form class="demo-table-expand" inline label-position="left">
              <el-form-item>
                <div v-for="url in scope.row.urls">
                  <a :href="url.href" target="_blank">{{ url.url }}</a>
                </div>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>

        <el-table-column
          label="错误码"
          min-width="80"
          prop="err_code"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-button size="mini" @click="openDialog(scope.row)">
              <a>{{ scope.row.err_code }}</a>
            </el-button>
          </template>
        </el-table-column>

        <el-table-column
          label="错误消息"
          min-width="220"
          prop="err_msg"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="异常数"
          prop="sumCount"
          show-overflow-tooltip
          width="100"
        ></el-table-column>

        <el-table-column
          label="urls数量"
          prop="urls_count"
          show-overflow-tooltip
          width="100"
        ></el-table-column>
      </el-table>
      <el-pagination
        v-if="totalPages > 0"
        v-model="currentPage"
        :page-size="pageSize"
        :layout="layout"
        :total="codeErrorData.termsCount"
        style="margin-top: 10px"
        :page-sizes="pageSizes"
        :pager-count="5"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      ></el-pagination>

      <el-dialog
        :visible.sync="dialogVisible"
        width="800px"
        @close="closeDialog"
      >
        <template #title>
          <div v-html="getDialogTitleHtml"></div>
        </template>
        <el-form ref="formData" :model="formData">
          <el-form-item>
            <el-radio-group
              v-model="formData.timevalue"
              size="mini"
              style="float: left; margin-right: 10px"
              @change="handleTimeLabelChange(true)"
            >
              <el-radio-button
                v-for="option in timeOptions"
                :key="option.value"
                :label="option.value"
                size="mini"
              >
                {{ option.label }}
              </el-radio-button>

              <el-popover
                :disabled="popoverDisable"
                placement="bottom"
                style="float: right; margin-left: 10px"
                trigger="click"
                width="auto"
              >
                <template #default="content">
                  <el-date-picker
                    v-model="DateTimePicker"
                    :default-time="['00:00:00', '23:59:59']"
                    end-placeholder="结束日期"
                    range-separator="至"
                    start-placeholder="开始日期"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    @change="changeFormData"
                  ></el-date-picker>
                </template>

                <template #reference>
                  <el-button size="mini" @click="openPopover">
                    {{ buttonText }}
                  </el-button>
                </template>
              </el-popover>
            </el-radio-group>
            <el-button
              circle
              icon="el-icon-refresh-right"
              size="mini"
              style="height: 30px; margin-top: 0px; margin-left: 200px"
              @click="changeFormData"
            ></el-button>
          </el-form-item>
          <card-chart
            :loading="chartLoading"
            :option="dislogOption"
          ></card-chart>
        </el-form>
      </el-dialog>
    </el-card>
  </el-row>
</template>

<style lang="scss" scoped>
  ::v-deep {
    .el-card__header {
      border-top: 0px solid #fff;
      border-bottom: 0px solid #f4f4f4;
    }
  }

  .header-container {
    align-items: center;
    height: 0px;
    padding: 0px;
  }

  .query-form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    margin-right: 0;
  }

  .form-item {
    margin-bottom: 0;
  }

  .el-radio-group,
  .el-radio-button,
  .el-input,
  .el-button {
    height: 100%;
  }

  .demo-table-expand {
    font-size: 0;
  }

  .demo-table-expand label {
    width: 10px;
    color: #99a9bf;
  }

  .demo-table-expand .el-form-item {
    margin-left: 20px;
    margin-bottom: 0;
    width: 50%;
  }

  ::v-deep .el-pagination .el-pagination__total {
    margin-left: 40px;
  }
</style>
