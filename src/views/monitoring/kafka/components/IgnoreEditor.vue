<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" label-width="80px">
      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="错误码"
            prop="err_code"
          >
            <el-select
              v-model="formData.err_code"
              :disabled="disabled"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in allErrorCode"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item
        class="label-right-align"
        label="url关键字"
        prop="url_pattern"
      >
        <el-input
          v-model.trim="formData.url_pattern"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item class="label-right-align" label="忽略原因" prop="reason">
        <el-input
          v-model.trim="formData.reason"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-alert
      effect="light"
      title="说明:"
      type="info"
      style="margin-bottom: -10px"
    >
      <div class="ci-alert-list">
        ○ 错误码：null表示全部错误码
        <br />
        ○ url关键字：用%代表通配符
      </div>
    </el-alert>
    <div slot="footer" class="dialog-footer">
      <el-button class="dialog-btn" size="small" @click="close">
        取 消
      </el-button>
      <el-button
        class="dialog-btn"
        size="small"
        :readonly="isedit"
        type="primary"
        @click="save"
      >
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { kafkaCodeList, postkafkaIgnoreRules } from '@/api/monitoring'

  export default {
    name: 'IgnoreEditor',
    props: ['filteredData'],
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          url_pattern: '',
          err_code: '',
          reason: '',
        },
        allErrorCode: [],
      }
    },

    computed: {},
    watch: {
      filteredData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
    },

    created() {
      this.fetchData()
    },

    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true
          this.formData = {
            ...row,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            const formDataToSend = {
              ...this.formData,
            }
            const response = await postkafkaIgnoreRules(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      async fetchData() {
        const CodeErrList = await kafkaCodeList()
        if (CodeErrList && CodeErrList.data) {
          const uniqueErrorCodes = new Set(CodeErrList.data)
          this.allErrorCode = Array.from(uniqueErrorCodes).map((errCode) => ({
            label: errCode,
            value: errCode,
          }))
        }

        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
  .dialog-footer {
    padding: 0px 0px;
    text-align: right;
    background-color: #f8f9fa;
    margin-top: 4px;

    .dialog-btn {
      padding: 8px 20px;
      margin-left: 10px;
      border-radius: 4px;
      font-size: 13px;

      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
        transition: all 0.2s;
      }
    }
  }
</style>
