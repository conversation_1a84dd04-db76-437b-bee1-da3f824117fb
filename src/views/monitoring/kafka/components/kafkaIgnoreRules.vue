<script>
  import Edit from '@/views/monitoring/kafka/components/IgnoreEditor.vue'
  import BaseKafkaLayout from './BaseKafkaLayout.vue'
  import { kafkaIgnoreRules } from '@/api/monitoring'

  export default {
    name: 'KafkaIgnore',
    components: { Edit, BaseKafkaLayout },

    data() {
      return {
        currentFilterStatus: 0,
        inputKeyword: '',
        elementLoadingText: '正在加载...',
        tableLoading: true,
        currentPage: 1,
        pageSize: 10,
        filteredData: [],
      }
    },

    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },

      paginatedData() {
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        return this.filteredData.slice(start, end)
      },

      totalPages() {
        return Math.ceil(this.filteredData.length / this.pageSize)
      },
    },
    watch: {
      ignoreRulesData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
      inputKeyword() {
        this.queryData(true)
      },
    },

    created() {
      this.fetchData()
    },

    mounted() {
      this.refreshTimer = setInterval(() => {
        this.queryData(true)
      }, 300000)
    },

    methods: {
      queryData(refresh) {
        if (refresh) {
          this.currentPage = 1
          this.currentFilterStatus = 0
          this.fetchData()
        }
      },
      filterData() {
        if (this.inputKeyword) {
          this.filteredData = this.ignoreRulesData.data.filter(
            (item) =>
              item.err_code.includes(this.inputKeyword) ||
              item.url_pattern.includes(this.inputKeyword) ||
              item.reason.includes(this.inputKeyword)
          )
        } else {
          this.filteredData = this.ignoreRulesData.data
        }
      },

      handleSizeChange(val) {
        this.pageSize = val
        this.currentPage = 1
        this.fetchData()
      },

      handlePageChange(page) {
        this.currentPage = page
      },

      handleEdit(row) {
        if (!this.$refs.edit) {
          console.error('Edit component reference not found')
          return
        }
        if (row && row.id) {
          this.$refs.edit.showEdit(row)
        } else {
          this.$refs.edit.showEdit()
        }
      },

      async fetchData() {
        this.tableLoading = true
        try {
          const ignoreRulesResponse = await kafkaIgnoreRules()
          if (ignoreRulesResponse && ignoreRulesResponse.data) {
            let filteredData = ignoreRulesResponse.data

            this.ignoreRulesData = Object.assign(
              {},
              {
                data: filteredData,
                termsCount: filteredData.length,
              }
            )
          }
          this.filterData()
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.tableLoading = false
        }
        setTimeout(() => {
          this.tableLoading = false
        }, 60)
      },
    },
  }
</script>

<template>
  <base-kafka-layout
    :table-data="paginatedData"
    :table-loading="tableLoading"
    :element-loading-text="elementLoadingText"
    :current-page.sync="currentPage"
    :page-size="pageSize"
    :total="filteredData.length"
    :input-keyword.sync="inputKeyword"
    :show-alert="false"
    @add="handleEdit"
    @search="queryData(true)"
    @size-change="handleSizeChange"
    @page-change="handlePageChange"
  >
    <template #table-columns>
      <el-table-column
        label="错误码"
        style="margin-left: 10px"
        min-width="40"
        prop="err_code"
      ></el-table-column>
      <el-table-column
        label="url模式"
        min-width="130"
        prop="url_pattern"
      ></el-table-column>
      <el-table-column label="原因" min-width="130" prop="reason">
        <template #default="scope">
          <span>{{ scope.row.reason }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="40" show-overflow-tooltip>
        <template #default="scope">
          <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </template>

    <template #editor>
      <edit
        ref="edit"
        :filtered-data="filteredData"
        @fetch-data="fetchData"
      ></edit>
    </template>
  </base-kafka-layout>
</template>

<style lang="scss" scoped>
  ::v-deep .el-table {
    th {
      background-color: #f0f6ff !important;
      color: #606266;
      font-size: 14px;
      font-weight: bold;
    }

    .cell {
      margin-left: 10px;
      font-weight: normal;
      font-size: 14px;
      color: #606266;
    }
  }

  .demo-table-expand {
    font-size: 0px;
  }

  .demo-table-expand label {
    width: 10px;
    color: #99a9bf;
  }

  .demo-table-expand .el-form-item {
    margin-left: 20px;
    margin-bottom: 0;
    width: 50%;
  }

  .formatted-content {
    font-family: 'Fira Code', 'Source Code Pro', monospace;
    word-wrap: break-word;
    font-family: monospace;

    background-color: #f5f5f5;
    width: 100vh;
    padding: 10px;
    border-radius: 5px;
    font-size: 10px;
    margin: 0;
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    height: 20px;
  }

  .left-section {
    display: flex;
    align-items: center;
  }

  .right-section {
    display: flex;
    align-items: center;
  }

  .query-form {
    margin-right: 0;
    margin-top: 0.2%;
    margin-bottom: 0.2%;
  }

  .form-item {
    margin-bottom: 0;
  }

  .vab-query-form-right-panel .el-form-item {
    margin-bottom: 0;
  }

  .vab-query-form-right-panel .el-input__inner,
  .vab-query-form-right-panel .el-form-item .el-button {
    height: 18px;
    line-height: 18px;
    padding: 0;
    font-size: 12px;
  }

  .centered-header {
    text-align: center;
    display: block;
    width: 80%;
  }
</style>
