<template>
  <el-row :gutter="20" class="flex-row" style="flex-direction: column">
    <el-alert
      v-if="showAlert"
      effect="light"
      title=""
      type="info"
      style="margin-bottom: 10px"
    >
      <div class="ci-alert-list" style="font-size: 14px; line-height: 1.6">
        <slot name="alert-content"></slot>
      </div>
    </el-alert>

    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="$emit('add')"
        >
          新增
        </el-button>
      </vab-query-form-left-panel>

      <vab-query-form-right-panel :span="12">
        <el-input
          :value="inputKeyword"
          clearable
          placeholder="请输入查询关键字"
          style="width: 220px"
          @input="$emit('update:inputKeyword', $event)"
          @change="$emit('search')"
        />
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-card>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        :element-loading-text="elementLoadingText"
        style="width: 100%"
      >
        <slot name="table-columns"></slot>
      </el-table>

      <el-pagination
        v-if="totalPages > 0"
        :current-page="currentPage"
        :layout="layout"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :total="total"
        style="margin-top: 10px"
        @size-change="$emit('size-change', $event)"
        @current-change="$emit('update:currentPage', $event)"
      ></el-pagination>
    </el-card>

    <slot name="editor"></slot>
  </el-row>
</template>

<script>
  export default {
    name: 'BaseKafkaLayout',
    props: {
      tableData: {
        type: Array,
        required: true,
      },
      tableLoading: {
        type: Boolean,
        default: false,
      },
      elementLoadingText: {
        type: String,
        default: '正在加载...',
      },
      currentPage: {
        type: Number,
        default: 1,
      },
      pageSize: {
        type: Number,
        default: 10,
      },
      total: {
        type: Number,
        default: 0,
      },
      inputKeyword: {
        type: String,
        default: '',
      },
      showAlert: {
        type: Boolean,
        default: true,
      },
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
      totalPages() {
        return Math.ceil(this.total / this.pageSize)
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table {
    th {
      background-color: #f0f6ff !important;
      color: #606266;
      font-size: 14px;
      font-weight: bold;
    }

    .cell {
      margin-left: 10px;
      font-weight: normal;
      font-size: 14px;
      color: #606266;
    }
  }

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    height: 20px;
  }

  .left-section {
    display: flex;
    align-items: center;
  }

  .right-section {
    display: flex;
    align-items: center;
  }
</style>
