<template>
  <div class="monitoring-config">
    <component :is="currentComponent" />
  </div>
</template>

<script>
  import MqIndex from './rocketMQ/index.vue'
  import PpIndex from './pinpoint/index.vue'
  import ErrIndex from './kafka/ErrConfig.vue'
  import HttpIndex from './elk/elkMonitor.vue'

  export default {
    name: 'MonitoringConfig',
    components: {
      MqIndex,
      PpIndex,
      ErrIndex,
      HttpIndex,
    },
    props: {
      defaultActive: {
        type: String,
        default: 'mq',
      },
    },
    data() {
      return {
        currentComponent: null,
      }
    },
    watch: {
      defaultActive: {
        handler(newVal) {
          this.setComponent(newVal)
        },
        immediate: true,
      },
    },
    created() {
      this.setComponent(this.defaultActive)
    },
    methods: {
      setComponent(type) {
        switch (type) {
          case 'mq':
            this.currentComponent = MqIndex
            break
          case 'pinpoint':
            this.currentComponent = PpIndex
            break
          case 'errcode':
            this.currentComponent = ErrIndex
            break
          case 'http':
            this.currentComponent = HttpIndex
            break
          default:
            this.currentComponent = MqIndex
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .monitoring-config {
    height: 100%;
    background: #fff;
  }
</style>
