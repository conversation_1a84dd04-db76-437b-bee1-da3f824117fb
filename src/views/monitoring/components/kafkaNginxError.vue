<script>
  import Card<PERSON>hart from '@/views/monitoring/report/componets/ModuleElrow.vue'
  import moment from 'moment'
  // import VabChart from '@/plugins/echarts'
  import { kafkaNginxError } from '@/api/monitoring'

  export default {
    name: 'KafkaNginxError',
    components: { CardChart },
    data() {
      return {
        queryForm: {
          value2: '',
          urls: '',
        },
        granularity: '',
        pickerOptions: {
          shortcuts: [
            {
              text: '1小时',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '半天',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000 * 12)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '一天',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000 * 24)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '一个月',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(0, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
            {
              text: '三个月',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(2, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
            {
              text: '半年',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(5, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
            {
              text: '一年',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(12, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
          ],
        },
        BeginTime: '',
        EndTime: '',
        nginxErrorData: {
          data: [],
          termsCount: 0,
        },
        urls: [],
        currentPage: 1,
        pageSize: 10,
        tableLoading: true,
        chartLoading: true,
        urlErrorOption: '',
        elementLoadingText: '正在加载...',
        chatLoadingText: '点击查看,图表显示...',
      }
    },

    computed: {
      paginatedData() {
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        return this.nginxErrorData.data.slice(start, end)
      },

      totalPages() {
        return Math.ceil(this.nginxErrorData.termsCount / this.pageSize)
      },
    },
    created() {
      this.setDefaultTime()
    },

    mounted() {
      this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },

      deleteGra(refresh) {
        if (refresh) {
          this.queryForm.urls = ''
          this.granularity = ''
          this.currentPage = 1
          this.chartLoading = true
          this.chatLoadingText = '点击查看,图表显示...'
          this.fetchData()
        }
      },
      setDefaultTime() {
        const todayStart = moment()
          .startOf('week')
          .format('YYYY-MM-DD HH:mm:ss')
        const todayEnd = moment().endOf('week').format('YYYY-MM-DD HH:mm:ss')
        this.queryForm.value2 = [todayStart, todayEnd]
        this.BeginTime = this.queryForm.value2[0]
        this.EndTime = this.queryForm.value2[1]
      },

      queryData(refresh) {
        if (refresh) {
          if (this.queryForm.value2 && this.queryForm.value2.length === 2) {
            this.BeginTime = this.queryForm.value2[0]
            this.EndTime = this.queryForm.value2[1]
            this.currentPage = 1
            this.fetchData()
          }
        }
      },

      async selectUrl(row) {
        this.chartLoading = true
        this.chatLoadingText = '图像加载中...'
        const requestData = {
          BeginTime: this.BeginTime,
          EndTime: this.EndTime,
          Granularity: this.granularity,
          url: row.url,
        }

        const { data } = await kafkaNginxError(requestData)

        this.chartOption(data)

        setTimeout(() => {}, 300)
        this.chartLoading = false
      },

      async fetchData() {
        const requestData = {
          BeginTime: this.BeginTime,
          EndTime: this.EndTime,
          Granularity: this.granularity,
          url: this.queryForm.urls,
        }

        this.tableLoading = true

        try {
          const nginxErrorResponse = await kafkaNginxError(requestData)
          if (nginxErrorResponse && nginxErrorResponse.data) {
            this.nginxErrorData = Object.assign(
              {},
              {
                data: nginxErrorResponse.data,
                termsCount: nginxErrorResponse.termsCount,
              }
            )
            this.urls = nginxErrorResponse.urls
            this.currentPage = 1
            this.$nextTick(() => {
              this.handlePageChange(this.currentPage)
            })
            this.$message.success('NginxError数据加载成功！')
          }
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.tableLoading = false
        }
        setTimeout(() => {
          this.tableLoading = false
        }, 60)
      },

      handlePageChange(page) {
        this.currentPage = page
      },

      chartOption(urlData) {
        const times = urlData.map((item) => item.time)
        const values = urlData.map((item) => item.value)

        let urlErrorOption = {
          title: {
            text: urlData[0].url,
            textStyle: {
              fontSize: 12,
              lineHeight: 18,
              fontWeight: 'normal',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          tooltip: {
            trigger: 'axis',
            formatter: (params) => {
              let tooltipContent = params[0].axisValue
              params.forEach((param) => {
                tooltipContent += `<br/>${param.value}`
              })
              return tooltipContent
            },
          },
          toolbox: {
            show: true,
            feature: {
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ['line', 'bar'] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          yAxis: {
            type: 'value',
          },
          xAxis: {
            type: 'category',
            data: times,
          },
          series: [
            {
              name: urlData[0].url,
              type: 'line',
              data: values,
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' },
                ],
              },
            },
          ],
        }
        this.urlErrorOption = urlErrorOption
      },
    },
  }
</script>

<template>
  <el-row :gutter="20" class="flex-row" style="flex-direction: column">
    <el-card>
      <el-col :span="24">
        <el-card>
          <el-row :gutter="20" justify="middle">
            <el-form
              :model="queryForm"
              inline
              label-position="left"
              label-width="80px"
            >
              <el-col :span="8">
                <el-form-item label="统计粒度">
                  <el-radio-group v-model="granularity" size="small">
                    <el-radio-button label="1m">每分钟</el-radio-button>
                    <el-radio-button label="5m">5分钟</el-radio-button>
                    <el-radio-button label="15m">15分钟</el-radio-button>
                    <el-radio-button label="30m">30分钟</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="16">
                <el-form-item label="时间区间">
                  <el-date-picker
                    v-model="queryForm.value2"
                    :default-time="['00:00:00', '23:59:59']"
                    :picker-options="pickerOptions"
                    end-placeholder="结束日期"
                    range-separator="至"
                    start-placeholder="开始日期"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                  ></el-date-picker>
                </el-form-item>

                <el-form-item label="">
                  <el-select
                    v-model="queryForm.urls"
                    clearable
                    filterable
                    placeholder="请选择查询URL"
                    remote
                  >
                    <el-option
                      v-for="item in urls"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="queryData(true)"
                  >
                    查询
                  </el-button>
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-delete"
                    style="float: right; margin-left: 5px"
                    type="danger"
                    @click="deleteGra(true)"
                  >
                    重置
                  </el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </el-card>
      </el-col>
      <el-col :span="14">
        <el-card shadow="never">
          <div slot="header">
            <span><b>NginxError</b></span>
          </div>
          <el-table
            v-loading="tableLoading"
            :data="paginatedData"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <el-table-column
              label="URL"
              prop="url"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="时间"
              prop="time"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="异常数"
              prop="value"
              show-overflow-tooltip
              width="120"
            ></el-table-column>

            <el-table-column label="操作" show-overflow-tooltip width="100">
              <template #default="{ row }">
                <el-button type="danger" @click="selectUrl(row)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="totalPages > 1"
            v-model="currentPage"
            :page-size="pageSize"
            :total="nginxErrorData.termsCount"
            style="margin-top: 10px"
            @current-change="handlePageChange"
          ></el-pagination>
        </el-card>
      </el-col>
      <el-col :span="10">
        <el-alert :closable="false" title="提示:" type="warning">
          <div class="ci-alert-list">点击[查看]按钮可查看url趋势。</div>
          <div class="ci-alert-list">[粒度]选择适用于[查看]按钮。</div>

          <p />
        </el-alert>
        <p />
        <el-card
          v-loading="chartLoading"
          :element-loading-text="chatLoadingText"
          class="box-card"
          style="height: 550px"
        >
          <div slot="header">
            <span><b>url趋势</b></span>
          </div>

          <card-chart
            :option="urlErrorOption"
            style="height: 450px"
          ></card-chart>
        </el-card>
      </el-col>
    </el-card>
  </el-row>
</template>

<style lang="scss" scoped></style>
