<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="24" class="grid-cell">
          <el-form-item class="label-right-align" label="集群名" prop="name">
            <el-input
              v-model.trim="formData.name"
              :readonly="isedit"
              :disabled="disabled"
              autocomplete="off"
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="grid-cell">
          <el-form-item class="label-right-align" label="Topic" prop="topic">
            <el-input
              v-model.trim="formData.topic"
              :readonly="isedit"
              :disabled="disabled"
              autocomplete="off"
              clearable
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" class="grid-cell">
          <el-form-item class="label-right-align" label="Group" prop="group">
            <el-input
              v-model.trim="formData.group"
              :readonly="isedit"
              :disabled="disabled"
              autocomplete="off"
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="required label-right-align"
            label="指标"
            prop="metric_name"
          >
            <el-select
              v-model.trim="formData.metric_name"
              class="full-width-input"
              clearable
              filterable
            >
              <el-option
                v-for="(item, index) in metric_nameOptions"
                :key="index"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="阈值"
            prop="threshold_value"
          >
            <el-input
              v-model.trim="formData.threshold_value"
              clearable
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item
            class="label-right-align"
            label="通知方式"
            prop="notify_method"
          >
            <el-select
              v-model.trim="formData.notify_method"
              class="full-width-input"
              clearable
              multiple
            >
              <el-option
                v-for="item in selectMethod"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="状态"
            prop="alarm_status"
          >
            <el-select
              v-model.trim="formData.alarm_status"
              class="full-width-input"
              clearable
            >
              <el-option
                v-for="(item, index) in selectStatus"
                :key="index"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="业务群组" prop="business_group">
            <el-select
              v-model.trim="formData.business_group"
              :filter-method="customFilter"
              class="full-width-input"
              clearable
              filterable
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in groupOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item
            class="label-right-align"
            label="接收用户"
            prop="notify_recipient"
          >
            <el-select
              v-model.trim="formData.notify_recipient"
              :filter-method="customFilter"
              class="full-width-input"
              clearable
              filterable
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in filteredUsersOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item
        class="label-right-align"
        label="Expr"
        prop="alarm_expression"
      >
        <el-input
          v-model.trim="formData.alarm_expression"
          clearable
          :autosize="{ minRows: 2, maxRows: 10 }"
          placeholder="请输入告警表达式"
          type="textarea"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-alert title="说明:" type="info" style="margin-bottom: -10px">
      <div class="ci-alert-list">○ 没有设置群组，默认发送到【告警中心】</div>
    </el-alert>
    <div slot="footer" class="dialog-footer">
      <el-button class="dialog-btn" size="small" @click="close">
        取 消
      </el-button>
      <el-button
        class="dialog-btn"
        :readonly="isedit"
        size="small"
        type="primary"
        @click="save"
      >
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { postMqmonitorConf } from '@/api/monitoring'
  import { getChatGroupName } from '@/api/feishu'

  export default {
    name: 'MqEditor',
    props: {
      usersOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      const validateExpr = (rule, value, callback) => {
        if (this.formData.alarm_status === 1 && !value) {
          callback(new Error('启用状态下，告警表达式不能为空'))
        } else {
          callback()
        }
      }

      return {
        isedit: false,
        dialogFormVisible: false,
        disabled: false,
        title: '',
        formData: {
          name: '',
          topic: '',
          group: '',
          metric_name: '1m',
          threshold_value: '',
          alarm_status: 2,
          alarm_expression: '',
          business_group: '',
          notify_recipient: [],
          notify_method: [2],
        },
        rules: {
          metric_name: [
            {
              required: true,
              message: '字段值不可为空',
            },
          ],
          threshold_value: [
            {
              pattern: /^[-]?\d+(\.\d+)?$/,
              trigger: ['blur', 'change'],
              message: '必须为数值',
            },
          ],
          alarm_expression: [
            {
              validator: validateExpr,
              trigger: ['blur', 'change'],
            },
          ],
        },
        metric_nameOptions: [
          {
            label: '1m',
            value: '1m',
          },
          {
            label: '3m',
            value: '3m',
          },
          {
            label: '5m',
            value: '5m',
          },
          {
            value: '10m',
            label: '10m',
          },
          {
            value: '15m',
            label: '15m',
          },
        ],
        selectStatus: [
          {
            label: '启用',
            value: 1,
          },
          {
            label: '关闭',
            value: 2,
          },
        ],
        groupOptions: [],
        searchQuery: '',
        selectMethod: [
          {
            label: '飞书',
            value: 2,
          },
          {
            label: '企微',
            value: 1,
          },
        ],
      }
    },
    computed: {
      filteredUsersOptions() {
        if (this.searchQuery.length === 0) {
          return this.usersOptions
        } else {
          return this.usersOptions.filter((item) => {
            return (
              item.label
                .toLowerCase()
                .includes(this.searchQuery.toLowerCase()) ||
              item.value.toLowerCase().includes(this.searchQuery.toLowerCase())
            )
          })
        }
      },
    },
    watch: {
      'formData.alarm_status'(newVal) {
        if (this.formData.alarm_expression !== undefined) {
          this.$refs['vForm'].validateField('alarm_expression')
        }
      },
    },
    created() {
      this.fetchData()
      this.getChatGroupName()
    },
    mounted() {},
    methods: {
      showEdit(row) {
        if (!row) {
          this.isedit = false
          this.$baseMessage('此数据异常无法修改', 'failed')
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true

          const businessGroups = row.business_group
            ? row.business_group.split(',')
            : []

          const notifyRecipients = row.notify_recipient
            ? row.notify_recipient.split(',')
            : []

          const notifyMethods = row.notify_method
            ? typeof row.notify_method === 'string'
              ? row.notify_method.split(',').map(Number)
              : Array.isArray(row.notify_method)
              ? row.notify_method.map(Number)
              : [Number(row.notify_method)]
            : [2] // 默认飞书

          this.formData = {
            ...row,
            business_group: businessGroups,
            notify_recipient: notifyRecipients,
            notify_method: notifyMethods,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            if (!this.isedit) {
              this.$baseMessage('数据不存在无法修改', 'failed')
            } else {
              const formDataToSend = {
                ...this.formData,
                business_group: Array.isArray(this.formData.business_group)
                  ? this.formData.business_group.join(',')
                  : this.formData.business_group,
                notify_recipient: Array.isArray(this.formData.notify_recipient)
                  ? this.formData.notify_recipient.join(',')
                  : this.formData.notify_recipient,
                notify_method: Array.isArray(this.formData.notify_method)
                  ? this.formData.notify_method.join(',')
                  : this.formData.notify_method,
              }
              const response = await postMqmonitorConf(formDataToSend)
              msg = response.msg
            }
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      async fetchData() {
        setTimeout(() => {}, 300)
      },
      async getChatGroupName() {
        const { data: chatGroupName } = await getChatGroupName()
        this.groupOptions = chatGroupName
      },
      customFilter(queryString) {
        this.searchQuery = queryString
      },
    },
  }
</script>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }

  .dialog-footer {
    padding: 0px 0px;
    text-align: right;
    background-color: #f8f9fa;
    margin-top: 4px;

    .dialog-btn {
      padding: 8px 20px;
      margin-left: 10px;
      border-radius: 4px;
      font-size: 13px;

      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
        transition: all 0.2s;
      }
    }
  }
</style>
