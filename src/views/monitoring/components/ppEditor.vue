<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="服务"
            prop="service_name"
          >
            <el-select
              v-model="formData.service_name"
              :disabled="disabled"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in allProject"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  {{ item.name }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="标题"
            prop="alarm_title"
          >
            <el-input
              v-model.trim="formData.alarm_title"
              autocomplete="off"
              clearable
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="指标"
            prop="metric_name"
          >
            <el-select
              v-model.trim="formData.metric_name"
              class="full-width-input"
              clearable
            >
              <el-option
                v-for="(item, index) in selectType"
                :key="index"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="阈值"
            prop="threshold_value"
          >
            <el-input
              v-model.trim="formData.threshold_value"
              clearable
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item
            class="label-right-align"
            label="接收用户"
            prop="notify_recipient"
          >
            <el-select
              v-model.trim="formData.notify_recipient"
              :filter-method="customFilter"
              class="full-width-input"
              clearable
              filterable
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in filteredUsersOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="业务群组" prop="business_group">
            <el-select
              v-model.trim="formData.business_group"
              :filter-method="customFilter"
              class="full-width-input"
              clearable
              filterable
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in groupOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item
            class="label-right-align"
            label="通知方式"
            prop="notify_method"
          >
            <el-select
              v-model.trim="formData.notify_method"
              class="full-width-input"
              clearable
              multiple
            >
              <el-option
                v-for="item in selectMethod"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="告警状态"
            prop="alarm_status"
          >
            <el-select
              v-model.trim="formData.alarm_status"
              class="full-width-input"
              clearable
            >
              <el-option
                v-for="item in selectStatus"
                :key="item.value"
                :disabled="item.disabled"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item
        class="label-right-align"
        label="expr"
        prop="alarm_expression"
      >
        <el-input
          v-model.trim="formData.alarm_expression"
          clearable
          placeholder="告警表达式"
          readonly
          type="text"
        ></el-input>
      </el-form-item>
    </el-form>
    <el-alert title="说明:" type="info" style="margin-bottom: -10px">
      <div class="ci-alert-list">○ 没有设置群组，默认发送到【告警中心】</div>
    </el-alert>

    <div slot="footer" class="dialog-footer">
      <el-button class="dialog-btn" size="small" @click="close">
        取 消
      </el-button>
      <el-button
        class="dialog-btn"
        :readonly="isedit"
        size="small"
        type="primary"
        @click="save"
      >
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getPinpointList, postPinpointConf } from '@/api/monitoring'
  import { getChatGroupName } from '@/api/feishu'
  export default {
    name: 'PpEditor',
    props: {
      usersOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        groupOptions: [
          {
            label: '业务线1',
            value: '1',
          },
          {
            label: '业务线2',
            value: '2',
          },
        ],
        formData: {
          service_name: '',
          alarm_title: '',
          metric_name: '',
          time_interval: null,
          threshold_value: '',
          alarm_status: 2,
          alarm_expression: '',
          notify_method: [2], // 通知方式，默认为飞书
          notify_recipient: '', // 告警接收用户
          business_group: '', // 业务群组
        },
        rules: {
          time_interval: [
            {
              required: true,
              message: '字段值不可为空',
            },
          ],
          threshold_value: [
            {
              pattern: /^[-]?\d+(\.\d+)?$/,
              trigger: ['blur', 'change'],
              message: '必须为数值',
            },
          ],
        },
        time_intervalOptions: [
          {
            label: '2m',
            value: '2m',
          },
          {
            label: '5m',
            value: '5m',
          },
          {
            value: '10m',
            label: '10m',
          },
        ],
        selectStatus: [
          {
            label: '启用',
            value: 1,
          },
          {
            label: '关闭',
            value: 2,
          },
        ],
        selectMethod: [
          {
            label: '飞书',
            value: 2,
          },
          {
            label: '企微',
            value: 1,
          },
        ],
        selectType: [
          {
            label: '3s',
            value: '3s',
          },
          {
            label: '5s',
            value: '5s',
          },
          {
            label: 'slowCount',
            value: 'slowCount',
          },
          {
            label: 'errorCount',
            value: 'errorCount',
          },
        ],
        searchQuery: '',
        allProject: [],
        queryForm: {
          pageNo: 1,
          pageSize: 1000,
          userName: '',
        },
      }
    },
    computed: {
      filteredUsersOptions() {
        if (this.searchQuery.length === 0) {
          return this.usersOptions
        } else {
          return this.usersOptions.filter((item) => {
            return (
              item.label
                .toLowerCase()
                .includes(this.searchQuery.toLowerCase()) ||
              item.value.toLowerCase().includes(this.searchQuery.toLowerCase())
            )
          })
        }
      },
    },
    watch: {
      'formData.service_name'(newValue) {
        this.updateExpr()
      },

      'formData.metric_name'(newValue) {
        this.updateExpr()
      },
    },
    created() {
      this.fetchData()
      this.getChatGroupName()
    },

    methods: {
      updateExpr() {
        if (this.formData.service_name && this.formData.metric_name) {
          this.formData.alarm_expression = `prod_service_info{appname="${this.formData.service_name}",job="pinpoint",serviceType="SPRING_BOOT",type="${this.formData.metric_name}"}`
        }
      },
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true

          const notifyRecipients = row.notify_recipient
            ? row.notify_recipient.split(',')
            : []

          const businessGroups = row.business_group
            ? row.business_group.split(',')
            : []

          const notifyMethods = row.notify_method
            ? typeof row.notify_method === 'string'
              ? row.notify_method.split(',').map(Number)
              : Array.isArray(row.notify_method)
              ? row.notify_method.map(Number)
              : [Number(row.notify_method)]
            : []

          this.formData = {
            ...row,
            notify_recipient: notifyRecipients,
            business_group: businessGroups,
            notify_method: notifyMethods,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg

            const formDataToSend = {
              ...this.formData,

              notify_recipient: Array.isArray(this.formData.notify_recipient)
                ? this.formData.notify_recipient.join(',')
                : this.formData.notify_recipient,
              business_group: Array.isArray(this.formData.business_group)
                ? this.formData.business_group.join(',')
                : this.formData.business_group,
              notify_method: Array.isArray(this.formData.notify_method)
                ? this.formData.notify_method.join(',')
                : this.formData.notify_method,
            }
            const response = await postPinpointConf(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      async fetchData() {
        const { data: allProject } = await getPinpointList()
        this.allProject = allProject

        setTimeout(() => {}, 300)
      },

      async getChatGroupName() {
        const { data: chatGroupName } = await getChatGroupName()
        this.groupOptions = chatGroupName
      },

      MyFilter(query) {
        if (query) {
          this.allProject = _.filter(this.allProject, (item) => {
            return item.name.includes(query) || item.appname.includes(query)
          })
        } else {
          this.fetchData()
        }
        setTimeout(() => {}, 300)
      },

      customFilter(queryString) {
        this.searchQuery = queryString
      },
    },
  }
</script>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
  .dialog-footer {
    padding: 0px 0px;
    text-align: right;
    background-color: #f8f9fa;
    margin-top: 4px;

    .dialog-btn {
      padding: 8px 20px;
      margin-left: 10px;
      border-radius: 4px;
      font-size: 13px;

      &:hover {
        opacity: 0.8;
        transform: translateY(-1px);
        transition: all 0.2s;
      }
    }
  }
</style>
