<template>
  <el-dialog
    :close-on-click-modal="true"
    :visible.sync="dialogFormVisible"
    width="800px"
    @close="close"
  >
    <el-tabs
      v-model="activeTab"
      type="border-card"
      @tab-click="handleTabchange"
    >
      <el-tab-pane name="settings">
        <span slot="label">
          <i class="el-icon-setting"></i>
          <b>监控配置</b>
        </span>
        <el-form ref="form" :model="form" label-width="100px">
          <el-descriptions
            :border="true"
            :column="2"
            class="margin-top"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">监控详情</template>
              {{ form.title }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">业务场景</template>
              {{ form.service_scenario }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">业务等级</template>
              <el-tag :type="form.service_level | statusType" size="small">
                {{ form.service_level | statusLevel }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                负责人
              </template>
              {{ form.owner }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">监控方式</template>
              <el-tag size="small">
                {{ form.monitor_type | monitorType }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">告警方式</template>
              <el-tag size="small">
                {{ form.alarm_type | alarmType }}
              </el-tag>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">创建时间</template>
              {{ form.create_time }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">更新时间</template>
              {{ form.update_time }}
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label">监控场景详述</template>
              {{ form.description }}
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">启用状态</template>
              <el-switch
                v-model="form.status"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">时间间隔</template>
              <el-input-number
                v-model="form.interval"
                :max="60"
                :min="5"
                :precision="0"
                label="描述文字"
              ></el-input-number>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">告警阈值</template>
              <el-tag
                v-if="form.threshold_01_key !== '' && k1Edited"
                key="t1"
                size="medium"
                @click="handleEditTag(1)"
              >
                {{ form.threshold_01_key }}：{{ form.threshold_01_value }}
              </el-tag>
              <el-input
                v-if="!k1Edited"
                ref="tagInput1"
                v-model="form.threshold_01_value"
                class="input-new-tag"
                placeholder="请输入监控值"
                size="mini"
                @blur="handleEditEnd(1)"
              >
                <template slot="prepend">{{ form.threshold_01_key }}</template>
              </el-input>

              <el-tag
                v-if="form.threshold_02_key !== '' && k2Edited"
                key="t2"
                size="medium"
                @click="handleEditTag(2)"
              >
                {{ form.threshold_02_key }}：{{ form.threshold_02_value }}
              </el-tag>
              <el-input
                v-if="!k2Edited"
                ref="tagInput2"
                v-model="form.threshold_02_value"
                class="input-new-tag"
                placeholder="请输入监控值"
                size="mini"
                @blur="handleEditEnd(2)"
              >
                <template slot="prepend">{{ form.threshold_02_key }}</template>
              </el-input>

              <el-tag
                v-if="form.threshold_03_key !== '' && k3Edited"
                key="t3"
                size="medium"
                @click="handleEditTag(3)"
              >
                {{ form.threshold_03_key }}：{{ form.threshold_03_value }}
              </el-tag>
              <el-input
                v-if="!k3Edited"
                ref="tagInput3"
                v-model="form.threshold_03_value"
                class="input-new-tag"
                placeholder="请输入监控值"
                size="mini"
                @blur="handleEditEnd(3)"
              >
                <template slot="prepend">{{ form.threshold_03_key }}</template>
              </el-input>

              <el-tag
                v-if="form.threshold_04_key !== '' && k4Edited"
                key="t4"
                size="medium"
                @click="handleEditTag(4)"
              >
                {{ form.threshold_04_key }}：{{ form.threshold_04_value }}
              </el-tag>
              <el-input
                v-if="!k4Edited"
                ref="tagInput4"
                v-model="form.threshold_04_value"
                class="input-new-tag"
                placeholder="请输入监控值"
                size="mini"
                @blur="handleEditEnd(4)"
              >
                <template slot="prepend">{{ form.threshold_04_key }}</template>
              </el-input>

              <el-tag
                v-if="form.threshold_05_key !== '' && k5Edited"
                key="t5"
                size="medium"
                @click="handleEditTag(5)"
              >
                {{ form.threshold_05_key }}：{{ form.threshold_05_value }}
              </el-tag>
              <el-input
                v-if="!k5Edited"
                ref="tagInput5"
                v-model="form.threshold_05_value"
                class="input-new-tag"
                placeholder="请输入监控值"
                size="mini"
                @blur="handleEditEnd(5)"
              >
                <template slot="prepend">{{ form.threshold_05_key }}</template>
              </el-input>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </el-tab-pane>
      <el-tab-pane name="graphic">
        <span slot="label">
          <i class="el-icon-s-data"></i>
          <b>数据曲线</b>
        </span>
        <el-radio-group
          v-model="timeRange"
          size="small"
          @change="handleTimeChange()"
        >
          <el-radio-button label="24小时"></el-radio-button>
          <el-radio-button label="两天"></el-radio-button>
          <el-radio-button label="一周"></el-radio-button>
        </el-radio-group>
        <p />
        <vab-chart
          :option="monitorGraphic"
          auto-resize
          style="height: 320px; width: 700px"
          theme="vab-echarts-theme"
        />
      </el-tab-pane>
    </el-tabs>

    <div slot="footer" class="dialog-footer">
      <el-button type="info" @click="close">关 闭</el-button>
      <el-button icon="el-icon-check" type="success" @click="saveApply">
        保 存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import VabChart from '@/plugins/echarts'
  import moment from 'moment'
  import { getPrometheusGraphic } from '@/api/monitoring'

  export default {
    name: 'EventEdit',
    components: { VabChart },
    filters: {
      statusLevel(status) {
        const statusMap = {
          0: '核心',
          1: '重要',
          2: '一般',
          3: '边缘',
        }
        return statusMap[status]
      },
      statusType(status) {
        const statusMap = {
          0: 'danger',
          1: 'warning',
          2: 'warning',
          3: 'success',
        }
        return statusMap[status]
      },
      monitorType(type) {
        const monitorType = {
          0: '数据库',
          1: '拨测',
          2: '日志',
        }
        return monitorType[type]
      },
      alarmType(type) {
        const monitorType = {
          0: 'AlertManager',
          1: 'Grafana',
          2: 'Script',
        }
        return monitorType[type]
      },
    },
    data() {
      return {
        activeTab: 'settings',
        form: {
          event_id: '',
          title: '',
          channel: 0,
          occur_time: '',
          end_time: '',
          level: 'P1级故障',
          influenced: '是',
          services: '',
          warned: '是',
          upgraded: '是',
          progress: '',
          owner: '',
          notice: [],
        },
        k1Edited: true,
        k2Edited: true,
        k3Edited: true,
        k4Edited: true,
        k5Edited: true,

        timerangeValue: 1,
        timeRange: '24小时',
        title: '',
        isedit: false,
        currentUser: '',
        isAdmin: false,
        dialogFormVisible: false,
        monitorGraphic: {
          animationDuration: 2000,
          animationDurationUpdate: 1000,
          title: {
            text: '获取数据失败',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: [],
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [],
          },
          yAxis: {
            type: 'value',
          },
          series: [],
        },
      }
    },
    computed: {},
    created() {},
    methods: {
      showEdit(row) {
        this.form = Object.assign({}, row)
        this.dialogFormVisible = true
      },
      handleTimeChange() {
        switch (this.timeRange) {
          case '24小时':
            this.timerangeValue = 1
            break
          case '两天':
            this.timerangeValue = 2
            break
          case '一周':
            this.timerangeValue = 7
            break
        }
        this.fetchData()
      },
      handleTabchange() {
        if (this.activeTab === 'graphic') {
          this.fetchData()
        }
      },
      handleEditTag(key) {
        switch (key) {
          case 1:
            this.k1Edited = false
            this.$nextTick((_) => {
              this.$refs.tagInput1.$refs.input.focus()
            })
            break
          case 2:
            this.k2Edited = false
            this.$nextTick((_) => {
              this.$refs.tagInput2.$refs.input.focus()
            })
            break
          case 3:
            this.k3Edited = false
            this.$nextTick((_) => {
              this.$refs.tagInput3.$refs.input.focus()
            })
            break
          case 4:
            this.k4Edited = false
            this.$nextTick((_) => {
              this.$refs.tagInput4.$refs.input.focus()
            })
            break
          case 5:
            this.k5Edited = false
            this.$nextTick((_) => {
              this.$refs.tagInput5.$refs.input.focus()
            })
            break
        }
      },
      handleEditEnd(key) {
        switch (key) {
          case 1:
            this.k1Edited = true
            break
          case 2:
            this.k2Edited = true
            break
          case 3:
            this.k3Edited = true
            break
          case 4:
            this.k4Edited = true
            break
          case 5:
            this.k5Edited = true
            break
        }
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      saveApply() {},
      async fetchData() {
        var querystring =
          'query=' +
          this.form.endpoint +
          '&end=' +
          moment().valueOf() / 1000 +
          '&start=' +
          moment().subtract(this.timerangeValue, 'day').valueOf() / 1000 +
          '&step=600'
        var { data } = await getPrometheusGraphic(querystring)

        this.monitorGraphic.title.text = this.form.title
        this.monitorGraphic.legend.data = []
        this.monitorGraphic.series = []
        this.monitorGraphic.xAxis.data = []
        var jstr
        for (var i in data.result) {
          jstr = JSON.stringify(data.result[i].metric)
          this.monitorGraphic.legend.data.push(jstr)
          var jsonObject = {}
          jsonObject.name = jstr
          jsonObject.type = 'line'
          jsonObject.data = []

          for (var d in data.result[i].values) {
            jsonObject.data.push(data.result[i].values[d][1])
            if (i === '0') {
              this.monitorGraphic.xAxis.data.push(
                moment(data.result[i].values[d][0] * 1000).format('MM-DD hh:mm')
              )
            }
          }
          this.monitorGraphic.series.push(jsonObject)
        }
        setTimeout(() => {
          this.listLoading = false
        }, 500)
      },
    },
  }
</script>
<style lang="scss" scoped>
  .input-new-tag {
    width: 150px;
    padding: 0 10px;
    vertical-align: bottom;
  }
</style>
