<template>
  <div class="elk-rules-container">
    <el-alert title="说明:" type="success">
      <div class="ci-alert-list">○ 新增：新增ELK接口告警配置</div>
      <div class="ci-alert-list">○ 更新：将告警规则应用到监控系统</div>
      <p />
    </el-alert>

    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          新增规则
        </el-button>
        <el-button icon="el-icon-upload" type="danger" @click="applyRules">
          应用规则
        </el-button>
      </vab-query-form-left-panel>

      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              clearable
              placeholder="请输入规则名称或URL"
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-card>
      <el-table
        v-loading="listLoading"
        :data="list"
        :element-loading-text="elementLoadingText"
        @selection-change="setSelectRows"
      >
        <el-table-column
          label="规则名称"
          prop="rule_name"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="URL路径"
          prop="target_value"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag v-if="row.target_value === 'GLOBAL'" type="danger">
              全局规则
            </el-tag>
            <span v-else>{{ row.target_value }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="告警条件"
          prop="conditions"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-popover placement="top" width="300" trigger="hover">
              <div
                v-for="(cond, index) in row.conditions"
                :key="index"
                class="condition-item"
              >
                {{ formatCondition(cond) }}
              </div>
              <template #reference>
                <el-tag>{{ row.conditions.length }}个条件</el-tag>
              </template>
            </el-popover>
          </template>
        </el-table-column>

        <el-table-column
          label="优先级"
          prop="priority"
          width="80"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="告警间隔"
          prop="frequency_interval"
          show-overflow-tooltip
        >
          <template #default="{ row }">{{ row.frequency_interval }}秒</template>
        </el-table-column>

        <el-table-column
          label="通知人"
          prop="notification_channels"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ getReceiverLabels(row.notification_channels) }}
          </template>
        </el-table-column>

        <el-table-column
          label="严重程度"
          prop="severity"
          width="100"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag :type="getSeverityType(row.severity)">
              {{ getSeverityLabel(row.severity) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
            <!--            <el-button type="text" size="small" @click="viewAlerts(scope.row)">-->
            <!--              告警历史-->
            <!--            </el-button>-->
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-pagination
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <rule-editor
      ref="ruleEditor"
      :users-options="UsersOptions"
      @fetch-data="fetchData"
    ></rule-editor>
  </div>
</template>

<script>
  import { getElkRules, applyElkRules, deleteElkRule } from '@/api/elk'
  import { getList } from '@/api/userManagement'
  import RuleEditor from './components/ElkRuleEditor.vue'

  const REFRESH_TEXT = '正在加载...'

  export default {
    name: 'ElkAlertRules',
    components: { RuleEditor },
    data() {
      return {
        list: [],
        listLoading: true,
        elementLoadingText: REFRESH_TEXT,
        total: 0,
        selectRows: [],
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        userForm: {
          pageNo: 1,
          pageSize: 1000,
          keyword: '',
        },
        UsersOptions: [],
        metricOptions: [
          { label: '4xx错误数', value: 'error_4xx' },
          { label: '5xx错误数', value: 'error_5xx' },
          { label: '4xx错误率', value: '4xx_ratio' },
          { label: '5xx错误率', value: '5xx_ratio' },
          { label: '平均响应时间', value: 'avg_rt' },
          { label: '请求总数', value: 'total_count' },
        ],
        operatorOptions: [
          { label: '>=', value: '>=' },
          { label: '>', value: '>' },
          { label: '=', value: '=' },
          { label: '<', value: '<' },
          { label: '<=', value: '<=' },
        ],
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },
    created() {
      this.fetchData()
    },

    methods: {
      async fetchData() {
        this.listLoading = true
        const { data, total } = await getElkRules(this.queryForm)
        this.list = data
        this.total = total

        const { data: userData } = await getList(this.userForm)
        const filteredUserData = userData.filter(
          (item) =>
            item.status &&
            !(item.access.length === 1 && item.access[0] === '') &&
            !(item.role.length === 1 && item.role[0] === '')
        )

        this.UsersOptions = filteredUserData.map((item) => ({
          label: item.displayname,
          value: item.username,
        }))

        this.UsersOptions.unshift({ label: '所有人', value: 'all' })

        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },

      formatCondition(condition) {
        const metricLabel =
          this.metricOptions.find(
            (option) => option.value === condition.metric_type
          )?.label || condition.metric_type

        return `${metricLabel} ${condition.operator} ${condition.threshold_value}`
      },

      applyRules() {
        this.$confirm('此操作将应用所有规则到监控系统, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            const loading = this.$loading({
              lock: true,
              text: '正在应用规则...',
              spinner: 'el-icon-loading',
              background: 'rgba(0,0,0,0.7)',
            })
            try {
              const { message } = await applyElkRules()
              await this.fetchData()
              this.$message({
                type: 'success',
                message: message,
              })
            } catch (error) {
              this.$message({
                type: 'error',
                message: '应用规则失败',
              })
            } finally {
              setTimeout(() => {
                loading.close()
              }, 1000)
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消操作',
            })
          })
      },

      handleDelete(row) {
        this.$confirm(`确认删除规则 "${row.rule_name}" 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            try {
              await deleteElkRule(row.id)
              this.$message({
                type: 'success',
                message: '删除成功',
              })
              this.fetchData()
            } catch (error) {
              this.$message({
                type: 'error',
                message: '删除失败',
              })
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除',
            })
          })
      },

      viewAlerts(row) {
        // 跳转到告警历史页面，带上规则ID
        this.$router.push({
          name: 'ElkAlertHistory',
          query: { ruleId: row.id },
        })
      },

      setSelectRows(val) {
        this.selectRows = val
      },

      handleEdit(row) {
        if (row.id) {
          this.$refs['ruleEditor'].showEdit(row)
        } else {
          this.$refs['ruleEditor'].showEdit()
        }
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },

      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },

      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      getReceiverLabels(receiver) {
        if (!receiver) return ''

        const receiverValues = receiver.split(',')

        const labels = receiverValues.map((value) => {
          const match = this.UsersOptions.find(
            (option) => option.value === value
          )
          return match ? match.label : value
        })

        return labels.join(', ')
      },

      getSeverityType(severity) {
        const types = {
          info: 'info',
          warning: 'warning',
          critical: 'danger',
        }
        return types[severity] || 'info'
      },

      getSeverityLabel(severity) {
        const labels = {
          info: '一般',
          warning: '警告',
          critical: '严重',
        }
        return labels[severity] || severity
      },
    },
  }
</script>

<style scoped>
  .condition-item {
    margin-bottom: 5px;
    line-height: 1.4;
  }
</style>
