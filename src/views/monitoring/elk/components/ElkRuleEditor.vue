<template>
  <el-dialog
    :title="textMap[dialogStatus]"
    :visible.sync="dialogVisible"
    width="750px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="规则名称" prop="rule_name">
        <el-input
          v-model="form.rule_name"
          placeholder="请输入规则名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="URL路径" prop="target_value">
        <el-input
          v-model="form.target_value"
          placeholder="特定URL路径或GLOBAL(全局规则)"
        >
          <template #append>
            <el-button @click="form.target_value = 'GLOBAL'">
              全局规则
            </el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="优先级" prop="priority">
        <el-input-number
          v-model="form.priority"
          :min="1"
          :max="100"
          placeholder="数值越小优先级越高"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="告警间隔(秒)" prop="frequency_interval">
        <el-input-number
          v-model="form.frequency_interval"
          :min="30"
          placeholder="同一告警再次触发的最小间隔时间(秒)"
        ></el-input-number>
      </el-form-item>

      <el-form-item label="严重程度" prop="severity">
        <el-select v-model="form.severity" placeholder="请选择严重程度">
          <el-option label="一般" value="info"></el-option>
          <el-option label="警告" value="warning"></el-option>
          <el-option label="严重" value="critical"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="通知人员" prop="notification_channels">
        <el-select
          v-model="form.notification_channels"
          multiple
          placeholder="请选择通知人员"
        >
          <el-option
            v-for="item in usersOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-divider content-position="left">告警条件</el-divider>

      <p class="condition-tip">
        多个条件之间为"与"关系，所有条件都满足才会触发告警
      </p>

      <div
        v-for="(condition, index) in form.conditions"
        :key="index"
        class="condition-row"
      >
        <el-form-item
          :label="index === 0 ? '条件' : ''"
          :prop="`conditions.${index}.metric_type`"
          :rules="{ required: true, message: '请选择指标', trigger: 'change' }"
        >
          <el-select
            v-model="condition.metric_type"
            placeholder="选择指标"
            style="width: 140px"
          >
            <el-option
              v-for="item in metricOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          :prop="`conditions.${index}.operator`"
          :rules="{
            required: true,
            message: '请选择操作符',
            trigger: 'change',
          }"
        >
          <el-select
            v-model="condition.operator"
            placeholder="操作符"
            style="width: 80px"
          >
            <el-option
              v-for="item in operatorOptions"
              :key="item.value"
              :label="item.value"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          :prop="`conditions.${index}.threshold_value`"
          :rules="{ required: true, message: '请输入阈值', trigger: 'blur' }"
        >
          <el-input-number
            v-model="condition.threshold_value"
            :precision="2"
            :step="0.1"
            placeholder="阈值"
            style="width: 120px"
          ></el-input-number>
        </el-form-item>

        <el-button
          type="danger"
          icon="el-icon-delete"
          circle
          size="mini"
          :disabled="form.conditions.length <= 1"
          @click="removeCondition(index)"
        ></el-button>
      </div>

      <div class="add-condition">
        <el-button type="primary" icon="el-icon-plus" @click="addCondition">
          添加条件
        </el-button>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { createElkRule, updateElkRule } from '@/api/elk'

  export default {
    name: 'ElkRuleEditor',
    props: {
      usersOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        dialogVisible: false,
        dialogStatus: 'create',
        textMap: {
          create: '新增告警规则',
          update: '编辑告警规则',
        },
        form: {
          id: undefined,
          rule_name: '',
          target_value: '',
          priority: 10,
          frequency_interval: 300,
          severity: 'warning',
          notification_channels: [],
          conditions: [
            {
              metric_type: '',
              operator: '>=',
              threshold_value: 0,
            },
          ],
        },
        rules: {
          rule_name: [
            { required: true, message: '请输入规则名称', trigger: 'blur' },
            {
              min: 2,
              max: 50,
              message: '长度在 2 到 50 个字符',
              trigger: 'blur',
            },
          ],
          target_value: [
            {
              required: true,
              message: '请输入URL路径或选择全局规则',
              trigger: 'blur',
            },
          ],
          priority: [
            { required: true, message: '请输入优先级', trigger: 'blur' },
          ],
          frequency_interval: [
            { required: true, message: '请输入告警间隔', trigger: 'blur' },
          ],
          severity: [
            { required: true, message: '请选择严重程度', trigger: 'change' },
          ],
        },
        metricOptions: [
          { label: '4xx错误数', value: 'error_4xx' },
          { label: '5xx错误数', value: 'error_5xx' },
          { label: '4xx错误率', value: '4xx_ratio' },
          { label: '5xx错误率', value: '5xx_ratio' },
          { label: '平均响应时间', value: 'avg_rt' },
          { label: '请求总数', value: 'total_count' },
        ],
        operatorOptions: [
          { label: '>=', value: '>=' },
          { label: '>', value: '>' },
          { label: '=', value: '=' },
          { label: '<', value: '<' },
          { label: '<=', value: '<=' },
        ],
      }
    },
    methods: {
      showEdit(row) {
        this.resetForm()
        if (row) {
          this.dialogStatus = 'update'
          this.form = JSON.parse(JSON.stringify(row))
          // 确保notification_channels是数组
          if (typeof this.form.notification_channels === 'string') {
            this.form.notification_channels = this.form.notification_channels
              .split(',')
              .filter((item) => item)
          }
        } else {
          this.dialogStatus = 'create'
        }
        this.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.form.clearValidate()
        })
      },

      close() {
        this.dialogVisible = false
      },

      resetForm() {
        this.form = {
          id: undefined,
          rule_name: '',
          target_value: '',
          priority: 10,
          frequency_interval: 300,
          severity: 'warning',
          notification_channels: [],
          conditions: [
            {
              metric_type: '',
              operator: '>=',
              threshold_value: 0,
            },
          ],
        }
      },

      addCondition() {
        this.form.conditions.push({
          metric_type: '',
          operator: '>=',
          threshold_value: 0,
        })
      },

      removeCondition(index) {
        if (this.form.conditions.length > 1) {
          this.form.conditions.splice(index, 1)
        }
      },

      submit() {
        this.$refs.form.validate(async (valid) => {
          if (!valid) return

          try {
            const formData = JSON.parse(JSON.stringify(this.form))
            // 处理notification_channels，将数组转为字符串
            if (Array.isArray(formData.notification_channels)) {
              formData.notification_channels =
                formData.notification_channels.join(',')
            }

            if (this.dialogStatus === 'create') {
              await createElkRule(formData)
              this.$message.success('创建成功')
            } else {
              await updateElkRule(formData.id, formData)
              this.$message.success('更新成功')
            }

            this.close()
            this.$emit('fetch-data')
          } catch (error) {
            this.$message.error('操作失败: ' + (error.message || '未知错误'))
          }
        })
      },
    },
  }
</script>

<style scoped>
  .condition-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .add-condition {
    margin-top: 10px;
    margin-bottom: 20px;
  }

  .condition-tip {
    color: #909399;
    font-size: 12px;
    margin-top: -10px;
    margin-bottom: 15px;
  }
</style>
