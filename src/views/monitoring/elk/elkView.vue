<template>
  <div class="http-monitoring-container">
    <el-alert title="说明:" type="success">
      <div class="ci-alert-list">
        ○ HTTP监控：监控HTTP接口的请求量、错误率和响应时间
      </div>
      <div class="ci-alert-list">
        ○ 告警管理：查看和管理接口告警，快速定位异常
      </div>
      <p />
    </el-alert>

    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-refresh" type="primary" @click="fetchData">
          刷新数据
        </el-button>
        <el-button icon="el-icon-setting" @click="goToRuleConfig">
          查看全部规则
        </el-button>
        <el-select
          v-model="timeRange"
          placeholder="时间范围"
          style="margin-left: 10px; width: 120px"
          @change="fetchData"
        >
          <el-option label="最近5分钟" :value="5"></el-option>
          <el-option label="最近15分钟" :value="15"></el-option>
          <el-option label="最近30分钟" :value="30"></el-option>
          <el-option label="最近1小时" :value="60"></el-option>
        </el-select>
      </vab-query-form-left-panel>

      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              clearable
              placeholder="请输入接口路径"
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <!-- 告警概览 -->
    <el-row :gutter="20" class="alert-summary">
      <el-col :span="6">
        <el-card
          shadow="hover"
          class="alert-card active-alerts"
          :class="{ 'active-card': filterType === 'alerts' }"
          @click.native="setFilter('alerts')"
        >
          <div class="alert-card-content">
            <div class="alert-icon">
              <i class="el-icon-warning"></i>
            </div>
            <div class="alert-info">
              <h3>{{ activeAlertCount }}</h3>
              <p>活跃告警</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card
          shadow="hover"
          class="alert-card all-endpoints"
          :class="{ 'active-card': filterType === 'all' }"
          @click.native="setFilter('all')"
        >
          <div class="alert-card-content">
            <div class="alert-icon">
              <i class="el-icon-menu"></i>
            </div>
            <div class="alert-info">
              <h3>{{ total }}</h3>
              <p>全部接口</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card
          shadow="hover"
          class="alert-card configured-endpoints"
          :class="{ 'active-card': filterType === 'configured' }"
          @click.native="setFilter('configured')"
        >
          <div class="alert-card-content">
            <div class="alert-icon">
              <i class="el-icon-setting"></i>
            </div>
            <div class="alert-info">
              <h3>{{ configuredEndpointCount }}</h3>
              <p>已配置告警</p>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card
          shadow="hover"
          class="alert-card unconfigured-endpoints"
          :class="{ 'active-card': filterType === 'unconfigured' }"
          @click.native="setFilter('unconfigured')"
        >
          <div class="alert-card-content">
            <div class="alert-icon">
              <i class="el-icon-bell"></i>
            </div>
            <div class="alert-info">
              <h3>{{ unconfiguredEndpointCount }}</h3>
              <p>未配置告警</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card>
      <el-table
        v-loading="listLoading"
        :data="tableData"
        :element-loading-text="elementLoadingText"
        :row-class-name="tableRowClassName"
        @row-click="handleRowClick"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="npath" label="接口路径" min-width="250">
          <template #default="scope">
            <div class="npath-container">
              <el-badge v-if="scope.row.has_alerts" value="🔥" type="danger">
                <span>{{ scope.row.npath }}</span>
              </el-badge>
              <span v-else>{{ scope.row.npath }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="total_count"
          label="总请求数"
          width="120"
          sortable="custom"
        ></el-table-column>

        <el-table-column label="错误数" width="180">
          <template #header>
            <div>
              <span>错误数</span>
              <el-dropdown
                size="small"
                trigger="click"
                @command="handleErrorSortCommand"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="error_4xx:desc">
                    4xx错误数↓
                  </el-dropdown-item>
                  <el-dropdown-item command="error_5xx:desc">
                    5xx错误数↓
                  </el-dropdown-item>
                  <el-dropdown-item command="error_4xx_ratio:desc">
                    4xx错误率↓
                  </el-dropdown-item>
                  <el-dropdown-item command="error_5xx_ratio:desc">
                    5xx错误率↓
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </template>
          <template #default="scope">
            <div>
              <a
                :href="getKibanaUrl(scope.row.npath, '4xx')"
                target="_blank"
                class="error-link"
                @click.stop
              >
                4xx: {{ scope.row.error_4xx }} ({{
                  (scope.row.error_4xx_ratio * 100).toFixed(2)
                }}%)
              </a>
            </div>
            <div>
              <a
                :href="getKibanaUrl(scope.row.npath, '5xx')"
                target="_blank"
                class="error-link"
                @click.stop
              >
                5xx: {{ scope.row.error_5xx }} ({{
                  (scope.row.error_5xx_ratio * 100).toFixed(2)
                }}%)
              </a>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="avg_response_time"
          label="平均响应时间 (s)"
          width="150"
          sortable="custom"
        >
          <template #default="scope">
            <span :class="{ 'slow-response': scope.row.avg_response_time > 1 }">
              {{ scope.row.avg_response_time }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="健康状态" width="120">
          <template #default="scope">
            <el-tooltip :content="getHealthTooltip(scope.row)" placement="top">
              <div>
                <i
                  :class="getHealthIcon(scope.row)"
                  :style="{ color: getHealthColor(scope.row) }"
                ></i>
                {{ getHealthText(scope.row) }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="告警配置" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.has_specific_rules" type="success">
              已配置
            </el-tag>
            <el-tag v-else type="info">未配置</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-view"
              @click.stop="viewDetails(scope.row)"
            >
              详情
            </el-button>
            <el-button
              type="text"
              size="small"
              icon="el-icon-bell"
              @click.stop="configRule(scope.row)"
            >
              告警配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-pagination
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <!-- 告警详情抽屉 -->
    <el-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      direction="rtl"
      size="60%"
      :before-close="handleDrawerClose"
    >
      <div v-if="drawerVisible" class="drawer-content">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="指标曲线" name="metrics">
            <div class="metrics-controls">
              <div class="time-range-controls">
                <el-radio-group
                  v-model="metricsTimeRange"
                  size="small"
                  @change="handleMetricsTimeRangeChange"
                >
                  <el-radio-button :label="6">近6小时</el-radio-button>
                  <el-radio-button :label="12">近12小时</el-radio-button>
                  <el-radio-button :label="24">近24小时</el-radio-button>
                  <el-radio-button :label="48">近2天</el-radio-button>
                  <el-radio-button :label="168">近7天</el-radio-button>
                </el-radio-group>
                <el-date-picker
                  v-model="metricsCustomTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                  style="margin-left: 10px"
                  @change="handleCustomTimeRangeChange"
                ></el-date-picker>
              </div>

              <div class="compare-controls">
                <el-switch
                  v-model="onlyErrorMetrics"
                  inactive-color="#13ce66"
                  active-color="#ff4949"
                  size="small"
                  style="margin-left: 10px"
                  @change="toggleErrorSeries"
                ></el-switch>

                <el-checkbox
                  v-model="showCompareMetrics"
                  style="margin-left: 10px"
                  @change="toggleCompareMetrics"
                >
                  显示对比数据
                </el-checkbox>
                <el-date-picker
                  v-if="showCompareMetrics"
                  v-model="compareTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="对比开始时间"
                  end-placeholder="对比结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                  style="margin-left: 10px; width: 360px"
                  @change="handleCompareTimeRangeChange"
                ></el-date-picker>
              </div>
            </div>

            <div v-loading="metricsLoading" class="metrics-chart-container">
              <div id="metrics-chart" class="metrics-chart"></div>
              <el-empty
                v-if="!metricsLoading && !hasMetricsData"
                description="暂无指标数据"
                :image-size="150"
              ></el-empty>
            </div>
          </el-tab-pane>

          <el-tab-pane label="告警详情" name="alerts">
            <div v-if="selectedRowAlerts.length > 0" class="alerts-list">
              <el-timeline>
                <el-timeline-item
                  v-for="(alert, index) in selectedRowAlerts"
                  :key="index"
                  :type="getSeverityType(alert.severity)"
                  :timestamp="formatDateTime(alert.alert_time)"
                >
                  <!-- 在告警卡片中添加条件检查 -->
                  <el-card class="alert-card">
                    <h4>{{ alert.message || '未知告警' }}</h4>
                    <div class="alert-info">
                      <p>
                        <strong>规则名称：</strong>
                        {{ getRuleName(alert) }}
                      </p>
                      <p>
                        <strong>严重程度：</strong>
                        <el-tag
                          :type="getSeverityType(getAlertSeverity(alert))"
                        >
                          {{ getSeverityLabel(getAlertSeverity(alert)) }}
                        </el-tag>
                      </p>
                      <p><strong>触发条件：</strong></p>
                      <ul v-if="getTriggeredConditions(alert).length > 0">
                        <li
                          v-for="(condition, idx) in getTriggeredConditions(
                            alert
                          )"
                          :key="idx"
                        >
                          {{ formatCondition(condition) }}
                        </li>
                      </ul>
                      <p v-else>无条件详情</p>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
            <el-empty
              v-else
              description="无告警记录"
              :image-size="200"
            ></el-empty>
          </el-tab-pane>

          <el-tab-pane label="历史告警" name="history">
            <div class="history-filters">
              <el-date-picker
                v-model="historyDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 350px"
                @change="fetchHistoryAlerts"
              ></el-date-picker>
            </div>

            <div v-loading="historyLoading" class="history-content">
              <el-table
                :data="historyData"
                style="width: 100%; margin-top: 20px"
                border
              >
                <el-table-column prop="alert_time" label="告警时间" width="180">
                  <template #default="scope">
                    {{ formatDateTime(scope.row.alert_time) }}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="message"
                  label="告警信息"
                  min-width="200"
                ></el-table-column>
                <el-table-column
                  prop="resolved_time"
                  label="恢复时间"
                  width="180"
                >
                  <template #default="scope">
                    {{
                      scope.row.resolved_time
                        ? formatDateTime(scope.row.resolved_time)
                        : '未恢复'
                    }}
                  </template>
                </el-table-column>
                <el-table-column label="持续时间" width="150">
                  <template #default="scope">
                    {{ calculateDuration(scope.row) }}
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                :current-page="historyQuery.pageNo"
                :layout="layout"
                :page-size="historyQuery.pageSize"
                :total="historyTotal"
                background
                @size-change="handleHistorySizeChange"
                @current-change="handleHistoryCurrentChange"
              ></el-pagination>
            </div>
            <el-empty
              v-if="!historyLoading && historyData.length === 0"
              description="无历史告警数据"
              :image-size="200"
            ></el-empty>
          </el-tab-pane>

          <!-- 规则配置标签页 -->
          <el-tab-pane label="规则配置" name="rules">
            <div v-loading="ruleLoading" class="rule-config">
              <div v-if="existingRules.length > 0" class="existing-rules">
                <h3>当前接口规则</h3>
                <el-table
                  :data="existingRules"
                  style="width: 100%; margin-bottom: 20px"
                  border
                  size="small"
                >
                  <el-table-column
                    prop="rule_name"
                    label="规则名称"
                    min-width="150"
                  ></el-table-column>
                  <el-table-column label="告警条件" min-width="200">
                    <template #default="scope">
                      <div
                        v-for="(cond, index) in scope.row.conditions"
                        :key="index"
                        class="condition-item"
                      >
                        {{ formatCondition(cond) }}
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="severity" label="严重程度" width="100">
                    <template #default="scope">
                      <el-tag :type="getSeverityType(scope.row.severity)">
                        {{ getSeverityLabel(scope.row.severity) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="frequency_interval"
                    label="告警间隔"
                    width="100"
                  >
                    <template #default="scope">
                      {{ scope.row.frequency_interval }}秒
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template #default="scope">
                      <el-button
                        type="text"
                        size="small"
                        @click="editRule(scope.row)"
                      >
                        编辑
                      </el-button>
                      <el-button
                        type="text"
                        size="small"
                        @click="deleteRule(scope.row)"
                      >
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <el-divider
                v-if="existingRules.length > 0"
                content-position="left"
              >
                <span>{{ isEditing ? '编辑规则' : '新增规则' }}</span>
              </el-divider>

              <el-form
                ref="ruleForm"
                :model="ruleForm"
                :rules="ruleRules"
                label-width="120px"
                size="small"
              >
                <el-form-item label="规则名称" prop="rule_name">
                  <el-input
                    v-model="ruleForm.rule_name"
                    placeholder="请输入规则名称"
                  ></el-input>
                </el-form-item>

                <el-form-item label="监控路径" prop="target_value">
                  <el-input
                    v-model="ruleForm.target_value"
                    placeholder="接口路径"
                    disabled
                  ></el-input>
                </el-form-item>

                <el-form-item label="优先级" prop="priority">
                  <el-input-number
                    v-model="ruleForm.priority"
                    :min="1"
                    :max="100"
                  ></el-input-number>
                  <span class="form-help">数字越小优先级越高</span>
                </el-form-item>

                <el-form-item label="告警间隔" prop="frequency_interval">
                  <el-input-number
                    v-model="ruleForm.frequency_interval"
                    :min="60"
                    :step="60"
                  ></el-input-number>
                  <span class="form-help">单位：秒</span>
                </el-form-item>

                <el-form-item label="严重程度" prop="severity">
                  <el-select
                    v-model="ruleForm.severity"
                    placeholder="选择严重程度"
                  >
                    <el-option label="一般" value="info"></el-option>
                    <el-option label="警告" value="warning"></el-option>
                    <el-option label="严重" value="critical"></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="通知人" prop="notification_channels">
                  <el-select
                    v-model="ruleForm.notification_channels"
                    multiple
                    placeholder="选择通知人"
                  >
                    <el-option
                      v-for="option in usersOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-divider content-position="left">告警条件</el-divider>

                <div
                  v-for="(condition, index) in ruleForm.conditions"
                  :key="index"
                  class="condition-form"
                >
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-form-item
                        :label="index === 0 ? '指标类型' : ''"
                        :prop="'conditions.' + index + '.metric_type'"
                      >
                        <el-select
                          v-model="condition.metric_type"
                          placeholder="选择指标类型"
                        >
                          <el-option
                            v-for="option in metricOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :span="5">
                      <el-form-item
                        :label="index === 0 ? '运算符' : ''"
                        :prop="'conditions.' + index + '.operator'"
                      >
                        <el-select
                          v-model="condition.operator"
                          placeholder="运算符"
                        >
                          <el-option
                            v-for="option in operatorOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :span="7">
                      <el-form-item
                        :label="index === 0 ? '阈值' : ''"
                        :prop="'conditions.' + index + '.threshold_value'"
                      >
                        <el-input-number
                          v-model="condition.threshold_value"
                          :precision="2"
                          :step="1"
                          :min="0"
                        ></el-input-number>
                      </el-form-item>
                    </el-col>

                    <el-col
                      :span="4"
                      style="display: flex; align-items: center"
                    >
                      <el-button
                        v-if="ruleForm.conditions.length > 1"
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        size="mini"
                        style="margin-top: 10px"
                        @click="removeCondition(index)"
                      ></el-button>
                    </el-col>
                  </el-row>
                </div>

                <div style="margin-bottom: 20px">
                  <el-button
                    type="primary"
                    plain
                    size="small"
                    icon="el-icon-plus"
                    @click="addCondition"
                  >
                    添加条件
                  </el-button>
                </div>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="submitLoading"
                    @click="submitRule"
                  >
                    保存
                  </el-button>
                  <el-button @click="resetRuleForm">重置</el-button>
                  <el-button v-if="isEditing" @click="cancelEdit">
                    取消编辑
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import {
    getHttpAggregation,
    getAlertHistory,
    getElkRules,
    getElkRule,
    createElkRule,
    updateElkRule,
    deleteElkRule,
    getNpathMetrics, // 新增API导入
  } from '@/api/elk'
  import { getList } from '@/api/userManagement'
  import * as echarts from 'echarts' // 添加echarts引入

  export default {
    name: 'HttpMonitoring',
    data() {
      return {
        // 表格数据
        tableData: [],
        listLoading: true,
        elementLoadingText: '加载中...',
        total: 0,
        queryForm: {
          pageNo: 1,
          pageSize: 50,
          keyword: '',
          sortField: 'total_count', // 添加排序字段
          sortOrder: 'desc', // 添加排序方向
        },

        // 过滤选项
        timeRange: 5,
        filterType: 'all', // 默认显示全部接口

        // 告警统计
        activeAlertCount: 0,
        alertedEndpointCount: 0,
        configuredEndpointCount: 0,

        // 详情抽屉
        drawerVisible: false,
        drawerTitle: '接口详情',
        selectedEndpoint: '',
        selectedRowAlerts: [],
        activeTab: 'metrics',

        // 历史告警
        historyData: [],
        historyQuery: {
          pageNo: 1,
          pageSize: 10,
        },
        historyTotal: 0,
        historyLoading: false,
        historyDateRange: [
          new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          new Date(),
        ],

        // 规则配置相关
        ruleLoading: false,
        submitLoading: false,
        existingRules: [],
        isEditing: false,
        editingRuleId: null,
        ruleForm: {
          rule_name: '',
          target_value: '',
          priority: 10,
          frequency_interval: 300,
          severity: 'warning',
          notification_channels: ['all'],
          conditions: [
            {
              metric_type: '5xx_ratio',
              operator: '>=',
              threshold_value: 5,
            },
          ],
        },
        ruleRules: {
          rule_name: [
            { required: true, message: '请输入规则名称', trigger: 'blur' },
            {
              min: 2,
              max: 50,
              message: '长度在 2 到 50 个字符',
              trigger: 'blur',
            },
          ],
          frequency_interval: [
            { required: true, message: '请设置告警间隔', trigger: 'change' },
          ],
          severity: [
            { required: true, message: '请选择严重程度', trigger: 'change' },
          ],
        },

        // 下拉选项
        usersOptions: [{ label: '所有人', value: 'all' }],
        metricOptions: [
          { label: '4xx错误数', value: 'error_4xx' },
          { label: '5xx错误数', value: 'error_5xx' },
          { label: '4xx错误率', value: '4xx_ratio' },
          { label: '5xx错误率', value: '5xx_ratio' },
          { label: '平均响应时间', value: 'avg_rt' },
          { label: '请求总数', value: 'total_count' },
        ],
        operatorOptions: [
          { label: '>=', value: '>=' },
          { label: '>', value: '>' },
          { label: '=', value: '=' },
          { label: '<', value: '<' },
          { label: '<=', value: '<=' },
        ],

        // 指标曲线相关
        metricsChart: null,
        metricsLoading: false,
        metricsTimeRange: 6, // 默认6小时
        metricsCustomTimeRange: null, // 自定义时间范围
        metricsData: [],
        hasMetricsData: false,
        showCompareMetrics: false,
        compareTimeRange: null,
        compareData: [],
        onlyErrorMetrics: false,
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
      unconfiguredEndpointCount() {
        return this.total - this.configuredEndpointCount
      },
    },
    watch: {
      drawerVisible(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.initializeMetricsChart()
            this.fetchMetricsData()
          })
        }
      },
    },
    created() {
      const { url } = this.$route.query
      if (url) {
        this.queryForm.keyword = url
      }
      this.fetchData()
      this.fetchUsers()
    },
    mounted() {
      // 添加窗口大小变化监听，用于图表自适应
      window.addEventListener('resize', this.resizeMetricsChart)
    },
    beforeDestroy() {
      // 组件销毁前移除监听
      window.removeEventListener('resize', this.resizeMetricsChart)
      // 销毁图表实例
      if (this.metricsChart) {
        this.metricsChart.dispose()
        this.metricsChart = null
      }
    },
    methods: {
      // 设置过滤类型
      setFilter(type) {
        this.filterType = type
        this.fetchData()
      },

      async fetchData() {
        this.listLoading = true
        try {
          const params = {
            pageNo: this.queryForm.pageNo,
            pageSize: this.queryForm.pageSize,
            keyword: this.queryForm.keyword,
            timeRange: this.timeRange,
            sortField: this.queryForm.sortField,
            sortOrder: this.queryForm.sortOrder,
          }

          const {
            data,
            total,
            activeAlertCount,
            alertedEndpointCount,
            configuredEndpointCount,
          } = await getHttpAggregation(params)

          // 根据当前过滤类型筛选数据
          let filteredData = [...data]
          switch (this.filterType) {
            case 'alerts':
              filteredData = filteredData.filter((item) => item.has_alerts)
              break
            case 'configured':
              filteredData = filteredData.filter(
                (item) => item.has_specific_rules
              )
              break
            case 'unconfigured':
              filteredData = filteredData.filter(
                (item) => !item.has_specific_rules
              )
              break
            case 'all':
            default:
              // 不需要过滤
              break
          }

          this.tableData = filteredData
          this.total = total
          this.activeAlertCount = activeAlertCount
          this.alertedEndpointCount = alertedEndpointCount
          this.configuredEndpointCount = configuredEndpointCount || 0
        } catch (error) {
          console.error('Failed to fetch HTTP aggregation data:', error)
          this.$message.error('获取数据失败，请重试')
        } finally {
          this.listLoading = false
        }
      },

      handleFilterChange() {
        this.fetchData()
      },

      // 处理表格排序变化
      handleSortChange(column) {
        if (column.prop && column.order) {
          this.queryForm.sortField = column.prop
          this.queryForm.sortOrder =
            column.order === 'descending' ? 'desc' : 'asc'
          this.fetchData()
        }
      },

      // 处理错误列排序下拉命令
      handleErrorSortCommand(command) {
        const [field, order] = command.split(':')
        this.queryForm.sortField = field
        this.queryForm.sortOrder = order
        this.fetchData()
      },

      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },

      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },

      tableRowClassName({ row }) {
        if (row.has_alerts) {
          return 'alert-row'
        }
        return ''
      },
      handleTabClick(tab) {
        if (tab.name === 'history') {
          this.fetchHistoryAlerts()
        }
        if (tab.name === 'rules') {
          // 确保监控路径正确设置
          this.updateRuleConfigPath(this.selectedEndpoint)

          // 重置表单，确保form其他字段也被正确初始化
          // 但保留target_value
          const currentPath = this.selectedEndpoint
          this.resetRuleForm()
          this.ruleForm.target_value = currentPath
        }
        if (tab.name === 'metrics') {
          // 添加延迟以确保DOM已经渲染完成
          this.$nextTick(() => {
            try {
              this.initializeMetricsChart()
              this.fetchMetricsData()
            } catch (error) {
              console.error('初始化图表时出错:', error)
              this.$message.error('加载指标图表失败，请稍后重试')
            }
          })
        }
      },
      updateRuleConfigPath(path) {
        if (!path) return

        console.log('Updating rule config path to:', path)

        this.ruleConfigPath = path

        // 清空现有规则
        this.existingRules = []

        // 更新表单中的目标值
        this.ruleForm.target_value = path

        if (this.$refs.ruleConfig) {
          this.$refs.ruleConfig.setPath(path)
        }

        // 调用正确的方法加载规则
        this.loadExistingRules()

        // 重置表单状态
        this.isEditing = false
        this.editingRuleId = null
      },
      getSeverityType(severity) {
        const types = {
          info: 'info',
          low: 'info',
          medium: 'warning',
          warning: 'warning',
          high: 'warning',
          critical: 'danger',
        }
        return types[severity?.toLowerCase()] || 'info'
      },
      // 获取规则名称
      getRuleName(alert) {
        if (!alert || !alert.rule_id) return '未知'

        // 从当前选中行的rules数组中查找匹配rule_id的规则
        const parentRow = this.tableData.find(
          (row) =>
            row.npath === this.selectedEndpoint && row.rules && row.rules.length
        )

        if (parentRow) {
          const matchingRule = parentRow.rules.find(
            (rule) => rule.id === alert.rule_id
          )
          if (matchingRule) return matchingRule.rule_name
        }

        return '未知'
      },

      // 解析触发条件
      getTriggeredConditions(alert) {
        if (!alert || !alert.triggered_conditions) return []

        try {
          // 尝试解析JSON字符串
          return JSON.parse(alert.triggered_conditions)
        } catch (e) {
          console.error('解析触发条件失败:', e)
          return []
        }
      },

      // 获取告警严重程度（根据alert_type或默认为warning）
      getAlertSeverity(alert) {
        if (!alert) return 'warning'

        // 可以根据alert_type映射到severity，或者从alert.message中提取
        // 这里简单返回默认值
        return alert.alert_type === 'error_5xx'
          ? 'critical'
          : alert.alert_type === 'error_4xx'
          ? 'warning'
          : 'info'
      },

      getSeverityLabel(severity) {
        const labels = {
          info: '一般',
          low: '低',
          medium: '中',
          warning: '警告',
          high: '高',
          critical: '严重',
        }
        return labels[severity?.toLowerCase()] || severity
      },

      getHealthIcon(row) {
        if (row.has_alerts) {
          return 'el-icon-warning'
        }
        if (row.error_5xx_ratio > 0.01) {
          return 'el-icon-warning'
        }
        if (row.error_4xx_ratio > 0.1) {
          return 'el-icon-warning-outline'
        }
        if (row.avg_response_time > 1) {
          return 'el-icon-time'
        }
        return 'el-icon-success'
      },
      formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return ''

        try {
          const date = new Date(dateTimeStr)

          // 检查日期是否有效
          if (isNaN(date.getTime())) return dateTimeStr

          // 格式化为：YYYY-MM-DD HH:mm:ss
          const year = date.getFullYear()
          const month = String(date.getMonth() + 1).padStart(2, '0')
          const day = String(date.getDate()).padStart(2, '0')
          const hours = String(date.getHours()).padStart(2, '0')
          const minutes = String(date.getMinutes()).padStart(2, '0')
          const seconds = String(date.getSeconds()).padStart(2, '0')

          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        } catch (e) {
          console.error('日期格式化错误:', e)
          return dateTimeStr
        }
      },
      showDrawer(row) {
        this.selectedEndpoint = row.npath
        this.selectedRowAlerts = row.alerts || []
        this.drawerVisible = true
        this.activeTab = 'metrics'

        // 同时设置规则配置的路径
        this.updateRuleConfigPath(row.npath)

        // 预加载历史告警数据
        this.$nextTick(() => {
          this.initializeMetricsChart()
          this.fetchMetricsData()
          this.fetchHistoryAlerts()
        })
      },

      getHealthColor(row) {
        if (row.has_alerts) {
          return '#F56C6C'
        }
        if (row.error_5xx_ratio > 0.01) {
          return '#F56C6C'
        }
        if (row.error_4xx_ratio > 0.1) {
          return '#E6A23C'
        }
        if (row.avg_response_time > 1) {
          return '#E6A23C'
        }
        return '#67C23A'
      },

      getHealthText(row) {
        if (row.has_alerts) {
          return '告警中'
        }
        if (row.error_5xx_ratio > 0.01) {
          return '异常'
        }
        if (row.error_4xx_ratio > 0.1) {
          return '警告'
        }
        if (row.avg_response_time > 1) {
          return '缓慢'
        }
        return '正常'
      },

      getHealthTooltip(row) {
        let reasons = []

        if (row.has_alerts) {
          reasons.push('存在活跃告警')
        }
        if (row.error_5xx_ratio > 0.01) {
          reasons.push(`5xx错误率: ${(row.error_5xx_ratio * 100).toFixed(2)}%`)
        }
        if (row.error_4xx_ratio > 0.1) {
          reasons.push(`4xx错误率: ${(row.error_4xx_ratio * 100).toFixed(2)}%`)
        }
        if (row.avg_response_time > 1) {
          reasons.push(`响应时间: ${row.avg_response_time}s`)
        }

        if (reasons.length === 0) {
          return '接口状态正常'
        }

        return `异常原因: ${reasons.join(', ')}`
      },

      handleRowClick(row) {
        this.selectedEndpoint = row.npath
        this.selectedRowAlerts = row.alerts || []
        this.drawerTitle = `接口详情: ${row.npath}`
        this.drawerVisible = true
        // 切换到告警详情标签
        this.activeTab = 'metrics'
      },

      viewDetails(row) {
        this.selectedEndpoint = row.npath
        this.selectedRowAlerts = row.alerts || []
        this.drawerTitle = `接口详情: ${row.npath}`
        this.drawerVisible = true
        this.activeTab = 'alerts'
        this.updateRuleConfigPath(row.npath)
      },

      configRule(row) {
        this.selectedEndpoint = row.npath
        this.drawerTitle = `告警配置: ${row.npath}`
        this.drawerVisible = true
        this.activeTab = 'rules'

        // 设置目标值
        this.ruleForm.target_value = row.npath

        // 加载已有规则
        this.loadExistingRules()
      },

      async loadExistingRules() {
        if (!this.selectedEndpoint) return
        console.log('Loading existing rules for:', this.selectedEndpoint)

        // 先清空现有规则
        this.existingRules = []

        this.ruleLoading = true
        try {
          const { data } = await getElkRules({
            keyword: this.selectedEndpoint,
          })

          // 过滤出目标值完全匹配的规则
          this.existingRules = data.filter(
            (rule) => rule.target_value === this.selectedEndpoint
          )
        } catch (error) {
          console.error('Failed to load existing rules:', error)
          this.$message.error('加载规则配置失败')
        } finally {
          this.ruleLoading = false
        }
      },

      async editRule(rule) {
        this.isEditing = true
        this.editingRuleId = rule.id

        this.ruleLoading = true
        try {
          // 获取规则详情
          const { data } = await getElkRule(rule.id)

          // 填充表单
          this.ruleForm = {
            rule_name: data.rule_name,
            target_value: data.target_value,
            priority: data.priority,
            frequency_interval: data.frequency_interval,
            severity: data.severity,
            notification_channels: data.notification_channels
              ? data.notification_channels.split(',')
              : ['all'],
            conditions: data.conditions || [],
          }
        } catch (error) {
          console.error('Failed to load rule details:', error)
          this.$message.error('加载规则详情失败')
        } finally {
          this.ruleLoading = false
        }
      },

      cancelEdit() {
        this.isEditing = false
        this.editingRuleId = null
        this.resetRuleForm()
      },

      deleteRule(rule) {
        this.$confirm(`确定要删除规则 "${rule.rule_name}" 吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            try {
              await deleteElkRule(rule.id)
              this.$message.success('删除成功')
              this.loadExistingRules()
              this.fetchData() // 刷新主列表
            } catch (error) {
              console.error('Failed to delete rule:', error)
              this.$message.error('删除规则失败')
            }
          })
          .catch(() => {
            // 取消删除，不做任何操作
          })
      },

      addCondition() {
        this.ruleForm.conditions.push({
          metric_type: '5xx_ratio',
          operator: '>=',
          threshold_value: 5,
        })
      },

      removeCondition(index) {
        this.ruleForm.conditions.splice(index, 1)
      },

      resetRuleForm() {
        this.$refs.ruleForm?.resetFields()
        this.ruleForm = {
          rule_name: '',
          target_value: this.selectedEndpoint,
          priority: 10,
          frequency_interval: 300,
          severity: 'warning',
          notification_channels: ['all'],
          conditions: [
            {
              metric_type: '5xx_ratio',
              operator: '>=',
              threshold_value: 5,
            },
          ],
        }
      },

      submitRule() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (!valid) return

          this.submitLoading = true
          try {
            // 准备数据
            const formData = {
              ...this.ruleForm,
              notification_channels:
                this.ruleForm.notification_channels.join(','),
            }

            if (this.isEditing) {
              // 使用updateElkRule API
              await updateElkRule(this.editingRuleId, formData)
            } else {
              // 使用createElkRule API
              await createElkRule(formData)
            }

            this.$message.success(
              this.isEditing ? '更新规则成功' : '创建规则成功'
            )
            this.isEditing = false
            this.editingRuleId = null
            this.resetRuleForm()

            // 重新加载规则列表
            this.loadExistingRules()
            this.fetchData() // 刷新主列表
          } catch (error) {
            console.error('Failed to save rule:', error)
            this.$message.error(
              this.isEditing ? '更新规则失败' : '创建规则失败'
            )
          } finally {
            this.submitLoading = false
          }
        })
      },

      async fetchHistoryAlerts() {
        if (!this.selectedEndpoint) return

        this.historyLoading = true
        try {
          // 格式化日期
          const startDate = this.historyDateRange[0].toISOString().split('T')[0]
          const endDate = this.historyDateRange[1].toISOString().split('T')[0]

          const params = {
            pageNo: this.historyQuery.pageNo,
            pageSize: this.historyQuery.pageSize,
            npath: this.selectedEndpoint,
            startDate,
            endDate,
          }

          const { data, total } = await getAlertHistory(params)
          this.historyData = data
          this.historyTotal = total
        } catch (error) {
          console.error('Failed to fetch history alerts:', error)
          this.$message.error('获取历史告警数据失败')
        } finally {
          this.historyLoading = false
        }
      },

      getKibanaUrl(npath, errorType) {
        // 使用组件中已选择的时间范围
        let timeRange = this.timeRange || 10 // 默认10分钟

        // 基础URL
        let baseUrl =
          'https://kib.in.szwego.com/app/kibana#/discover/376b0950-c8cb-11eb-beaf-012b7c96e129'

        // 时间过滤器
        let timeFilter = `?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-${timeRange}m,to:now))`

        // 根据错误类型构建查询条件
        let queryCondition
        if (errorType === '4xx') {
          queryCondition =
            '(status>=400 and status<500 or bk_status>=400 and bk_status<500)'
        } else {
          // 5xx
          queryCondition = '(status>=500 or bk_status>=500)'
        }

        // 对路径进行编码
        let encodedPath = encodeURIComponent(`"${npath}"`)

        // 构建完整查询
        let queryParams = `&_a=(columns:!(status,npath,bk_status,x_errcode,txid,request_time),filters:!(),index:'7aa73370-4800-11eb-8024-6335eded8129',interval:auto,query:(language:kuery,query:'${queryCondition} and npath: ${encodedPath}'),sort:!(!('@timestamp',desc)))`

        return baseUrl + timeFilter + queryParams
      },

      handleHistorySizeChange(val) {
        this.historyQuery.pageSize = val
        this.fetchHistoryAlerts()
      },

      handleHistoryCurrentChange(val) {
        this.historyQuery.pageNo = val
        this.fetchHistoryAlerts()
      },

      formatCondition(condition) {
        if (!condition || typeof condition !== 'object') {
          return '未知条件'
        }

        let metricName
        const metricOption = this.metricOptions.find(
          (opt) => opt.value === condition.metric_type
        )
        metricName = metricOption ? metricOption.label : condition.metric_type

        let actualValue = ''
        if (
          condition.actual_value !== undefined &&
          condition.actual_value !== null
        ) {
          actualValue = ` (当前值: ${condition.actual_value})`
        }

        return `${metricName} ${condition.operator} ${condition.threshold_value}${actualValue}`
      },

      calculateDuration(alert) {
        if (!alert.alert_time || !alert.resolved_time) {
          return '告警中'
        }

        const start = new Date(alert.alert_time)
        const end = new Date(alert.resolved_time)
        const diff = Math.floor((end - start) / 1000) // 秒

        if (diff < 60) {
          return `${diff}秒`
        } else if (diff < 3600) {
          return `${Math.floor(diff / 60)}分钟`
        } else if (diff < 86400) {
          return `${Math.floor(diff / 3600)}小时${Math.floor(
            (diff % 3600) / 60
          )}分钟`
        } else {
          return `${Math.floor(diff / 86400)}天${Math.floor(
            (diff % 86400) / 3600
          )}小时`
        }
      },

      goToRuleConfig() {
        this.$router.push('/devmonitor/alterCenter')
      },

      async fetchUsers() {
        try {
          const { data } = await getList({
            pageNo: 1,
            pageSize: 1000,
          })

          const filteredUserData = data.filter(
            (item) =>
              item.status &&
              !(item.access.length === 1 && item.access[0] === '') &&
              !(item.role.length === 1 && item.role[0] === '')
          )

          this.usersOptions = filteredUserData.map((item) => ({
            label: item.displayname,
            value: item.username,
          }))

          this.usersOptions.unshift({ label: '所有人', value: 'all' })
        } catch (error) {
          console.error('Failed to fetch users:', error)
        }
      },

      handleDrawerClose() {
        this.drawerVisible = false

        // 如果当前是在规则编辑状态，重置编辑状态
        if (this.activeTab === 'rules' && this.isEditing) {
          this.isEditing = false
          this.editingRuleId = null
          this.resetRuleForm()
        }
      },

      // 指标曲线相关方法
      initializeMetricsChart() {
        try {
          // 确保DOM已存在
          this.$nextTick(() => {
            const chartContainer = document.getElementById('metrics-chart')
            if (!chartContainer) {
              console.error('找不到图表容器元素')
              return
            }

            // 如果已有实例则销毁重建
            if (this.metricsChart) {
              this.metricsChart.dispose()
              this.metricsChart = null
            }

            // 创建新实例
            this.metricsChart = echarts.init(chartContainer)

            // 设置空的初始选项
            const option = {
              title: {
                text: `${this.selectedEndpoint} 指标数据`,
                left: 'center',
              },
              tooltip: {
                trigger: 'axis',
                axisPointer: {
                  type: 'cross',
                  animation: false,
                  label: {
                    backgroundColor: '#505765',
                  },
                },
              },
              legend: {
                data: [
                  '请求量',
                  '平均响应时间',
                  '平均大小',
                  '4xx错误数',
                  '5xx错误数',
                ],
                top: 30,
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
              },
              toolbox: {
                // 不配置 saveAsImage 可以去除下载按钮
                feature: {},
              },
              xAxis: {
                type: 'time',
                boundaryGap: false,
                axisLine: { onZero: false },
                splitLine: {
                  show: true,
                },
              },
              yAxis: [
                {
                  type: 'value',
                  name: '请求量',
                  position: 'left',
                  splitLine: {
                    show: true,
                  },
                },
                {
                  type: 'value',
                  name: '响应时间(s)',
                  position: 'right',
                  splitLine: {
                    show: false,
                  },
                },
              ],
              series: [],
            }

            this.metricsChart.setOption(option)
            this.metricsChart.showLoading()
          })
        } catch (error) {
          console.error('图表初始化失败:', error)
          // 不要中断执行流程，只记录错误
        }
      },

      // 获取指标数据
      async fetchMetricsData() {
        if (!this.selectedEndpoint) return

        this.metricsLoading = true
        try {
          let params = {
            npath: this.selectedEndpoint,
            timeRange: this.metricsTimeRange,
          }

          // 使用自定义时间范围
          if (this.metricsCustomTimeRange) {
            params.startTime = this.metricsCustomTimeRange[0]
            params.endTime = this.metricsCustomTimeRange[1]
            delete params.timeRange
          }

          // 添加对比时间范围
          if (this.showCompareMetrics && this.compareTimeRange) {
            params.compareStart = this.compareTimeRange[0]
            params.compareEnd = this.compareTimeRange[1]
          }

          const response = await getNpathMetrics(params)

          this.metricsData = response.data || []
          this.hasMetricsData = this.metricsData.length > 0

          // 对比数据
          this.compareData = response.compareData || []

          // 更新图表
          this.updateMetricsChart(response)
        } catch (error) {
          console.error('Failed to fetch metrics data:', error)
          this.$message.error('获取指标数据失败')
          this.hasMetricsData = false
        } finally {
          this.metricsLoading = false
          if (this.metricsChart) {
            this.metricsChart.hideLoading()
          }
        }
      },

      // 更新图表显示
      updateMetricsChart(response) {
        if (!this.metricsChart) {
          console.error('图表实例不存在，尝试重新初始化')
          try {
            this.initializeMetricsChart()
            // 如果仍然没有初始化成功，直接返回
            if (!this.metricsChart) return
          } catch (error) {
            console.error('重新初始化图表失败:', error)
            return
          }
        }

        const { data, compareData, timeRange, compareTimeRange, interval } =
          response

        // 准备时间轴数据 - 即使没有数据点，也确保时间轴显示完整
        const xAxisData = []

        // 准备系列数据
        const seriesData = {
          requestCount: [],
          responseTime: [],
          avgSize: [],
          error4xx: [],
          error5xx: [],
        }

        // 准备对比数据
        const compareSeriesData = {
          requestCount: [],
          responseTime: [],
          avgSize: [],
          error4xx: [],
          error5xx: [],
        }

        // 填充主数据
        if (data && data.length) {
          data.forEach((item) => {
            const time = item.c_time
            xAxisData.push(time)

            seriesData.requestCount.push([time, item.total_count || 0])
            seriesData.responseTime.push([time, item.avg_response_time || 0])
            seriesData.avgSize.push([time, item.avg_size || 0])
            seriesData.error4xx.push([time, item.error_4xx || 0])
            seriesData.error5xx.push([time, item.error_5xx || 0])
          })
        }

        // 填充对比数据
        if (compareData && compareData.length) {
          compareData.forEach((item) => {
            const time = item.c_time

            compareSeriesData.requestCount.push([time, item.total_count || 0])
            compareSeriesData.responseTime.push([
              time,
              item.avg_response_time || 0,
            ])
            compareSeriesData.avgSize.push([time, item.avg_size || 0])
            compareSeriesData.error4xx.push([time, item.error_4xx || 0])
            compareSeriesData.error5xx.push([time, item.error_5xx || 0])
          })
        }

        // 找出最大值用于纵轴缩放
        const findMax = (data) => Math.max(...data.map((item) => item[1]), 0)
        const maxRequestCount = findMax(seriesData.requestCount)
        const maxResponseTime = findMax(seriesData.responseTime)
        const maxAvgSize = findMax(seriesData.avgSize)
        const maxError4xx = findMax(seriesData.error4xx)
        const maxError5xx = findMax(seriesData.error5xx)

        // 获取比例系数，使得不同量级的指标可以一起在图表上显示
        const getScaleFactor = (value, baseValue) => {
          if (!value || !baseValue) return 1
          return Math.min(Math.max(baseValue / value, 0.1), 10)
        }

        // 请求量作为基准
        const rtScaleFactor = getScaleFactor(maxResponseTime, maxRequestCount)
        const sizeScaleFactor = getScaleFactor(maxAvgSize, maxRequestCount)
        const error4xxScaleFactor = maxError4xx > 0 ? 1 : 1
        const error5xxScaleFactor = maxError5xx > 0 ? 1 : 1

        // 定义各指标系列
        const series = [
          {
            name: '请求量',
            type: 'line',
            smooth: true,
            emphasis: {
              focus: 'series',
            },
            data: seriesData.requestCount,
            yAxisIndex: 0,
          },
          {
            name: '平均响应时间',
            type: 'line',
            smooth: true,
            emphasis: {
              focus: 'series',
            },
            data: seriesData.responseTime,
            yAxisIndex: 1,
          },
          {
            name: '平均大小',
            type: 'line',
            smooth: true,
            emphasis: {
              focus: 'series',
            },
            data: seriesData.avgSize,
            yAxisIndex: 0,
            // 调整比例让多个指标可以一起显示
            itemStyle: {
              opacity: 0.7,
            },
          },
          {
            name: '4xx错误数',
            type: 'line',
            smooth: true,
            emphasis: {
              focus: 'series',
            },
            data: seriesData.error4xx,
            yAxisIndex: 0,
          },
          {
            name: '5xx错误数',
            type: 'line',
            smooth: true,
            emphasis: {
              focus: 'series',
            },
            data: seriesData.error5xx,
            yAxisIndex: 0,
          },
        ]

        // 如果有对比数据，添加对比系列
        if (this.showCompareMetrics && compareData && compareData.length) {
          series.push(
            {
              name: '对比-请求量',
              type: 'line',
              smooth: true,
              emphasis: {
                focus: 'series',
              },
              data: compareSeriesData.requestCount,
              yAxisIndex: 0,
              lineStyle: {
                type: 'dashed',
              },
            },
            {
              name: '对比-平均响应时间',
              type: 'line',
              smooth: true,
              emphasis: {
                focus: 'series',
              },
              data: compareSeriesData.responseTime,
              yAxisIndex: 1,
              lineStyle: {
                type: 'dashed',
              },
            },
            {
              name: '对比-平均大小',
              type: 'line',
              smooth: true,
              emphasis: {
                focus: 'series',
              },
              data: compareSeriesData.avgSize,
              yAxisIndex: 0,
              lineStyle: {
                type: 'dashed',
              },
              itemStyle: {
                opacity: 0.7,
              },
            },
            {
              name: '对比-4xx错误数',
              type: 'line',
              smooth: true,
              emphasis: {
                focus: 'series',
              },
              data: compareSeriesData.error4xx,
              yAxisIndex: 0,
              lineStyle: {
                type: 'dashed',
              },
            },
            {
              name: '对比-5xx错误数',
              type: 'line',
              smooth: true,
              emphasis: {
                focus: 'series',
              },
              data: compareSeriesData.error5xx,
              yAxisIndex: 0,
              lineStyle: {
                type: 'dashed',
              },
            }
          )
        }

        // 构建图表选项
        const option = {
          title: {
            text: `${this.selectedEndpoint} 指标数据`,
            left: 'center',
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              animation: false,
              label: {
                backgroundColor: '#505765',
              },
            },
            formatter: function (params) {
              let result = `${params[0].axisValue}<br/>`
              params.forEach((param) => {
                let rawValue = param.value[1]
                // 尝试解析数字，如果解析失败则设为0
                let value = parseFloat(rawValue)
                if (isNaN(value)) {
                  value = 0
                }

                if (param.seriesName.includes('响应时间')) {
                  value = `${value.toFixed(3)}s`
                } else if (param.seriesName.includes('平均大小')) {
                  value = `${value.toFixed(0)} bytes`
                } else {
                  value = value.toFixed(0)
                }

                result += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`
                result += `${param.seriesName}: ${value}<br/>`
              })
              return result
            },
          },
          legend: {
            data: this.showCompareMetrics
              ? [
                  '请求量',
                  '平均响应时间',
                  '平均大小',
                  '4xx错误数',
                  '5xx错误数',
                  '对比-请求量',
                  '对比-平均响应时间',
                  '对比-平均大小',
                  '对比-4xx错误数',
                  '对比-5xx错误数',
                ]
              : [
                  '请求量',
                  '平均响应时间',
                  '平均大小',
                  '4xx错误数',
                  '5xx错误数',
                ],
            top: 30,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          toolbox: {
            feature: {
              restore: {},
            },
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: true },
          },
          yAxis: [
            {
              type: 'value',
              name: '请求量/错误数',
              position: 'left',
              splitLine: { show: true },
            },
            {
              type: 'value',
              name: '响应时间(s)',
              position: 'right',
              splitLine: { show: false },
              axisLabel: {
                formatter: '{value} s',
              },
            },
          ],
          series: series,
        }

        // 设置图表选项
        this.metricsChart.setOption(option, true)

        // 触发图表大小调整
        this.resizeMetricsChart()
      },

      // 图表自适应窗口大小
      resizeMetricsChart() {
        if (this.metricsChart) {
          this.metricsChart.resize()
        }
      },
      toggleErrorSeries(val) {
        // val 为 true 时，仅显示4xx/5xx；否则恢复所有系列
        if (!this.metricsChart) return
        const allSeriesNames = [
          '请求量',
          '平均响应时间',
          '平均大小',
          '4xx错误数',
          '5xx错误数',
        ]
        if (val) {
          allSeriesNames.forEach((name) => {
            this.metricsChart.dispatchAction({
              type:
                name === '4xx错误数' || name === '5xx错误数'
                  ? 'legendSelect'
                  : 'legendUnSelect',
              name: name,
            })
          })
        } else {
          allSeriesNames.forEach((name) => {
            this.metricsChart.dispatchAction({
              type: 'legendSelect',
              name: name,
            })
          })
        }
      },
      // 时间范围变化处理
      handleMetricsTimeRangeChange() {
        this.metricsCustomTimeRange = null // 清空自定义时间
        this.fetchMetricsData()
      },

      // 自定义时间范围变化处理
      handleCustomTimeRangeChange() {
        if (this.metricsCustomTimeRange) {
          this.metricsTimeRange = 0 // 清除预设时间范围
          this.fetchMetricsData()
        }
      },

      // 切换对比曲线
      toggleCompareMetrics(val) {
        if (val && !this.compareTimeRange) {
          // 默认对比前一天同时段
          const now = new Date()
          const yesterdayStart = new Date(now.getTime() - 24 * 60 * 60 * 1000)
          const yesterdayEnd = new Date(
            now.getTime() -
              24 * 60 * 60 * 1000 +
              this.metricsTimeRange * 60 * 60 * 1000
          )

          this.compareTimeRange = [
            yesterdayStart.toISOString().slice(0, 19).replace('T', ' '),
            yesterdayEnd.toISOString().slice(0, 19).replace('T', ' '),
          ]
        }

        this.fetchMetricsData()
      },

      // 对比时间范围变化处理
      handleCompareTimeRangeChange() {
        if (this.showCompareMetrics) {
          this.fetchMetricsData()
        }
      },
    },
  }
</script>

<style scoped>
  .http-monitoring-container {
    padding: 20px;
  }
  .alert-summary {
    margin: 20px 0;
  }
  .alert-card {
    cursor: pointer;
    transition: all 0.3s;
  }
  .alert-card:hover {
    transform: translateY(-5px);
  }
  .active-card {
    border: 2px solid #409eff;
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  .alert-card-content {
    display: flex;
    align-items: center;
  }
  .alert-icon {
    font-size: 40px;
    margin-right: 20px;
  }
  .alert-info h3 {
    font-size: 24px;
    margin: 0;
    color: #303133;
  }
  .alert-info p {
    margin: 5px 0 0 0;
    color: #909399;
  }
  .active-alerts .alert-icon {
    color: #f56c6c;
  }
  .alerted-endpoints .alert-icon {
    color: #e6a23c;
  }
  .all-endpoints .alert-icon {
    color: #409eff;
  }
  .configured-endpoints .alert-icon {
    color: #67c23a;
  }
  .unconfigured-endpoints .alert-icon {
    color: #909399;
  }
  .alert-row {
    background-color: #fef0f0 !important;
  }
  .alert-expand {
    padding: 15px;
  }
  .alert-expand h4 {
    margin-top: 0;
    margin-bottom: 10px;
  }
  .no-alerts {
    padding: 20px;
    text-align: center;
  }
  .slow-response {
    color: #e6a23c;
  }
  .drawer-content {
    padding: 20px;
  }
  .alerts-list {
    margin-top: 20px;
  }
  .history-filters {
    margin: 20px 0;
  }
  .history-content {
    min-height: 300px;
  }
  .rule-config {
    padding: 0 10px;
  }
  .condition-form {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
  }
  .form-help {
    margin-left: 10px;
    color: #909399;
    font-size: 12px;
  }
  .condition-item {
    margin-bottom: 5px;
    line-height: 1.4;
  }

  /* 指标曲线相关样式 */
  .metrics-controls {
    margin: 10px 0 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
  }
  .time-range-controls {
    margin-bottom: 10px;
  }
  .compare-controls {
    margin-bottom: 10px;
  }
  .metrics-chart-container {
    position: relative;
    height: 450px;
    width: 100%;
  }
  .metrics-chart {
    height: 100%;
    width: 100%;
  }

  /* 表格和下拉菜单样式 */
  .el-table .cell {
    line-height: 30px; /* 增加单元格行高 */
    padding-top: 6px;
    padding-bottom: 6px;
  }
  .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 5px;
  }
  .el-dropdown-link:hover {
    color: #66b1ff;
  }

  /* 确保角标完整显示 */
  .npath-container {
    position: relative;
    padding-right: 20px;
    min-height: 34px; /* 保证高度足够显示角标 */
    display: flex;
    align-items: center;
  }

  /* 调整el-badge的样式 */
  .npath-container .el-badge__content {
    height: auto;
    line-height: 16px;
    padding: 2px 6px;
    z-index: 4;
  }

  .error-link {
    text-decoration: underline;
    color: inherit;
    cursor: pointer;
  }
  .error-link:hover {
    color: #409eff;
  }
</style>
