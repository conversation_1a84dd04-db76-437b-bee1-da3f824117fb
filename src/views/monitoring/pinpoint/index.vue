<template>
  <div class="projectManagement-container">
    <el-alert title="" type="success" style="margin-bottom: 10px">
      <div class="ci-alert-list" style="font-size: 14px">
        ○ 新增编辑配置后，记得点击更新 😯
        <br />
        ○
        新增发送到指定群组功能，配置了飞书通知，未选择群组将默认发送到告警中心群
      </div>
    </el-alert>

    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button
          icon="el-icon-plus"
          size="mini"
          type="primary"
          @click="handleEdit"
        >
          新增
        </el-button>
        <el-button
          icon="el-icon-upload"
          size="mini"
          type="danger"
          @click="updateData"
        >
          更新
        </el-button>
      </vab-query-form-left-panel>

      <vab-query-form-right-panel :span="12">
        <el-input
          v-model.trim="queryForm.keyword"
          clearable
          placeholder="请输入服务名称进行查询"
          style="width: 220px"
          @change="queryData"
        />
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-card>
      <el-row :gutter="24" class="flex-row" style="flex-direction: column">
        <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
          <el-table
            v-loading="listLoading"
            :data="list"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-form label-position="left" inline class="table-expand">
                  <el-form-item label="表达式">
                    <span>
                      {{ props.row.alarm_expression }} >
                      {{ props.row.threshold_value }}
                    </span>
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>

            <el-table-column
              label="通知标题"
              prop="alarm_title"
              show-overflow-tooltip
              min-width="150"
            ></el-table-column>

            <el-table-column
              label="服务名称"
              prop="service_name"
              show-overflow-tooltip
              min-width="100"
            ></el-table-column>

            <el-table-column
              label="指标"
              prop="metric_name"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="阈值"
              prop="threshold_value"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="状态"
              prop="alarm_status"
              show-overflow-tooltip
              min-width="50"
            >
              <template #default="{ row }">
                <el-tag :type="row.alarm_status | statusType">
                  {{ row.alarm_status | statusMap }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" show-overflow-tooltip width="80">
              <template #default="scope">
                <el-button type="text" @click="handleEdit(scope.row)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </el-card>

    <el-pagination
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="queryForm.pageSize"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

    <edit
      ref="edit"
      :users-options="UsersOptions"
      @fetch-data="fetchData"
    ></edit>
  </div>
</template>

<script>
  import { getPinpointConf, pushPinpointConf } from '@/api/monitoring'
  import Edit from '../components/ppEditor.vue'
  import { getList } from '@/api/userManagement'

  const REFRESH_TEXT = '正在加载...'
  // const CONFIRM_UPDATE_TEXT = '确认更新告警规则吗？'

  export default {
    name: 'PinpointService',
    components: { Edit },
    filters: {
      statusType(alarm_status) {
        const statusMap = {
          2: 'danger',
          1: 'success',
        }
        return statusMap[alarm_status]
      },
      statusMap(alarm_status) {
        const statusMap = {
          2: '关闭',
          1: '开启',
        }
        return statusMap[alarm_status]
      },

      notifyMethodMap(notify_method) {
        const notifyMethodMap = {
          2: '飞书',
          1: '企微',
          3: '邮箱',
        }

        if (Array.isArray(notify_method)) {
          return notify_method
            .map((method) => notifyMethodMap[method])
            .join('、')
        }
        if (typeof notify_method === 'string' && notify_method.includes(',')) {
          return notify_method
            .split(',')
            .map((method) => notifyMethodMap[method.trim()])
            .join('、')
        }
        return notifyMethodMap[notify_method] || ''
      },
    },

    data() {
      return {
        list: [],
        listLoading: true,
        elementLoadingText: REFRESH_TEXT,
        total: 0,
        selectRows: [],
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
        userForm: {
          pageNo: 1,
          pageSize: 1000,
          keyword: '',
        },
        UsersOptions: [],
        debouncedQuery: null,
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },
    watch: {
      'queryForm.keyword'() {
        this.debouncedQuery()
      },
    },
    created() {
      this.debouncedQuery = this.$_.debounce(() => {
        this.queryData()
      }, 300)
      this.fetchData()
    },
    beforeDestroy() {
      this.debouncedQuery.cancel()
    },

    methods: {
      async fetchData() {
        this.listLoading = true
        const { data: List, totalCount } = await getPinpointConf(this.queryForm)
        this.list = List
        this.total = totalCount

        const { data: userData } = await getList(this.userForm)

        console.log('pinpoint 查询参数：', this.userForm)
        console.log('pinpoint:', userData)

        // const filteredUserData = userData.filter(
        //   (item) =>
        //     item.status &&
        //     !(item.access.length === 1 && item.access[0] === '') &&
        //     !(item.role.length === 1 && item.role[0] === '')
        // )

        const filteredUserData = userData.filter(
          (item) =>
            item.status && !(item.role.length === 1 && item.role[0] === '')
        )

        this.UsersOptions = filteredUserData.map((item) => ({
          label: item.displayname,
          value: item.username,
        }))

        this.UsersOptions.unshift({ label: '所有人', value: 'all' })

        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
      async updateData() {
        this.$confirm('此操作将更新规则文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(async () => {
            const loading = this.$loading({
              lock: true,
              text: '规则文件更新中...',
              spinner: 'el-icon-loading',
              background: 'rgba(0,0,0,0.7)',
            })
            try {
              const { msg } = await pushPinpointConf()
              await this.fetchData()
              this.$message({
                type: 'success',
                message: msg,
              })
            } catch (error) {
              this.$message({
                type: 'error',
                message: '更新失败',
              })
            } finally {
              setTimeout(() => {
                loading.close()
              }, 2000)
            }
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消更新',
            })
          })
      },
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      async queryData() {
        this.queryForm.pageNo = 1
        this.queryForm.pageSize = 10
        this.total = 0
        this.list = []
        await this.fetchData()
      },

      getReceiverLabels(receiver) {
        if (!receiver) return ''

        const receiverValues = receiver.split(',')

        const labels = receiverValues.map((value) => {
          const match = this.UsersOptions.find(
            (option) => option.value === value
          )
          return match ? match.label : value
        })

        return labels.join(', ')
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table {
    th {
      background-color: #f0f6ff !important;
      color: #606266;
      font-size: 14px;
      font-weight: bold;
    }

    .cell {
      font-weight: normal;
      font-size: 14px;
      color: #606266;
    }
  }

  .table-expand {
    padding: 10px 20px;

    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
      width: 100%;
      line-height: 0.8;
    }

    ::v-deep .el-form-item__label {
      color: #99a9bf;
      font-weight: bold;
      font-size: 14px;
      line-height: 1;
      padding-bottom: 10px;
    }

    ::v-deep .el-form-item__content {
      font-family: monospace;
      word-break: break-all;
      line-height: 1.2;
      font-size: 12px;
      padding-top: 0;
    }
  }
</style>
