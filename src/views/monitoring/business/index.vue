<template>
  <div class="sandboxmain-container">
    <el-row :gutter="20">
      <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <span><b>监控列表</b></span>
          </div>
          <el-table
            v-loading="listLoading"
            :data="monitorlist"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <el-table-column
              label="监控名称"
              prop="title"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="业务场景"
              prop="service_scenario"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column label="业务等级" show-overflow-tooltip width="120">
              <template #default="{ row }">
                <el-tag :type="row.service_level | statusType">
                  {{ row.service_level | statusLevel }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              label="业务责任人"
              prop="owner"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column label="启用状态" show-overflow-tooltip>
              <template #default="{ row }">
                <el-tag :type="row.status | enableType">
                  {{ row.status | enableStatus }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" show-overflow-tooltip width="100">
              <template #default="{ row }">
                <el-button type="text" @click="handleEdit(row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="queryForm.pageNo"
            :layout="layout"
            :page-sizes="[10, 30, 50, 100]"
            :page-size="queryForm.pageSize"
            :total="total"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
          <edit ref="edit" @fetch-data="fetchData"></edit>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import moment from 'moment'
  import countTo from 'vue-count-to'
  import Edit from '../components/monitorEdit.vue'
  import BusDashboard from './dashboard.vue'

  export default {
    name: 'SlaInfo',
    components: { Edit, countTo, BusDashboard },
    filters: {
      enableStatus(status) {
        const statusMap = {
          0: '禁用',
          1: '启用',
        }
        return statusMap[status]
      },
      enableType(status) {
        const statusMap = {
          1: 'success',
          0: 'info',
        }
        return statusMap[status]
      },
      statusLevel(status) {
        const statusMap = {
          0: '核心',
          1: '重要',
          2: '一般',
          3: '边缘',
        }
        return statusMap[status]
      },
      statusType(status) {
        const statusMap = {
          0: 'danger',
          1: 'warning',
          2: 'warning',
          3: 'success',
        }
        return statusMap[status]
      },
      slaClass(val) {
        if (val > 0) {
          return 'title-success'
        } else if (val < 1) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },

      faultClass(val) {
        if (val < 1) {
          return 'title-success'
        } else if (val < 5) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },
    },
    data() {
      return {
        timeRange: '本月',
        changing: false,
        start_time: '',
        end_time: '',
        stat_warncount: 2,
        stat_count: 2,
        class_sla: 'title-success',
        class_fault: 'title-success',
        class_faulttime: 'title-success',
        class_remain: 'title-success',

        activities: [
          {
            content: '速度更快的2.8版本相册服务重构版本上线了！',
            timestamp: '2021-10-14',
          },
        ],

        currentUser: '',
        isAdmin: '',
        total: 0,
        reverse: true,
        listLoading: true,
        selectRows: '',
        layout: 'total, sizes, prev, pager, next, jumper',
        elementLoadingText: '正在加载...',
        monitorlist: [
          {
            id: 1,
            title: '手机现网H5扫码',
            service_scenario: '新用户点分享下载',
            service_level: 0,
            status: 1,
            monitor_type: '0',
            alarm_type: 0,
            owner: 'gufenglian',
            create_time: '2022-09-08 16:29',
            update_time: '2022-09-08 16:29',
            description: '用于监控安卓和苹果平台手机新用户扫码下载次数',
            interval: 10,
            alarm_sql:
              "select sum(if($lib='Android',1,0)) Android_num,sum(if($lib='iOS',1,0)) iOS_num,0 Error_Android,0 Error_iOS from events where event='AppInstall' and time>'{}' and $utm_campaign like '%现网-H5扫码%' /*SA(production)*/",
            threshold_01_key: '安卓告警值',
            threshold_01_value: '10',
            threshold_02_key: '苹果告警值',
            threshold_02_value: '10',
            threshold_03_key: '',
            threshold_03_value: '',
            threshold_04_key: '',
            threshold_04_value: '',
            threshold_05_key: '',
            threshold_05_value: '',
            endpoint: 'sum+by%28system%29+%28business_system_info%29',
          },
        ],
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    created() {
      this.start_time = moment().startOf('month').format('YYYY-MM-DD')
      this.end_time = moment().endOf('month').format('YYYY-MM-DD')
      this.fetchData()
      this.queryForm.str_time = this.start_time
      this.queryForm.end_time = this.end_time
      this.fetchEventData()
    },
    mounted() {},
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },
      handleTimeChange(refresh) {
        var s, e
        switch (this.timeRange) {
          case '本月':
            s = moment(+new Date()).startOf('month')
            break
          case '近半年':
            s = moment().subtract(5, 'month').startOf('month')
            break
          case '今年':
            s = moment().startOf('year')
            break
          case '全部':
            s = moment('2020-07-01')
            break
        }
        this.start_time = s.format('YYYY-MM-DD')
        this.end_time = moment()
          .subtract('month', -1)
          .startOf('month')
          .format('YYYY-MM-DD')

        if (refresh) {
          this.refreshPage()
        }
      },

      refreshPage() {
        this.changing = true
        this.fetchData()
        this.fetchEventData()
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchEventData()
      },

      async fetchEventData() {
        this.listLoading = true

        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },

      async fetchData() {
        const Loading = this.$baseLoading(6, '数据统计中...')

        setTimeout(() => {
          this.changing = false
          Loading.close()
        }, 300)
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table {
    th {
      background-color: #f0f6ff !important;
      color: #606266;
      font-size: 14px;
      font-weight: bold;
    }

    .cell {
      margin-left: 10px;
      font-weight: normal;
      font-size: 14px;
      color: #606266;
    }
  }

  .sla-panel {
    line-height: 0;
    text-align: center;
  }

  .sla-desc {
    font-size: 16px;
  }

  .title-danger {
    color: #f56c6c;
    font-size: 55px;
  }

  .title-warning {
    color: #e6a23c;
    font-size: 55px;
  }

  .title-success {
    color: #67c23a;
    font-size: 55px;
  }

  .title-info {
    color: #161616;
    font-size: 55px;
  }

  .el-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .sandboxmain-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }
</style>
