<template>
  <div class="biz-dashboard">
    <el-row :gutter="24" class="mb15">
      <el-card>
        <div slot="header" class="clearfix">
          <span
            style="display: flex; justify-content: center; align-items: center"
          >
            {{ monitorTitle() }}
          </span>
        </div>
        <div>
          <e-chart
            v-if="InitData"
            v-loading="plotChartLoading"
            :element-loading-text="elementLoadingText"
            :option="plotChart(InitData)"
            style="height: 500px"
          />
          <el-empty v-else description="暂无数据"></el-empty>
        </div>
      </el-card>
    </el-row>
  </div>
</template>

<script>
  import EChart from '@/views/security/component/echartsModel.vue'
  import { getBizMonidataIds, postBizMoniData } from '@/api/monitoring'
  import { commonEChartsOptions } from '@/views/monitoring/report/componets/commonEchartsOptions'

  export default {
    name: 'AlertDashboard',
    components: { EChart },
    data() {
      return {
        title: '业务监控',
        elementLoadingText: '图形加载中....',
        form: {
          biz_id: '',
          current_start_date: '',
          current_end_date: '',
          days: 3,
          window_size: 3,
          threshold: 0.3,
        },
        bizIdSelectData: [],
        InitData: {},
        plotChartLoading: true,
      }
    },

    async mounted() {
      try {
        this.plotChartLoading = true
        this.setDefaultDateRange()
        await Promise.all([this.fetchBizIds(), this.InitQueryData()])
      } catch (error) {
        console.error('Error during initialization:', error)
      } finally {
        this.plotChartLoading = false
      }
    },

    methods: {
      setDefaultDateRange() {
        const { biz_id, start_time, end_time } = this.$route.query
        this.form.biz_id = biz_id ? decodeURIComponent(biz_id) : ''
        this.form.current_start_date = start_time
          ? decodeURIComponent(start_time)
          : ''
        this.form.current_end_date = end_time
          ? decodeURIComponent(end_time)
          : ''
      },

      async fetchBizIds() {
        try {
          let res = await getBizMonidataIds()
          this.bizIdSelectData = res.data
        } catch (error) {
          console.error('Error fetching biz IDs:', error)
        }
      },

      async InitQueryData() {
        this.InitData = {}
        this.compareChartLoading = true
        try {
          let res = await postBizMoniData(this.form)
          if (res.data) {
            this.InitData = res.data
          } else {
            this.$message.error('数据获取失败')
            console.error('数据获取失败')
          }
        } catch (error) {
          this.$message.error('Error querying data: 参数异常')
          console.error('Error querying data: 参数异常')
        }
      },

      monitorTitle() {
        let filteredItem = this.bizIdSelectData.filter(
          (item) => item.value === this.form.biz_id
        )
        let titleName = filteredItem[0] || {}
        return `${titleName.label} ：【近3天对比率超过 ${
          this.form.threshold * 100
        }% 】`
      },

      plotChart(data) {
        if (!data.time || !data.time.length) {
          return {}
        }

        const option = {
          title: {
            text: '',
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let result = params[0].name + '<br/>'
              params.forEach(function (item) {
                result +=
                  item.marker +
                  item.seriesName +
                  ': ' +
                  parseFloat(item.value).toFixed(3) +
                  '<br/>'
              })
              const significantIndex = data.significant_deviation_time.indexOf(
                params[0].name
              )
              if (significantIndex !== -1) {
                const thresholdValue =
                  (parseFloat(data.thresholds[significantIndex]) * 100).toFixed(
                    2
                  ) + '%'
                const lastSpanIndex = result.lastIndexOf('</span>')
                const nextBrIndex = result.indexOf('<br/>', lastSpanIndex)
                if (lastSpanIndex !== -1 && nextBrIndex !== -1) {
                  const prefix = result.substring(0, lastSpanIndex + 7)
                  const suffix = result.substring(nextBrIndex)
                  const thresholdLabel = `Significant Deviation Threshold: ${thresholdValue}`
                  result = prefix + thresholdLabel + suffix
                }
              }
              return result
            },
          },
          legend: {
            data: ['Smoothed Current Data', 'Past Days Average'],
          },
          xAxis: {
            type: 'category',
            data: data.time,
          },
          textStyle: {
            ...commonEChartsOptions.textStyle,
          },
          toolbox: {
            left: 'right',
            top: 'center',
            bottom: 'center',
            orient: 'vertical',
            showTitle: false,
            ...commonEChartsOptions.toolbox,
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: function (value) {
                let formattedValue = parseFloat(value).toFixed(3)
                if (parseFloat(value) >= 1000) {
                  formattedValue = (value / 1000).toFixed(3) + 'K'
                }
                return formattedValue
              },
            },
          },
          series: [
            {
              name: 'Smoothed Current Data',
              type: 'line',
              data: data.smoothed_value,
              smooth: true,
            },
            {
              name: 'Past Days Average',
              type: 'line',
              data: data.value_avg,
              smooth: true,
            },
          ],
        }
        if (
          data.significant_deviation_time.length > 0 &&
          data.thresholds.length > 0
        ) {
          option.series.push({
            name: 'Significant Deviation',
            type: 'scatter',
            data: data.significant_deviation_time.map((time) => ({
              name: 'Significant Deviation',
              value: [time, data.smoothed_value[data.time.indexOf(time)]],
            })),
            itemStyle: {
              color: 'red',
            },
            symbol: 'circle',
            symbolSize: 10,
          })
        }
        return option
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb15 {
    margin: 8px 8px 15px 8px !important;
  }

  .base-el-col {
    padding: 0 8px;
    boder: 1px solid #f1f2f3;
  }

  .form-container {
    display: flex;
    align-items: center;

    h2 {
      margin: 0 20px 0 0;
      display: inline-block !important;
      height: 40px;
    }
  }

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
    flex: 1;
  }

  .timeSelect {
    flex: 1;
  }

  .span-value {
    float: right;
    color: #8492a6;
    font-size: 13px;
    margin-left: 20px;
  }

  ::v-deep .el-range-editor.el-input__inner {
    display: inline-flex;
    align-items: center;
    padding: 0px 10px !important;
  }
</style>
