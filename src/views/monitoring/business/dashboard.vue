<template>
  <div class="biz-dashboard">
    <el-row :gutter="24" class="mb15">
      <el-col class="base-el-col" style="margin-bottom: 0px; margin-top: 10px">
        <div class="form-container">
          <h2>{{ title }}</h2>
          <el-form ref="postForm" :inline="true" :model="form">
            <el-form-item prop="biz_id">
              <el-select
                v-model="form.biz_id"
                placeholder="选择业务ID"
                size="mini"
              >
                <el-option
                  v-for="item in bizIdSelectData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left">{{ item.label }}</span>
                  <span class="span-value">{{ item.value }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="监控ID">
              <el-input
                v-model="form.biz_id"
                disabled
                placeholder="实例名称"
                size="mini"
              ></el-input>
            </el-form-item>

            <el-form-item prop="dateRange">
              <el-date-picker
                v-model="form.dateRange"
                :default-time="['00:00:00', '23:59:59']"
                :picker-options="pickerOptions"
                end-placeholder="结束时间"
                size="mini"
                start-placeholder="开始时间"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
              ></el-date-picker>
            </el-form-item>

            <el-form-item prop="compare" style="margin-left: -11px">
              <el-select
                v-model="form.compare"
                clearable
                placeholder="比较选择"
                size="mini"
                @change="resetCompare"
              >
                <el-option
                  v-for="item in CompareSelect"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left">{{ item.label }}</span>
                  <span class="span-value">{{ item.value }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button size="mini" type="primary" @click="handleSubmit">
                Query
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="24" class="mb15">
      <el-card>
        <div slot="header" class="clearfix">
          <span>{{ compareTitle() }}</span>
        </div>
        <e-chart
          :key="chartKey"
          v-loading="compareChartLoading"
          :element-loading-text="elementLoadingText"
          :option="chartOptions"
          style="height: 500px"
        />
      </el-card>
    </el-row>
  </div>
</template>

<script>
  import EChart from '@/views/security/component/echartsModel.vue'
  import { getBizMonidataIds, postBizMoniDataCompare } from '@/api/monitoring'
  import moment from 'moment'
  import { commonEChartsOptions } from '@/views/monitoring/report/componets/commonEchartsOptions'

  export default {
    name: 'BusDashBoard',
    components: { EChart },
    data() {
      return {
        title: '业务监控',
        elementLoadingText: '图形加载中....',
        form: {
          biz_id: '',
          dateRange: [],
          days: 3,
          window_size: 3,
          threshold: 0.3,
          compare: '',
        },
        pickerOptions: {
          shortcuts: [
            {
              text: '近一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '近一个月',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setMonth(start.getMonth() - 1)
                picker.$emit('pick', [start, end])
              },
            },
          ],
        },
        bizIdSelectData: [],
        CompareSelect: [
          { label: '同比(上周同时间段)', value: 'YoW' },
          { label: '环比(昨天同时间段)', value: 'MoD' },
        ],
        compareAllData: {
          current_value: [],
          current_time: [],
          compare_value: [],
          compare_time: [],
        },
        chartKey: 0,
        compareChartLoading: false,
        chartOptions: {},
      }
    },
    watch: {
      'form.biz_id': {
        async handler(newValue, oldValue) {
          if (newValue !== oldValue) {
            this.compareChartLoading = true
            try {
              await this.InitQueryData()
            } catch (error) {
              console.error('initQueryData failed:', error)
            } finally {
              this.compareChartLoading = false
            }
          }
        },
        immediate: false,
      },
    },

    async mounted() {
      try {
        this.plotChartLoading = true
        this.setDefaultDateRange()
        await this.fetchBizIds()

        if (this.bizIdSelectData.length > 0) {
          this.form.biz_id = this.bizIdSelectData[0]['value']
        }
        await Promise.all([this.InitQueryData()])
      } catch (error) {
        console.error('Error during initialization:', error)
      } finally {
        this.plotChartLoading = false
      }
    },

    methods: {
      async resetCompare(value) {
        if (!value) {
          this.form.compare = ''
        }
        await this.InitQueryData()
        this.chartKey += 1
      },
      setDefaultDateRange() {
        const now = moment()
        const oneDayBefore = moment().subtract(1, 'days')
        this.form.dateRange = [
          oneDayBefore.format('YYYY-MM-DD HH:mm:ss'),
          now.format('YYYY-MM-DD HH:mm:ss'),
        ]
      },

      async fetchBizIds() {
        try {
          let res = await getBizMonidataIds()
          this.bizIdSelectData = res.data
        } catch (error) {
          console.error('Error fetching biz IDs:', error)
        } finally {
          this.plotChartLoading = false
        }
      },

      async InitQueryData() {
        this.compareChartLoading = true
        this.chartOptions = {}

        const [current_start_date, current_end_date] = this.form.dateRange

        const params = {
          biz_id: this.form.biz_id,
          current_start_date: current_start_date,
          current_end_date: current_end_date,
          compare: this.form.compare,
        }

        try {
          let res = await postBizMoniDataCompare(params)

          if (res) {
            this.compareAllData = res

            this.chartOptions = this.compareChart(this.compareAllData)
          } else {
            this.$message.error('数据获取失败')
            console.error('数据获取失败')
          }
        } catch (error) {
          this.$message.error('Error querying data: 参数异常')
          console.error('Error querying data: 参数异常')
        } finally {
          this.compareChartLoading = false
        }
      },

      async handleSubmit() {
        this.$refs['postForm'].validate(async (valid) => {
          if (valid) {
            await this.InitQueryData()
          } else {
            console.error('Form validation failed')
          }
        })
        this.chartKey += 1
      },

      compareTitle() {
        let filteredItem = this.bizIdSelectData.filter(
          (item) => item.value === this.form.biz_id
        )
        let titleName = filteredItem[0] || {}
        const labelName = this.CompareSelect.filter(
          (item) => item.value === this.form.compare
        )
        if (labelName.length > 0) {
          return `${titleName.label}：【${labelName[0].label}】`
        }
        return `${titleName.label}`
      },

      compareChart(data) {
        if (!data.current_time || !data.current_time.length) {
          return {}
        }

        const compare_time = data.compare_time

        const option = {
          title: {
            text: '',
          },
          textStyle: {
            ...commonEChartsOptions.textStyle,
          },
          toolbox: {
            left: 'right',
            top: 'center',
            bottom: 'center',
            orient: 'vertical',
            showTitle: false,
            ...commonEChartsOptions.toolbox,
          },

          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              let result = ''
              params.forEach((item, index) => {
                if (index === 0) {
                  result += 'Now Time: ' + item.name + '<br/>'
                }
                if (item.seriesName === 'Smoothed Current Data') {
                  result +=
                    item.marker +
                    item.seriesName +
                    ': ' +
                    parseFloat(item.value).toFixed(3) +
                    '<br/>'
                }
                if (
                  item.seriesName === 'Past Days Data' &&
                  compare_time.length > 0 &&
                  index < compare_time.length
                ) {
                  const currentTime = compare_time[item.dataIndex]
                  result += 'Compare Time: ' + currentTime + '<br/>'
                  result +=
                    item.marker +
                    item.seriesName +
                    ': ' +
                    parseFloat(item.value).toFixed(3) +
                    '<br/>'
                }
              })
              return result
            },
          },
          legend: {},
          xAxis: {
            type: 'category',
            data: data.current_time,
            axisLabel: {
              interval: 'auto',
              inside: false,
              rotate: -20,
              formatter: function (value) {
                const date = new Date(value)
                const year = date.getFullYear()
                const month = ('0' + (date.getMonth() + 1)).slice(-2)
                const day = ('0' + date.getDate()).slice(-2)
                const hour = ('0' + date.getHours()).slice(-2)
                const minute = ('0' + date.getMinutes()).slice(-2)
                return `${year}-${month}-${day} ${hour}:${minute}`
              },
            },
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: function (value) {
                let formattedValue = parseFloat(value).toFixed(3)
                if (parseFloat(value) >= 1000) {
                  formattedValue = (value / 1000).toFixed(3) + 'K'
                }
                return formattedValue
              },
            },
          },
          series: [
            {
              name: 'Smoothed Current Data',
              type: 'line',
              data: data.current_value,
              smooth: true,
            },
          ],
        }

        if (data.compare_value && data.compare_value.length > 0) {
          option.series.push({
            name: 'Past Days Data',
            type: 'line',
            data: data.compare_value,
            smooth: true,
          })
        }
        return option
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb15 {
    margin: 8px 8px 15px 8px !important;
  }

  .base-el-col {
    padding: 0 8px;
    boder: 1px solid #f1f2f3;
  }

  .form-container {
    display: flex;
    align-items: center;

    h2 {
      margin: 0 20px 0 0;
      display: inline-block !important;
      height: 40px;
    }
  }

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
    flex: 1;
  }

  .span-value {
    float: right;
    color: #8492a6;
    font-size: 13px;
    margin-left: 20px;
  }

  ::v-deep .el-range-editor.el-input__inner {
    display: inline-flex;
    align-items: center;
    padding: 0px 10px !important;
  }
</style>
