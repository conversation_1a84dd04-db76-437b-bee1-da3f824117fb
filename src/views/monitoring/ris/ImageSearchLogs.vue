<template>
  <div class="image-search-container">
    <!-- 顶部搜索栏 -->
    <vab-query-form>
      <el-form
        :inline="true"
        class="search-form"
        @submit.native.prevent="handleSearch"
      >
        <el-form-item label="albumid">
          <el-input
            v-model="searchParams.albumId"
            placeholder="albumid"
            clearable
            size="small"
            style="width: 100px"
          ></el-input>
        </el-form-item>

        <el-form-item label="URL">
          <el-input
            v-model="searchParams.url"
            placeholder="图搜url"
            clearable
            size="small"
            style="width: 150px"
          ></el-input>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="small"
            format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 380px"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="平均距离">
          <el-select
            v-model="searchParams.similarityOperator"
            size="small"
            style="width: 100px"
            placeholder="条件"
          >
            <el-option label="小于" value="lt"></el-option>
            <el-option label="小于等于" value="lte"></el-option>
            <el-option label="大于" value="gt"></el-option>
            <el-option label="大于等于" value="gte"></el-option>
          </el-select>
          <el-input-number
            v-model="searchParams.similarityThreshold"
            :min="0"
            :max="1"
            :step="0.1"
            :precision="2"
            size="small"
            style="width: 120px"
            placeholder="阈值"
          ></el-input-number>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="small"
            :loading="searchLoading"
            @click="handleSearch"
          >
            搜索
          </el-button>
          <el-button size="small" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </vab-query-form>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 左侧日志列表 -->
      <div class="left-panel" :class="{ collapsed: mobileView && showDetail }">
        <div class="log-list-header">
          <span>搜索日志列表 ({{ totalLogs }})</span>
          <el-button
            type="text"
            size="mini"
            :loading="searchLoading"
            @click="refreshLogs"
          >
            刷新
          </el-button>
        </div>

        <div v-loading="searchLoading" class="log-list">
          <div v-if="logs.length === 0 && !searchLoading" class="empty-state">
            <p>暂无日志数据</p>
          </div>

          <div
            v-for="(log, index) in logs"
            :key="log.id"
            class="log-item"
            :class="{ active: selectedLog && selectedLog.id === log.id }"
            @click="handleSelectLog(log)"
          >
            <div class="log-time">
              {{ formatTime(log.timestamp) }}
            </div>
            <div class="log-info">
              <div class="log-album">
                {{ log.albumId }}
              </div>
              <span
                class="log-status"
                :class="log.status === 200 ? 'status-success' : 'status-error'"
              >
                {{ log.status }}
              </span>
            </div>
            <div class="log-meta">
              <span class="meta-item">{{ log.responseTime }}ms</span>
              <span class="meta-item">{{ log.resultCount }} 结果</span>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalLogs > 0" class="pagination-wrapper">
          <el-pagination
            small
            layout="total, prev, pager, next"
            :total="totalLogs"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          ></el-pagination>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div
        ref="rightPanel"
        class="right-panel"
        :class="{ expanded: mobileView && showDetail }"
      >
        <div v-if="!selectedLog" class="empty-state" style="margin: auto">
          <p>请选择一条日志查看详情</p>
        </div>

        <div v-else class="detail-container">
          <el-button
            v-if="mobileView"
            type="text"
            style="margin-bottom: 10px"
            @click="showDetail = false"
          >
            <i class="el-icon-arrow-left"></i>
            返回列表
          </el-button>

          <!-- 搜索图片信息 -->
          <div class="source-section">
            <div class="source-header">
              <div class="source-title">搜索图片</div>
              <el-tag
                :type="selectedLog.status === 200 ? 'success' : 'danger'"
                size="small"
              >
                状态: {{ selectedLog.status }}
              </el-tag>
            </div>

            <div class="source-content">
              <div class="source-image-container">
                <img
                  :src="getPreviewUrl(selectedLog.searchImage)"
                  :alt="'搜索图片'"
                  class="source-image"
                  @click="viewOriginalImage(selectedLog.searchImage)"
                  @error="handleImageError"
                />
              </div>

              <div class="source-details">
                <div class="detail-item">
                  <div class="detail-label">url</div>
                  <div class="detail-value">{{ selectedLog.searchImage }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">albumid</div>
                  <div class="detail-value">{{ selectedLog.albumId }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">User Album ID</div>
                  <div class="detail-value">{{ selectedLog.userAlbumId }}</div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">响应时间</div>
                  <div class="detail-value">
                    {{ selectedLog.responseTime }}ms
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">结果数量</div>
                  <div class="detail-value">{{ selectedLog.resultCount }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 搜索结果 -->
          <div
            v-if="selectedLog.results && selectedLog.results.length > 0"
            class="results-section"
          >
            <div class="results-header">
              <div class="results-title">搜索结果</div>
              <div class="results-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ selectedLog.results.length }}</div>
                  <div class="stat-label">总数</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    {{ getAvgSimilarity(selectedLog.results) }}%
                  </div>
                  <div class="stat-label">平均相似度</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">
                    {{ getMaxSimilarity(selectedLog.results) }}%
                  </div>
                  <div class="stat-label">最高相似度</div>
                </div>
              </div>
            </div>

            <div class="results-grid">
              <div
                v-for="(result, index) in selectedLog.results"
                :key="`${selectedLog.id}-${index}`"
                class="result-card"
                @click="showResultDetail(result, index)"
              >
                <div class="result-rank">#{{ index + 1 }}</div>
                <img
                  :src="getPreviewUrl(result.param1)"
                  :alt="`结果 ${index + 1}`"
                  class="result-image"
                  @error="handleImageError"
                />
                <div class="result-info">
                  <div
                    class="similarity-score"
                    :class="getSimilarityClass(result.similarity)"
                  >
                    {{ (result.similarity * 100).toFixed(2) }}%
                  </div>
                  <div class="similarity-bar">
                    <div
                      class="similarity-fill"
                      :style="{ width: result.similarity * 100 + '%' }"
                    ></div>
                  </div>
                  <div class="result-filename">
                    {{ getFilename(result.param1) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 结果详情对话框 -->
    <el-dialog
      :title="`结果 #${currentResultIndex + 1} 详情`"
      :visible.sync="resultDialogVisible"
      width="80%"
      top="5vh"
    >
      <div v-if="currentResult">
        <div class="compare-container">
          <div class="compare-item">
            <h4>原图</h4>
            <img
              :src="selectedLog.searchImage"
              alt="原图"
              class="compare-image"
            />
          </div>
          <div class="compare-arrow">→</div>
          <div class="compare-item">
            <h4>搜索结果</h4>
            <img
              :src="currentResult.param1"
              alt="搜索结果"
              class="compare-image"
            />
          </div>
        </div>

        <el-descriptions :column="2" border style="margin-top: 20px">
          <el-descriptions-item label="相似度">
            <el-tag :type="getSimilarityTagType(currentResult.similarity)">
              {{ (currentResult.similarity * 100).toFixed(4) }}%
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="索引ID">
            {{ currentResult.index }}
          </el-descriptions-item>
          <el-descriptions-item label="搜索类型">
            {{ currentResult.searchType }}
          </el-descriptions-item>
          <el-descriptions-item label="哈希值">
            {{ currentResult.param2 }}
          </el-descriptions-item>
          <el-descriptions-item label="图片URL" :span="2">
            <el-link
              :href="currentResult.param1"
              target="_blank"
              type="primary"
            >
              {{ currentResult.param1 }}
            </el-link>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    getImageSearchLogs,
    getImageSearchStatistics,
    getImageSearchRawLog,
    exportImageSearchLogs,
  } from '@/api/logcenter'
  import moment from 'moment'

  export default {
    name: 'ImageSearchLogs',
    data() {
      // 计算15分钟前的时间
      const now = new Date()
      const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000)

      return {
        // 搜索参数
        searchParams: {
          albumId: '',
          url: '',
          userAlbumId: '',
          similarityOperator: 'lt',
          similarityThreshold: 0.4,
        },
        timeRange: [fifteenMinutesAgo, now],

        // 状态管理
        searchLoading: false,
        mobileView: window.innerWidth < 768,
        showDetail: false,

        // 分页
        currentPage: 1,
        pageSize: 50,
        totalLogs: 0,

        // 数据
        logs: [],
        selectedLog: null,

        // 对话框
        resultDialogVisible: false,
        currentResult: null,
        currentResultIndex: 0,

        // 定时器
        refreshTimer: null,
      }
    },

    computed: {
      searchParamsWithTime() {
        return {
          ...this.searchParams,
          startTime: this.timeRange[0],
          endTime: this.timeRange[1],
        }
      },
    },

    mounted() {
      this.handleSearch()

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
      }
    },

    methods: {
      handleResize() {
        this.mobileView = window.innerWidth < 768
      },

      async handleSearch() {
        this.currentPage = 1
        this.selectedLog = null
        await this.fetchLogs()
      },

      async handleReset() {
        const now = new Date()
        const fifteenMinutesAgo = new Date(now.getTime() - 15 * 60 * 1000)

        this.searchParams = {
          albumId: '',
          url: '',
          userAlbumId: '',
        }
        this.timeRange = [fifteenMinutesAgo, now]
        this.currentPage = 1

        await this.fetchLogs()
      },

      async fetchLogs(silent = false) {
        if (!silent) {
          this.searchLoading = true
        }

        try {
          const query = this.buildQuery()
          const indices = this.generateIndices()
          const from = (this.currentPage - 1) * this.pageSize

          // 先获取日志列表
          const logsResponse = await getImageSearchLogs({
            query,
            indices,
            size: this.pageSize,
            from: from,
          })
          // 处理日志数据
          if (logsResponse) {
            this.logs = this.transformLogs(logsResponse.hits.hits)
            this.totalLogs =
              logsResponse.hits.total.value || logsResponse.hits.total
          }

          // 如果是第一页且有数据，自动选中第一条
          if (!silent && this.currentPage === 1 && this.logs.length > 0) {
            this.handleSelectLog(this.logs[0])
          }
        } catch (error) {
          console.error('获取日志失败:', error)
          if (!silent) {
            this.$message.error(
              '获取日志失败：' +
                (error.response?.data?.message || error.message)
            )
          }
        } finally {
          this.searchLoading = false
        }
      },

      // 单独处理统计信息
      async fetchStatistics(query, indices) {
        try {
          const response = await getImageSearchStatistics({ query, indices })
          if (response && response.aggregations) {
            this.statistics = this.transformStatistics(response.aggregations)
          }
        } catch (error) {
          // 统计失败不影响主功能
          console.error('获取统计信息失败:', error)
        }
      },
      // 查看原始日志
      async viewRawLog(log) {
        try {
          const response = await getImageSearchRawLog(log.id, log.index)

          // 格式化JSON显示
          const formattedJson = JSON.stringify(response.data, null, 2)

          this.$alert(
            `<pre style="max-height: 500px; overflow: auto;">${this.escapeHtml(
              formattedJson
            )}</pre>`,
            '原始日志',
            {
              dangerouslyUseHTMLString: true,
              customClass: 'raw-log-dialog',
              confirmButtonText: '关闭',
            }
          )
        } catch (error) {
          this.$message.error('获取原始日志失败：' + error.message)
        }
      },
      // 导出日志
      async exportLogs() {
        try {
          const query = this.buildQuery()
          const indices = this.generateIndices()

          this.$confirm('确定要导出当前搜索条件下的所有日志吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            const loading = this.$loading({
              lock: true,
              text: '正在导出...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)',
            })

            try {
              const response = await exportImageSearchLogs({
                query,
                indices,
                size: 10000, // 最大导出10000条
              })

              // 创建下载链接
              const blob = new Blob([response.data], {
                type: 'application/json',
              })
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = `image_search_logs_${moment().format(
                'YYYYMMDD_HHmmss'
              )}.json`
              document.body.appendChild(link)
              link.click()
              document.body.removeChild(link)
              window.URL.revokeObjectURL(url)

              this.$message.success('导出成功')
            } finally {
              loading.close()
            }
          })
        } catch (error) {
          this.$message.error('导出失败：' + error.message)
        }
      },

      // HTML转义
      escapeHtml(unsafe) {
        return unsafe
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#039;')
      },

      // 转换统计数据
      transformStatistics(aggs) {
        return {
          statusDistribution: this.transformStatusDistribution(
            aggs.status_stats
          ),
          responseTimeDistribution: this.transformResponseTimeDistribution(
            aggs.response_time_stats
          ),
          avgResponseTime: this.formatResponseTime(
            aggs.avg_response_time?.value || 0
          ),
          dbDistribution: this.transformDbDistribution(aggs.db_distribution),
          albumStats: this.transformAlbumStats(aggs.album_stats),
          hourlyTrend: this.transformHourlyTrend(aggs.hourly_trend),
          recentErrors: this.transformRecentErrors(aggs.recent_errors),
          similarityDistribution: this.transformSimilarityDistribution(
            aggs.similarity_distribution
          ),
        }
      },

      // 格式化响应时间
      formatResponseTime(seconds) {
        return `${Math.round(seconds * 1000)}ms`
      },

      buildQuery() {
        const must = []

        // 时间范围（必须）
        must.push({
          range: {
            '@timestamp': {
              gte: this.searchParamsWithTime.startTime.toISOString(),
              lte: this.searchParamsWithTime.endTime.toISOString(),
            },
          },
        })

        // Album ID
        if (this.searchParams.albumId) {
          must.push({
            term: {
              'request.albumId.keyword': this.searchParams.albumId,
            },
          })
        }

        // URL
        if (this.searchParams.url) {
          must.push({
            match_phrase_prefix: {
              'request.url': this.searchParams.url,
            },
          })
        }

        // 相似度平均值过滤
        if (
          this.searchParams.similarityThreshold !== null &&
          this.searchParams.similarityThreshold !== undefined &&
          this.searchParams.similarityOperator
        ) {
          // 使用 script query 计算平均相似度
          must.push({
            script: {
              script: {
                source: `
            if (!doc.containsKey('response.result.similarity') || doc['response.result.similarity'].size() == 0) {
              return false;
            }
            double sum = 0;
            for (def similarity : doc['response.result.similarity']) {
              sum += similarity;
            }
            double avg = sum / doc['response.result.similarity'].size();

            if (params.operator == 'lt') return avg < params.threshold;
            else if (params.operator == 'lte') return avg <= params.threshold;
            else if (params.operator == 'gt') return avg > params.threshold;
            else if (params.operator == 'gte') return avg >= params.threshold;
            return false;
          `,
                params: {
                  operator: this.searchParams.similarityOperator,
                  threshold: this.searchParams.similarityThreshold,
                },
              },
            },
          })
        }

        return {
          bool: {
            must,
          },
        }
      },

      generateIndices() {
        const indices = []
        const start = new Date(this.searchParamsWithTime.startTime)
        const end = new Date(this.searchParamsWithTime.endTime)

        // 生成日期范围内的所有索引名称
        for (let d = new Date(start); d <= end; d.setHours(d.getHours() + 1)) {
          const year = d.getFullYear()
          const month = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          const hour = String(d.getHours()).padStart(2, '0')
          const indexName = `imgsearch_result_${year}.${month}.*`

          if (!indices.includes(indexName)) {
            indices.push(indexName)
          }
        }

        return indices.join(',')
      },

      transformLogs(hits) {
        return hits.map((hit) => {
          const source = hit._source
          return {
            id: hit._id,
            index: hit._index,
            timestamp: source['@timestamp'],
            status: source.status || 0,
            responseTime: Math.round((source.response_time || 0) * 1000),
            albumId: source.request?.albumId || '',
            userAlbumId: source.request?.userAlbumId || '',
            dbName: source.request?.dbName || '',
            locScoreMin: source.request?.locScoreMin || 0,
            searchImage: source.request?.url || '',
            resultCount: source.result_size || 0,
            results: source.response?.result || [],
            errorCode: source.error_code || source.response?.errcode,
            message: source.message || '',
            _source: source, // 保留原始数据
          }
        })
      },

      handleSelectLog(log) {
        this.selectedLog = log
        if (this.mobileView) {
          this.showDetail = true
        }
        this.$nextTick(() => {
          const detailContainer = document.querySelector('.detail-container')
          if (detailContainer) {
            detailContainer.scrollTop = 0
          }
        })
      },

      handlePageChange(page) {
        this.currentPage = page
        this.fetchLogs()
      },

      refreshLogs() {
        this.fetchLogs()
      },

      formatTime(timestamp) {
        return moment(timestamp).format('YYYY-MM-DD HH:mm:ss')
      },

      getPreviewUrl(url) {
        if (!url) return ''
        const separator = url.includes('?') ? '&' : '?'
        return (
          url +
          separator +
          'imageMogr2/auto-orient/thumbnail/!320x320r/quality/100/format/jpg'
        )
      },

      viewOriginalImage(url) {
        window.open(url, '_blank')
      },

      getFilename(url) {
        if (!url) return 'unknown'
        const parts = url.split('/')
        return parts[parts.length - 1] || 'unknown'
      },

      getSimilarityClass(similarity) {
        if (similarity > 0.3) return 'similarity-high'
        if (similarity > 0.25) return 'similarity-medium'
        return 'similarity-low'
      },

      getSimilarityTagType(similarity) {
        if (similarity > 0.3) return 'danger'
        if (similarity > 0.25) return 'warning'
        return 'primary'
      },

      getAvgSimilarity(results) {
        if (!results || results.length === 0) return '0'
        const sum = results.reduce((acc, r) => acc + r.similarity, 0)
        return ((sum / results.length) * 100).toFixed(1)
      },

      getMaxSimilarity(results) {
        if (!results || results.length === 0) return '0'
        const max = Math.max(...results.map((r) => r.similarity))
        return (max * 100).toFixed(1)
      },

      showResultDetail(result, index) {
        this.currentResult = result
        this.currentResultIndex = index
        this.resultDialogVisible = true
      },

      handleImageError(e) {
        e.target.src =
          'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2VlZSIvPjx0ZXh0IHRleHQtYW5jaG9yPSJtaWRkbGUiIHg9IjEwMCIgeT0iMTAwIiBzdHlsZT0iZmlsbDojYWFhO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1zaXplOjEzcHg7Zm9udC1mYW1pbHk6QXJpYWwsSGVsdmV0aWNhLHNhbnMtc2VyaWY7ZG9taW5hbnQtYmFzZWxpbmU6Y2VudHJhbCI+图片加载失败</3RleHQ+PC9zdmc+'
      },
    },
  }
</script>

<style scoped>
  .image-search-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
  }

  /* 头部样式 */
  .header {
    background: white;
    padding: 15px 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    z-index: 100;
  }

  .search-form {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  /* 主容器样式 */
  .main-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    height: calc(100vh - 70px); /* 减去顶部搜索栏的高度 */
  }

  /* 左侧面板样式 */
  .left-panel {
    width: 400px;
    background: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .log-list-header {
    padding: 15px;
    border-bottom: 1px solid #e4e7ed;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
  }

  .log-list {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
  }

  .log-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f2f5;
    cursor: pointer;
    transition: all 0.2s;
  }

  .log-item:hover {
    background: #f5f7fa;
  }

  .log-item.active {
    background: #ecf5ff;
    border-left: 3px solid #409eff;
    padding-left: 12px;
  }

  .log-time {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  .log-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  .log-album {
    font-size: 13px;
    color: #606266;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
  }

  .log-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 3px;
    margin-left: 10px;
  }

  .status-success {
    background: #f0f9ff;
    color: #67c23a;
  }

  .status-error {
    background: #fef0f0;
    color: #f56c6c;
  }

  .log-meta {
    display: flex;
    gap: 10px;
    font-size: 11px;
    color: #909399;
  }

  .meta-item {
    display: inline-flex;
    align-items: center;
  }

  .pagination-wrapper {
    padding: 10px;
    border-top: 1px solid #e4e7ed;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
  }

  /* 右侧面板样式 */
  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
    height: 100%;
    min-width: 0;
  }

  .detail-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    min-height: 0;
  }

  /* 源图片部分样式 */
  .source-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  .source-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .source-title {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
  }

  .source-content {
    display: flex;
    gap: 20px;
  }

  .source-image-container {
    flex-shrink: 0;
  }

  .source-image {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: zoom-in;
    transition: transform 0.3s;
  }

  .source-image:hover {
    transform: scale(1.02);
  }

  .source-details {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
  }

  .detail-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  .detail-value {
    font-size: 14px;
    color: #303133;
    font-weight: 500;
    word-break: break-all;
  }

  /* 结果部分样式 */
  .results-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .results-title {
    font-size: 18px;
    font-weight: 500;
    color: #303133;
  }

  .results-stats {
    display: flex;
    gap: 30px;
  }

  .stat-item {
    text-align: center;
  }

  .stat-value {
    font-size: 20px;
    font-weight: 600;
    color: #409eff;
  }

  .stat-label {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }

  .results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .result-card {
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    cursor: pointer;
    position: relative;
  }

  .result-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  .result-rank {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
  }

  .result-image {
    width: 100%;
    height: 180px;
    object-fit: cover;
  }

  .result-info {
    padding: 12px;
  }

  .similarity-score {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 6px;
  }

  .similarity-high {
    color: #f56c6c;
  }

  .similarity-medium {
    color: #e6a23c;
  }

  .similarity-low {
    color: #409eff;
  }

  .similarity-bar {
    height: 4px;
    background: #f0f2f5;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
  }

  .similarity-fill {
    height: 100%;
    background: linear-gradient(90deg, #409eff 0%, #f56c6c 100%);
    transition: width 0.3s;
  }

  .result-filename {
    font-size: 12px;
    color: #909399;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 空状态样式 */
  .empty-state {
    text-align: center;
    padding: 40px;
    color: #909399;
  }

  /* 对比容器样式 */
  .compare-container {
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .compare-item {
    text-align: center;
  }

  .compare-item h4 {
    margin-bottom: 10px;
    color: #606266;
  }

  .compare-image {
    max-width: 400px;
    max-height: 400px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .compare-arrow {
    font-size: 24px;
    color: #909399;
  }

  /* 响应式样式 */
  @media (max-width: 768px) {
    .left-panel {
      width: 100%;
    }

    .main-container {
      height: calc(100vh - 80px); /* 移动端调整高度 */
    }

    .right-panel {
      display: none;
    }

    .left-panel.collapsed {
      display: none;
    }

    .right-panel.expanded {
      display: flex;
    }

    .source-content {
      flex-direction: column;
    }

    .source-image {
      width: 100%;
      max-width: 300px;
      height: auto;
    }

    .compare-container {
      flex-direction: column;
    }

    .compare-arrow {
      transform: rotate(90deg);
    }
  }
</style>
