<!-- src/views/monitor/components/OverviewCards.vue -->
<template>
  <div class="overview-cards">
    <div class="metric-card">
      <div class="metric-label">总体 QPS</div>
      <div v-loading="loading" class="metric-value-container">
        <span class="metric-value">{{ formatQps(metrics.totalQps) }}</span>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-label">错误数</div>
      <div v-loading="loading" class="metric-value-container">
        <div
          class="metric-value filter-button"
          title="点击筛选错误记录"
          @click.stop.prevent="onErrorClick"
        >
          <span>{{ metrics.errorCount }}</span>
        </div>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-label">平均响应时间</div>
      <div v-loading="loading" class="metric-value-container">
        <span class="metric-value">
          {{ formatResponseTime(metrics.avgResponseTime) }}
        </span>
        <span class="metric-unit">毫秒</span>
      </div>
    </div>

    <div class="metric-card">
      <div class="metric-label">链路追踪</div>
      <div v-loading="loading" class="metric-value-container">
        <div
          class="metric-value filter-button"
          title="点击查看链路追踪"
          @click.stop.prevent="onTraceClick"
        >
          <span>{{ metrics.errorTracingCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'OverviewCards',
    props: {
      metrics: {
        type: Object,
        default: () => ({
          totalQps: 0,
          errorCount: 0,
          avgResponseTime: 0,
          errorTracingCount: 0,
        }),
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    methods: {
      formatQps(num) {
        return Math.round(num)
      },
      formatResponseTime(time) {
        return (time * 1000).toFixed(1)
      },
      onErrorClick() {
        // 触发自定义事件，让父组件添加 status<>200 的过滤条件
        this.$emit('error-click')
      },
      onTraceClick() {
        // 触发自定义事件，让父组件显示错误追踪详情
        this.$emit('trace-click')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .overview-cards {
    display: flex;
    margin-bottom: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .metric-card {
      flex: 1;
      padding: 12px 16px;
      display: flex;
      flex-direction: column;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 20%;
        height: 60%;
        width: 1px;
        background-color: #ebeef5;
      }

      .metric-label {
        font-size: 13px;
        color: #909399;
        margin-bottom: 6px;
      }

      .metric-value-container {
        display: flex;
        align-items: baseline;

        .metric-value {
          font-size: 24px;
          font-weight: 500;
          line-height: 1;
        }

        .filter-button {
          display: flex;
          align-items: center;
          cursor: pointer;

          .filter-btn {
            margin-left: 8px;
            padding: 2px 8px;
            font-size: 12px;
            line-height: 1.2;
            border-radius: 12px;
            display: inline-flex;
            align-items: center;

            i {
              margin-right: 4px;
            }
          }

          &:hover {
            opacity: 0.8;
          }
        }

        .metric-unit {
          font-size: 12px;
          color: #909399;
          margin-left: 4px;
        }
      }

      &:nth-child(1) .metric-value {
        color: #409eff;
      }

      &:nth-child(2) .metric-value {
        color: #f56c6c;
      }

      &:nth-child(3) .metric-value {
        color: #67c23a;
      }

      &:nth-child(4) .metric-value {
        color: #e6a23c;
      }
    }
  }
</style>
