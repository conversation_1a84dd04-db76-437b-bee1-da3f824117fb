<!-- src/views/monitor/components/TraceDetails.vue -->
<template>
  <div class="trace-details">
    <div class="header">
      <h3>链路追踪详情</h3>
      <el-button size="small" @click="$emit('close')">关闭</el-button>
    </div>

    <el-table v-loading="loading" :data="traceData" style="width: 100%" border>
      <el-table-column prop="created_at" label="时间" width="130">
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="host"
        label="服务"
        width="120"
        show-overflow-tooltip
      />
      <el-table-column
        prop="uri"
        label="接口"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column prop="status" label="状态" width="70">
        <template slot-scope="scope">
          <span class="status-code" :class="getStatusClass(scope.row.status)">
            {{ scope.row.status }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="err_code" label="错误码" width="90">
        <template slot-scope="scope">
          <el-tooltip
            v-if="scope.row.err_msg"
            placement="top"
            :content="scope.row.err_msg"
          >
            <span class="error-code">{{ scope.row.err_code || '-' }}</span>
          </el-tooltip>
          <span v-else>{{ scope.row.err_code || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="user_id"
        label="用户ID"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column label="操作" width="80">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-tooltip
              v-if="scope.row.trace_id"
              content="Pinpoint"
              placement="top"
            >
              <a
                :href="getPinpointUrl(scope.row.trace_id)"
                target="_blank"
                class="icon-button"
              >
                <i class="el-icon-share"></i>
              </a>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.request_id"
              content="Kibana"
              placement="top"
            >
              <a
                :href="getKibanaUrl(scope.row.request_id)"
                target="_blank"
                class="icon-button"
              >
                <i class="el-icon-document"></i>
              </a>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        layout="total, prev, pager, next"
        background
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script>
  import { getTraceDetails } from '@/api/apigw.js'

  export default {
    name: 'TraceDetails',
    props: {
      timeRange: {
        type: Object,
        required: true,
      },
      filters: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        traceData: [],
        loading: false,
        pagination: {
          currentPage: 1,
          pageSize: 10,
          total: 0,
        },
      }
    },
    watch: {
      timeRange: {
        handler: 'fetchTraceDetails',
        deep: true,
      },
      filters: {
        handler: 'fetchTraceDetails',
        deep: true,
      },
    },
    created() {
      this.fetchTraceDetails()
    },
    methods: {
      withTimeout(promise, timeout = 10000) {
        return Promise.race([
          promise,
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error(`Timeout after ${timeout} ms`)),
              timeout
            )
          ),
        ])
      },
      async fetchTraceDetails() {
        this.loading = true
        const params = {
          start_time: this.timeRange.start.toISOString(),
          end_time: this.timeRange.end.toISOString(),
          page: this.pagination.currentPage,
          page_size: this.pagination.pageSize,
        }

        // Add filters
        this.filters.forEach((filter) => {
          if (filter.type === 'path') {
            params.uri = filter.value
          } else if (filter.type === 'responseTime') {
            // Skip response time filters
          } else if (filter.type === 'status') {
            if (!filter.include && filter.value === 200) {
              // If excluding status 200, show only errors (status >= 400)
              params.status = '>400'
            } else {
              params[filter.type] = filter.value
            }
          } else {
            if (Array.isArray(filter.value)) {
              params[filter.type] = filter.value.join(',')
            } else {
              params[filter.type] = filter.value
            }

            if (!filter.include) {
              params['not_' + filter.type] = params[filter.type]
              delete params[filter.type]
            }
          }
        })

        try {
          const response = await this.withTimeout(
            getTraceDetails(params),
            18000
          )
          this.traceData = response.data
          this.pagination.total = response.total
        } catch (error) {
          console.error('Error fetching trace details:', error)
        } finally {
          this.loading = false
        }
      },
      handlePageChange(page) {
        this.pagination.currentPage = page
        this.fetchTraceDetails()
      },
      getPinpointUrl(traceId) {
        // Create the transaction info object
        const transactionInfo = {
          agentId: decodeURIComponent(traceId),
          spanId: '-1',
          traceId: decodeURIComponent(traceId),
          collectorAcceptTime: '0',
        }

        // Encode the JSON object as a URL parameter
        const encodedInfo = encodeURIComponent(JSON.stringify(transactionInfo))

        // Return the complete Pinpoint URL
        return `https://pinpoint.in.szwego.com/transactionDetail?transactionInfo=${encodedInfo}`
      },
      getKibanaUrl(requestId) {
        // Calculate the time range based on the component's timeRange prop
        const fromTime = this.calculateTimeRangeForKibana()

        // Create the Kibana URL with the proper time range and request ID
        return `https://kib.in.szwego.com/app/kibana#/discover/376b0950-c8cb-11eb-beaf-012b7c96e129?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:${fromTime},to:now))&_a=(columns:!(status,npath,x_errcode,request_body,x_req_id),filters:!(),index:'7aa73370-4800-11eb-8024-6335eded8129',interval:auto,query:(language:kuery,query:'x_req_id:${requestId}'),sort:!(!('@timestamp',desc)))`
      },

      // Helper method to calculate the time range string for Kibana
      calculateTimeRangeForKibana() {
        // Calculate the difference between timeRange.start and timeRange.end in minutes
        const diffMs = this.timeRange.end - this.timeRange.start
        const diffMinutes = Math.round(diffMs / (1000 * 60))

        // If the time range is less than 24 hours, use relative time (now-Xm)
        if (diffMinutes <= 24 * 60) {
          return `now-${diffMinutes}m`
        }
        // If longer, use relative time in hours or days
        else if (diffMinutes <= 72 * 60) {
          const hours = Math.round(diffMinutes / 60)
          return `now-${hours}h`
        } else {
          const days = Math.round(diffMinutes / (60 * 24))
          return `now-${days}d`
        }
      },
      formatDate(dateString) {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
      },
      getStatusClass(status) {
        if (!status) return ''
        status = parseInt(status)
        if (status >= 500) return 'status-5xx'
        if (status >= 400) return 'status-4xx'
        if (status >= 300) return 'status-3xx'
        if (status >= 200) return 'status-2xx'
        return ''
      },
    },
  }
</script>

<style lang="scss" scoped>
  .trace-details {
    padding: 5px 0;

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .pagination {
      margin-top: 15px;
      text-align: right;
    }

    .action-buttons {
      display: flex;
      justify-content: space-around;

      .icon-button {
        color: #409eff;
        cursor: pointer;
        margin: 0 3px;

        &:hover {
          color: #66b1ff;
        }
      }
    }

    .error-code {
      cursor: help;
      text-decoration: underline dotted;
    }

    .status-code {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 500;

      &.status-2xx {
        background-color: #f0f9eb;
        color: #67c23a;
      }

      &.status-3xx {
        background-color: #f4f4f5;
        color: #909399;
      }

      &.status-4xx {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      &.status-5xx {
        background-color: #fef0f0;
        color: #f56c6c;
      }
    }
  }
</style>
