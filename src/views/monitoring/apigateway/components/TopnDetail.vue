<!-- src/views/monitor/components/TopnDetail.vue -->
<template>
  <div v-loading="loading" class="topn-detail">
    <el-table
      :data="detailData"
      style="width: 100%"
      border
      stripe
      height="calc(100vh - 200px)"
    >
      <el-table-column type="index" label="#" width="50"></el-table-column>

      <el-table-column
        v-if="displayColumns.includes('host')"
        prop="host"
        label="微服务"
        width="180"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        v-if="displayColumns.includes('path')"
        prop="path"
        label="路径"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        v-if="displayColumns.includes('qps')"
        prop="qps"
        label="QPS"
        width="100"
        align="right"
      >
        <template slot-scope="scope">
          {{ scope.row.qps.toFixed(2) }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="displayColumns.includes('avg_response_time')"
        prop="avg_response_time"
        label="平均响应时间(ms)"
        width="150"
        align="right"
      >
        <template slot-scope="scope">
          {{ (scope.row.avg_response_time * 1000).toFixed(2) }}
        </template>
      </el-table-column>

      <el-table-column
        v-if="displayColumns.includes('error_count')"
        prop="error_count"
        label="错误数"
        width="100"
        align="right"
      ></el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="pagination.total"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
  import { getTopN } from '@/api/apigw.js'

  export default {
    name: 'TopnDetail',
    props: {
      dimension: {
        type: String,
        default: '',
      },
      filters: {
        type: Array,
        default: () => [],
      },
      timeRange: {
        type: Object,
        required: true,
      },
    },
    data() {
      return {
        detailData: [],
        loading: false,
        pagination: {
          currentPage: 1,
          pageSize: 50,
          total: 0,
        },
      }
    },
    computed: {
      displayColumns() {
        switch (this.dimension) {
          case 'url_qps':
            return ['host', 'path', 'qps']
          case 'service_qps':
            return ['host', 'qps']
          case 'url_rt':
            return ['host', 'path', 'avg_response_time']
          case 'service_rt':
            return ['host', 'avg_response_time']
          case 'url_error':
            return ['host', 'path', 'error_count']
          case 'error_host':
            return ['host', 'error_count']
          default:
            return []
        }
      },
    },
    watch: {
      dimension() {
        this.fetchData()
      },
    },
    mounted() {
      this.fetchData()
    },
    methods: {
      fetchData() {
        if (!this.dimension) return

        this.loading = true

        const params = {
          start_time: this.timeRange.start.toISOString(),
          end_time: this.timeRange.end.toISOString(),
          dimension: this.dimension,
          limit: this.pagination.pageSize,
          offset: (this.pagination.currentPage - 1) * this.pagination.pageSize,
        }

        // 添加过滤条件
        this.filters.forEach((filter) => {
          if (filter.include) {
            params[filter.type] = filter.value
          }
        })

        getTopN(params)
          .then((response) => {
            this.detailData = response.data || response
            this.pagination.total = response.total || 0
          })
          .finally(() => {
            this.loading = false
          })
      },

      handleCurrentChange(currentPage) {
        this.pagination.currentPage = currentPage
        this.fetchData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .topn-detail {
    padding: 20px;

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
