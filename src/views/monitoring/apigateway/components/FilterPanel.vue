<template>
  <el-card shadow="hover" class="filter-panel">
    <div class="filter-section">
      <div class="panel-left">
        <div class="time-filter">
          <el-radio-group
            v-model="localTimeRange.preset"
            size="small"
            @change="handlePresetChange"
          >
            <el-radio-button label="last-10m">10分钟</el-radio-button>
            <el-radio-button label="last-30m">30分钟</el-radio-button>
            <el-radio-button label="last-hour">1小时</el-radio-button>
            <el-radio-button label="last-6h">6小时</el-radio-button>
            <el-radio-button label="last-day">24小时</el-radio-button>
            <el-radio-button label="custom">自定义</el-radio-button>
          </el-radio-group>

          <el-date-picker
            v-if="localTimeRange.preset === 'custom'"
            v-model="customTimeRange"
            type="datetimerange"
            range-separator="to"
            start-placeholder="Start time"
            end-placeholder="End time"
            :default-time="['00:00:00', '23:59:59']"
            size="small"
            style="margin-left: 10px; width: 380px"
            @change="handleCustomTimeChange"
          />
        </div>

        <div class="filters-container">
          <el-tag
            v-for="(filter, index) in localFilters"
            :key="index"
            :type="
              filter.type === 'responseTime'
                ? 'success'
                : filter.include
                ? 'primary'
                : 'danger'
            "
            closable
            class="filter-tag"
            @close="handleRemoveFilter(index)"
            @click="startEditingFilter(index)"
          >
            {{ getFilterDisplayText(filter) }}
          </el-tag>

          <!-- 添加过滤器按钮 -->
          <el-popover
            v-model="addFilterVisible"
            placement="bottom"
            width="300"
            trigger="click"
          >
            <div class="add-filter-form">
              <el-form :model="newFilter" label-width="80px" size="small">
                <el-form-item label="类型">
                  <el-select v-model="newFilter.type" style="width: 100%">
                    <el-option label="环境" value="env"></el-option>
                    <el-option label="微服务" value="host"></el-option>
                    <el-option label="URL" value="path"></el-option>
                    <el-option label="状态码" value="status"></el-option>
                    <el-option
                      label="响应时间"
                      value="responseTime"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-if="newFilter.type !== 'responseTime'"
                  label="值"
                >
                  <el-autocomplete
                    v-if="!['status', 'env'].includes(newFilter.type)"
                    v-model="newFilter.value"
                    :fetch-suggestions="queryFilterOptions"
                    placeholder="输入过滤值"
                    style="width: 100%"
                  ></el-autocomplete>

                  <el-select
                    v-else-if="newFilter.type === 'status'"
                    v-model="newFilter.value"
                    style="width: 100%"
                  >
                    <el-option label="200 OK" value="200"></el-option>
                    <el-option label="400 Bad Request" value="400"></el-option>
                    <el-option label="401 Unauthorized" value="401"></el-option>
                    <el-option label="403 Forbidden" value="403"></el-option>
                    <el-option label="404 Not Found" value="404"></el-option>
                    <el-option label="500 Server Error" value="500"></el-option>
                    <el-option label="502 Bad Gateway" value="502"></el-option>
                    <el-option
                      label="503 Service Unavailable"
                      value="503"
                    ></el-option>
                    <el-option
                      label="504 Gateway Timeout"
                      value="504"
                    ></el-option>
                  </el-select>

                  <el-select
                    v-else-if="newFilter.type === 'env'"
                    v-model="newFilter.value"
                    style="width: 100%"
                  >
                    <el-option label="生产环境 (prd)" value="prd"></el-option>
                    <el-option label="灰度环境 (stg)" value="stg"></el-option>
                    <el-option
                      label="发版环境 (canary)"
                      value="canary"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  v-if="newFilter.type === 'responseTime'"
                  label="阈值"
                >
                  <div class="response-time-input">
                    <el-input-number
                      v-model="newFilter.threshold"
                      :min="0"
                      :step="100"
                      size="small"
                      style="width: 130px"
                    ></el-input-number>
                    <span class="unit">毫秒</span>
                  </div>
                </el-form-item>

                <el-form-item
                  v-if="newFilter.type === 'responseTime'"
                  label="条件"
                >
                  <el-radio-group v-model="newFilter.operator">
                    <el-radio label="gte">大于</el-radio>
                    <el-radio label="lt">小于</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item
                  v-if="newFilter.type !== 'responseTime'"
                  label="关系"
                >
                  <el-radio-group v-model="newFilter.include">
                    <el-radio :label="true">包含</el-radio>
                    <el-radio :label="false">不包含</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleAddFilter"
                  >
                    增加过滤
                  </el-button>
                  <el-button size="small" @click="addFilterVisible = false">
                    取消
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <el-button
              slot="reference"
              type="primary"
              size="small"
              icon="el-icon-plus"
              class="filter-action-button"
            >
              增加过滤
            </el-button>
          </el-popover>
        </div>
      </div>

      <div class="panel-right">
        <el-dropdown trigger="click" size="small" @command="handleQuickFilter">
          <el-button type="primary" size="small" icon="el-icon-magic-stick">
            快速过滤
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="errors">
              错误请求 (非200)
            </el-dropdown-item>
            <el-dropdown-item command="slow500">
              慢请求 (>500ms)
            </el-dropdown-item>
            <el-dropdown-item command="slow1000">
              很慢请求 (>1000ms)
            </el-dropdown-item>
            <el-dropdown-item divided command="env-stg">
              灰度异常 (stg)
            </el-dropdown-item>
            <el-dropdown-item command="env-canary">
              发版异常 (canary)
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <el-button
          size="small"
          icon="el-icon-delete"
          :disabled="localFilters.length === 0"
          class="clear-button"
          @click="handleClearAllFilters"
        >
          清除过滤
        </el-button>

        <el-button
          size="small"
          icon="el-icon-refresh"
          class="clear-button"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <!-- 编辑过滤器的弹出框 -->
    <el-dialog
      title="编辑过滤器"
      :visible.sync="editFilterVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="filter-form">
        <el-form :model="currentFilter" label-width="80px" size="small">
          <el-form-item label="Type">
            <el-select v-model="currentFilter.type" style="width: 100%">
              <el-option label="环境" value="env"></el-option>
              <el-option label="服务" value="host"></el-option>
              <el-option label="URL" value="path"></el-option>
              <el-option label="状态码" value="status"></el-option>
              <el-option label="响应时间" value="responseTime"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="currentFilter.type !== 'responseTime'" label="值">
            <el-autocomplete
              v-if="!['status', 'env'].includes(currentFilter.type)"
              v-model="currentFilter.value"
              :fetch-suggestions="queryFilterOptions"
              placeholder="过滤值"
              style="width: 100%"
            ></el-autocomplete>

            <el-select
              v-else-if="currentFilter.type === 'status'"
              v-model="currentFilter.value"
              style="width: 100%"
            >
              <el-option label="200 OK" value="200"></el-option>
              <el-option label="400 Bad Request" value="400"></el-option>
              <el-option label="401 Unauthorized" value="401"></el-option>
              <el-option label="403 Forbidden" value="403"></el-option>
              <el-option label="404 Not Found" value="404"></el-option>
              <el-option label="500 Server Error" value="500"></el-option>
              <el-option label="502 Bad Gateway" value="502"></el-option>
              <el-option
                label="503 Service Unavailable"
                value="503"
              ></el-option>
              <el-option label="504 Gateway Timeout" value="504"></el-option>
            </el-select>

            <el-select
              v-else-if="currentFilter.type === 'env'"
              v-model="currentFilter.value"
              style="width: 100%"
            >
              <el-option label="生产环境 (prd)" value="prd"></el-option>
              <el-option label="灰度环境 (stg)" value="stg"></el-option>
              <el-option label="发版环境 (canary)" value="canary"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="currentFilter.type === 'responseTime'"
            label="阈值"
          >
            <div class="response-time-input">
              <el-input-number
                v-model="currentFilter.threshold"
                :min="0"
                :step="100"
                size="small"
                style="width: 130px"
              ></el-input-number>
              <span class="unit">毫秒</span>
            </div>
          </el-form-item>

          <el-form-item
            v-if="currentFilter.type === 'responseTime'"
            label="条件"
          >
            <el-radio-group v-model="currentFilter.operator">
              <el-radio label="gte">大于等于</el-radio>
              <el-radio label="lt">小于</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="currentFilter.type !== 'responseTime'"
            label="Mode"
          >
            <el-radio-group v-model="currentFilter.include">
              <el-radio :label="true">包含</el-radio>
              <el-radio :label="false">不包含</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editFilterVisible = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateFilter">更新</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
  import { getFilterOptions } from '@/api/apigw'
  export default {
    name: 'FilterPanel',
    props: {
      timeRange: {
        type: Object,
        default: () => ({
          start: new Date(Date.now() - 1800 * 1000),
          end: new Date(),
          preset: 'last-30m',
        }),
      },
      filters: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        localTimeRange: JSON.parse(JSON.stringify(this.timeRange)),
        localFilters: JSON.parse(JSON.stringify(this.filters)),
        customTimeRange: [this.timeRange.start, this.timeRange.end],
        addFilterVisible: false,
        editFilterVisible: false,
        newFilter: {
          type: 'path',
          value: '',
          include: true,
          threshold: 500,
          operator: 'gte', // 'gte'=大于等于, 'lt'=小于
        },
        currentFilter: {
          type: 'path',
          value: '',
          include: true,
          threshold: 500,
          operator: 'gte',
        },
        editingFilterIndex: -1,
      }
    },
    watch: {
      timeRange: {
        handler(val) {
          this.localTimeRange = JSON.parse(JSON.stringify(val))
          this.customTimeRange = [val.start, val.end]
        },
        deep: true,
      },
      filters: {
        handler(val) {
          this.localFilters = JSON.parse(JSON.stringify(val))
        },
        deep: true,
      },
    },
    methods: {
      handlePresetChange(preset) {
        const now = new Date()
        let start = new Date()
        switch (preset) {
          case 'last-10m':
            start = new Date(now.getTime() - 10 * 60 * 1000)
            break
          case 'last-30m':
            start = new Date(now.getTime() - 30 * 60 * 1000)
            break
          case 'last-hour':
            start = new Date(now.getTime() - 60 * 60 * 1000)
            break
          case 'last-6h':
            start = new Date(now.getTime() - 6 * 60 * 60 * 1000)
            break
          case 'last-day':
            start = new Date(now.getTime() - 24 * 60 * 60 * 1000)
            break
          case 'custom':
            // 自定义时不做调整，直接让用户选取
            return
        }
        this.localTimeRange = {
          start,
          end: now,
          preset,
        }
        this.customTimeRange = [start, now]
        this.$emit('time-change', this.localTimeRange)
      },
      handleCustomTimeChange(range) {
        if (range && range.length === 2) {
          this.localTimeRange = {
            start: range[0],
            end: range[1],
            preset: 'custom',
          }
          this.$emit('time-change', this.localTimeRange)
        }
      },
      startEditingFilter(index) {
        const filter = this.localFilters[index]
        this.editingFilterIndex = index

        // 复制过滤器的值到当前表单
        if (filter.type === 'responseTime') {
          this.currentFilter = {
            type: filter.type,
            threshold: filter.threshold,
            operator: filter.operator || 'gte',
          }
        } else {
          this.currentFilter = {
            type: filter.type,
            include: filter.include,
            value: Array.isArray(filter.value)
              ? filter.value.join(',')
              : filter.value,
          }
        }

        // 直接打开编辑对话框
        this.editFilterVisible = true
      },
      handleUpdateFilter() {
        if (this.currentFilter.type === 'responseTime') {
          if (
            this.currentFilter.threshold === undefined ||
            this.currentFilter.threshold < 0
          ) {
            this.$message.warning('响应时间阈值必须大于等于0')
            return
          }

          this.localFilters[this.editingFilterIndex] = {
            type: 'responseTime',
            threshold: this.currentFilter.threshold,
            operator: this.currentFilter.operator,
          }
        } else {
          if (!this.currentFilter.value) {
            this.$message.warning('Filter value cannot be empty')
            return
          }

          // 编辑现有过滤器
          let values = []
          if (typeof this.currentFilter.value === 'string') {
            values = this.currentFilter.value
              .split(',')
              .map((v) => v.trim())
              .filter((v) => v !== '')
          } else {
            // 如果值不是字符串（比如数字或其它类型），直接用一个数组包装它
            values = [this.currentFilter.value]
          }

          if (values.length === 0) {
            this.$message.warning('Filter value cannot be empty')
            return
          }

          this.localFilters[this.editingFilterIndex] = {
            type: this.currentFilter.type,
            value: values.length === 1 ? values[0] : values,
            include: this.currentFilter.include,
          }
        }

        // 关闭对话框
        this.editFilterVisible = false
        this.$emit('filter-change', this.localFilters)
      },
      handleAddFilter() {
        if (this.newFilter.type === 'responseTime') {
          if (
            this.newFilter.threshold === undefined ||
            this.newFilter.threshold < 0
          ) {
            this.$message.warning('响应时间阈值必须大于等于0')
            return
          }

          this.localFilters.push({
            type: 'responseTime',
            threshold: this.newFilter.threshold,
            operator: this.newFilter.operator,
          })
        } else {
          if (!this.newFilter.value) {
            this.$message.warning('Filter value cannot be empty')
            return
          }

          // 检查是否已存在相同 type 和模式的过滤条件，若存在则合并新的值
          const existing = this.localFilters.find(
            (filter) =>
              filter.type === this.newFilter.type &&
              filter.include === this.newFilter.include
          )

          if (existing) {
            if (!Array.isArray(existing.value)) {
              existing.value = [existing.value]
            }
            if (!existing.value.includes(this.newFilter.value)) {
              existing.value.push(this.newFilter.value)
            } else {
              this.$message.info('This value already exists in the filter.')
            }
          } else {
            this.localFilters.push({
              type: this.newFilter.type,
              value: this.newFilter.value,
              include: this.newFilter.include,
            })
          }
        }

        // 重置表单并关闭 Popover
        this.newFilter = {
          type: 'path',
          value: '',
          include: true,
          threshold: 500,
          operator: 'gte',
        }
        this.addFilterVisible = false
        this.$emit('filter-change', this.localFilters)
      },
      handleRemoveFilter(index) {
        this.localFilters.splice(index, 1)
        this.$emit('filter-remove', index)
      },
      getFilterDisplayText(filter) {
        const typeLabels = {
          host: 'Host',
          path: 'Path',
          status: 'Status',
          responseTime: '响应时间',
          env: '环境',
        }

        if (filter.type === 'responseTime') {
          const operator = filter.operator === 'lt' ? '<' : '≥'
          return `${typeLabels[filter.type]} ${operator} ${filter.threshold}ms`
        }

        const prefix = filter.include ? '' : 'NOT '
        let valueText = ''
        if (Array.isArray(filter.value)) {
          valueText = filter.value.join(', ')
        } else {
          valueText = filter.value
        }

        // 为环境类型显示友好名称
        if (filter.type === 'env') {
          const envNames = {
            prd: '生产环境 (prd)',
            stg: '灰度环境 (stg)',
            canary: '发版环境 (canary)',
          }
          if (envNames[valueText]) {
            valueText = envNames[valueText]
          }
        }

        return `${prefix}${typeLabels[filter.type]}: ${valueText}`
      },
      queryFilterOptions(queryString, callback) {
        // 根据当前正在编辑的表单确定查询类型
        const filterType = this.editFilterVisible
          ? this.currentFilter.type
          : this.newFilter.type

        if (
          !filterType ||
          filterType === 'responseTime' ||
          filterType === 'env'
        ) {
          callback([])
          return
        }

        getFilterOptions({
          type: filterType,
          keyword: queryString,
        })
          .then((response) => {
            const options = response.map((item) => ({ value: item }))
            callback(options)
          })
          .catch(() => {
            callback([])
          })
      },
      handleClearAllFilters() {
        this.localFilters = []
        this.$emit('filter-change', this.localFilters)
        this.$message.success('已清除所有过滤条件')
      },
      handleRefresh() {
        if (this.localTimeRange.preset !== 'custom') {
          const now = new Date()
          let start = new Date()
          const preset = this.localTimeRange.preset
          switch (preset) {
            case 'last-10m':
              start = new Date(now.getTime() - 10 * 60 * 1000)
              break
            case 'last-30m':
              start = new Date(now.getTime() - 30 * 60 * 1000)
              break
            case 'last-hour':
              start = new Date(now.getTime() - 60 * 60 * 1000)
              break
            case 'last-6h':
              start = new Date(now.getTime() - 6 * 60 * 60 * 1000)
              break
            case 'last-day':
              start = new Date(now.getTime() - 24 * 60 * 60 * 1000)
              break
            // 如果是 custom，则不变动时间
            default:
              break
          }
          this.localTimeRange = { start, end: now, preset }
          this.customTimeRange = [start, now]
          this.$emit('time-change', this.localTimeRange)
        } else {
          // 如果是自定义时间，就直接发出时间变化事件
          this.$emit('time-change', this.localTimeRange)
        }
      },
      handleQuickFilter(command) {
        switch (command) {
          case 'errors':
            // 添加非200状态码的过滤器
            this.localFilters = [
              {
                type: 'status',
                value: '200',
                include: false,
              },
            ]
            this.$emit('filter-change', this.localFilters)
            break
          case 'success':
            // 添加200状态码的过滤器
            this.localFilters = [
              {
                type: 'status',
                value: '200',
                include: true,
              },
            ]
            this.$emit('filter-change', this.localFilters)
            break
          case 'slow500':
            // 添加响应时间 >= 500ms 的过滤器
            this.localFilters = [
              {
                type: 'responseTime',
                threshold: 500,
                operator: 'gte',
              },
            ]
            this.$emit('filter-change', this.localFilters)
            break
          case 'slow1000':
            // 添加响应时间 >= 1000ms 的过滤器
            this.localFilters = [
              {
                type: 'responseTime',
                threshold: 1000,
                operator: 'gte',
              },
            ]
            this.$emit('filter-change', this.localFilters)
            break
          case 'env-stg':
            // 添加灰度环境过滤器
            this.localFilters = [
              {
                type: 'env',
                value: 'stg',
                include: true,
              },
              {
                type: 'status',
                value: 200,
                include: false,
              },
            ]
            this.$emit('filter-change', this.localFilters)
            break
          case 'env-canary':
            this.localFilters = [
              {
                type: 'env',
                value: 'canary',
                include: true,
              },
              {
                type: 'status',
                value: 200,
                include: false,
              },
            ]
            this.$emit('filter-change', this.localFilters)
            break
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .filter-panel {
    margin-bottom: 20px;

    .filter-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .panel-left {
        flex: 1;

        .time-filter {
          margin-bottom: 15px;
        }

        .filters-container {
          display: flex;
          flex-wrap: wrap;
          align-items: center;

          .filter-tag {
            margin-right: 8px;
            margin-bottom: 8px;
            cursor: pointer;
          }

          .filter-action-button {
            margin-bottom: 8px;
          }
        }
      }

      .panel-right {
        display: flex;
        align-items: center;
        margin-left: 16px;

        .clear-button {
          margin-left: 8px;
        }
      }
    }

    .add-filter-form,
    .filter-form {
      padding: 10px;
    }

    .response-time-input {
      display: flex;
      align-items: center;

      .unit {
        margin-left: 8px;
        color: #606266;
      }
    }
  }
</style>
