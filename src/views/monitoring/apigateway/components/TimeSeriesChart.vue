<template>
  <div v-loading="loading" class="timeseries-chart">
    <div class="chart-header">
      <span>QPS趋势</span>

      <div v-if="canCompare" class="compare-controls">
        <span>对比:</span>
        <el-date-picker
          v-model="compareDate"
          type="date"
          placeholder="选择日期"
          size="small"
          style="width: 150px; margin-left: 10px"
          :picker-options="datePickerOptions"
          @change="handleCompareChange"
        ></el-date-picker>
        <el-button
          v-if="compareDate"
          type="text"
          icon="el-icon-close"
          @click="clearCompare"
        ></el-button>
      </div>
    </div>

    <div ref="chartContainer" class="chart-container"></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'TimeSeriesChart',
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      compareData: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      timeRange: {
        type: Object,
        required: true,
      },
    },
    data() {
      const now = new Date()
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

      return {
        chart: null,
        compareDate: null,
        datePickerOptions: {
          dateFormat: 'Y-m-d',
          disabledDate(time) {
            return (
              time.getTime() < sevenDaysAgo.getTime() ||
              time.getTime() > now.getTime()
            )
          },
        },
      }
    },
    computed: {
      canCompare() {
        // 只允许时间范围不超过24小时的对比
        if (!this.timeRange.start || !this.timeRange.end) return false

        const diff =
          this.timeRange.end.getTime() - this.timeRange.start.getTime()
        return diff <= 24 * 60 * 60 * 1000
      },
    },
    watch: {
      data() {
        this.updateChart()
      },
      compareData() {
        this.updateChart()
      },
      timeRange: {
        deep: true,
        handler() {
          this.updateChart()
        },
      },
    },
    mounted() {
      this.initChart()
      this.updateChart()
      window.addEventListener('resize', this.resizeChart)
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
      window.removeEventListener('resize', this.resizeChart)
    },
    methods: {
      initChart() {
        this.chart = echarts.init(this.$refs.chartContainer)
      },

      updateChart() {
        if (!this.chart) return

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
          },
          legend: {
            data: ['当前', '对比'],
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            min: this.timeRange.start
              ? this.timeRange.start.getTime()
              : undefined,
            max: this.timeRange.end ? this.timeRange.end.getTime() : undefined,
            axisLabel: {
              formatter: function (value) {
                const date = new Date(value)
                return new Intl.DateTimeFormat('zh-CN', {
                  hour12: false,
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  timeZone: 'Asia/Shanghai',
                }).format(date)
              },
            },
          },
          yAxis: {
            type: 'value',
            name: 'QPS',
            nameLocation: 'end',
          },
          series: [
            {
              name: '当前',
              type: 'line',
              data: this.data.map((item) => [
                new Date(item.time).getTime(),
                item.qps,
              ]),
              smooth: true,
              showSymbol: false,
              lineStyle: {
                width: 2,
              },
              areaStyle: {
                opacity: 0.2,
              },
            },
          ],
        }

        if (this.compareData && this.compareData.length > 0) {
          option.series.push({
            name: '对比',
            type: 'line',
            data: this.compareData.map((item) => [
              new Date(item.time).getTime(),
              item.qps,
            ]),
            smooth: true,
            showSymbol: false,
            lineStyle: {
              width: 2,
              type: 'dashed',
            },
          })
        }

        this.chart.setOption(option)
      },

      resizeChart() {
        if (this.chart) {
          this.chart.resize()
        }
      },

      handleCompareChange(date) {
        if (date) {
          this.$emit('date-compare', date)
        }
      },

      clearCompare() {
        this.compareDate = null
        this.$emit('date-compare', null)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .timeseries-chart {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .compare-controls {
        display: flex;
        align-items: center;
        font-size: 14px;
      }
    }

    .chart-container {
      height: 250px;
    }
  }
</style>
