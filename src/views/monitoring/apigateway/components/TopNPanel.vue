<!-- src/views/monitor/components/TopNPanel.vue -->
<template>
  <el-card v-loading="loading" shadow="hover" class="topn-panel">
    <div slot="header" class="card-header">
      <span>{{ title }}</span>
      <el-button
        type="text"
        size="mini"
        @click="$emit('view-more', dimension, title)"
      >
        查看更多
      </el-button>
    </div>

    <div v-if="data.length === 0" class="no-data">暂无数据</div>

    <div v-else class="topn-content">
      <div v-for="(item, index) in data" :key="index" class="topn-item">
        <div class="item-info">
          <div class="item-rank" :class="{ 'top-rank': index < 3 }">
            {{ index + 1 }}
          </div>
          <div class="item-name" :title="getItemDisplayName(item)">
            {{ getItemDisplayName(item) }}
          </div>
        </div>

        <div class="item-value">
          {{ getItemDisplayValue(item) }}
        </div>

        <div class="item-actions">
          <el-dropdown
            trigger="click"
            @command="(command) => handleCommand(command, item)"
          >
            <span class="el-dropdown-link">
              <i class="el-icon-more"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="include">
                加入过滤(包含)
              </el-dropdown-item>
              <el-dropdown-item command="exclude">
                加入过滤(排除)
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script>
  export default {
    name: 'TopNPanel',
    props: {
      title: {
        type: String,
        required: true,
      },
      dimension: {
        type: String,
        required: true,
      },
      data: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    methods: {
      getItemDisplayName(item) {
        if (
          this.dimension === 'url_qps' ||
          this.dimension === 'url_rt' ||
          this.dimension === 'url_error'
        ) {
          return item.path
        } else if (
          this.dimension === 'service_qps' ||
          this.dimension === 'service_rt' ||
          this.dimension === 'error_host'
        ) {
          return item.host
        }
        return ''
      },

      getItemDisplayValue(item) {
        if (this.dimension === 'url_qps' || this.dimension === 'service_qps') {
          return item.qps.toFixed(1)
        } else if (
          this.dimension === 'url_rt' ||
          this.dimension === 'service_rt'
        ) {
          return (item.avg_response_time * 1000).toFixed(2) + ' ms'
        } else if (
          this.dimension === 'url_error' ||
          this.dimension === 'error_host'
        ) {
          return item.error_count
        }
        return ''
      },

      handleCommand(command, item) {
        console.log(this.dimension, item)
        this.$emit('item-click', item, this.dimension, command)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .topn-panel {
    margin-bottom: 20px;
    height: calc(100% - 20px);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .no-data {
      height: 200px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #909399;
    }

    .topn-content {
      .topn-item {
        display: flex;
        padding: 10px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .item-info {
          flex: 1;
          display: flex;
          align-items: center;
          overflow: hidden;

          .item-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            font-weight: bold;
            font-size: 14px;

            &.top-rank {
              background-color: #f56c6c;
              color: white;
            }
          }

          .item-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .item-value {
          width: 120px;
          text-align: right;
          font-weight: bold;
          color: #409eff;
        }

        .item-actions {
          width: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }
</style>
