<!-- src/views/monitor/components/DetailTable.vue -->
<template>
  <div v-loading="loading" class="detail-table">
    <div class="table-header">
      <span>请求明细</span>
    </div>

    <el-table :data="data" style="width: 100%" border stripe>
      <el-table-column prop="time_bucket" label="时间" width="180">
        <template slot-scope="scope">
          {{ formatDateTime(scope.row.time_bucket) }}
        </template>
      </el-table-column>

      <el-table-column
        prop="host"
        label="服务"
        width="80"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column
        prop="path"
        label="URL"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column prop="status" label="状态码" width="80">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        prop="count"
        label="请求数"
        width="100"
        align="right"
      ></el-table-column>

      <el-table-column prop="avg_time" label="rt(ms)" width="100" align="right">
        <template slot-scope="scope">
          {{ (scope.row.avg_time * 1000).toFixed(2) }}
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        layout="total, prev, pager, next"
        :total="pagination.total"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DetailTable',
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      pagination: {
        type: Object,
        default: () => ({
          currentPage: 1,
          pageSize: 10,
          total: 0,
        }),
      },
    },
    methods: {
      formatDateTime(isoString) {
        const date = new Date(isoString)
        return date.toLocaleString()
      },

      getStatusType(status) {
        if (status < 300) return 'success'
        if (status < 400) return 'warning'
        if (status < 500) return 'danger'
        return 'danger'
      },

      handleCurrentChange(currentPage) {
        this.$emit('page-change', currentPage)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .detail-table {
    .table-header {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
