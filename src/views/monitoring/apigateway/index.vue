<!-- src/views/monitor/index.vue -->
<template>
  <div class="app-container">
    <!-- 概览卡片 -->
    <overview-cards
      :metrics="overviewMetrics"
      :loading="loadingOverview"
      @error-click="handleErrorClick"
      @trace-click="handleTraceClick"
    />

    <!-- 两栏布局 -->
    <el-row :gutter="16" class="main-content">
      <!-- 左侧区域 -->
      <el-col :span="16">
        <!-- 筛选面板 - 始终显示 -->
        <el-card class="compact-card chart-card">
          <div class="chart-header">
            <filter-panel
              :time-range="timeRange"
              :filters="filters"
              @time-change="handleTimeChange"
              @filter-change="handleFilterChange"
              @filter-remove="handleFilterRemove"
            />
          </div>

          <!-- 根据视图类型显示不同内容 -->
          <div v-if="!showTraceView">
            <time-series-chart
              :data="timeseriesData"
              :compare-data="compareData"
              :loading="loadingTimeseries"
              :time-range="timeRange"
              @date-compare="handleDateCompare"
            />
          </div>
        </el-card>

        <!-- 下方内容区域 - 根据视图类型显示不同内容 -->
        <el-card class="compact-card table-card">
          <detail-table
            v-if="!showTraceView"
            :data="detailData"
            :loading="loadingDetails"
            :pagination="pagination"
            @page-change="handlePageChange"
          />

          <trace-details
            v-else
            :time-range="timeRange"
            :filters="filters"
            @close="showTraceView = false"
          />
        </el-card>
      </el-col>

      <!-- 右侧：TopN 指标 -->
      <el-col :span="8">
        <el-row :gutter="16">
          <el-col v-for="(topn, index) in topNPanels" :key="index" :span="24">
            <el-card class="compact-card topn-card">
              <top-n-panel
                :title="topn.title"
                :data="topn.data"
                :loading="topn.loading"
                :dimension="topn.dimension"
                @item-click="handleTopNItemClick"
                @view-more="handleViewMore"
              />
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>

    <!-- TopN 详情抽屉 -->
    <el-drawer
      :title="topnDrawerTitle"
      :visible.sync="topnDrawerVisible"
      direction="rtl"
      size="70%"
    >
      <topn-detail
        :dimension="topnDrawerDimension"
        :filters="filters"
        :time-range="timeRange"
      />
    </el-drawer>
  </div>
</template>

<script>
  import {
    getOverview,
    getTopN,
    getDetails,
    getTimeseries,
  } from '@/api/apigw.js'
  import FilterPanel from './components/FilterPanel'
  import TopNPanel from './components/TopNPanel'
  import TimeSeriesChart from './components/TimeSeriesChart'
  import DetailTable from './components/DetailTable'
  import TopnDetail from './components/TopnDetail'
  import OverviewCards from './components/OverviewCards'
  import TraceDetails from './components/TraceDetails' // 添加引入链路追踪组件

  export default {
    name: 'ApiMonitor',
    components: {
      FilterPanel,
      TopNPanel,
      TimeSeriesChart,
      DetailTable,
      TopnDetail,
      OverviewCards,
      TraceDetails, // 注册链路追踪组件
    },
    data() {
      return {
        timeRange: {
          start: new Date(Date.now() - 600 * 1000), // 最近十分钟
          end: new Date(),
          preset: 'last-10m',
        },
        filters: [],
        overviewMetrics: {
          totalQps: 0,
          errorCount: 0,
          avgResponseTime: 0,
          errorTracingCount: 0,
        },
        loadingOverview: false,
        topNPanels: [
          {
            title: 'URL QPS Top5',
            dimension: 'url_qps',
            data: [],
            loading: false,
          },
          {
            title: '服务错误 Top5',
            dimension: 'error_host',
            data: [],
            loading: false,
          },
          {
            title: 'URL 响应时间 Top5',
            dimension: 'url_rt',
            data: [],
            loading: false,
          },
          {
            title: '服务QPS Top5',
            dimension: 'service_qps',
            data: [],
            loading: false,
          },
        ],
        timeseriesData: [],
        compareData: [],
        loadingTimeseries: false,
        detailData: [],
        loadingDetails: false,
        pagination: { currentPage: 1, pageSize: 10, total: 0 },
        topnDrawerVisible: false,
        topnDrawerTitle: '',
        topnDrawerDimension: '',
        showTraceView: false, // 添加链路追踪视图显示状态
      }
    },
    created() {
      this.fetchAllData()
    },
    mounted() {
      this.initializeFiltersFromURL()
    },
    methods: {
      withTimeout(promise, timeout = 10000) {
        return Promise.race([
          promise,
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error(`Timeout after ${timeout} ms`)),
              timeout
            )
          ),
        ])
      },
      async fetchAllData() {
        this.fetchOverview()
        this.fetchAllTopN()
        this.fetchTimeseries()
        this.fetchDetails()
      },
      async fetchOverview() {
        this.loadingOverview = true
        const params = this.getTimeAndFilterParams()

        this.withTimeout(getOverview(params), 18000)
          .then((response) => {
            this.overviewMetrics = response
          })
          .catch((error) => {
            console.error('fetchOverview error:', error)
          })
          .finally(() => {
            this.loadingOverview = false
          })
      },
      async fetchAllTopN() {
        this.topNPanels.forEach((_, index) => this.fetchTopN(index))
      },
      async fetchTopN(index) {
        const panel = this.topNPanels[index]
        panel.loading = true
        const params = {
          ...this.getTimeAndFilterParams(),
          dimension: panel.dimension,
          limit: 5,
        }

        this.withTimeout(getTopN(params), 18000)
          .then((response) => {
            panel.data = response
          })
          .catch((error) => {
            console.error(`fetchTopN error at index ${index}:`, error)
            // 根据需要处理错误
          })
          .finally(() => {
            panel.loading = false
          })
      },
      async fetchTimeseries(compareDate = null) {
        this.loadingTimeseries = true
        const params = { ...this.getTimeAndFilterParams() }
        if (compareDate) {
          params.compare_date = compareDate.toISOString()
        }

        this.withTimeout(getTimeseries(params), 18000)
          .then((response) => {
            this.timeseriesData = response.main_series
            this.compareData = response.compare_series || []
          })
          .catch((error) => {
            console.error('fetchTimeseries error:', error)
            // 错误处理逻辑
          })
          .finally(() => {
            this.loadingTimeseries = false
          })
      },
      async fetchDetails() {
        this.loadingDetails = true
        const params = {
          ...this.getTimeAndFilterParams(),
          page: this.pagination.currentPage,
          page_size: this.pagination.pageSize,
        }

        this.withTimeout(getDetails(params), 18000)
          .then((response) => {
            this.detailData = response.data
            this.pagination.total = response.total
          })
          .catch((error) => {
            console.error('fetchDetails error:', error)
            this.detailData = []
            // 错误处理逻辑
          })
          .finally(() => {
            this.loadingDetails = false
          })
      },
      getTimeAndFilterParams() {
        const params = {
          start_time: this.timeRange.start.toISOString(),
          end_time: this.timeRange.end.toISOString(),
        }

        this.filters.forEach((filter) => {
          // 处理响应时间过滤器
          if (filter.type === 'responseTime') {
            if (filter.operator === 'gte') {
              params.response_time_min = filter.threshold
            } else if (filter.operator === 'lt') {
              params.response_time_max = filter.threshold
            }
          }
          // 处理其他类型的过滤器
          else {
            let filterValue = ''
            if (Array.isArray(filter.value)) {
              filterValue = filter.value.join(',')
            } else {
              filterValue = filter.value
            }

            if (filter.include) {
              params[filter.type] = filterValue
            } else {
              params['not_' + filter.type] = filterValue
            }
          }
        })

        return params
      },
      initializeFiltersFromURL() {
        const { path, host, status } = this.$route.query
        if (path) {
          this.filters.push({
            type: 'path',
            value: path || '',
            include: true,
          })
        }
        if (host) {
          this.filters.push({
            type: 'host',
            value: host || '',
            include: true,
          })
        }
        if (status) {
          this.filters.push({
            type: 'status',
            value: status || 200,
            include: true,
          })
        }
        this.fetchAllData()
      },
      handleTimeChange(timeRange) {
        this.timeRange = timeRange
        this.fetchAllData()
      },
      handleFilterChange(filters) {
        this.filters = filters
        this.fetchAllData()
      },
      handleFilterRemove(index) {
        this.filters.splice(index, 1)
        this.fetchAllData()
      },
      handleDateCompare(date) {
        this.fetchTimeseries(date)
      },
      handlePageChange(page) {
        this.pagination.currentPage = page
        this.fetchDetails()
      },
      handleTopNItemClick(item, dimension, includeExclude) {
        const filterType = dimension.includes('url') ? 'path' : 'host'
        const filterValue = dimension.includes('url') ? item.path : item.host
        const include = includeExclude === 'include'
        const existing = this.filters.find(
          (f) => f.type === filterType && f.include === include
        )
        if (existing) {
          if (Array.isArray(existing.value)) {
            if (!existing.value.includes(filterValue)) {
              existing.value.push(filterValue)
            }
          } else {
            if (existing.value !== filterValue) {
              existing.value = [existing.value, filterValue]
            }
          }
        } else {
          this.filters.push({ type: filterType, value: filterValue, include })
        }
        this.fetchAllData()
      },
      handleViewMore(dimension, title) {
        this.topnDrawerDimension = dimension
        this.topnDrawerTitle = title
        this.topnDrawerVisible = true
      },
      handleErrorClick() {
        // 添加错误状态过滤条件：状态非200
        const errorFilter = { type: 'status', value: 200, include: false }
        // 检查该过滤条件是否已添加
        const exists = this.filters.some(
          (f) => f.type === 'status' && f.value === 200 && f.include === false
        )
        if (!exists) {
          this.filters.push(errorFilter)
          this.fetchAllData()
        }
      },
      // 添加处理错误追踪点击的方法
      handleTraceClick() {
        // 切换到链路追踪视图
        this.showTraceView = true
      },
    },
  }
</script>

<style lang="scss" scoped>
  .app-container {
    padding: 20px;
  }

  .main-content {
    margin-top: 20px;
    transition: all 0.3s ease; /* 添加平滑过渡效果 */
  }

  .chart-card,
  .table-card,
  .topn-card {
    margin-bottom: 16px;
    padding: 8px;
  }

  .chart-header {
    margin-bottom: 15px;
  }

  .topn-content {
    margin-top: 16px;
  }
</style>
