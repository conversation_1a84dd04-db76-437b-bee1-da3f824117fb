<template>
  <div class="alert-detail">
    <el-descriptions :column="1" border>
      <el-descriptions-item label="告警标题">
        <div class="title-container">
          <span>{{ alert.summary }}</span>
          <el-link
            v-if="getPolicyUrl(alert)"
            :href="getPolicyUrl(alert)"
            class="action-link"
            target="_blank"
            type="primary"
          >
            <i class="el-icon-link"></i>
            告警详情
          </el-link>

          <div class="action-buttons">
            <el-button
              v-if="alert.status === 'ALARM'"
              size="small"
              type="success"
              @click="handleRecover"
            >
              <i class="el-icon-check"></i>
              恢复告警
            </el-button>
          </div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="告警级别/状态">
        <el-tag :type="getSeverityType(alert.severity)">
          {{ formatSeverity(alert.severity) }}
        </el-tag>
        <el-tag :type="getStatusType(alert.status)">
          {{ formatStatus(alert.status) }}
        </el-tag>
      </el-descriptions-item>

      <el-descriptions-item label="告警来源">
        <template v-if="alert.source === 'tencent_cloud' && alert.account_id">
          <el-tag :type="getSourceType(alert.source)">
            {{ formatSourceWithAccount(alert) }}
          </el-tag>
        </template>
        <template v-else>
          <el-tag :type="getSourceType(alert.source)">
            {{ formatSource(alert.source) }}
          </el-tag>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="产品名称">
        <div class="tags-container">
          <el-tag
            v-if="alert.labels && alert.labels.namespace"
            :key="'namespace'"
            :effect="getTagEffect('namespace')"
            class="label-tag"
            size="small"
          >
            {{ alert.labels.namespace }}
          </el-tag>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="开始时间">
        {{ alert.starts_at }}
      </el-descriptions-item>
      <el-descriptions-item label="结束时间">
        {{ alert.ends_at || '-' }}
      </el-descriptions-item>
      <el-descriptions-item :span="2" label="告警描述">
        <pre>{{ alert.description }}</pre>
      </el-descriptions-item>

      <el-descriptions-item :span="2" label="标签">
        <div class="tags-container">
          <el-tag
            v-for="(value, key) in alert.labels"
            :key="key"
            :effect="getTagEffect(key)"
            class="label-tag"
            size="small"
          >
            {{ key }}: {{ value }}
          </el-tag>
        </div>
      </el-descriptions-item>
      <el-descriptions-item :span="2" label="注解">
        <div v-if="alert.annotations" class="annotations-container">
          <template v-if="alert.source === 'tencent_cloud'">
            <div class="tags-container">
              <el-tag
                v-for="(value, key) in alert.annotations"
                :key="key"
                class="annotation-tag"
                effect="plain"
                size="small"
                type="info"
              >
                {{ key }}: {{ formatTencentCloudAnnotation(value) }}
              </el-tag>
            </div>
          </template>

          <template v-else-if="alert.source === 'custom'">
            <div
              v-for="(value, key) in alert.annotations"
              :key="key"
              class="annotation-item"
            >
              <template v-if="key === 'extra_info'">
                <div
                  v-for="(extraValue, extraKey) in parseExtraInfo(value)"
                  :key="extraKey"
                  class="extra-info-item"
                >
                  <strong>{{ extraKey }}:</strong>
                  <span>{{ extraValue }}</span>
                </div>
              </template>
              <template v-else>
                <strong>{{ key }}:</strong>
                <span v-if="typeof value === 'object'" class="json-value">
                  {{ formatJsonValue(value) }}
                </span>
                <span v-else class="formatted-text">
                  {{ formatAnnotationText(value) }}
                </span>
              </template>
            </div>
          </template>

          <template v-else>
            <div
              v-for="(value, key) in alert.annotations"
              :key="key"
              class="annotation-item"
            >
              <strong>{{ key }}:</strong>
              <span v-if="typeof value === 'object'" class="json-value">
                {{ formatJsonValue(value) }}
              </span>
              <span v-else class="formatted-text">
                {{ formatAnnotationText(value) }}
              </span>
            </div>
          </template>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
  export default {
    name: 'AlertDetail',
    props: {
      alert: {
        type: Object,
        required: true,
      },
    },
    methods: {
      async handleRecover() {
        if (!this.alert.fingerprint) {
          this.$message({
            type: 'error',
            message: '告警指纹信息缺失',
          })
          return
        }

        try {
          await this.$confirm('确认将该告警标记为已恢复？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })

          this.$emit('recover', this.alert.fingerprint)
        } catch (error) {
          if (error !== 'cancel') {
            console.error('恢复告警失败:', error)
          }
        }
      },

      getSeverityType(severity) {
        const types = {
          critical: 'danger',
          major: 'danger',
          warning: 'warning',
          resolved: 'success',
          info: 'info',
        }
        return types[severity] || 'info'
      },

      getStatusType(status) {
        const types = {
          ALARM: 'danger',
          OK: 'success',
          NO_DATA: 'info',
          NO_CONF: 'warning',
        }
        return types[status] || 'info'
      },

      getSourceType(source) {
        const sourceTypeMap = {
          prometheus: 'warning',
          grafana: 'success',
          custom: 'info',
          tencent_cloud: 'primary',
          pinpoint: 'primary',
        }
        return sourceTypeMap[source] || 'info'
      },

      getAccountColor(accountId) {
        const accountName = accountId.split('|')[0]
        const accountColorMap = {
          PunkSong: '#409EFF',
          微购科技2: '#67C23A',
        }
        return accountColorMap[accountName] || '#909399'
      },

      formatSource(source) {
        const sourceDisplayMap = {
          prometheus: 'Prometheus',
          grafana: 'Grafana',
          custom: '自定义',
          tencent_cloud: '腾讯云',
          pinpoint: 'Pinpoint',
        }
        return sourceDisplayMap[source] || source
      },

      formatSourceWithAccount(alert) {
        if (alert.source === 'tencent_cloud' && alert.account_id) {
          const accountName = alert.account_id.split('|')[0]
          return `腾讯云 ${accountName}`
        }
        return this.formatSource(alert.source)
      },

      formatSeverity(severity) {
        const severityMap = {
          critical: '严重',
          major: '重要',
          warning: '警告',
          resolved: '已恢复',
          info: '一般',
        }
        return severityMap[severity] || severity
      },

      formatStatus(status) {
        const statusMap = {
          ALARM: '告警中',
          OK: '已恢复',
          NO_DATA: '无数据',
          NO_CONF: '未配置',
        }
        return statusMap[status] || status
      },

      getTagEffect(key) {
        return ['alertStatus', 'severity', 'type'].includes(key)
          ? 'light'
          : 'plain'
      },

      formatJsonValue(value) {
        return JSON.stringify(value, null, 2)
      },

      formatAnnotationText(text) {
        try {
          const parsed = JSON.parse(text)
          if (typeof parsed === 'object') {
            for (const key in parsed) {
              if (typeof parsed[key] === 'string') {
                parsed[key] = this.processEscapeCharacters(parsed[key])
              }
            }
            return JSON.stringify(parsed, null, 2)
          }
          return this.processEscapeCharacters(text)
        } catch (e) {
          return this.processEscapeCharacters(text)
        }
      },

      processEscapeCharacters(text) {
        return text
          .replace(/\\n/g, '\n')
          .replace(/\\t/g, '\t')
          .replace(/\n\t/g, '\n    ')
          .replace(/\t/g, '    ')
      },

      formatTencentCloudAnnotation(value) {
        if (typeof value === 'object') {
          return JSON.stringify(value)
        }

        return this.processEscapeCharacters(value)
          .split('\n')
          .map((line) => line.trim())
          .filter((line) => line)
          .join(' | ')
      },

      getPolicyUrl(alert) {
        if (alert.source === 'tencent_cloud') {
          return alert.annotations?.policy_detail_url || null
        }
        if (
          alert.source === 'custom' ||
          alert.source === 'prometheus' ||
          alert.source === 'grafana' ||
          alert.source === 'pinpoint'
        ) {
          const extra_info = alert.annotations?.extra_info || {}
          const jump_url = extra_info.jump_url
          return jump_url || null
        }
        return alert.annotations?.policy_detail_url || null
      },

      getJumpUrl(annotations) {
        if (!annotations?.extra_info) return null

        const extraInfo =
          typeof annotations.extra_info === 'string'
            ? JSON.parse(annotations.extra_info)
            : annotations.extra_info

        return extraInfo.jump_url || null
      },

      parseExtraInfo(value) {
        try {
          return typeof value === 'string' ? JSON.parse(value) : value
        } catch (e) {
          return {}
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .alert-detail {
    padding: 20px;

    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      margin: 0;
      font-family: monospace;
    }

    .title-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12px;

      .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: wrap;
      }

      .action-link {
        font-size: 13px;
        margin-right: 100px;

        .el-icon-link {
          margin-right: 4px;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .tags-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 4px 0;

      .label-tag,
      .annotation-tag {
        margin: 0;
        max-width: 100%;
        word-break: break-all;

        &:hover {
          cursor: default;
        }
      }
    }

    .annotations-container {
      .annotation-item {
        margin-bottom: 8px;
        line-height: 1.5;

        strong {
          margin-right: 8px;
        }
      }

      .extra-info-item {
        margin: 4px 0;
        padding-left: 16px;
      }
    }

    .json-value {
      display: block;
      margin-top: 4px;
      padding: 8px;
      background-color: #fff;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
    }

    .formatted-text {
      display: block;
      margin-top: 4px;
      padding: 8px;
      background-color: #fff;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-word;
    }

    ::v-deep .el-descriptions {
      padding: 0;
    }

    ::v-deep .el-descriptions-item__label {
      width: 120px;
      background-color: #f5f7fa;
    }

    ::v-deep .el-tag {
      border-radius: 4px;
      padding: 0 10px;
      height: 24px;
      line-height: 24px;
    }
  }
</style>
