<template>
  <div class="active-alerts-view">
    <div class="stats-container">
      <div class="stat-card-wrapper">
        <el-card
          :body-style="{ padding: '12px' }"
          class="stat-card"
          shadow="never"
          @mouseenter="handleHover('critical')"
          @mouseleave="handleHover('')"
        >
          <div class="stat-content">
            <div class="stat-icon critical">
              <i class="el-icon-error"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">严重</div>
              <div class="stat-value">{{ statistics.critical }}</div>
            </div>
          </div>
        </el-card>
      </div>
      <div class="stat-card-wrapper">
        <el-card
          :body-style="{ padding: '12px' }"
          class="stat-card"
          shadow="never"
          @mouseenter="handleHover('major')"
          @mouseleave="handleHover('')"
        >
          <div class="stat-content">
            <div class="stat-icon major">
              <i class="el-icon-warning-outline"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">重要</div>
              <div class="stat-value">{{ statistics.major }}</div>
            </div>
          </div>
        </el-card>
      </div>
      <div class="stat-card-wrapper">
        <el-card
          :body-style="{ padding: '12px' }"
          class="stat-card"
          shadow="never"
          @mouseenter="handleHover('warning')"
          @mouseleave="handleHover('')"
        >
          <div class="stat-content">
            <div class="stat-icon warning">
              <i class="el-icon-warning"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">警告</div>
              <div class="stat-value">{{ statistics.warning }}</div>
            </div>
          </div>
        </el-card>
      </div>
      <div class="stat-card-wrapper">
        <el-card
          :body-style="{ padding: '12px' }"
          class="stat-card"
          shadow="never"
          @mouseenter="handleHover('events')"
          @mouseleave="handleHover('')"
        >
          <div class="stat-content">
            <div class="stat-icon events">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">
                事件告警
                <!-- <span v-if="timeRangeType !== 'custom'" class="stat-hint" >当天</span> -->
                <el-tag
                  v-if="timeRangeType !== 'custom'"
                  class="stat-hint"
                  effect="dark"
                  type="warning"
                  size="small"
                >
                  当天
                </el-tag>
              </div>
              <div class="stat-value">{{ statistics.events }}</div>
            </div>
          </div>
        </el-card>
      </div>
      <div class="stat-card-wrapper">
        <el-card
          :body-style="{ padding: '12px' }"
          class="stat-card"
          shadow="never"
          @mouseenter="handleHover('alarm')"
          @mouseleave="handleHover('')"
        >
          <div class="stat-content">
            <div class="stat-icon alarm">
              <i class="el-icon-message"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">{{ getAlarmLabel }}</div>
              <div class="stat-value">{{ statistics.alarm }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <el-card class="filter-card">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <div class="filter-row">
          <el-form-item label="级别">
            <el-radio-group
              v-model="queryParams.severity"
              size="mini"
              @change="handleSeverityChange"
            >
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="critical">严重</el-radio-button>
              <el-radio-button label="warning">警告</el-radio-button>
              <el-radio-button label="major">重要</el-radio-button>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="告警来源">
            <el-radio-group
              v-model="queryParams.source"
              size="small"
              @change="handleSourceChange"
            >
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button
                v-for="source in sourceList"
                :key="source.source"
                :label="source.source"
              >
                {{ formatSource(source.source) }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>

        <div class="filter-row">
          <el-form-item class="time-range-item" label="范围">
            <div class="time-range-container">
              <el-radio-group
                v-model="timeRangeType"
                @change="handleTimeRangeChange"
              >
                <el-radio-button label="15m">15分钟</el-radio-button>
                <el-radio-button label="30m">30分钟</el-radio-button>
                <el-radio-button label="1h">1小时</el-radio-button>
                <el-radio-button label="6h">6小时</el-radio-button>
                <el-radio-button label="custom">自定义</el-radio-button>
              </el-radio-group>
              <el-date-picker
                v-if="timeRangeType === 'custom'"
                v-model="timeRange"
                class="custom-date-picker"
                end-placeholder="结束时间"
                range-separator="至"
                start-placeholder="开始时间"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
                :default-time="['00:00:00', '23:59:59']"
                @change="handleCustomTimeChange"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>

    <el-row :gutter="24">
      <el-col :span="18">
        <div class="table-card">
          <!--<div v-if="selection.length > 0" class="table-operations">
            <el-button size="small" type="success" @click="handleBatchRecover">
              批量恢复 （{{ selection.length }}）
            </el-button>
          </div>
          -->

          <el-table
            v-loading="loading"
            :data="tableData"
            :height="null"
            style="width: 100%"
          >
            <!--
          <el-table
            v-loading="loading"
            :data="tableData"
            :height="null"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              :selectable="checkSelectable"
              type="selection"
              width="55"
            />
            -->
            <el-table-column label="" prop="severity" min-width="20">
              <template #default="scope">
                <div class="severity-icon-wrapper">
                  <i
                    :class="getSeverityIcon(scope.row.severity)"
                    :style="{ color: getSeverityColor(scope.row.severity) }"
                    class="severity-icon"
                  ></i>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="告警时间" prop="starts_at" min-width="120">
              <template #default="scope">
                <div class="time-wrapper">
                  <span class="formatted-time">
                    {{ formatFullTime(scope.row.starts_at) }}
                  </span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="命名空间" prop="namespace" min-width="100">
              <template #default="scope">
                <el-tag
                  v-if="
                    scope.row.source === 'tencent_cloud' && scope.row.labels
                  "
                  :effect="getTagEffect('namespace')"
                  class="label-tag"
                  size="small"
                >
                  {{
                    scope.row.labels.namespace ||
                    scope.row.labels.product_show_name
                  }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="对象" prop="alert_object" min-width="150">
              <template #default="scope">
                <span v-if="getAlertObject(scope.row)" size="small" type="info">
                  {{ getAlertObject(scope.row) }}
                </span>
                <span v-else class="no-object">-</span>
              </template>
            </el-table-column>

            <el-table-column
              label=""
              prop="summary"
              min-width="150"
              show-overflow-tooltip
            />

            <el-table-column fixed="right" label="操作" min-width="80">
              <template #default="scope">
                <el-button
                  class="detail-button"
                  size="small"
                  type="primary"
                  @click="handleDetailClick(scope.row)"
                >
                  <i class="el-icon-view"></i>
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              :current-page="currentPage"
              :page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </el-col>

      <el-col :span="6">
        <div class="stats-section">
          <div class="section-header">
            <h3>告警对象统计 (活跃)</h3>
          </div>
          <alert-object-pie
            v-loading="objectStatsLoading"
            :data="alertObjectList"
            layout="vertical"
            :active-table-height="tableHeight"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import {
    getAlertCenterSources,
    getAlertsCenterActive,
    recoverAlert,
    getEventStatistics,
  } from '@/api/monitoring'
  import { debounce } from 'lodash'
  import dayjs from 'dayjs'
  import utc from 'dayjs/plugin/utc'
  import timezone from 'dayjs/plugin/timezone'
  import AlertObjectPie from './AlertObjectPie.vue'

  dayjs.extend(utc)
  dayjs.extend(timezone)
  dayjs.tz.setDefault('Asia/Shanghai')

  export default {
    name: 'ActiveAlerts',
    components: {
      AlertObjectPie,
    },

    data() {
      return {
        loading: false,
        timeRange: this.getDefaultTimeRange(),
        queryParams: {
          severity: '',
          status: 'ALARM',
          source: '',
        },
        statistics: {
          critical: 0,
          major: 0,
          warning: 0,
          events: 0,
          total: 0,
          alarm: 0,
          noData: 0,
          ok: 0,
        },
        tableData: [],
        severityFilters: [
          { text: '严重', value: 'critical' },
          { text: '警告', value: 'warning' },
        ],
        sourceList: [],
        sourceFilters: [],
        sourceTypeMap: {
          prometheus: 'warning',
          grafana: 'success',
          custom: 'info',
          tencent_cloud: 'primary',
          pinpoint: '#409EFF',
        },
        statusFilters: [
          { text: '告警中', value: 'ALARM' },
          { text: '无数据', value: 'NO_DATA' },
        ],
        timeRangeType: '1h',
        lastTimeRange: null,
        currentPage: 1,
        pageSize: 20,
        total: 0,
        severityColorMap: {
          critical: 'danger',
          major: 'danger',
          warning: 'warning',
          resolved: 'success',
        },
        statusColorMap: {
          ALARM: 'warning',
          OK: 'success',
          NO_DATA: 'info',
        },
        accountColorMap: { PunkSong: 'primary', 微购科技2: 'success' },
        objectStatsLoading: false,
        alertObjectList: [],
        debouncedFetchData: null,
        hoveredCard: '',
        refreshTimer: null,
        refreshInterval: 5 * 60 * 1000,
        selection: [],
        tableHeight: 300,
        tableObserver: null,
        allFilteredData: [],
        // 自定义时间选择器配置
        pickerOptions: {
          // 禁用今天之后的所有日期
          disabledDate: (time) => {
            // 使用 dayjs 进行精确的日期比较，只比较到天级别
            const today = dayjs().endOf('day')
            return dayjs(time).isAfter(today)
          },
          // 设置快捷选项，便于用户快速选择常用时间范围
          shortcuts: [
            {
              text: '最近一周',
              onClick: (picker) => {
                const end = dayjs().endOf('day')
                const start = end.subtract(6, 'day').startOf('day')
                picker.$emit('pick', [start.toDate(), end.toDate()])
              },
            },
            {
              text: '最近一个月',
              onClick: (picker) => {
                const end = dayjs().endOf('day')
                const start = end.subtract(29, 'day').startOf('day')
                picker.$emit('pick', [start.toDate(), end.toDate()])
              },
            },
            {
              text: '最近三个月',
              onClick: (picker) => {
                const end = dayjs().endOf('day')
                const start = end.subtract(89, 'day').startOf('day')
                picker.$emit('pick', [start.toDate(), end.toDate()])
              },
            },
          ],
          // 控制默认显示的月份：左边为上个月，右边为当前月
          onPick: ({ maxDate, minDate }) => {
            // 当选择了开始日期但还没选择结束日期时
            if (minDate && !maxDate) {
              // 返回一个日期，让右侧面板显示该日期所在的月份
              // 这里返回当前月的第一天，确保右侧显示当前月
              return dayjs().startOf('month').toDate()
            }
          },
        },
      }
    },
    computed: {
      getAlarmLabel() {
        const { severity, status, source } = this.queryParams

        if (severity || status || source) {
          return `${this.formatSource(source)}${this.formatSeverity(
            severity
          )}${this.formatStatus(status)}`
        }
        return '告警总数'
      },

      // 计算默认显示的日期范围：左边为上个月，右边为当前月
      defaultDateRange() {
        // 上个月的第一天
        const lastMonthStart = dayjs().subtract(1, 'month').startOf('month')
        // 当前月的第一天
        const currentMonthStart = dayjs().startOf('month')

        return [lastMonthStart.toDate(), currentMonthStart.toDate()]
      },
    },
    watch: {
      '$route.query': {
        handler(newQuery) {
          if (newQuery.severity !== undefined) {
            this.queryParams.severity = newQuery.severity || ''
          }

          if (newQuery.source !== undefined) {
            this.queryParams.source = newQuery.source || ''
          }

          if (newQuery.start_time && newQuery.end_time) {
            this.timeRange = [newQuery.start_time, newQuery.end_time]
            this.timeRangeType = 'custom'
            this.lastTimeRange = [newQuery.start_time, newQuery.end_time]
          }

          this.handleQuery()
        },
        deep: true,
      },
    },

    mounted() {
      this.$nextTick(() => {
        this.calculateTableHeight()
        this.setupTableObserver()
        window.addEventListener('resize', this.handleWindowResize)
      })
    },

    deactivated() {
      this.stopAutoRefresh()
    },

    created() {
      this.debouncedFetchData = debounce(this.fetchData, 300)

      const { start_time, end_time } = this.$route.query
      if (start_time && end_time) {
        this.timeRange = [start_time, end_time]
        this.timeRangeType = 'custom'
        this.lastTimeRange = [start_time, end_time]
      }

      this.queryParams.severity = this.$route.query.severity || ''
      this.queryParams.status = 'ALARM'
      this.queryParams.source = this.$route.query.source || ''

      this.handleQuery()
      this.startAutoRefresh()
      this.fetchAlertSources()
      // 初始化时获取当天的事件统计数据
      this.initEventStatistics()
    },

    beforeDestroy() {
      this.stopAutoRefresh()
      window.removeEventListener('resize', this.handleWindowResize)
      if (this.tableObserver) {
        this.tableObserver.disconnect()
      }
    },

    activated() {
      this.startAutoRefresh()
    },
    methods: {
      getTagEffect(key) {
        return ['alertStatus', 'severity', 'type'].includes(key)
          ? 'light'
          : 'plain'
      },

      setInitialFilters(filters) {
        this.tableData = []
        if (filters.start_time && filters.end_time) {
          this.timeRangeType = 'custom'
          this.timeRange = [
            dayjs(filters.start_time).format('YYYY-MM-DD HH:mm:ss'),
            dayjs(filters.end_time).format('YYYY-MM-DD HH:mm:ss'),
          ]
          this.lastTimeRange = [...this.timeRange]
        } else {
          this.timeRangeType = '1h'
          this.timeRange = this.getDefaultTimeRange()
        }

        this.queryParams = {
          severity: filters.severity || '',
          status: 'ALARM',
          source: filters.source || '',
        }

        this.currentPage = 1
        this.pageSize = 20

        this.$nextTick(() => {
          this.handleQuery()
        })
      },

      formatDateTime(date) {
        const pad = (num) => String(num).padStart(2, '0')
        return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(
          date.getDate()
        )} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(
          date.getSeconds()
        )}`
      },

      getCurrentDayStart() {
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        return today
      },
      getCurrentDayEnd() {
        const today = new Date()
        today.setHours(23, 59, 59, 999)
        return today
      },
      formatISODate(date) {
        if (!date) return ''
        const d = date instanceof Date ? date : new Date(date)

        if (isNaN(d.getTime())) return ''
        return d.toISOString().slice(0, 19).replace('T', ' ')
      },

      getSeverityType(severity) {
        return this.severityColorMap[severity] || 'info'
      },

      getSeverityIcon(severity) {
        const iconMap = {
          critical: 'el-icon-error',
          warning: 'el-icon-warning',
          major: 'el-icon-warning-outline',
          resolved: 'el-icon-success',
        }
        return iconMap[severity] || 'el-icon-info'
      },

      getSeverityColor(severity) {
        const colorMap = {
          critical: '#F56C6C',
          warning: '#E6A23C',
          major: '#FF8C00',
          resolved: '#67C23A',
        }
        return colorMap[severity] || '#909399'
      },

      formatFullTime(time) {
        if (!time) return '-'
        return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
      },

      getAlertObject(row) {
        if (!row || !row.labels) return null

        const checkNamespace = (keyword) =>
          row.labels.namespace?.includes(keyword) || false

        if (row.source === 'tencent_cloud') {
          const hasTke = checkNamespace('tke')
          const hasCdb = checkNamespace('cdb')
          const hasCes = checkNamespace('ces')

          const tkeFallbackFields = [
            'dim_instanceid',
            'dim_unInstanceId',
            'dim_instance_id',
            'dim_objId',
            'dim_objName',
            'target',
            'alert_object',
          ]

          let alertObject = hasTke
            ? row.labels.dim_objName || null
            : this.findFirstValidField(row.labels, tkeFallbackFields)

          if (alertObject && hasTke) {
            const podMatch = alertObject.match(/Pod名称:([^|]+)/)
            return podMatch?.[1]?.trim() || null
          }

          if (alertObject && hasCdb) return row.labels.dim_uInstanceId
          if (alertObject && hasCes) return row.labels.dim_cluster_name

          return alertObject
        }
        if (row.source === 'custom') {
          if (row.annotations?.extra_info?.jump_url) {
            try {
              const url = new URL(row.annotations.extra_info.jump_url)
              const searchParams = new URLSearchParams(url.search)
              let pathValue = searchParams.get('path')

              if (!pathValue && url.hash) {
                const hashParams = new URLSearchParams(
                  url.hash.split('?')[1] || ''
                )
                pathValue = hashParams.get('path')
              }
              return (
                pathValue || row.labels.path || row.labels.alert_object || null
              )
            } catch (error) {
              console.error('解析URL失败:', error)
            }
          }
          return row.labels.path || row.labels.alert_object || null
        }
        return row.labels.alert_object || null
      },
      findFirstValidField(obj, fields) {
        for (const field of fields) {
          if (obj[field] !== undefined && obj[field] !== null) {
            return obj[field]
          }
        }
        return null
      },
      getStatusType(status) {
        return this.statusColorMap[status] || 'info'
      },
      getSourceType(source) {
        const sourceTypeMap = {
          prometheus: 'warning',
          grafana: 'success',
          custom: 'info',
          tencent_cloud: 'primary',
          pinpoint: 'primary',
        }
        return sourceTypeMap[source] || 'info'
      },
      getAccountColor(accountId) {
        const accountName = accountId.split('|')[0]
        const accountColorMap = {
          PunkSong: '#409EFF',
          微购科技2: '#67C23A',
        }
        return accountColorMap[accountName] || '#909399'
      },
      filterSeverity(value, row) {
        return row.severity === value
      },
      filterSource(value, row) {
        return row.source === value
      },
      filterStatus(value, row) {
        return row.status === value
      },
      formatSeverity(severity) {
        const map = {
          critical: '严重',
          major: '重要',
          warning: '警告',
          resolved: '已恢复',
        }
        return map[severity] || severity
      },
      formatStatus(status) {
        const map = {
          ALARM: '告警中',
          OK: '已恢复',
          NO_DATA: '无数据',
        }
        return map[status] || status
      },
      formatSource(source) {
        const sourceDisplayMap = {
          prometheus: 'Prometheus',
          grafana: 'Grafana',
          custom: '自定义',
          tencent_cloud: '腾讯云',
          pinpoint: 'Pinpoint',
        }
        return sourceDisplayMap[source] || source
      },

      formatSourceWithAccount(alert) {
        if (alert.source === 'tencent_cloud' && alert.account_id) {
          const accountName = alert.account_id.split('|')[0]
          return `腾讯云 ${accountName}`
        }
        return this.formatSource(alert.source)
      },

      async resetQuery() {
        this.queryParams = { severity: '', status: 'ALARM', source: '' }
        this.timeRangeType = '1h'
        this.handleTimeRangeChange('1h')
        this.currentPage = 1
        await this.handleQuery()
        // 重置时强制刷新事件统计数据
        await this.fetchEventStatistics(true)
      },

      updateAlertObjectStats() {
        const objectMap = new Map()

        this.tableData.forEach((alert) => {
          const alertObject = this.getAlertObject(alert) || '未知对象'

          if (objectMap.has(alertObject)) {
            objectMap.set(alertObject, objectMap.get(alertObject) + 1)
          } else {
            objectMap.set(alertObject, 1)
          }
        })

        this.alertObjectList = Array.from(objectMap.entries()).map(
          ([alertObject, count]) => ({
            alertObject,
            count,
          })
        )
      },

      getDefaultTimeRange() {
        const end = dayjs().tz()
        const start = end.subtract(1, 'hour')
        return [
          start.format('YYYY-MM-DD HH:mm:ss'),
          end.format('YYYY-MM-DD HH:mm:ss'),
        ]
      },

      handleTimeRangeChange(type) {
        // 记录之前的时间范围类型，用于判断是否从自定义切换到预设
        const previousTimeRangeType = this.timeRangeType

        if (type === 'custom') {
          if (this.lastTimeRange) {
            this.timeRange = [...this.lastTimeRange]
          }
          return
        }

        const end = dayjs().tz()
        let start = end.clone()

        switch (type) {
          case '15m':
            start = end.subtract(15, 'minute')
            break
          case '30m':
            start = end.subtract(30, 'minute')
            break
          case '1h':
            start = end.subtract(1, 'hour')
            break
          case '6h':
            start = end.subtract(6, 'hour')
            break
          default:
            start = end.subtract(1, 'hour')
            break
        }

        this.timeRange = [
          start.format('YYYY-MM-DD HH:mm:ss'),
          end.format('YYYY-MM-DD HH:mm:ss'),
        ]

        this.lastTimeRange = [...this.timeRange]

        if (type !== 'custom') {
          this.currentPage = 1
          this.handleQuery()

          // 如果从自定义时间切换到预设时间，强制刷新事件统计
          if (previousTimeRangeType === 'custom') {
            console.log(
              '事件告警：从自定义时间切换到预设时间，强制刷新当天数据'
            )
            this.fetchEventStatistics(true)
          }
        }
      },

      handleCustomTimeChange(val) {
        if (val && val.length === 2) {
          this.timeRange = val
          this.lastTimeRange = [...val]
          this.currentPage = 1
          this.handleQuery()
        } else {
          this.timeRange = []
        }
      },

      resetStatistics() {
        this.statistics = {
          critical: 0,
          major: 0,
          warning: 0,
          events: 0,
          total: 0,
          alarm: 0,
          noData: 0,
          ok: 0,
        }
      },

      updateStatistics(apiStats, paginationTotal) {
        // 保存 events 值，避免被重置
        const currentEvents = this.statistics.events
        this.resetStatistics()
        // 恢复 events 值
        this.statistics.events = currentEvents

        if (!apiStats) {
          this.statistics.total = paginationTotal || 0
          return
        }
        if (apiStats.severity_stats) {
          this.statistics.critical = apiStats.severity_stats.critical || 0
          this.statistics.major = apiStats.severity_stats.major || 0
          this.statistics.warning = apiStats.severity_stats.warning || 0

          this.statistics.ok = apiStats.severity_stats.resolved || 0
        }

        this.statistics.alarm = paginationTotal - this.statistics.ok || 0

        this.statistics.total = paginationTotal || 0
      },

      async fetchData() {
        this.loading = true
        try {
          if (!this.timeRange || this.timeRange.length !== 2) {
            console.warn(
              'fetchData called with invalid time range:',
              this.timeRange
            )
            return
          }

          const params = {
            severity: this.queryParams.severity || null,
            status: this.queryParams.status || null,
            source: this.queryParams.source || null,
            start_time: this.timeRange[0],
            end_time: this.timeRange[1],
            page: this.currentPage,
            per_page: this.pageSize,
          }

          const response = await getAlertsCenterActive(params)

          if (response.status === 'success' && response.data) {
            const data = response.data
            this.tableData = data.alerts || []
            const paginationTotal = data.pagination
              ? data.pagination.total_records
              : 0
            this.total = paginationTotal
            this.updateStatistics(data.statistics, paginationTotal)
            this.updateAlertObjectStats()

            const maxPage = Math.ceil(paginationTotal / this.pageSize)
            if (this.currentPage > maxPage && maxPage > 0) {
              this.currentPage = maxPage
              await this.fetchData()
              return
            }
          } else {
            this.$message.error(response.message || '获取活跃告警数据失败')
            this.tableData = []
            this.total = 0
            this.resetStatistics()
          }
        } catch (error) {
          console.error('获取活跃告警数据时发生错误:', error)
          this.$message.error('获取活跃告警数据时发生网络或服务器错误')
          this.tableData = []
          this.total = 0
          this.resetStatistics()
        } finally {
          const maxPage = Math.ceil(this.total / this.pageSize)
          if (!(this.currentPage > maxPage && maxPage > 0)) {
            this.loading = false
          }
        }
      },

      async fetchAlertSources() {
        if (this.sourceStatsLoading) return
        this.sourceStatsLoading = true

        try {
          let startTime, endTime

          if (this.timeRange && this.timeRange.length === 2) {
            ;[startTime, endTime] = this.timeRange
          } else {
            startTime = this.formatDateTime(this.getCurrentDayStart())
            endTime = this.formatDateTime(this.getCurrentDayEnd())
          }

          const params = {
            start_time: startTime,
            end_time: endTime,
            status: 'true',
            source: this.queryParams.source || null,
          }

          const response = await getAlertCenterSources(params)

          if (
            response.status === 'success' &&
            response.data &&
            response.data.sources
          ) {
            this.sourceList = response.data.sources.map((item) => ({
              source: item.source,
              count: item.count,
            }))
          } else {
            throw new Error(response.message || '获取告警来源数据失败')
          }
        } catch (error) {
          console.error('获取告警来源失败:', error)
          this.$message.error(error.message || '获取告警来源失败')
          this.sourceList = []
        } finally {
          this.sourceStatsLoading = false
        }
      },

      async fetchEventStatistics(forceRefresh = false) {
        try {
          // 如果是非自定义时间范围且不是强制刷新，则跳过
          if (this.timeRangeType !== 'custom' && !forceRefresh) {
            console.log(
              '事件告警：非自定义时间范围，跳过接口调用，保持当天数据'
            )
            return
          }

          let params = null
          if (
            this.timeRangeType === 'custom' &&
            this.timeRange &&
            this.timeRange.length === 2
          ) {
            params = {
              start_time: this.timeRange[0],
              end_time: this.timeRange[1],
            }
            console.log('事件告警：自定义时间范围，使用时间参数', params)
          } else {
            console.log('事件告警：获取当天数据')
          }

          const response = await getEventStatistics(params)
          console.log('事件告警：接口调用成功', response.data)

          if (response.status === 'success' && response.data) {
            this.statistics.events = response.data.total_events || 0
            console.log('事件告警：更新统计数据', this.statistics.events)
          } else {
            console.warn('获取事件统计数据失败:', response.message)
            this.statistics.events = 0
          }
        } catch (error) {
          console.error('获取事件统计失败:', error)
          this.statistics.events = 0
        }
      },

      async initEventStatistics() {
        try {
          console.log('初始化事件告警统计：获取当天数据')
          const response = await getEventStatistics()

          if (response.status === 'success' && response.data) {
            this.statistics.events = response.data.total_events || 0
            console.log('初始化事件告警统计成功:', this.statistics.events)
          } else {
            console.warn('初始化事件统计数据失败:', response.message)
            this.statistics.events = 0
          }
        } catch (error) {
          console.error('初始化事件统计失败:', error)
          this.statistics.events = 0
        }
      },

      handleSizeChange(val) {
        this.pageSize = val
        this.currentPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.currentPage = val
        this.fetchData()
      },

      handleDetailClick(row) {
        this.$emit('show-detail', row)
      },

      async recoverAlert(fingerprint) {
        return await recoverAlert(fingerprint)
      },
      isCustomColorSource(source) {
        return source === 'pinpoint'
      },
      handleHover(type) {
        this.hoveredCard = type
      },

      async handleQuery() {
        try {
          await Promise.all([this.fetchData(), this.fetchAlertSources()])
          await this.fetchEventStatistics(true)
        } catch (error) {
          console.error('查询失败:', error)
          this.$message.error('查询失败，请重试')
        }
      },

      async handleFilterChange() {
        this.currentPage = 1
        await this.handleQuery()
      },
      async handleSeverityChange() {
        this.currentPage = 1
        await this.handleQuery()
      },

      handleSourceChange(value) {
        this.queryParams.source = value
        this.currentPage = 1
        this.handleQuery()
      },

      startAutoRefresh() {
        this.stopAutoRefresh()

        this.refreshTimer = setInterval(() => {
          this.handleQuery()
        }, this.refreshInterval)
      },
      stopAutoRefresh() {
        if (this.refreshTimer) {
          clearInterval(this.refreshTimer)
          this.refreshTimer = null
        }
      },
      async handleRecover(row) {
        if (!row.fingerprint) {
          this.$message({
            type: 'error',
            message: '告警指纹信息缺失',
          })
          return
        }

        try {
          await this.$confirm('确认将该告警标记为已恢复？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })

          const loading = this.$loading({
            lock: true,
            text: '正在处理...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })

          await recoverAlert(row.fingerprint)

          this.$message({
            type: 'success',
            message: '告警已恢复',
          })

          await this.fetchData()
        } catch (error) {
          if (error !== 'cancel') {
            this.$message({
              type: 'error',
              message: '操作失败：' + (error.message || '未知错误'),
            })
          }
        } finally {
          this.$loading().close()
        }
      },

      handleSelectionChange(selection) {
        this.selection = selection
      },

      checkSelectable(row) {
        return row.status === 'ALARM'
      },

      async handleBatchRecover() {
        if (this.selection.length === 0) {
          this.$message({
            type: 'warning',
            message: '请选择需要恢复的告警',
          })
          return
        }

        const invalidAlerts = this.selection.filter((item) => !item.fingerprint)
        if (invalidAlerts.length > 0) {
          this.$message({
            type: 'error',
            message: '存在告警指纹信息缺失，无法处理',
          })
          return
        }

        try {
          await this.$confirm(
            `确认将选中的 ${this.selection.length} 条告警标记为已恢复？`,
            '批量恢复确认',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )

          const loading = this.$loading({
            lock: true,
            text: '正在处理...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)',
          })

          try {
            await Promise.all(
              this.selection.map((item) => recoverAlert(item.fingerprint))
            )

            this.$message({
              type: 'success',
              message: '批量恢复成功',
            })

            await this.fetchData()
          } finally {
            loading.close()
          }
        } catch (error) {
          if (error !== 'cancel') {
            this.$message({
              type: 'error',
              message: '操作失败：' + (error.message || '未知错误'),
            })
          }
        }
      },
      calculateTableHeight() {
        this.$nextTick(() => {
          const tableEl = this.$el.querySelector('.el-table__body-wrapper')
          if (tableEl && tableEl.clientHeight > 0) {
            this.tableHeight = tableEl.clientHeight
          } else {
            const windowHeight = window.innerHeight
            this.tableHeight = Math.max(windowHeight * 0.4, 300)
          }
        })
      },

      setupTableObserver() {
        const tableEl = this.$el.querySelector('.el-table')
        if (tableEl) {
          this.tableObserver = new MutationObserver(() => {
            this.calculateTableHeight()
          })

          this.tableObserver.observe(tableEl, {
            attributes: true,
            childList: true,
            subtree: true,
          })
        }
      },

      handleWindowResize() {
        this.calculateTableHeight()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .active-alerts-view {
    padding: 8px 5px;

    .stats-container {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;

      @media (max-width: 1200px) {
        flex-wrap: wrap;
        gap: 20px;
      }

      @media (max-width: 992px) {
        gap: 16px;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;
      }
    }

    .stat-card-wrapper {
      flex: 1;
      min-width: 0;

      @media (max-width: 1200px) and (min-width: 993px) {
        flex: 0 0 calc(33.333% - 14px);
      }

      @media (max-width: 992px) and (min-width: 769px) {
        flex: 0 0 calc(50% - 8px);
      }

      @media (max-width: 768px) {
        flex: none;
      }
    }

    .stat-card {
      border: 1px solid #e8eaec;
      border-radius: 8px;
      background: #ffffff;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: #d9d9d9;
      }

      ::v-deep .el-card__body {
        padding: 12px !important;
        height: 100%;
        display: flex;
        align-items: center;
      }
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .stat-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 20px;
        color: white;
      }

      &.critical {
        background-color: #f56c6c;
      }

      &.major {
        background-color: #ff8c00;
      }

      &.warning {
        background-color: #e6a23c;
      }

      &.events {
        // background-color: #67c23a;
        background-color: #909399;
      }

      &.alarm {
        background-color: #409eff;
      }
    }

    .stat-info {
      flex: 1;
      min-width: 0;
    }

    .stat-title {
      font-size: 13px;
      color: #8c8c8c;
      margin-bottom: 4px;
      line-height: 1.2;
      font-weight: 400;
      min-height: 20px;
      display: flex;
      align-items: center;
    }

    .stat-hint {
      font-size: 11px;
      color: #c0c0c0;
      font-weight: 300;
      margin-left: 4px;
      // 针对 el-tag 的对齐修复
      vertical-align: middle;
      // 确保标签高度不影响整体布局
      height: 16px;
      line-height: 14px;
      margin-left: 20px;
    }

    .stat-value {
      font-size: 22px;
      font-weight: 600;
      color: #262626;
      line-height: 1.2;
    }

    // 响应式优化
    @media (max-width: 992px) {
      .stat-content {
        gap: 10px;
      }

      .stat-icon {
        width: 36px;
        height: 36px;

        i {
          font-size: 18px;
        }
      }

      .stat-title {
        font-size: 12px;
      }

      .stat-value {
        font-size: 20px;
      }
    }

    @media (max-width: 768px) {
      .stat-content {
        gap: 8px;
      }

      .stat-icon {
        width: 32px;
        height: 32px;

        i {
          font-size: 16px;
        }
      }

      .stat-title {
        font-size: 11px;
      }

      .stat-value {
        font-size: 18px;
      }
    }
  }

  ::v-deep .el-tag {
    border-radius: 4px;
    padding: 0 10px;
    height: 24px;
    line-height: 24px;

    &.el-tag--warning {
      background-color: #ffab2e;
      border-color: #ffab2e;
      color: white;

      &:hover {
        background-color: #ffab2e;
        border-color: #ffab2e;
        color: white;
      }
    }

    &.el-tag--danger {
      background-color: #fe495a;
      border-color: #fe495a;
      color: white;

      &:hover {
        background-color: #fe495a;
        border-color: #fe495a;
        color: white;
      }
    }
  }

  .time-range-item {
    .el-date-editor {
      width: 380px !important;
      margin-left: 10px;
    }
    .time-range-container {
      .custom-date-picker {
        width: 400px;
      }
    }
  }

  .filter-card {
    margin-top: 10px;
    margin-bottom: 20px;

    ::v-deep .el-card__body {
      padding: 2px;
    }

    .filter-form {
      .filter-row {
        padding: 10px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .el-form-item {
          margin-bottom: 0;
          margin-top: 1;
        }
      }
    }
  }

  .table-card {
    .table-operations {
      margin-bottom: 16px;

      .el-button {
        margin-right: 8px;
      }
    }
  }

  ::v-deep .el-table__row.selected {
    background-color: #f5f7fa;
  }

  .severity-icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;

    .severity-icon {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .time-wrapper {
    .formatted-time {
      font-size: 12px;
      color: #606266;
      font-family: monospace;
    }
  }

  .no-object {
    color: #c0c4cc;
    font-style: italic;
  }

  .detail-button {
    border-radius: 4px;
    font-size: 12px;

    i {
      margin-right: 4px;
    }
  }
</style>
