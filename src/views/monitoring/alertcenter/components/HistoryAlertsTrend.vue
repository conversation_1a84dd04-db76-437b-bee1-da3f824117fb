<template>
  <div class="history-alerts-trend">
    <el-card class="trend-card">
      <div class="card-header">
        <h4>趋势分析</h4>
        <div class="legend-container">
          <div
            v-for="(item, index) in legendItems"
            :key="index"
            class="legend-item"
          >
            <span
              class="legend-dot"
              :style="{ backgroundColor: item.color }"
            ></span>
            <span class="legend-text">{{ item.name }}</span>
          </div>
        </div>
      </div>
      <div ref="trendChart" class="chart-container"></div>
    </el-card>
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  import dayjs from 'dayjs'

  export default {
    name: 'HistoryAlertsTrend',
    props: {
      trendData: {
        type: Object,
        required: true,
        default: () => ({
          interval: 'hour',
          trend: [],
          summary: {
            total: 0,
            critical: 0,
            warning: 0,
            resolved: 0,
          },
        }),
      },
      timeRange: {
        type: Array,
        required: true,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        trendChart: null,
        legendItems: [
          { name: '总数', color: '#409EFF' },
          { name: '严重', color: '#F56C6C' },
          { name: '警告', color: '#E6A23C' },
          { name: '已恢复', color: '#67C23A' },
        ],
      }
    },
    watch: {
      trendData: {
        handler() {
          this.updateChart()
        },
        deep: true,
      },
    },
    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      this.trendChart?.dispose()
    },
    methods: {
      initChart() {
        if (!this.trendChart) {
          this.trendChart = echarts.init(this.$refs.trendChart)
        }
        this.updateChart()
      },

      generateTimeSequence() {
        const [startTime, endTime] = this.timeRange
        const start = dayjs(startTime)
        const end = dayjs(endTime)
        const interval = this.trendData.interval
        const times = []

        let current = start.clone()
        while (current.isBefore(end) || current.isSame(end)) {
          times.push(
            current.format(
              interval === 'hour' ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
            )
          )
          current = current.add(1, interval)
        }

        return times
      },

      getDataForTimestamp(timestamp, trendMap) {
        return (
          trendMap.get(timestamp) || {
            total: 0,
            critical: 0,
            warning: 0,
            resolved: 0,
          }
        )
      },

      updateChart() {
        if (!this.trendChart) return

        const { trend } = this.trendData
        const times = trend.map((item) => item.timestamp)

        const option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'line',
              lineStyle: {
                color: '#ddd',
                width: 1,
                type: 'solid',
              },
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: times,
            axisLine: {
              lineStyle: {
                color: '#ddd',
              },
            },
            axisLabel: {
              formatter: (value) => {
                if (this.trendData.interval === 'day') {
                  return value
                }
                return value.split(' ')[1]
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#eee',
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          series: [
            {
              name: '总数',
              type: 'line',
              smooth: true,
              showSymbol: false,
              symbol: 'circle',
              symbolSize: 6,
              itemStyle: { color: '#409EFF' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#409EFF' },
                  { offset: 1, color: 'rgba(255, 255, 255, 0.1)' },
                ]),
              },
              data: trend.map((item) => item.total),
            },
            {
              name: '严重',
              type: 'line',
              smooth: true,
              showSymbol: false,
              itemStyle: { color: '#F56C6C' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#F56C6C' },
                  { offset: 1, color: 'rgba(255, 255, 255, 0.1)' },
                ]),
              },
              data: trend.map((item) => item.critical),
            },
            {
              name: '警告',
              type: 'line',
              smooth: true,
              showSymbol: false,
              itemStyle: { color: '#E6A23C' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#E6A23C' },
                  { offset: 1, color: 'rgba(255, 255, 255, 0.1)' },
                ]),
              },
              data: trend.map((item) => item.warning),
            },
            {
              name: '已恢复',
              type: 'line',
              smooth: true,
              showSymbol: false,
              itemStyle: { color: '#67C23A' },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#67C23A' },
                  { offset: 1, color: 'rgba(255, 255, 255, 0.1)' },
                ]),
              },
              data: trend.map((item) => item.resolved),
            },
          ],
        }

        this.trendChart.setOption(option)
      },

      handleResize() {
        this.trendChart?.resize()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .history-alerts-trend {
    .trend-card {
      height: 100%;

      .card-header {
        margin-bottom: 20px;

        h4 {
          margin: 0 0 15px 0;
          font-size: 14px;
          color: #303133;
          font-weight: 500;
        }
      }

      .legend-container {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .legend-item {
          display: flex;
          align-items: center;
          cursor: pointer;

          .legend-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
          }

          .legend-text {
            font-size: 12px;
            color: #606266;
          }
        }
      }

      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
  }
</style>
