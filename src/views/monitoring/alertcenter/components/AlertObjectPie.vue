<template>
  <div
    class="alert-object-pie-container"
    :class="{ 'horizontal-layout': layout === 'horizontal' }"
  >
    <div ref="pieChart" class="pie-chart"></div>
    <div class="data-table-container">
      <el-table
        :data="tableData"
        size="small"
        :height="tableHeight"
        :highlight-current-row="true"
        @row-click="handleRowClick"
        @row-mouse-enter="handleRowHover"
        @row-mouse-leave="handleRowLeave"
      >
        <el-table-column prop="name" label="对象" min-width="150">
          <template slot-scope="scope">
            <el-tooltip
              :content="scope.row.name"
              placement="top"
              :disabled="scope.row.name.length < 20"
            >
              <span>
                {{
                  scope.row.name.length > 20
                    ? scope.row.name.substring(0, 17) + '...'
                    : scope.row.name
                }}
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="value"
          label=""
          min-width="20"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="percentage"
          label="占比"
          width="80"
          align="center"
        >
          <template slot-scope="scope">{{ scope.row.percentage }}%</template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'
  export default {
    name: 'AlertObjectPie',

    props: {
      data: {
        type: Array,
        required: true,
        default: () => [],
      },
      height: {
        type: String,
        default: '100%',
      },
      width: {
        type: String,
        default: '100%',
      },
      layout: {
        type: String,
        default: 'vertical', // 'vertical' 或 'horizontal'
        validator: (value) => ['vertical', 'horizontal'].includes(value),
      },
      activeTableHeight: {
        type: [Number, String],
        default: null,
      },
    },

    data() {
      return {
        chart: null,
        tableData: [],
        currentIndex: -1,
        tableHeight: '200px',
      }
    },

    watch: {
      data: {
        handler() {
          this.$nextTick(() => {
            this.updateChart()
          })
        },
        deep: true,
      },
      layout: {
        handler() {
          this.$nextTick(() => {
            this.handleResize()
            this.updateTableHeight()
          })
        },
      },
      activeTableHeight: {
        handler() {
          this.updateTableHeight()
        },
      },
    },

    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
      this.updateTableHeight()
    },

    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    methods: {
      async initChart() {
        await this.$nextTick()

        if (this.chart) {
          this.chart.dispose()
        }

        const container = this.$refs.pieChart
        if (!container) return

        this.chart = echarts.init(container)
        this.chart.on('mouseover', this.handlePieHover)
        this.chart.on('mouseout', this.handlePieLeave)
        this.chart.on('click', this.handlePieClick)
        this.updateChart()
      },

      updateChart() {
        if (!this.chart) return

        const total = this.data.reduce((sum, item) => sum + item.count, 0)
        const chartData = this.data.map((item) => {
          let objectName = item.alertObject

          if (!objectName || objectName.trim() === '') {
            objectName = '未知对象'
          }

          return {
            name: objectName,
            value: item.count,
          }
        })

        this.tableData = chartData
          .map((item) => {
            const percentage =
              total > 0 ? ((item.value / total) * 100).toFixed(1) : '0.0'
            return {
              ...item,
              percentage,
            }
          })
          .sort((a, b) => b.value - a.value)

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
          },
          legend: {
            show: false, // 隐藏图例
          },
          series: [
            {
              name: '告警对象',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '50%'],
              avoidLabelOverlap: true,
              emphasis: {
                focus: 'series',
                scaleSize: 10,
              },
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: true,
                position: 'outside',
                formatter: (params) => {
                  const name = params.name
                  // 如果名称过长，在标签中截断显示
                  const displayName =
                    name.length > 10 ? name.substring(0, 7) + '...' : name
                  return `${displayName}\n${params.percent}%`
                },
                fontSize: 12,
                color: '#606266',
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 10,
                smooth: true,
              },
              data: chartData,
            },
          ],
          color: [
            '#409EFF',
            '#67C23A',
            '#E6A23C',
            '#F56C6C',
            '#909399',
            '#C45656',
            '#73C0DE',
            '#3BA272',
          ],
        }

        this.chart.setOption(option)
      },

      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
        this.updateTableHeight()
      },

      handlePieHover(params) {
        if (params.dataIndex !== undefined) {
          this.currentIndex = params.dataIndex
          const table = this.$el.querySelector('.el-table')
          if (table && table.setCurrentRow) {
            table.setCurrentRow(this.tableData[params.dataIndex])
          }
        }
      },

      handlePieLeave() {
        this.currentIndex = -1
        const table = this.$el.querySelector('.el-table')
        if (table && table.setCurrentRow) {
          table.setCurrentRow(null)
        }
      },

      // // 处理饼图点击事件
      // handlePieClick(params) {
      //   if (params.dataIndex !== undefined) {
      //     // 可以在这里添加点击饼图的其他交互
      //     console.log('Clicked pie segment:', params.data)
      //   }
      // },

      handleRowClick(row, column, event) {
        const index = this.tableData.findIndex((item) => item.name === row.name)
        if (index !== -1) {
          this.chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: index,
          })

          setTimeout(() => {
            this.chart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: index,
            })
          }, 1500)
        }
      },

      handleRowHover(row) {
        const index = this.tableData.findIndex((item) => item.name === row.name)
        if (index !== -1) {
          this.chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            dataIndex: index,
          })
        }
      },

      handleRowLeave(row) {
        const index = this.tableData.findIndex((item) => item.name === row.name)
        if (index !== -1) {
          this.chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            dataIndex: index,
          })
        }
      },
      updateTableHeight() {
        this.$nextTick(() => {
          const container = this.$el
          const pieChart = this.$refs.pieChart

          if (this.layout === 'horizontal') {
            this.tableHeight = pieChart ? `${pieChart.clientHeight}px` : '200px'
          } else {
            let pieChartHeight = pieChart ? pieChart.clientHeight : 0

            if (this.activeTableHeight) {
              this.tableHeight = `${Math.max(
                this.activeTableHeight - pieChartHeight - 20,
                150
              )}px`
            } else {
              const containerHeight = container.clientHeight
              console.log('contantHeight:', containerHeight)
              this.tableHeight = `${Math.max(
                containerHeight - pieChartHeight - 20,
                150
              )}px`
            }
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .alert-object-pie-container {
    width: v-bind(width);
    height: v-bind(height);
    display: flex;
    flex-direction: column;

    &.horizontal-layout {
      flex-direction: row;

      .pie-chart {
        width: 50%;
        height: 100%;
        min-height: 180px;
      }

      .data-table-container {
        width: 50%; // 设置为50%确保与饼图等宽
        height: 100%;
        max-height: 700px;
        margin-top: 0;
        margin-left: 10px; // 添加一些间距
        overflow: hidden; // 防止溢出
      }
    }

    .pie-chart {
      width: 100%;
      height: 60%;
      min-height: 180px;
    }

    .data-table-container {
      width: 100%;
      height: 40%;
      margin-top: 10px;
      overflow: auto; // 添加滚动条

      :deep(.el-table) {
        font-size: 12px;
      }

      :deep(.el-table__row) {
        cursor: pointer;
      }
    }
  }
</style>
