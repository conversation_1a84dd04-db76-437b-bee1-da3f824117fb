<template>
  <div class="alert-source-pie-container">
    <div ref="pieChart" class="pie-chart"></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'AlertSourcePie',

    props: {
      data: {
        type: Array,
        required: true,
        default: () => [],
      },
      height: {
        type: String,
        default: '300px',
      },
      width: {
        type: String,
        default: '100%',
      },
    },

    data() {
      return {
        chart: null,
      }
    },

    watch: {
      data: {
        handler() {
          this.$nextTick(() => {
            this.updateChart()
          })
        },
        deep: true,
      },
    },

    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
    },

    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    },

    methods: {
      async initChart() {
        await this.$nextTick()

        if (this.chart) {
          this.chart.dispose()
        }

        const container = this.$refs.pieChart
        if (!container) return

        this.chart = echarts.init(container)
        this.updateChart()
      },

      updateChart() {
        if (!this.chart) return

        const sourceDisplayMap = {
          prometheus: 'Prometheus',
          grafana: 'Grafana',
          custom: '自定义',
          tencent_cloud: '腾讯云',
          pinpoint: 'Pinpoint',
          rocketmq: 'RocketMQ',
        }

        const total = this.data.reduce((sum, item) => sum + item.count, 0)
        const chartData = this.data.map((item) => ({
          name: sourceDisplayMap[item.source] || item.source,
          value: item.count,
        }))

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
          },
          legend: {
            orient: 'horizontal',
            bottom: '5%',
            left: 'center',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              fontSize: 12,
              color: '#606266',
            },
          },
          series: [
            {
              name: '告警来源',
              type: 'pie',
              radius: ['40%', '70%'],
              center: ['50%', '45%'],
              avoidLabelOverlap: true,
              itemStyle: {
                borderRadius: 4,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}\n{d}%',
                fontSize: 12,
                color: '#606266',
              },
              labelLine: {
                show: true,
                length: 15,
                length2: 10,
                smooth: true,
              },
              data: chartData,
            },
          ],
          color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'],
        }

        this.chart.setOption(option)
      },

      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .alert-source-pie-container {
    width: v-bind(width);
    height: v-bind(height);

    .pie-chart {
      width: 100%;
      height: 100%;
      min-height: 300px;
    }
  }
</style>
