<template>
  <div class="namespace-stats-view">
    <div>
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <div class="filter-row">
          <el-form-item label="" class="time-range-item">
            <div class="time-range-container">
              <el-date-picker
                v-model="timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                class="custom-date-picker"
                @change="handleTimeChange"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      @row-click="handleRowClick"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="name" label="命名空间/产品" min-width="200">
        <template #default="scope">
          <div class="name-cell">
            <span>{{ scope.row.name }}</span>
            <el-tag v-if="scope.row.source_count > 1" size="mini">
              {{ scope.row.source_count }}个来源
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="告警级别" min-width="220">
        <template #default="scope">
          <div class="severity-bars">
            <el-progress
              v-if="scope.row.severity_items.critical"
              :percentage="
                (scope.row.severity_items.critical / scope.row.total) * 100
              "
              :show-text="false"
              status="exception"
              :stroke-width="12"
            />
            <el-progress
              v-if="scope.row.severity_items.warning"
              :percentage="
                (scope.row.severity_items.warning / scope.row.total) * 100
              "
              :show-text="false"
              status="warning"
              :stroke-width="12"
            />
            <div class="severity-counts">
              <el-tag
                v-if="scope.row.severity_items.critical"
                type="danger"
                size="small"
              >
                严重: {{ scope.row.severity_items.critical }}
              </el-tag>
              <el-tag
                v-if="scope.row.severity_items.warning"
                type="warning"
                size="small"
              >
                警告: {{ scope.row.severity_items.warning }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="来源分布" min-width="220">
        <template #default="scope">
          <div class="source-distribution-container">
            <div ref="chartContainer" class="chart-container">
              <div
                :id="`chart-${scope.row.nameId}`"
                class="echarts-container"
              ></div>
            </div>
            <div class="source-details">
              <template
                v-if="Object.keys(scope.row.sources_items).length === 1"
              >
                <div
                  v-for="(count, source) in scope.row.sources_items"
                  :key="source"
                  class="source-item"
                >
                  <div class="source-tag">
                    <span
                      class="source-dot"
                      :class="getSourceDotClass(source)"
                    ></span>
                    <span class="source-text">
                      {{ formatSource(source) }} {{ count }}
                    </span>
                  </div>
                </div>
              </template>

              <template v-else>
                <div
                  v-for="(count, source) in scope.row.sources_items"
                  :key="source"
                  class="source-item"
                >
                  <div class="source-tag">
                    <span
                      class="source-dot"
                      :class="getSourceDotClass(source)"
                    ></span>
                    <span class="source-text">
                      {{ formatSource(source) }} {{ count }}
                    </span>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="total" label="总数" min-width="100" sortable>
        <template #default="scope">
          <span class="total-count">{{ scope.row.total }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { getNamespaceStats } from '@/api/monitoring'
  import dayjs from 'dayjs'
  import * as echarts from 'echarts/core'
  import { GaugeChart } from 'echarts/charts'
  import { CanvasRenderer } from 'echarts/renderers'
  import { TitleComponent, TooltipComponent } from 'echarts/components'

  echarts.use([GaugeChart, CanvasRenderer, TitleComponent, TooltipComponent])

  export default {
    name: 'NamespaceStats',
    data() {
      return {
        loading: false,
        timeRange: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().format('YYYY-MM-DD HH:mm:ss'),
        ],
        queryParams: {},
        tableData: [],
        chartInstances: [],
        totalStats: {
          critical: 0,
          warning: 0,
          total: 0,
        },
        sortTimer: null,
      }
    },
    mounted() {
      this.handleQuery()
    },
    updated() {
      this.$nextTick(() => {
        this.initCharts()
      })
    },
    beforeDestroy() {
      if (this.chartInstances && this.chartInstances.length) {
        this.chartInstances.forEach((chart) => {
          chart.dispose()
        })
      }

      if (this.sortTimer) {
        clearTimeout(this.sortTimer)
      }
    },
    methods: {
      handleSortChange() {
        if (this.sortTimer) {
          clearTimeout(this.sortTimer)
        }
        this.sortTimer = setTimeout(() => {
          this.initCharts()
        }, 300)
      },
      getSourceDotClass(source) {
        if (source.includes('PunkSong')) {
          return 'dot-success'
        }
        if (source.includes('微购科技2')) {
          return 'dot-info'
        }
        if (source.includes('custom')) {
          return 'dot-success'
        }
        if (source.includes('rocketmq')) {
          return 'dot-warning'
        }
        if (source.includes('pinpoint')) {
          return 'dot-primary'
        }

        return 'dot-info'
      },
      formatSource(source) {
        if (source.includes('|')) {
          return source.split('|')[0]
        }

        return source
      },
      handleTimeChange(val) {
        this.timeRange = val
        this.handleQuery()
      },
      initCharts() {
        this.chartInstances.forEach((chart) => {
          chart.dispose()
        })
        this.chartInstances = []

        this.$nextTick(() => {
          if (!this.tableData || this.tableData.length === 0) {
            return
          }

          this.tableData.forEach((row) => {
            const chartId = `chart-${row.nameId}`
            const chartDom = document.getElementById(chartId)
            if (!chartDom) {
              console.warn(`Chart DOM not found for: ${chartId}`)
              return
            }

            try {
              const existingInstance = echarts.getInstanceByDom(chartDom)
              if (existingInstance) {
                existingInstance.dispose()
              }

              const chart = echarts.init(chartDom)
              this.chartInstances.push(chart)

              const total = row.total || 1

              const sourceColors = []
              const sourceData = []

              const sources = Object.entries(row.sources_items)
              const totalAngle = 180

              if (sources.length > 0) {
                let currentStartAngle = 180

                sources.forEach(([source, count], index) => {
                  const percentage = count / total
                  const angleRange = totalAngle * percentage
                  const sourceEndAngle = currentStartAngle - angleRange

                  let color = '#F34D37'
                  if (source.includes('PunkSong')) {
                    color = '#67C23A'
                  } else if (source.includes('custom')) {
                    color = '#67C23A'
                  } else if (source.includes('pinpoint')) {
                    color = '#409EFF'
                  } else if (source.includes('rocketmq')) {
                    color = '#E6A23C'
                  } else if (source.includes('微购科技2')) {
                    color = '#909399'
                  }

                  sourceColors.push({
                    source,
                    color,
                    startAngle: currentStartAngle,
                    endAngle: sourceEndAngle,
                    count,
                    percentage,
                  })

                  currentStartAngle = sourceEndAngle
                })
              }

              const series = sourceColors.map((item) => {
                return {
                  type: 'gauge',
                  startAngle: item.startAngle,
                  endAngle: item.endAngle,
                  center: ['50%', '75%'],
                  radius: '90%',
                  min: 0,
                  max: 100,
                  splitNumber: 1,
                  axisLine: {
                    lineStyle: {
                      width: 6,
                      color: [[1, item.color]],
                    },
                  },
                  pointer: {
                    show: false,
                  },
                  axisTick: {
                    show: false,
                  },
                  splitLine: {
                    show: false,
                  },
                  axisLabel: {
                    show: false,
                  },
                  detail: {
                    show: false,
                  },
                  data: [
                    {
                      value: 100,
                      name: '',
                    },
                  ],
                }
              })

              series.push({
                type: 'gauge',
                startAngle: 180,
                endAngle: 0,
                center: ['50%', '75%'],
                radius: '0%',
                min: 0,
                max: 100,
                splitNumber: 1,
                axisLine: {
                  show: false,
                },
                pointer: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
                detail: {
                  valueAnimation: true,
                  width: '60%',
                  lineHeight: 40,
                  borderRadius: 8,
                  offsetCenter: [0, '-15%'],
                  fontSize: 20,
                  fontWeight: 'bold',
                  formatter: '{value}',
                  color: '#3F9EFF',
                },
                title: {
                  show: true,
                  fontSize: 12,
                  offsetCenter: [0, '30%'],
                },
                data: [
                  {
                    value: total,
                  },
                ],
              })

              const option = {
                series: series,
              }

              chart.setOption(option)
            } catch (error) {
              console.error('初始化图表失败:', error, chartId)
            }
          })
        })
      },

      async handleQuery() {
        this.loading = true
        try {
          if (!this.timeRange || !this.timeRange[0] || !this.timeRange[1]) {
            this.$message.warning('请选择有效的时间范围')
            this.loading = false
            return
          }

          const params = {
            start_time: this.timeRange[0],
            end_time: this.timeRange[1],
          }

          const res = await getNamespaceStats(params)

          if (res.status === 'success') {
            this.tableData = res.data.stats || []

            this.calculateTotalStats()

            setTimeout(() => {
              this.initCharts()
            }, 200)
          } else {
            this.$message.error(res.message || '获取数据失败')
          }
        } catch (error) {
          console.error('获取命名空间统计失败:', error)
          this.$message.error(
            '获取命名空间统计失败: ' + (error.message || '未知错误')
          )
        } finally {
          this.loading = false
        }
      },

      calculateTotalStats() {
        this.totalStats = {
          critical: 0,
          warning: 0,
          total: 0,
        }

        this.tableData.forEach((item) => {
          if (item.severity_items) {
            this.totalStats.critical += item.severity_items.critical || 0
            this.totalStats.warning += item.severity_items.warning || 0
          }
          this.totalStats.total += item.total || 0
        })
      },

      handleRowClick(row) {
        this.$emit('namespace-click', row)
      },

      showNamespaceDetail(namespace) {},
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table {
    .el-table__cell {
      padding: 0px 0px;
    }
  }
  .namespace-stats-view {
    padding: 8px 5px;
  }

  .filter-form .filter-row {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .time-range-item .el-date-editor {
    width: 380px !important;
    margin-left: 10px;
  }

  .stats-summary {
    margin-bottom: 20px;

    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: white;
        }

        &.critical {
          background-color: #f56c6c;
        }

        &.warning {
          background-color: #e6a23c;
        }

        &.total {
          background-color: #409eff;
        }
      }

      .stat-info {
        .stat-title {
          font-size: 14px;
          color: #909399;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }

  .name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .severity-bars {
    .el-progress {
      margin-bottom: 4px;
    }

    .severity-counts {
      display: flex;
      gap: 8px;
      margin-top: 4px;
    }
  }

  .source-distribution-container {
    display: flex;
    align-items: center;
  }

  .chart-container {
    width: 100px;
    height: 80px;
    margin-right: 10px;
  }

  .echarts-container {
    width: 100%;
    height: 100%;
  }

  .source-details {
    display: flex;
    flex-direction: column;
    gap: -4px; /* 从4px减小到2px */

    .source-item {
      margin-bottom: 1px; /* 从2px减小到1px */

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .circle-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 12px;

    &.small {
      margin-right: 8px;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .circle-chart {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f0f0f0;
    border: 4px solid var(--el-color-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px;

    &.small {
      width: 50px;
      height: 50px;
      border-width: 3px;
    }
  }

  .circle-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .circle-number {
    font-size: 20px;
    font-weight: bold;
    color: var(--el-color-primary);

    .small & {
      font-size: 14px;
    }
  }

  .circle-label {
    font-size: 12px;
    color: #909399;

    .small & {
      display: none;
    }
  }

  .source-name {
    margin-top: 4px;
    text-align: center;
  }

  .multi-source-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .source-counts {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .source-item {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .source-details {
    .source-item {
      .source-item {
        display: flex;
        justify-content: space-between;
        padding: 4px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .total-count {
    font-weight: bold;
    color: var(--el-color-primary);
  }

  .source-tag {
    display: flex;
    align-items: center;
    padding: 0px 0; /* 从4px减小到2px */
  }

  .source-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .dot-success {
    background-color: #67c23a;
  }

  .dot-info {
    background-color: #909399;
  }

  .dot-warning {
    background-color: #e6a23c;
  }

  .dot-primary {
    background-color: #409eff;
  }

  .source-text {
    font-size: 12px;
    color: #606266;
  }
</style>
