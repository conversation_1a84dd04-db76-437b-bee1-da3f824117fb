<template>
  <div class="statistics-cards">
    <div class="stats-container">
      <div
        v-for="(item, index) in statisticsItems"
        :key="index"
        class="stat-card-wrapper"
      >
        <el-card
          :body-style="{ padding: '12px' }"
          class="stat-card"
          shadow="never"
        >
          <div class="stat-content">
            <div class="stat-icon" :class="item.iconClass">
              <i :class="item.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">
                {{ item.title }}
                <el-tag
                  v-if="item.showTag"
                  class="stat-hint"
                  effect="dark"
                  type="warning"
                  size="small"
                >
                  {{ item.tagText }}
                </el-tag>
              </div>
              <div class="stat-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
  import { getEventStatistics } from '@/api/monitoring'
  import dayjs from 'dayjs'
  import utc from 'dayjs/plugin/utc'
  import timezone from 'dayjs/plugin/timezone'

  dayjs.extend(utc)
  dayjs.extend(timezone)
  dayjs.tz.setDefault('Asia/Shanghai')

  export default {
    name: 'StatisticsCards',
    props: {
      data: {
        type: Object,
        required: true,
        default: () => ({
          statistics: {
            status_stats: {},
            severity_stats: {},
            source_stats: {},
          },
          total_all: 0,
        }),
      },
      timeRange: {
        type: Array,
        default: () => null,
      },
      timeRangeType: {
        type: String,
        default: 'default',
      },
    },
    data() {
      return {
        eventStatistics: 0,
        eventLoading: false,
      }
    },
    computed: {
      statisticsItems() {
        const { statistics, total_all } = this.data
        const severityStats = statistics?.severity_stats || {}
        const statusStats = statistics?.status_stats || {}

        const total = total_all || 0
        const critical = severityStats.critical || 0
        const warning = severityStats.warning || 0
        const resolved = statusStats.OK || 0

        // 判断是否显示"当天"标签
        // const isDefaultTimeRange = !this.timeRange || this.timeRangeType === 'default'

        return [
          {
            title: '总告警数',
            value: total,
            icon: 'el-icon-warning',
            iconClass: 'total',
            span: 4,
          },
          {
            title: '严重',
            value: critical,
            icon: 'el-icon-error',
            iconClass: 'critical',
            span: 4,
          },
          {
            title: '警告',
            value: warning,
            icon: 'el-icon-warning',
            iconClass: 'warning',
            span: 4,
          },
          {
            title: '已恢复',
            value: resolved,
            icon: 'el-icon-success',
            iconClass: 'resolved',
            span: 4,
          },
          {
            title: '事件告警',
            value: this.eventStatistics,
            icon: 'el-icon-document',
            iconClass: 'events',
            span: 4,
            // showTag: isDefaultTimeRange,
            tagText: '当天',
          },
        ]
      },
    },
    watch: {
      timeRange: {
        handler() {
          this.fetchEventStatistics()
        },
        deep: true,
      },
    },
    mounted() {
      this.fetchEventStatistics()
    },
    methods: {
      async fetchEventStatistics() {
        try {
          this.eventLoading = true

          let params = null
          // 如果有时间范围参数，则传递给接口
          if (this.timeRange && this.timeRange.length === 2) {
            params = {
              start_time: this.timeRange[0],
              end_time: this.timeRange[1],
            }
            console.log('StatisticsCards: 使用自定义时间参数', params)
          } else {
            console.log('StatisticsCards: 获取当天数据')
          }

          const response = await getEventStatistics(params)

          if (response.status === 'success' && response.data) {
            this.eventStatistics = response.data.total_events || 0
            console.log(
              'StatisticsCards: 事件统计数据更新',
              this.eventStatistics
            )
          } else {
            console.warn(
              'StatisticsCards: 获取事件统计数据失败',
              response.message
            )
            this.eventStatistics = 0
          }
        } catch (error) {
          console.error('StatisticsCards: 获取事件统计失败', error)
          this.eventStatistics = 0
        } finally {
          this.eventLoading = false
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .statistics-cards {
    margin-bottom: 20px;

    .stats-container {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;

      @media (max-width: 1200px) {
        flex-wrap: wrap;
        gap: 16px;
      }

      @media (max-width: 992px) {
        gap: 12px;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;
      }
    }

    .stat-card-wrapper {
      flex: 1;
      min-width: 0;

      @media (max-width: 1200px) and (min-width: 993px) {
        flex: 0 0 calc(33.333% - 14px);
      }

      @media (max-width: 992px) and (min-width: 769px) {
        flex: 0 0 calc(50% - 6px);
      }

      @media (max-width: 768px) {
        flex: none;
      }
    }

    .stat-card {
      // height: 100%;
      border: 1px solid #e8eaec;
      border-radius: 8px;
      background: #ffffff;
      transition: all 0.3s ease;
      box-shadow: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border-color: #d9d9d9;
      }

      ::v-deep .el-card__body {
        padding: 12px !important;
        height: 100%;
        display: flex;
        align-items: center;
      }
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .stat-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 20px;
        color: white;
      }

      &.total {
        background-color: #409eff;
      }

      &.critical {
        background-color: #f56c6c;
      }

      &.warning {
        background-color: #e6a23c;
      }

      &.resolved {
        background-color: #67c23a;
      }

      &.events {
        background-color: #909399;
      }
    }

    .stat-info {
      flex: 1;
      min-width: 0;
    }

    .stat-title {
      font-size: 13px;
      color: #8c8c8c;
      margin-bottom: 4px;
      line-height: 1.2;
      font-weight: 400;
      min-height: 20px;
      display: flex;
      align-items: center;
    }

    .stat-hint {
      font-size: 11px;
      font-weight: 300;
      margin-left: 4px;
      vertical-align: middle;
      height: 16px;
      line-height: 14px;
    }

    .stat-value {
      font-size: 22px;
      font-weight: 600;
      color: #262626;
      line-height: 1.2;
    }

    // 响应式优化
    @media (max-width: 992px) {
      .stat-content {
        gap: 10px;
      }

      .stat-icon {
        width: 36px;
        height: 36px;

        i {
          font-size: 18px;
        }
      }

      .stat-value {
        font-size: 20px;
      }
    }

    @media (max-width: 768px) {
      .stat-content {
        gap: 8px;
      }

      .stat-icon {
        width: 32px;
        height: 32px;

        i {
          font-size: 16px;
        }
      }

      .stat-title {
        font-size: 12px;
      }

      .stat-value {
        font-size: 18px;
      }
    }
  }
</style>
