<template>
  <el-drawer
    :visible.sync="localVisible"
    custom-class="sql-drawer"
    direction="rtl"
    size="50%"
    @close="handleClose"
  >
    <template #title>
      <div class="drawer-header">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="分析" name="analysis"></el-tab-pane>
          <el-tab-pane label="统计" name="statistics"></el-tab-pane>
          <el-tab-pane label="明细" name="details"></el-tab-pane>
        </el-tabs>
      </div>
    </template>

    <div class="divider-container">
      <el-divider></el-divider>
    </div>

    <div class="drawer-body">
      <div v-if="activeTab === 'analysis'" class="content">
        <h3>执行样例</h3>

        <div class="button-group">
          <el-button
            v-for="button in buttons"
            :key="button.value"
            :class="{ active: selectedButton === button.value }"
            plain
            @click="selectButton(button.value)"
          >
            {{ button.label }}
          </el-button>
        </div>

        <div
          v-if="selectedButton === 'sqlTemplate'"
          :key="`${selectedButton}-${formattedSql}`"
        >
          <pre
            class="language-sql"
          ><code ref="sqlCode">{{ formatHighlightSql(sqlTemplate) }}</code></pre>
          <div style="margin-top: 20px">
            <el-divider></el-divider>
          </div>
        </div>

        <div
          v-if="selectedButton === 'sqlDetails' && adviceData"
          :key="`${selectedButton}`"
        >
          <pre
            class="language-sql"
          ><code ref="sqlCode">{{ formatHighlightSql(sqlTemplateAllData[0].SqlText) }}</code></pre>
          <div style="margin-top: 20px">
            <el-divider></el-divider>
          </div>
        </div>

        <div class="optimization-suggestions">
          <h3>优化建议</h3>
          <div v-if="hasAdvice">
            <div
              v-for="(advice, index) in adviceData"
              :key="index"
              class="advice-item"
            >
              <div
                v-for="(key, idx) in advice.Keys"
                v-if="key.SqlText && key.SqlText !== '[]'"
                :key="idx"
                class="advice-div"
              >
                <el-card>
                  <div>
                    <label>库：{{ advice.TableSchema }}</label>
                  </div>
                  <div>
                    <label>表：{{ advice.TableName }}</label>
                  </div>
                  <pre
                    class="language-sql"
                  ><code>{{ formatHighlightSql(key.SqlText) }}</code></pre>
                </el-card>
              </div>
            </div>
          </div>

          <div v-else class="no-data-container">
            <p class="no-data-text">暂无数据</p>
          </div>
        </div>
      </div>
    </div>

    <div class="analysis-container">
      <div v-if="activeTab === 'statistics'" class="content">
        <div v-for="item in StatisticsItems" :key="item.prop">
          <div
            v-loading="ChartLoading"
            :element-loading-text="elementLoadingText"
            class="analysis-item"
          >
            <h3>{{ item.title }}</h3>
            <el-empty
              v-if="!item.ChartOption"
              description="还未开始，请等待......"
            ></el-empty>
            <e-chart
              v-else
              :key="item.prop"
              :option="item.ChartOption"
              style="height: 300px"
            />
            <el-divider></el-divider>
          </div>
        </div>
      </div>
    </div>
    <div v-if="activeTab === 'details'" class="content">
      <div class="custom-table">
        <el-table
          v-loading="isLoading"
          :border="false"
          :data="sqlTemplateAllData"
          :default-sort="{ prop: 'Timestamp', order: 'descending' }"
          :stripe="true"
          element-loading-text="加载中...."
          height="570"
        >
          <el-table-column
            v-for="item in tableItems"
            :key="item.prop"
            :fixed="item.prop === 'Timestamp' ? true : false"
            :label="item.label"
            :min-width="
              item.prop === 'SqlText'
                ? '100px'
                : item.prop === 'Timestamp'
                ? '130px'
                : item.prop === 'UserHost'
                ? '100px'
                : item.prop === 'QueryTime'
                ? '110px'
                : ''
            "
            :prop="item.prop"
            :show-overflow-tooltip="item.prop !== 'SqlText'"
            :sortable="item.prop === 'QueryTime' ? true : false"
          >
            <template v-if="item.prop === 'SqlText'" #default="scope">
              <el-button
                :key="scope.row[item.prop]"
                style="color: black"
                type="text"
                @click="showSqlText(scope.row[item.prop])"
              >
                {{ scope.row[item.prop] }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageIndex"
          :layout="layout"
          :page-size="pageSize"
          :total="totalItems"
          background
          small
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      title="SQL 语句详情"
      width="60%"
      @close="dialogHandleClose"
    >
      <pre
        class="language-sql"
      ><code :key="dialogSqlText">{{ formatHighlightSql(dialogSqlText) }}</code></pre>
    </el-dialog>
    <el-divider></el-divider>
  </el-drawer>
</template>

<script>
  import { format as sqlFormatter } from 'sql-formatter'
  import Prism from 'prismjs'
  import 'prismjs/themes/prism.css'
  import 'prismjs/components/prism-sql'
  import { postMysqlSlowAdvice, postStatisticsData } from '@/api/monitoring'
  import EChart from '@/views/security/component/echartsModel.vue'

  export default {
    name: 'SqlDrawer',
    components: { EChart },
    props: {
      instanceId: String,
      sqlTemplate: String,
      startTime: String,
      endTime: String,
      visible: {
        type: Boolean,
        default: false,
      },
    },

    data() {
      return {
        elementLoadingText: '正在加载中.....',
        ChartLoading: false,
        dialogVisible: false,
        dialogSqlText: '',
        isLoading: true,
        localVisible: false,
        activeTab: 'analysis',
        selectedButton: 'sqlTemplate',
        formattedSql: '',
        adviceData: [],
        sqlTemplateAllData: [],
        hasAdvice: false,

        buttons: [
          { label: 'SQL 模板', value: 'sqlTemplate' },
          { label: 'SQL 样例', value: 'sqlDetails' },
        ],

        tableItems: [
          { label: '开始时间', prop: 'Timestamp' },
          { label: 'SQL语句', prop: 'SqlText' },
          { label: '数据库', prop: 'Database' },
          { label: 'User来源', prop: 'UserName' },
          { label: 'IP来源', prop: 'UserHost' },
          { label: '执行时间(s)', prop: 'QueryTime' },
          { label: '🔐时间(s)', prop: 'LockTime' },
          { label: '扫描行数', prop: 'RowsExamined' },
          { label: '扫描行数', prop: 'RowsSent' },
        ],

        StatisticsItems: [
          { title: '耗时分布', prop: 'QueryTime', ChartOption: {} },
          { title: '来源 IP 分布', prop: 'UserHost', ChartOption: {} },
          { title: '来源 User 分布', prop: 'UserName', ChartOption: {} },
        ],

        StatisticsData: {},
        QueryTimeOption: {},
        UserHostOption: {},
        UserNameOption: {},

        pageIndex: 1,
        pageSize: 10,
        totalItems: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
      }
    },

    watch: {
      sqlTemplate(newVal) {
        this.startLoading(newVal)
      },
      localVisible(newVal) {
        if (newVal) {
          this.startLoading(this.sqlTemplate)
        }
      },
    },

    mounted() {
      this.getSlowAdvice()
    },

    methods: {
      startLoading(sql) {
        this.localVisible = true
        this.isLoading = true
        this.formattedSql = this.formatHighlightSql(sql)
        this.getSlowAdvice().then(() => {
          this.isLoading = false
        })
      },

      formatHighlightSql(sql) {
        if (sql) {
          const formatted = sqlFormatter(sql)
          this.$nextTick(() => {
            Prism.highlightAll()
          })
          return formatted
        }
        return ''
      },

      handleClose() {
        this.localVisible = false
        this.resetData()
        this.$emit('update:visible', false)
      },

      selectButton(button) {
        this.selectedButton = button
        if (button === 'sqlTemplate') {
          this.getSlowAdvice()
        }
      },

      handleTabClick(tab) {
        if (tab.name === 'statistics') {
          this.activeTab = 'statistics'
          this.getStatisticsData()
        }
      },

      async getSlowAdvice() {
        if (this.sqlTemplate) {
          let data = {
            SqlTemplate: this.sqlTemplate,
            instanceId: this.instanceId,
            pageSize: this.pageSize,
            pageIndex: this.pageIndex,
          }

          let res = await postMysqlSlowAdvice(data)

          this.sqlTemplateAllData = res.data
          this.totalItems = res.totalCount

          this.hasAdvice = false
          this.adviceData = []

          if (res.data && res.data.length > 0) {
            const uniqueAdvices = new Set()
            for (let item of res.data) {
              const advice = item.Advices
              if (advice && advice !== '[]' && advice !== '') {
                const parsedAdvices = JSON.parse(advice)
                parsedAdvices.forEach((adviceItem) => {
                  const adviceString = JSON.stringify(adviceItem)
                  if (!uniqueAdvices.has(adviceString)) {
                    uniqueAdvices.add(adviceString)
                    this.adviceData.push(adviceItem)
                  }
                })
                this.hasAdvice = true
              }
            }
          }
        } else {
          this.hasAdvice = false
        }
      },

      showSqlText(sql) {
        this.dialogSqlText = sql
        this.dialogVisible = true
      },

      dialogHandleClose() {
        this.dialogSqlText = ''
        this.dialogVisible = false
      },

      resetData() {
        this.activeTab = 'analysis'
        this.selectedButton = 'sqlTemplate'
        this.formattedSql = ''
        this.adviceData = []
        this.hasAdvice = false
        this.dialogVisible = false
        this.dialogSqlText = ''
      },

      handleSizeChange(size) {
        this.pageSize = size
        this.pageIndex = 1
        this.getSlowAdvice()
      },

      handleCurrentChange(page) {
        this.pageIndex = page
        this.getSlowAdvice()
      },

      async getStatisticsData() {
        this.ChartLoading = true

        const data = {
          instanceId: this.instanceId,
          SqlTemplate: this.sqlTemplate,
          startTime: this.startTime,
          endTime: this.endTime,
        }
        try {
          const res = await postStatisticsData(data)

          this.StatisticsData = res
          this.updateStatisticsCharts()
        } catch (error) {
          console.error('getStatisticsData 获取数据失败', error)
        } finally {
          this.ChartLoading = false
        }
      },

      updateStatisticsCharts() {
        this.drawQueryTimeChart()
        this.drawUserHostChart()
        this.drawUserNameChart()
        this.StatisticsItems = [
          {
            title: '耗时分布',
            prop: 'QueryTime',
            ChartOption: this.QueryTimeOption,
          },
          {
            title: '来源 IP 分布',
            prop: 'UserHost',
            ChartOption: this.UserHostOption,
          },
          {
            title: '来源 User 分布',
            prop: 'UserName',
            ChartOption: this.UserNameOption,
          },
        ]
      },

      drawQueryTimeChart() {
        const data = this.StatisticsData.analysis_QueryTime
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: 'SQL个数占比: {c}%',
          },
          yAxis: {
            type: 'category',
            data: data.map((item) => item.Key),
          },
          xAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} %',
            },
          },
          series: [
            {
              data: data.map((item) => ({
                value: parseFloat(item.Percent),
                itemStyle: {
                  color:
                    item.Key === '>5s'
                      ? '#ff4d4f'
                      : item.Key === '1-5s'
                      ? '#faad14'
                      : '#1890ff',
                },
              })),
              type: 'bar',
              barWidth: '40%',
              orientation: 'horizontal',
            },
          ],
        }
        this.QueryTimeOption = option
        this.$nextTick(() => {
          this.QueryTimeOption = option
        })
      },
      drawUserHostChart() {
        const data = this.StatisticsData.analysis_UserHost
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
          },
          legend: {
            orient: 'vertical',
            right: 10,
            top: 10,
            data: data.map((item) => item.Key),
          },
          series: [
            {
              type: 'pie',
              radius: ['50%', '80%'],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
              data: data.map((item) => ({
                name: item.Key,
                value: item.Value,
              })),
            },
          ],
        }
        this.UserHostOption = option
        this.$nextTick(() => {
          this.UserHostOption = option
        })
      },
      drawUserNameChart() {
        const data = this.StatisticsData.analysis_UserName
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
          },
          series: [
            {
              type: 'pie',
              radius: ['50%', '80%'],
              data: data.map((item) => ({
                name: item.Key,
                value: item.Value,
              })),
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            },
          ],
        }
        this.UserNameOption = option
        this.$nextTick(() => {
          this.UserNameOption = option
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-drawer__header {
    margin-bottom: 10px;
    align-items: center;
    color: #72767b;
    display: flex;
    padding: 20px 20px 0;
  }

  .sql-drawer .el-drawer__body {
    padding: 0;
  }

  .drawer-header {
    padding: 0 20px 0 10px;
    margin-bottom: 0px;
  }

  .divider-container {
    margin-top: -10px;
  }

  .el-button {
    margin: 0px;
  }

  .drawer-body {
    padding: 0 20px 0 20px;
  }

  .content {
    padding: 0px;
  }

  .button-group {
    display: flex;
    margin-bottom: 20px;
  }

  .el-button.active {
    border-color: #409eff;
    color: #409eff;
  }

  .advice-header {
    display: flex;
    justify-content: space-between;
  }

  .optimization-suggestions {
    margin-bottom: 20px;
  }

  .analysis-container {
    padding: 0 20px 0 20px;
    margin-bottom: 20px;
  }

  .advice-div {
    margin-bottom: 20px;

    div {
      margin-bottom: 10px;

      label {
        font-weight: bold;
      }
    }
  }

  .custom-table {
    margin-left: 20px;
  }

  .sql-drawer-content {
    padding: 0px;
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 100vh;
    overflow-y: auto;
    margin-left: 20px;
  }
</style>
