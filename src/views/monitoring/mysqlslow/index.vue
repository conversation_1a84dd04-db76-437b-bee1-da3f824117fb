<template>
  <div class="security-dashboard">
    <el-row :gutter="24" class="mb15">
      <el-col class="form-el-col" style="margin-bottom: 0px; margin-top: 10px">
        <div class="form-container">
          <h2>{{ title }}</h2>
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
              <el-select
                v-model="selectedSqlType"
                clearable
                placeholder="选择 SqlType"
                @change="filterInstanceIds"
              >
                <el-option
                  v-for="type in sqlTypes"
                  :key="type"
                  :label="type"
                  :value="type"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="实例ID">
              <el-select
                v-model="selectedInstanceId"
                clearable
                filterable
                placeholder="选择实例 ID"
              >
                <el-option
                  v-for="item in filteredInstanceIds"
                  :key="item.key"
                  :label="`${item.key}  (${item.label})`"
                  :value="item.key"
                >
                  <div class="item-container">
                    <span class="item-key">{{ item.key }}</span>
                    <span class="item-label">({{ item.label }})</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="实例名称">
              <el-input
                v-model="instanceName"
                disabled
                placeholder="实例名称"
              ></el-input>
            </el-form-item>
            <el-form-item label="接入点地址">
              <el-input
                v-model="endpoint"
                disabled
                placeholder="接入点地址"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="24" class="mb15">
      <el-col class="base-el-col" style="margin-bottom: 20px; margin-top: 20px">
        <div class="header">
          <div class="timeSelect">
            <el-form ref="queryForm" :model="queryForm" @submit="changeData">
              <div style="display: flex; align-items: center">
                <el-radio-group
                  v-model="queryForm.timevalue"
                  size="mini"
                  style="float: left; margin-right: -1px"
                  @change="handleTimeLabelChange(true)"
                >
                  <el-radio-button
                    v-for="option in timeOptions"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio-button>
                </el-radio-group>

                <el-date-picker
                  v-model="DateTimePicker"
                  end-placeholder="结束日期"
                  range-separator="至"
                  size="mini"
                  start-placeholder="开始日期"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="changeFormData"
                ></el-date-picker>
                <i
                  class="el-icon-refresh"
                  style="margin-left: 10px; cursor: pointer"
                  @click="fetchData()"
                />
              </div>
            </el-form>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="24" class="mb15">
      <el-col
        v-for="item in StatisticsItems"
        :key="item.prop"
        :span="12"
        class="chart-col"
      >
        <el-card>
          <div slot="header" class="clearfix">
            <div style="display: flex">
              <span style="flex: 1; font-weight: bold">{{ item.title }}</span>
              <span
                v-if="item.prop === 'allData'"
                style="font-size: 12px; color: #888"
              >
                时间：{{ queryForm.start_time }} - {{ queryForm.end_time }}
              </span>
            </div>
          </div>
          <div
            v-loading="ChartLoading"
            :element-loading-text="elementLoadingText"
            class="analysis-item"
            style="height: 80%"
          >
            <el-empty
              v-if="StatisticsData.analysis_total === 0"
              description="还未开始，请等待......"
            ></el-empty>
            <e-chart
              v-else
              :key="item.prop"
              :option="item.ChartOption"
              style="height: 300px"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="24" class="mb15">
      <el-col class="content-col">
        <el-table
          v-loading="tableLoading"
          :border="false"
          :data="slowLogAdviceData.data"
          :stripe="true"
          class="custom-table"
          element-loading-text="加载中...."
        >
          <el-table-column
            v-for="item in tableItems"
            :key="item.prop"
            :align="item.prop === 'SqlTemplate' ? 'left' : 'center'"
            :label="item.label"
            :min-width="
              item.prop === 'SqlTemplate'
                ? '200px'
                : item.prop === 'Database'
                ? '130px'
                : ''
            "
            :prop="item.prop"
            :show-overflow-tooltip="item.prop !== 'SqlTemplate'"
          >
            <template #default="scope">
              <el-button
                v-if="item.prop === 'SqlTemplate'"
                style="color: black"
                type="text"
                @click="showFullSql(scope.row[item.prop])"
              >
                {{ scope.row[item.prop] }}
              </el-button>
              <span
                v-else-if="
                  item.prop === 'SumQueryTime' || item.prop === 'Count'
                "
                style="font-weight: bold; text-align: center; display: block"
              >
                {{ scope.row[item.prop] }}
              </span>
              <span v-else style="text-align: center; display: block">
                {{ scope.row[item.prop] }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <sql-drawer
          ref="sqlDrawer"
          :end-time="queryForm.end_time"
          :instance-id="selectedInstanceId"
          :sql-template="fullSqlTemplate"
          :start-time="queryForm.start_time"
          :visible.sync="drawerVisible"
          @close="handleDrawerClose"
        ></sql-drawer>
        <el-pagination
          :current-page="queryForm.pageIndex"
          :layout="layout"
          :page-size="queryForm.pageSize"
          :total="slowLogAdviceData.totalCount"
          background
          small
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import EChart from '@/views/security/component/echartsModel.vue'
  import moment from 'moment/moment'

  import {
    getMysqlInstance,
    getMysqlSlowGroupedLogs,
    postStatisticsData,
  } from '@/api/monitoring'
  import { reject } from 'lodash'
  import sqlDrawer from './components/sqlDrawer.vue'

  export default {
    name: 'MysqlSlow',
    components: { EChart, sqlDrawer },
    data() {
      return {
        tableLoading: false,
        title: 'SQL慢日志分析',
        queryForm: {
          timevalue: '15',
          keyword: '',
          start_time: '',
          end_time: '',
          pageIndex: 1,
          pageSize: 10,
        },
        DateTimePicker: [],
        timeOptions: [
          { label: '10min', value: '10' },
          { label: '15min', value: '15' },
          { label: '30min', value: '30' },
          { label: 'day', value: 'day' },
        ],
        tableItems: [
          { label: 'SQL模板', prop: 'SqlTemplate' },
          { label: '数据库', prop: 'Database' },
          { label: '次数', prop: 'Count' },
          { label: '总耗时', prop: 'SumQueryTime' },
          { label: '最大耗时', prop: 'MaxQueryTime' },
          { label: '平均耗时', prop: 'AvgQueryTime' },
          { label: '最大🔐等待(s)', prop: 'MaxLockTime' },
          { label: '平均🔐等待(s)', prop: 'AvgLockTime' },
          { label: '最大扫描行数', prop: 'MaxRowsExamined' },
          { label: '平均扫描行数', prop: 'AvgRowsExamined' },
          { label: '最大返回行数', prop: 'MaxRowsSent' },
          { label: '平均返回行数', prop: 'AvgRowsSent' },
        ],

        allInstances: [],
        sqlTypes: [],
        initialFilteredInstanceIds: [],
        filteredInstanceIds: [],
        selectedSqlType: '',
        selectedInstanceId: '',
        instanceName: '',
        endpoint: '',

        layout: 'total, sizes, prev, pager, next, jumper',

        eventNotifyGroup: [],
        slowLogAdviceData: {
          data: [],
          totalCount: null,
        },

        selectedAlbumId: null,
        decryptDialogVisible: false,
        decryptionDialogTitle: '解密结果',
        decryptedData: [],

        StatisticsData: {},
        StatisticsItems: [
          { title: 'SQL 统计', prop: 'allData', ChartOption: {} },
          { title: '耗时分布', prop: 'QueryTime', ChartOption: {} },
        ],
        AllDataOption: {},
        QueryTimeOption: {},
        ChartLoading: false,
        elementLoadingText: '正在加载中.....',

        drawerVisible: false,
        fullSqlTemplate: '',
        formattedSql: '',
      }
    },
    computed: {
      hourlyData() {
        const hourlyCounts = {}
        this.StatisticsData.all_data.forEach((item) => {
          const hour = item.Timestamp.slice(11, 13)
          if (hourlyCounts[hour]) {
            hourlyCounts[hour].count += item.Count
            hourlyCounts[hour].end = item.Timestamp
          } else {
            hourlyCounts[hour] = {
              count: item.Count,
              start: item.Timestamp,
              end: item.Timestamp,
            }
          }
        })

        const sortedData = Object.keys(hourlyCounts)
          .sort()
          .map((hour) => ({
            hour,
            count: hourlyCounts[hour].count,
            start: hourlyCounts[hour].start,
            end: hourlyCounts[hour].end,
          }))

        return sortedData
      },
    },

    watch: {
      selectedInstanceId(newId) {
        if (newId) {
          this.updateInstanceDetails()
          this.fetchData()
        }
      },
    },
    async mounted() {
      try {
        this.tableLoading = true
        this.handleTimeLabelChange(false)
        await Promise.all([this.fetchInstances(), this.fetchData()])
      } catch (error) {
        console.error('Error during initialization:', error)
      } finally {
        this.tableLoading = false
      }
    },

    methods: {
      showFullSql(sql) {
        this.fullSqlTemplate = sql
        if (this.$refs.sqlDrawer) {
          this.$refs.sqlDrawer.startLoading(sql)
        }
        this.drawerVisible = true
      },
      handleDrawerClose() {
        this.drawerVisible = false
        this.fullSqlTemplate = ''
        if (this.$refs.sqlDrawer) {
          this.$refs.sqlDrawer.resetData()
        }
      },

      formatDetails(text) {
        if (!text) return ''
        let formattedText = text.replace(/(.*?)(=|：)(.*?)\s*,\s*/g, '$1$2$3\n')
        formattedText = formattedText.replace(
          /,\s*token\s*=(.*)/g,
          '\ntoken=$1'
        )
        return formattedText.trim()
      },

      handleTimeLabelChange(refresh) {
        let s, e
        switch (this.queryForm.timevalue) {
          case '10':
            s = moment().subtract(10, 'minutes').startOf('minute')
            e = moment().endOf('minute')
            break
          case '15':
            s = moment().subtract(15, 'minutes').startOf('minute')
            e = moment().endOf('minute')
            break
          case '30':
            s = moment().subtract(30, 'minutes').startOf('minute')
            e = moment().endOf('minute')
            break
          case 'day':
            s = moment().startOf('day')
            e = moment().endOf('day')
            break
        }
        if (s && e) {
          this.queryForm.start_time = s.format('yyyy-MM-DD HH:mm:ss')
          this.queryForm.end_time = e.format('yyyy-MM-DD HH:mm:ss')
          this.DateTimePicker = [
            this.queryForm.start_time,
            this.queryForm.end_time,
          ]
        }
        if (refresh) {
          this.fetchData()
        }
      },

      async changeFormData() {
        this.queryForm.start_time = this.DateTimePicker[0]
        this.queryForm.end_time = this.DateTimePicker[1]
        this.queryForm.timevalue = ''
        await this.fetchData()
      },

      async changeData() {
        await this.fetchData()
      },

      async fetchInstances() {
        try {
          const response = await getMysqlInstance()
          this.allInstances = response.data
          this.sqlTypes = [
            ...new Set(this.allInstances.map((instance) => instance.SqlType)),
          ]

          if (this.sqlTypes.length > 0) {
            this.selectedSqlType = this.sqlTypes[0]
            this.filterInstanceIds()
          }
        } catch (error) {
          console.error('Error fetching instances:', error)
        }
      },

      filterInstanceIds() {
        const filtered = this.allInstances.filter(
          (instance) => instance.SqlType === this.selectedSqlType
        )
        this.filteredInstanceIds = filtered.map((instance) => ({
          key: instance.InstanceId,
          label: instance.InstanceName,
        }))
        this.selectedInstanceId =
          this.filteredInstanceIds.length > 0
            ? this.filteredInstanceIds[0].key
            : ''
        this.updateInstanceDetails()
      },

      updateInstanceDetails() {
        const selectedInstance = this.allInstances.find(
          (instance) => instance.InstanceId === this.selectedInstanceId
        )

        if (selectedInstance) {
          this.instanceName = selectedInstance.InstanceName
          this.endpoint = `${selectedInstance.Vip}:3306`
        }
      },

      async getEventQuery(timeoutMs = 5000) {
        try {
          const { data, totalCount } = await getMysqlSlowGroupedLogs({
            startTime: this.queryForm.start_time,
            endTime: this.queryForm.end_time,
            pageIndex: this.queryForm.pageIndex,
            pageSize: this.queryForm.pageSize,
            keyword: this.queryForm.keyword,
            instanceId: this.selectedInstanceId,
          })
          this.slowLogAdviceData.data = data
          this.slowLogAdviceData.totalCount = totalCount
        } catch (error) {
          console.error('获取数据异常')
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
      },

      async getStatisticsData() {
        this.ChartLoading = true

        const data = {
          instanceId: this.selectedInstanceId,
          startTime: this.queryForm.start_time,
          endTime: this.queryForm.end_time,
        }
        try {
          const res = await postStatisticsData(data)
          this.StatisticsData = res
          this.updateStatisticsCharts()
        } catch (error) {
          console.error('getStatisticsData 获取数据失败', error)
        } finally {
          this.ChartLoading = false
        }
      },

      updateStatisticsCharts() {
        this.allDataChart()
        this.queryTimeChart()
        this.StatisticsItems = [
          {
            title: 'SQL 统计',
            prop: 'allData',
            ChartOption: this.AllDataOption,
          },
          {
            title: '耗时分布',
            prop: 'QueryTime',
            ChartOption: this.QueryTimeOption,
          },
        ]
      },

      allDataChart() {
        const xAxisData = this.hourlyData.map((item) => `${item.hour}:00`)
        const seriesData = this.hourlyData.map((item) => ({
          value: item.count,
          startTime: item.start,
          endTime: item.end,
        }))
        const yAxisData = this.hourlyData.map((item) => item.count)
        const option = {
          title: {},
          tooltip: {
            trigger: 'axis',
            formatter: (params) => {
              const data = params[0].data
              return `<b>${data.startTime} - ${data.endTime}</b><br/>慢查询次数: ${data.value}`
            },
          },

          xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
              interval: 0,
              rotate: 45,
            },
          },

          yAxis: {
            type: 'value',
          },
          series: [
            {
              name: 'Count',
              type: 'bar',
              data: seriesData,
              itemStyle: {
                color: '#4f81bd',
              },
            },
            {
              name: '',
              type: 'line',
              data: yAxisData,
              tooltip: {
                show: false,
              },
              smooth: true,
              itemStyle: {
                color: '#c0504d',
              },
            },
          ],
        }

        this.AllDataOption = option
        this.$nextTick(() => {
          this.AllDataOption = option
        })
      },
      queryTimeChart() {
        const data = this.StatisticsData.analysis_QueryTime
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: 'SQL个数占比: {c}%',
          },
          yAxis: {
            type: 'category',
            data: data.map((item) => item.Key),
          },
          xAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value} %',
            },
          },
          series: [
            {
              data: data.map((item) => ({
                value: parseFloat(item.Percent),
                itemStyle: {
                  color:
                    item.Key === '>5s'
                      ? '#ff4d4f'
                      : item.Key === '1-5s'
                      ? '#faad14'
                      : '#1890ff',
                },
              })),
              type: 'bar',
              barWidth: '40%',
              orientation: 'horizontal',
            },
          ],
        }
        this.QueryTimeOption = option
        this.$nextTick(() => {
          this.QueryTimeOption = option
        })
      },

      async fetchData() {
        if (!this.selectedInstanceId) return
        try {
          this.queryForm.pageIndex = 1
          this.queryForm.pageSize = 10
          this.tableLoading = true
          await this.getEventQuery()
          await this.getStatisticsData()
          this.tableLoading = false
        } catch (error) {
          console.error('Error fetching data:', error)
        }
      },

      refreshPage() {
        this.fetchInstances()
        this.fetchData()
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.queryForm.pageIndex = 1
        this.getEventQuery()
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
        this.getEventQuery()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .mb15 {
    margin: 8px 8px 15px !important;

    .form-el-col {
      padding: 0 8px;
      boder: 1px solid #f1f2f3;
    }

    .base-el-col {
      margin-bottom: 0px !important;
      margin-top: 0px !important;
      padding: 0 8px;
      boder: 1px solid #f1f2f3;
    }

    .content-col {
      margin-top: -10px !important;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .chart-col {
      margin-top: -10px !important;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }

  .bold-header {
    font-weight: bold;
  }

  .header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
  }

  .timeSelect {
    flex: 1;
  }

  .inputButtonGroup {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .search-input {
    width: 400px;
  }

  .expand-content {
    white-space: pre-wrap;
    word-break: break-all;
    overflow-x: auto;
    max-height: 200px;
    background-color: #f9f9f9;
    padding: 20px;
  }

  .el-pagination {
    margin-top: 35px !important;
    margin-bottom: 35px !important;
  }

  .item-container {
    display: flex;
    align-items: center;
  }

  .item-key {
    float: left;
    margin-right: 16px;
  }

  .item-label {
    float: right;
    color: #8492a6;
    font-size: 13px;
  }

  .form-container {
    display: flex;
    align-items: center;

    h2 {
      margin: 0 20px 0 0;
      display: inline-block !important;
      height: 40px;
    }
  }

  .el-form-item {
    ::v-deep .el-form-item__label {
      font-size: 12px !important;
      color: rgba(0, 0, 0, 0.4) !important;
    }

    ::v-deep .el-input--small .el-input__inner {
      font-weight: bolder !important;
    }
  }
</style>
