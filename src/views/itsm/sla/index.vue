<template>
  <div class="sandboxmain-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-radio-group
          v-model="timeRange"
          size="small"
          :disabled="changing"
          @change="handleTimeChange(true)"
        >
          <el-radio-button label="本月"></el-radio-button>
          <el-radio-button label="上月"></el-radio-button>
          <el-radio-button label="近半年"></el-radio-button>
          <el-radio-button label="今年"></el-radio-button>
          <el-radio-button label="全部"></el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">SLA指标</p>
            <p :class="stat_sla | slaClass">
              <countTo
                :start-val="0"
                :end-val="stat_sla"
                :decimals="2"
                :duration="1000"
                suffix="%"
              ></countTo>
            </p>
          </div>
        </el-card>
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">故障总数</p>
            <p :class="stat_haltcount | faultClass">
              <countTo
                :start-val="0"
                :end-val="stat_haltcount"
                :duration="2000"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">故障时间（分）</p>
            <p :class="stat_halttime | timeClass">
              <countTo
                :start-val="0"
                :end-val="stat_halttime"
                :duration="2000"
              ></countTo>
            </p>
          </div>
        </el-card>
        <el-card shadow="never">
          <div class="sla-panel">
            <el-tooltip
              class="item"
              effect="dark"
              content="达标99.92%结余时间"
              placement="top"
            >
              <p class="sla-desc">剩余时间（分）</p>
            </el-tooltip>
            <p :class="stat_remaintime | remainClass">
              <countTo
                :start-val="0"
                :end-val="stat_remaintime"
                :duration="1000"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="16" :lg="16" :xl="16">
        <el-card shadow="never" style="overflow-y: auto; overflow-x: hidden">
          <vab-chart
            auto-resize
            theme="vab-echarts-theme"
            :option="slalist"
            style="height: 310px"
            @click="handleChartSla"
          />
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :xs="24" :sm="12" :md="8" :lg="8" :xl="8">
        <el-card shadow="never" style="overflow-y: auto; overflow-x: hidden">
          <vab-chart
            auto-resize
            theme="vab-echarts-theme"
            :option="eventlist"
            @click="handleChartlevel"
          />
        </el-card>

        <el-card shadow="never" style="overflow-y: auto; overflow-x: hidden">
          <div slot="header">
            <span>业务线统计</span>
          </div>
          <vab-chart
            auto-resize
            theme="vab-echarts-theme"
            :option="linelist"
            style="height: 210px"
            @click="handleChartLine"
          />
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16">
        <el-alert title="Tips:" type="success">
          <div class="ci-alert-list">单击图例，可以按分类筛选事件。</div>
        </el-alert>
        <p />
        <el-card shadow="never">
          <div slot="header">
            <span><b>事件列表</b></span>
            <div style="float: right">
              <el-tag
                v-for="tag in dynamicTags"
                :key="tag"
                closable
                :disable-transitions="false"
                @close="handleTagClose(tag)"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
          <el-table
            v-loading="listLoading"
            :data="list"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <el-table-column
              show-overflow-tooltip
              prop="title"
              label="事件描述"
            ></el-table-column>

            <el-table-column show-overflow-tooltip label="事件等级" width="120">
              <template #default="{ row }">
                <el-tag :type="row.level | statusType">
                  {{ row.level }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="occur_time"
              label="开始时间"
              width="150"
            ></el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="owner"
              label="负责人"
              width="100"
            ></el-table-column>

            <el-table-column
              show-overflow-tooltip
              prop="sla_time"
              label="时长"
              width="50"
            ></el-table-column>
          </el-table>
          <el-pagination
            background
            :current-page="queryForm.pageNo"
            :page-size="queryForm.pageSize"
            :layout="layout"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import VabChart from '@/plugins/echarts'
  import moment from 'moment'
  import countTo from 'vue-count-to'
  import { getEventsList } from '@/api/itsm'

  import { getSlaStats } from '@/api/itsm'

  export default {
    name: 'SlaInfo',
    components: { VabChart, countTo },
    filters: {
      statusType(status) {
        const statusMap = {
          P0级故障: 'danger',
          P1级故障: 'warning',
          P2级故障: 'warning',
          一般事故: 'success',
        }
        return statusMap[status]
      },
      slaClass(val) {
        if (val >= 99.95) {
          return 'title-success'
        } else if (val >= 99.9) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },

      faultClass(val) {
        if (val < 1) {
          return 'title-success'
        } else if (val < 5) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },

      timeClass(val) {
        if (val < 10) {
          return 'title-success'
        } else if (val < 25) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },

      remainClass(val) {
        if (val > 25) {
          return 'title-success'
        } else if (val > 0) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },
    },
    data() {
      return {
        timeRange: '本月',
        changing: false,
        start_time: '',
        end_time: '',
        stat_remaintime: 0,
        stat_haltcount: 0,
        stat_sla: 0.0,
        stat_halttime: 0,

        class_sla: 'title-success',
        class_fault: 'title-success',
        class_faulttime: 'title-success',
        class_remain: 'title-success',

        dynamicTags: [],

        linelist: {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
            },
          },
          legend: {},
          grid: {
            top: '0%',
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'value',
            minInterval: 1,
            boundaryGap: [0, 0.001],
          },
          yAxis: {
            type: 'category',
            data: ['商品和内容', '增长和会员', '微商相册_生意云', '群输入法'],
          },
          series: [
            {
              type: 'bar',
              data: [2, 4, 1, 6],
            },
          ],
        },

        eventlist: {
          color: ['#F56C6C', '#E6A23C', '#909399', '#67C23A'],
          tooltip: {
            trigger: 'item',
          },
          legend: {
            show: true,
            top: '5%',
            left: '0%',
            orient: 'vertical',
          },
          series: [
            {
              name: '业务线',
              center: ['65%', '50%'],
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: true,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '12',
                  fontWeight: 'bold',
                },
              },
              data: [],
            },
          ],
        },

        slalist: {
          title: {
            text: 'SLA指标趋势',
          },
          tooltip: {
            trigger: 'axis',
          },
          toolbox: {
            show: true,
            feature: {
              magicType: { type: ['line', 'bar'] },
              saveAsImage: {},
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [],
          },
          yAxis: {
            type: 'value',
            minInterval: 0.01,
            min: 99.5,
            max: 100,
            axisLabel: {
              formatter: '{value}',
            },
          },
          series: [
            {
              name: 'SLA',
              type: 'line',
              data: [],
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' },
                ],
                silent: true,
              },
              markLine: {
                data: [{ type: 'average', name: 'Avg' }],
              },
            },
          ],
        },

        currentUser: '',
        isAdmin: '',
        total: 0,
        reverse: true,
        listLoading: true,
        selectRows: '',
        layout: 'total, sizes, prev, pager, next, jumper',
        elementLoadingText: '正在加载...',
        list: null,
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          keyword: '',
        },
      }
    },
    created() {
      this.start_time = moment().startOf('month').format('YYYY-MM-DD')
      this.end_time = moment().endOf('month').format('YYYY-MM-DD')
      this.fetchData()
      this.queryForm.str_time = this.start_time
      this.queryForm.end_time = this.end_time
      this.fetchEventData()
    },
    mounted() {},
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleView(row) {},
      handleEdit(row) {},
      handleTimeChange(refresh) {
        var s, e
        switch (this.timeRange) {
          case '本月':
            s = moment(+new Date()).startOf('month')
            break
          case '上月':
            s = moment(+new Date()).subtract('month', 1).startOf('month')
            break
          case '近半年':
            s = moment().subtract(5, 'month').startOf('month')
            break
          case '今年':
            s = moment().startOf('year')
            break
          case '全部':
            s = moment('2020-07-01')
            break
        }
        this.start_time = s.format('YYYY-MM-DD')
        this.end_time = moment()
          .subtract('month', -1)
          .startOf('month')
          .format('YYYY-MM-DD')

        if (this.timeRange === '上月') {
          this.end_time = moment().startOf('month').format('YYYY-MM-DD')
        }

        if (refresh) {
          this.refreshPage()
        }
      },

      refreshPage() {
        this.changing = true
        this.fetchData()

        this.dynamicTags = []
        this.queryForm.dept_name = ''
        this.queryForm.level = ''
        this.queryForm.str_time = this.start_time
        this.queryForm.end_time = this.end_time
        this.fetchEventData()
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchEventData()
      },

      handleChartLine(params) {
        for (var tag in this.dynamicTags) {
          if (this.dynamicTags[tag].indexOf('业务线：') === 0) {
            this.dynamicTags.splice(tag, 1)
          }
        }
        this.queryForm.dept_name = params.name
        this.dynamicTags.push('业务线：' + params.name)

        this.handleFilterChange()
      },
      handleChartSla(params) {
        for (var tag in this.dynamicTags) {
          if (this.dynamicTags[tag].indexOf('时间：') === 0) {
            this.dynamicTags.splice(tag, 1)
          }
        }
        var s = moment(params.name).startOf('month').format('YYYY-MM-DD')
        var e = moment(params.name)
          .subtract('month', -1)
          .startOf('month')
          .format('YYYY-MM-DD')
        this.queryForm.str_time = s
        this.queryForm.end_time = e
        this.dynamicTags.push('时间：' + s + '-' + e)

        this.handleFilterChange()
      },
      handleChartlevel(params) {
        for (var tag in this.dynamicTags) {
          if (this.dynamicTags[tag].indexOf('故障级别：') === 0) {
            this.dynamicTags.splice(tag, 1)
          }
        }
        this.queryForm.level = params.name
        this.dynamicTags.push('故障级别：' + params.name)
        this.handleFilterChange()
      },

      handleTagClose(tag) {
        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1)
        this.handleFilterChange()
      },

      handleFilterChange() {
        const tag = this.dynamicTags.join(',')
        if (tag.indexOf('时间：') === -1) {
          this.handleTimeChange(false)
          this.queryForm.str_time = this.start_time
          this.queryForm.end_time = this.end_time
        }
        if (tag.indexOf('业务线：') === -1) {
          this.queryForm.dept_name = ''
        }
        if (tag.indexOf('故障级别：') === -1) {
          this.queryForm.level = ''
        }
        this.fetchEventData()
      },

      async fetchEventData() {
        this.listLoading = true
        const { data, totalCount } = await getEventsList(this.queryForm)
        this.list = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },

      async fetchData() {
        const Loading = this.$baseLoading(6, '数据统计中...')
        const param =
          'str_time=' + this.start_time + '&end_time=' + this.end_time
        const {
          eventlist,
          slalist,
          linelist,
          remaining_time,
          sla_kpi,
          slatime_sum,
          total,
        } = await getSlaStats(param)
        this.stat_remaintime = parseFloat(Number(remaining_time).toFixed(1))
        this.stat_sla = parseFloat(sla_kpi)
        this.stat_halttime = parseFloat(Number(slatime_sum))
        this.stat_haltcount = Number(total)
        this.eventlist.series[0].data = eventlist
        this.slalist.xAxis.data = []
        this.slalist.series[0].data = []
        this.slalist.yAxis.min = 99.5
        Object.keys(slalist).forEach((index) => {
          for (var key in slalist[index]) {
            this.slalist.xAxis.data.push(key)
            this.slalist.series[0].data.push(slalist[index][key])
            if (parseFloat(slalist[index][key]) < this.slalist.yAxis.min) {
              this.slalist.yAxis.min = slalist[index][key]
              //console.log(slalist[index][key],this.slalist.yAxis.min)
            }
          }
        })

        this.linelist.yAxis.data = []
        this.linelist.series[0].data = []
        Object.keys(linelist).forEach((index) => {
          this.linelist.yAxis.data.push(linelist[index]['name'])
          this.linelist.series[0].data.push(linelist[index]['value'])
        })

        setTimeout(() => {
          this.changing = false
          Loading.close()
        }, 300)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sla-panel {
    line-height: 0;
    text-align: center;
  }
  .sla-desc {
    font-size: 16px;
  }

  .title-danger {
    color: #f56c6c;
    font-size: 55px;
  }
  .title-warning {
    color: #e6a23c;
    font-size: 55px;
  }
  .title-success {
    color: #67c23a;
    font-size: 55px;
  }
  .title-info {
    color: #161616;
    font-size: 55px;
  }

  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .sandboxmain-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;

    ::v-deep {
      .el-alert {
        padding: $base-padding;

        &--info.is-light {
          min-height: 82px;
          padding: $base-padding;
          margin-bottom: 15px;
          color: #909399;
          background-color: $base-color-white;
          border: 1px solid #ebeef5;
        }
      }

      .el-card__body {
        .echarts {
          width: 100%;
          height: 160px;
        }
      }
    }

    .card {
      ::v-deep {
        .el-card__body {
          .echarts {
            width: 100%;
            height: 305px;
          }
        }
      }
    }

    .bottom {
      padding-top: 20px;
      margin-top: 5px;
      color: #595959;
      text-align: left;
      border-top: 1px solid $base-border-color;
    }

    .table {
      width: 100%;
      color: #666;
      border-collapse: collapse;
      background-color: #fff;

      td {
        position: relative;
        min-height: 20px;
        padding: 9px 15px;
        font-size: 14px;
        line-height: 20px;
        border: 1px solid #e6e6e6;

        &:nth-child(odd) {
          width: 20%;
          text-align: right;
          background-color: #f7f7f7;
        }
      }
    }

    .icon-panel {
      height: 117px;
      text-align: center;
      cursor: pointer;

      svg {
        font-size: 40px;
      }

      p {
        margin-top: 10px;
      }
    }

    .bottom-btn {
      button {
        margin: 5px 10px 15px 0;
      }
    }
  }
</style>
