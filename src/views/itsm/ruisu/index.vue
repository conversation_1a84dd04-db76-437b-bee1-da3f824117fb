<template>
  <div class="ruisu-container">
    <el-alert type="success">
      <div class="ci-alert-list">○ 数据信息：全球监控一览图</div>
    </el-alert>
    <p />

    <ip-count-country></ip-count-country>
    <global-data></global-data>
  </div>
</template>

<script>
  import GlobalData from '@/views/itsm/ruisu/components/GlobalData.vue'
  import IpCountCountry from '@/views/itsm/ruisu/components/IpCountCountry.vue'

  export default {
    name: 'RuiSuIndex',
    components: { IpCountCountry, GlobalData },
    data() {
      return {}
    },
  }
</script>

<style>
  .ruisu-container {
    padding: 0 !important;
    margin: 0 !important;
    background: #f5f7f8 !important;
  }
</style>
