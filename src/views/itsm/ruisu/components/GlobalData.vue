<template>
  <el-card>
    <vab-query-form>
      <el-form :model="queryForm" label-position="left" label-width="80px">
        <el-form-item label="国家/地域">
          <el-select
            v-model="Country"
            clearable
            filterable
            placeholder="请选择国家或地域"
          >
            <el-option
              v-for="item in Countries"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="统计粒度">
          <el-radio-group v-model="Granularity" label="统计粒度" size="small">
            <el-radio-button label="1h">每小时</el-radio-button>
            <el-radio-button label="1d">每天</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间区间">
          <el-col :span="12">
            <el-date-picker
              v-model="queryForm.value2"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
              end-placeholder="结束日期"
              range-separator="至"
              start-placeholder="开始日期"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
            ></el-date-picker>
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-button
            icon="el-icon-search"
            type="primary"
            @click="queryData(true)"
          >
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </vab-query-form>
    <el-card v-loading="acdb_loading" class="box-card">
      <card-chart :option="networkOption"></card-chart>
    </el-card>
  </el-card>
</template>

<script>
  import { ruisuACDNData } from '@/api/monitoring'
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'
  import moment from 'moment'
  import VabChart from '@/plugins/echarts/'

  export default {
    name: 'GlobalData',
    components: { VabChart, ruisuACDNData, CardChart },
    data() {
      return {
        queryForm: {
          value2: '',
        },
        acdb_loading: true,
        Countries: [],
        Country: [],
        Granularity: '',
        networkOption: '',
        pickerOptions: {
          shortcuts: [
            {
              text: '1小时',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '半天',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000 * 12)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '一天',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000 * 24)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '一周',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                picker.$emit('pick', [start, end])
              },
            },
            {
              text: '一个月',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(0, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
            {
              text: '三个月',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(2, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
            {
              text: '半年',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(5, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
            {
              text: '一年',
              onClick(picker) {
                const s = moment()
                  .startOf('month')
                  .subtract(12, 'month')
                  .toDate()
                const e = moment().endOf('month').toDate()
                picker.$emit('pick', [s, e])
              },
            },
          ],
        },
        timevalue: '',
        ACDNBeginTime: '',
        ACDNEndTime: '',
        ACDNData: {
          data: [],
          termsCount: 0,
          totalCount: 0,
        },
      }
    },
    created() {
      this.setDefaultTime()
    },

    mounted() {
      this.fetchMapData()
    },
    methods: {
      initNetworkChart_x_conuntry() {
        this.ACDNData.data.sort(function (a, b) {
          return b.time - a.time
        })
        const times = this.ACDNData.data.map((item) => item.time)
        const values = this.ACDNData.data.map((item) => item.value)
        const countryName =
          this.ACDNData.data.length > 0 &&
          this.ACDNData.data[0].country !== 'All'
            ? this.ACDNData.data[0].country
            : '全球'

        let networkOption = {
          title: {
            text: countryName + '请求数',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            itemGap: 1,
            scroll: true,
            data: [countryName],
          },
          toolbox: {
            show: true,
            feature: {
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ['line', 'bar'] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          yAxis: {
            type: 'value',
          },
          xAxis: {
            type: 'category',
            data: times,
          },
          series: [
            {
              name: countryName,
              type: 'line',
              data: values,
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' },
                ],
              },
            },
          ],
        }
        this.networkOption = networkOption
      },

      initMapData() {
        this.initNetworkChart_x_conuntry()
      },

      setDefaultTime() {
        const s = moment()
          .startOf('days')
          .subtract(7, 'days')
          .format('YYYY-MM-DD HH:mm:ss')
        const e = moment().endOf('days').format('YYYY-MM-DD HH:mm:ss')

        this.queryForm.value2 = [s, e]
        this.ACDNBeginTime = s
        this.ACDNEndTime = e
      },

      queryData(refresh) {
        if (refresh) {
          if (this.queryForm.value2 && this.queryForm.value2.length === 2) {
            this.ACDNBeginTime = this.queryForm.value2[0]
            this.ACDNEndTime = this.queryForm.value2[1]
            this.acdb_loading = true
            this.fetchMapData()
          }
        }
      },

      async fetchMapData() {
        const acdnrequestData = {
          BeginTime: this.ACDNBeginTime,
          EndTime: this.ACDNEndTime,
          Country: this.Country,

          Granularity: this.Granularity,
        }

        try {
          const acdnResponse = await ruisuACDNData(acdnrequestData)
          if (acdnResponse && acdnResponse.data) {
            this.ACDNData = {
              data: acdnResponse.data,
              termsCount: acdnResponse.termsCount,
              totalCount: acdnResponse.totalCount,
            }
            this.Countries = acdnResponse.countries
          } else {
            console.error(
              'Unexpected response structure from ruisuACDNData',
              acdnResponse
            )
          }
          this.initMapData()
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.acdb_loading = false
        }

        setTimeout(() => {
          this.acdb_loading = this.map_loading = false
        }, 60)
      },
    },
  }
</script>
