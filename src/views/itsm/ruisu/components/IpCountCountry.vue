<template>
  <el-row>
    <el-col>
      <el-card style="width: 100%">
        <div ref="mapChart" style="width: 100%; height: 650px"></div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  import * as echarts from 'echarts'
  import { ruisuIpByCountry } from '@/api/monitoring'
  import CardChart from '@/views/monitoring/report/componets/ModuleElrow.vue'
  import 'moment'

  export default {
    name: 'IpCountCountry',
    components: { ruisuIpByCountry, CardChart },
    data() {
      return {
        queryForm: {
          value1: '',
        },
        map_loading: true,
        barOption: '',
        pickerOptions: {
          shortcuts: [
            {
              text: '天',
              onClick(picker) {
                const end = new Date()
                const start = new Date()
                start.setTime(end.getTime() - 3600 * 1000 * 24)
                picker.$emit('pick', [start, end])
              },
            },
          ],
        },
        timevalue: '',
        IpCBeginTime: '',
        IpCEndTime: '',
        myChart: null,
        ipCountryData: {
          data: [],
          termsCount: 0,
          totalCount: 0,
          MaxAvg: 0,
          dataRange: '',
        },
        barChart: '',
      }
    },
    created() {
      this.setDefaultTime()
    },

    mounted() {
      this.myChart = echarts.init(this.$refs.mapChart)
      this.fetchMapData()
    },
    methods: {
      initBarChart_x_country() {
        this.ipCountryData.data.sort(function (a, b) {
          return b.ipCount - a.ipCount
        })
        const countries = this.ipCountryData.data.map((item) => item.country)
        const ipCounts = this.ipCountryData.data.map((item) => item.ipCount)

        const sortedIpCounts = [...ipCounts].sort((a, b) => a - b)
        const midIndex = Math.floor(sortedIpCounts.length / 2)
        const median = sortedIpCounts[midIndex]
        const max = sortedIpCounts[sortedIpCounts.length - 1]

        const medianIndex = ipCounts.indexOf(median)
        const maxIndex = ipCounts.indexOf(max)

        const startPercent = (medianIndex / ipCounts.length) * 100
        const endPercent = (maxIndex / ipCounts.length) * 100

        const numberOfCountriesToShow = 4
        let indicesToShow
        if (countries.length <= numberOfCountriesToShow) {
          indicesToShow = countries.map((_, index) => index)
        } else {
          const interval = Math.floor(
            (countries.length - 1) / (numberOfCountriesToShow - 1)
          )
          indicesToShow = []

          for (let i = 0; i < numberOfCountriesToShow; i++) {
            let index = i * interval

            if (i === numberOfCountriesToShow - 1) {
              index = countries.length - 1
            }
            indicesToShow.push(index)
          }
        }

        let barOption = {
          title: {
            text: 'IP数日均统计分布',
            subtext: `${this.ipCountryData.dataRange}`,
            top: '0px',
          },
          grid: {
            bottom: '10%',
            top: 80,
          },

          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: ['Rainfall', 'Evaporation'],
          },
          toolbox: {
            show: true,
            feature: {
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ['line', 'bar'] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          calculable: true,
          xAxis: [
            {
              type: 'category',
              data: countries,
              axisLabel: {
                interval: 0,
                formatter: function (value, index) {
                  return indicesToShow.includes(index) ? value : ''
                },
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              left: 'left',
            },
          ],
          dataZoom: [
            {
              show: true,
              backgroundColor: 'rgba(47, 69, 84, 0)',
              showDetail: false,
              left: '10%',
              right: '10%',
              start: startPercent,
              end: endPercent,
            },
            {
              type: 'slider',
            },
          ],
          series: [
            {
              id: 'baroption',
              type: 'bar',
              data: ipCounts,
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' },
                ],
              },
            },
          ],
        }
        this.barOption = barOption
      },

      initBarChart_y_conuntry() {
        this.ipCountryData.data.sort(function (a, b) {
          return b.ipCount - a.ipCount
        })
        const countries = this.ipCountryData.data.map((item) => item.country)
        const ipCounts = this.ipCountryData.data.map((item) => item.ipCount)

        const numberOfCountriesToShow = 4
        const step = Math.floor(
          countries.length / (numberOfCountriesToShow - 1)
        )
        const indicesToShow = new Set([0, countries.length - 1])

        for (let i = step; i < countries.length - 1; i += step) {
          indicesToShow.add(i)
        }

        let barOption = {
          title: {
            text: 'CountryByIp',
            subtext: `${this.ipCountryData.dataRange} IP数日均统计分布`,
            top: '20px',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            data: ['Rainfall', 'Evaporation'],
          },
          toolbox: {
            show: true,
            feature: {
              dataView: { show: true, readOnly: false },
              magicType: { show: true, type: ['line', 'bar'] },
              restore: { show: true },
              saveAsImage: { show: true },
            },
          },
          calculable: true,
          yAxis: [
            {
              type: 'category',
              data: countries,
              axisLabel: {
                formatter: function (value, index) {
                  return indicesToShow.has(index) ? value : ''
                },
              },
            },
          ],
          xAxis: [
            {
              type: 'value',
              left: 'left',
            },
          ],
          dataZoom: [
            {
              show: true,
              start: 1293,
            },
            {
              type: 'slider',
              start: 1293,
            },
          ],
          series: [
            {
              id: 'baroption',
              type: 'bar',
              data: ipCounts,
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' },
                ],
              },
            },
          ],
        }
        this.barOption = barOption
      },

      async initMap() {
        this.Loading = true
        try {
          const response = await fetch('/world.json')
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }
          const worldJson = await response.json()
          echarts.registerMap('world', worldJson)
        } catch (error) {
          console.error('Error fetching world.json:', error)
        }

        this.ipCountryData.data.sort(function (a, b) {
          return a.ipCount - b.ipCount
        })

        let mapOption = {
          title: {
            text: 'IP全球分布',
            subtext: `${this.ipCountryData.dataRange} 日均 IP 数`,

            left: 'center',
          },
          tooltip: {
            trigger: 'item',
            showDelay: 0,
            transitionDuration: 0.2,
            formatter: '{b}<br/>{c}',
          },

          visualMap: {
            min: 0,
            max: this.ipCountryData.MaxAvg,
            left: 'left',
            text: ['High', 'Low'],
            calculable: true,
            inRange: {
              color: [
                '#e0f3f8',
                '#ffffbf',
                '#fee090',
                '#fdae61',
                '#f46d43',
                '#d73027',
                '#a50026',
              ],
            },
          },
          series: [
            {
              name: 'World Ip Country',
              type: 'map',
              map: 'world',
              roam: true,

              data: this.ipCountryData.data
                .map((item) => ({ name: item.country, value: item.ipCount }))
                .sort((a, b) => b.value - a.value),
              universalTransition: true,
            },
          ],
          toolbox: {
            show: true,
            left: 'left',
            top: 'top',
            feature: {
              dataView: { readOnly: false, show: true },
              restore: {},
              saveAsImage: {},
            },
          },
        }
        this.myChart.setOption(mapOption)
      },

      initMapData() {
        this.initMap()
        this.initBarChart_x_country()
      },

      setDefaultTime() {
        let startOfWeek, endOfWeek
        if (moment().day() === 1) {
          startOfWeek = moment()
            .subtract(1, 'weeks')
            .startOf('isoWeek')
            .format('YYYY-MM-DD HH:mm:ss')
          endOfWeek = moment()
            .subtract(1, 'weeks')
            .endOf('isoWeek')
            .format('YYYY-MM-DD HH:mm:ss')
        } else {
          startOfWeek = moment()
            .startOf('isoWeek')
            .format('YYYY-MM-DD HH:mm:ss')
          endOfWeek = moment().endOf('isoWeek').format('YYYY-MM-DD HH:mm:ss')
        }

        const todayStart = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
        const todayEnd = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')

        this.IpCBeginTime = startOfWeek
        this.IpCEndTime = endOfWeek
      },

      queryData(refresh) {
        if (refresh) {
          if (this.queryForm.value1 && this.queryForm.value1.length === 2) {
            this.IpCBeginTime = this.queryForm.value1[0]
            this.IpCEndTime = this.queryForm.value1[1]
            this.map_loading = true
            this.fetchMapData()
          }
        }
      },

      async fetchMapData() {
        const iprequestData = {
          BeginTime: this.IpCBeginTime,
          EndTime: this.IpCEndTime,
        }

        try {
          const ipCountryResponse = await ruisuIpByCountry(iprequestData)
          if (ipCountryResponse && ipCountryResponse.data) {
            this.ipCountryData = {
              data: ipCountryResponse.data,
              termsCount: ipCountryResponse.termsCount,
              totalCount: ipCountryResponse.totalCount,
              MaxAvg: ipCountryResponse.MaxAvg,
              dataRange: ipCountryResponse.dataRange,
            }
          } else {
            console.error(
              'Unexpected response structure from ruisuIpByCountry',
              ipCountryResponse
            )
          }

          this.initMapData()
        } catch (error) {
          console.error('Error fetching data:', error)
        } finally {
          this.map_loading = false
        }

        setTimeout(() => {
          this.map_loading = false
        }, 60)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .el-row .el-col {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
