<template>
  <div class="userManagement-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleEdit">
          事故登记
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.native.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.keyword"
              clearable
              placeholder="请输入关键字"
            />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" type="primary" @click="queryData">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      v-loading="listLoading"
      :data="list"
      :element-loading-text="elementLoadingText"
      @selection-change="setSelectRows"
    >
      <el-table-column show-overflow-tooltip type="selection"></el-table-column>
      <el-table-column
        fixed
        label="事故ID"
        prop="event_id"
        show-overflow-tooltip
        width="150"
      >
        <template #default="scope">
          <span v-if="scope.row.reportlink">
            <el-tooltip content="跳转到事故报告">
              <a :href="scope.row.reportlink" target="_blank" type="primary">
                {{ scope.row.event_id }}
              </a>
            </el-tooltip>
          </span>
          <span v-else>{{ scope.row.event_id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="事故描述"
        prop="title"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column label="事故等级" show-overflow-tooltip width="120">
        <template #default="{ row }">
          <el-tag :type="row.level | statusType">
            {{ row.level }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="开始时间"
        prop="occur_time"
        show-overflow-tooltip
        width="150"
      ></el-table-column>

      <el-table-column
        label="结束时间"
        prop="end_time"
        show-overflow-tooltip
        width="150"
      ></el-table-column>

      <el-table-column
        label="第一负责人"
        prop="owner"
        show-overflow-tooltip
        width="120"
      ></el-table-column>
      <el-table-column
        label="第二负责人"
        prop="secondaryOwner"
        show-overflow-tooltip
        width="120"
      ></el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        show-overflow-tooltip
        width="100"
      >
        <template #default="{ row }">
          <el-button type="success" @click="handleEdit(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="queryForm.pageNo"
      :layout="layout"
      :page-size="queryForm.pageSize"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <edit ref="edit" @fetch-data="fetchData"></edit>
  </div>
</template>

<script>
  import { getEventsList } from '@/api/itsm'
  import Edit from './components/EventsEdit'

  export default {
    name: 'EventList',
    components: { Edit },
    filters: {
      statusType(status) {
        const statusMap = {
          P0级故障: 'danger',
          P1级故障: 'warning',
          P2级故障: 'warning',
          一般事故: 'success',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          false: '禁用',
          true: '正常',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        list: null,
        listLoading: true,
        layout: 'total, sizes, prev, pager, next, jumper',
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
        queryForm: {
          pageNo: 1,
          pageSize: 10,
          userName: '',
        },
      }
    },
    created() {
      this.fetchData()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      handleUpload() {
        this.$baseConfirm(
          '确认将权限信息同步至SSO服务器吗？',
          null,
          async () => {
            const { msg } = await doUpload()
            this.$baseMessage(msg, 'success')
          }
        )
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要禁用当前用户吗', null, async () => {
            const { msg } = await doDelete({ ids: row.id })
            this.$baseMessage(msg, 'success')
            this.fetchData()
          })
        } else {
          if (this.selectRows.length > 0) {
            const ids = this.selectRows.map((item) => item.id).join()
            this.$baseConfirm('你确定要删除选中项吗', null, async () => {
              const { msg } = await doDelete({ ids })
              this.$baseMessage(msg, 'success')
              this.fetchData()
            })
          } else {
            this.$baseMessage('未选中任何行', 'error')
            return false
          }
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.fetchData()
      },
      queryData() {
        this.queryForm.pageNo = 1
        this.fetchData()
      },
      async fetchData() {
        this.listLoading = true
        const { data, totalCount } = await getEventsList(this.queryForm)
        this.list = data
        this.total = totalCount
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
    },
  }
</script>
