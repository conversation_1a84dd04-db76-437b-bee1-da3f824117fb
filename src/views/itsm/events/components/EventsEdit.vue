<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="800px"
    :close-on-click-modal="isedit"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row>
        <el-col :span="24">
          <el-form-item v-if="isedit" label="事故编号">
            <el-input
              v-model.trim="form.event_id"
              :readonly="true"
              placeholder="自动生成"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发生时间">
            <el-date-picker
              v-model="form.occur_time"
              type="datetime"
              value-format="yyyy-MM-dd[T]HH:mm:ss.sss[Z]"
              placeholder="选择日期时间"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="结束时间">
            <el-date-picker
              v-model="form.end_time"
              type="datetime"
              value-format="yyyy-MM-dd[T]HH:mm:ss.sss[Z]"
              placeholder="选择日期时间"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="事故描述" prop="title">
            <el-input
              v-model.trim="form.title"
              autocomplete="off"
              placeholder="简述事故的现象和影响"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="事故等级">
            <el-radio-group v-model="form.level" size="medium">
              <el-radio-button label="P0级故障">P0级故障</el-radio-button>
              <el-radio-button label="P1级故障">P1级故障</el-radio-button>
              <el-radio-button label="P2级故障">P2级故障</el-radio-button>
              <el-radio-button label="一般事故">一般事故</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否有告警">
            <el-radio-group v-model="form.warned" size="mini">
              <el-radio-button label="是">是</el-radio-button>
              <el-radio-button label="否">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="是否影响用户">
            <el-radio-group v-model="form.influenced" size="mini">
              <el-radio-button label="是">是</el-radio-button>
              <el-radio-button label="否">否</el-radio-button>
              <el-radio-button label="未知">未知</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="是否有变更">
            <el-radio-group v-model="form.upgraded" size="mini">
              <el-radio-button label="是">是</el-radio-button>
              <el-radio-button label="否">否</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="第一负责人">
            <el-input v-model.trim="form.owner" autocomplete="off"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="第二负责人">
            <el-input
              v-model.trim="form.secondaryOwner"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="当前处理进展">
            <el-input
              v-model.trim="form.progress"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="故障报告">
            <el-input
              v-model.trim="form.reportlink"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="影响范围" prop="impactscope">
            <el-input
              v-model.trim="form.impactScope"
              autocomplete="off"
              placeholder="用户数"
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="推送通知">
            <el-checkbox-group v-model="form.notice">
              <el-checkbox label="企微现网问题群" name="notice"></el-checkbox>
              <el-checkbox label="飞书中后台群" name="notice"></el-checkbox>
              <el-checkbox label="微信群" name="notice"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="info" @click="close">关 闭</el-button>
      <el-button
        v-if="!isedit"
        type="success"
        icon="el-icon-check"
        @click="saveApply"
      >
        提 交
      </el-button>
      <el-button
        v-if="isedit"
        type="success"
        icon="el-icon-check"
        @click="saveApply"
      >
        更 新
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getUserInfo } from '@/api/user'
  import { slaEvents } from '@/api/itsm'

  export default {
    name: 'EventEdit',
    filters: {
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'success',
          2: 'danger',
        }
        return statusMap[status]
      },
      statusMap(status) {
        const statusMap = {
          0: '申请中',
          1: '已审核',
          2: '已禁用',
        }
        return statusMap[status]
      },
    },
    data() {
      return {
        form: {
          event_id: '',
          title: '',
          channel: 0,
          occur_time: '',
          end_time: '',
          level: 'P1级故障',
          influenced: '是',
          services: '',
          warned: '是',
          upgraded: '是',
          progress: '',
          owner: '',
          primaryOwner: '',
          secondaryOwner: '',
          notice: [],
          reportlink: '',
          impactScope: '',
        },

        rules: {
          title: [
            { required: true, trigger: 'blur', message: '请输入事故描述' },
          ],
          progress: [
            { required: true, trigger: 'blur', message: '请输入当前进展' },
          ],
        },
        title: '',
        isedit: false,
        currentUser: '',
        isAdmin: false,
        dialogFormVisible: false,
        uricount: 0,
      }
    },
    computed: {},
    created() {
      //this.fetchData()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '登记事故'
          this.isedit = false
          //this.form.applicant = this.currentUser
        } else {
          this.title = '查看事故'
          this.isedit = true
          this.form = Object.assign({}, row)
          //格式转换，防止报错
          //this.form.end_time = moment(new Date(row.end_time)).add(8 * 3600,'second').utc().format('YYYY-MM-DD[T]HH:mm:ss.SSS[Z]')
          //this.form.occur_time = moment(new Date(row.occur_time)).add(8 * 3600,'second').utc().format('YYYY-MM-DD[T]HH:mm:ss.SSS[Z]')
          this.form.end_time = row.end_time
          this.form.occur_time = row.occur_time
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      updateStatus(newStatus) {
        var msg =
          newStatus === 1 ? '确认启用这些URL吗？' : '确认禁用这些URL吗？'
        this.$baseConfirm(msg, null, async () => {
          var statusData = {
            status: newStatus,
            apply_ci_appid: this.form.apply_ci_appid,
            approver: this.currentUser,
          }
          const { msg } = await doUpdate(statusData)
          this.$baseMessage(msg, 'success')
          this.$emit('fetch-data')
          this.close()
        })
      },
      saveApply() {
        var confirmmsg =
          this.form.notice.length === 0
            ? '确认保存事故信息吗？'
            : `确认保存事故信息并发送群通知吗？`
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            this.$baseConfirm(confirmmsg, null, async () => {
              const { msg } = await slaEvents(this.form)
              this.$baseMessage(msg, 'success')
              this.$emit('fetch-data')
              this.close()
            })
          } else {
            return false
          }
        })
      },
      async fetchData() {
        const { user, groups } = await getUserInfo()
        this.currentUser = user
        this.isAdmin = groups.indexOf('admin') !== -1
        setTimeout(() => {}, 300)
      },
    },
  }
</script>
