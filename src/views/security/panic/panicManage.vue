<template>
  <div class="panicManagement-container">
    <el-row :gutter="20">
      <el-col :lg="6" :md="6" :sm="12" :xl="6" :xs="24">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">API异常</p>
            <p :class="panicStats.api_erros | panicClass">
              <countTo
                :duration="1000"
                :end-val="panicStats.api_erros"
                :start-val="0"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="6" :md="6" :sm="12" :xl="6" :xs="24">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">服务异常</p>
            <p :class="panicStats.service_errors | panicClass">
              <countTo
                :duration="2000"
                :end-val="panicStats.service_errors"
                :start-val="0"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="6" :md="6" :sm="12" :xl="6" :xs="24">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">降级接口</p>
            <p class="title-success">
              <countTo
                :duration="2000"
                :end-val="panicStats.panic_api"
                :start-val="0"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>
      <el-col :lg="6" :md="6" :sm="12" :xl="6" :xs="24">
        <el-card shadow="never">
          <div class="sla-panel">
            <p class="sla-desc">降级服务</p>
            <p class="title-success">
              <countTo
                :duration="1000"
                :end-val="panicStats.panic_service"
                :start-val="0"
              ></countTo>
            </p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :lg="16" :md="16" :sm="24" :xl="16" :xs="24">
        <el-card shadow="never">
          <div slot="header">
            <span><b>接口错误</b></span>

            <el-button
              icon="el-icon-delete"
              size="mini"
              style="float: right; margin-left: 5px"
              type="danger"
            ></el-button>

            <el-button
              icon="el-icon-refresh"
              size="mini"
              style="float: right; margin-left: 20px"
              type="primary"
              @click="handleRefreshApi"
            ></el-button>

            <el-radio-group
              v-model="timerange"
              size="mini"
              style="float: right"
            >
              <el-radio-button label="30秒"></el-radio-button>
              <el-radio-button label="1分钟"></el-radio-button>
              <el-radio-button label="5分钟"></el-radio-button>
              <el-radio-button label="10分钟"></el-radio-button>
            </el-radio-group>
          </div>
          <el-table
            v-loading="listLoading"
            :data="errorList"
            :element-loading-text="elementLoadingText"
            @selection-change="setSelectRows"
          >
            <el-table-column
              show-overflow-tooltip
              type="selection"
            ></el-table-column>
            <el-table-column
              label="URL"
              prop="url"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column label="保障等级" show-overflow-tooltip width="80">
              <template #default="{ row }">
                <el-tag :type="row.level | levelType">
                  {{ row.level | levelName }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column
              label="异常数"
              prop="count"
              show-overflow-tooltip
              width="120"
            ></el-table-column>

            <el-table-column label="操作" show-overflow-tooltip width="100">
              <template #default="{ row }">
                <el-button type="danger" @click="updatePanic(row)">
                  降级
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <el-card shadow="never">
          <div slot="header">
            <span><b>快捷操作</b></span>
          </div>
          <el-row :gutter="20">
            <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
              <el-button size="large" type="danger" @click="addDownGrade(1)">
                保障一级服务
              </el-button>
            </el-col>
            <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
              <el-button
                :disabled="true"
                size="large"
                type="danger"
                @click="addDownGrade(2)"
              >
                保障二级服务
              </el-button>
            </el-col>
            <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
              <el-button :disabled="true" size="large" type="danger">
                保障三级服务
              </el-button>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
      <el-col :lg="8" :md="8" :sm="24" :xl="8" :xs="24">
        <el-alert :closable="false" title="提示:" type="warning">
          <template slot>
            <div class="ci-alert-list">
              修改降级规则后，点击「同步」按钮才会生效。
            </div>
            <p />
          </template>
        </el-alert>
        <p />
        <el-card shadow="never">
          <div slot="header">
            <span><b>降级列表</b></span>

            <el-button
              icon="el-icon-delete"
              size="mini"
              style="float: right; margin-left: 5px"
              type="success"
              @click="cleanPanic"
            ></el-button>

            <el-button
              icon="el-icon-upload"
              size="mini"
              style="float: right; margin-left: 20px"
              type="warning"
              @click="syncPanic"
            ></el-button>
          </div>
          <el-table
            v-loading="paniclistLoading"
            :data="panicList"
            :element-loading-text="elementLoadingText"
          >
            <el-table-column
              label="URL"
              prop="uri"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column label="操作" show-overflow-tooltip width="100">
              <template #default="{ row }">
                <el-button type="success" @click="deletePanic(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import countTo from 'vue-count-to'
  import {
    cleanPanicList,
    delPanicList,
    getPanicList,
    getUserErrors,
    panicDowngrade,
    syncPanicList,
    updatePanicList,
  } from '@/api/ciWorkflow'

  export default {
    name: 'PanicManagement',
    components: { countTo },
    filters: {
      levelName(status) {
        const statusMap = {
          1: '一级',
          2: '二级',
          3: '三级',
          0: '无',
        }
        return statusMap[status]
      },
      levelType(status) {
        const statusMap = {
          1: 'danger',
          2: 'warning',
          3: 'warning',
          0: 'success',
        }
        return statusMap[status]
      },
      panicClass(val) {
        if (val <= 50) {
          return 'title-success'
        } else if (val <= 200) {
          return 'title-warning'
        } else {
          return 'title-danger'
        }
      },
    },
    data() {
      return {
        panicStats: {
          api_erros: 102,
          service_errors: 0,
          rules: 0,
          panic_api: 0,
          panic_service: 0,
        },
        downGrade: {
          level: 1,
        },
        timerange: '30秒',
        paniclistLoading: false,
        listLoading: false,
        errorList: [],
        panicList: [],
        total: 0,
        selectRows: '',
        elementLoadingText: '正在加载...',
      }
    },
    created() {
      this.fetchData()
      this.fetchPanic()
    },
    methods: {
      setSelectRows(val) {
        this.selectRows = val
      },
      handleRefreshApi(row) {
        this.fetchData()
      },
      handleDelete(row) {},
      async fetchData() {
        var s = 30
        switch (this.timerange) {
          case '30秒':
            s = 30
            break
          case '1分钟':
            s = 60
            break
          case '5分钟':
            s = 300
            break
          case '10分钟':
            s = 600
            break
        }

        const { data, total } = await getUserErrors(s)
        this.errorList = data
        this.panicStats.api_erros = total
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
      async fetchPanic() {
        const { data, total } = await getPanicList()
        this.panicList = data
        this.panicStats.panic_api = total
        setTimeout(() => {
          this.listLoading = false
        }, 300)
      },
      async updatePanic(row) {
        var item = {
          status: 1,
          uri: row.url,
        }
        const { data } = await updatePanicList(item)
        setTimeout(() => {
          this.fetchPanic()
        }, 300)
      },

      async deletePanic(row) {
        this.$baseConfirm('确认取消这条规则？', '警告', async () => {
          const { data } = await delPanicList(row)
          setTimeout(() => {
            this.fetchPanic()
          }, 300)
        })
      },
      async cleanPanic() {
        this.$baseConfirm('确认清除所有规则吗？', '警告', async () => {
          const { data } = await cleanPanicList()
          setTimeout(() => {
            this.fetchPanic()
          }, 300)
        })
      },

      async syncPanic() {
        this.$baseConfirm(
          '推送后当前降级列表立即生效，确认继续？',
          '警告',
          async () => {
            const { data, total } = await syncPanicList()

            setTimeout(() => {
              this.$baseMessage('更新完成', 'success')
            }, 300)
          }
        )
      },
      async addDownGrade(level) {
        this.downGrade.level = level
        this.$baseConfirm('确认降级吗？', '警告', async () => {
          const { data, total } = await panicDowngrade(this.downGrade)
          setTimeout(() => {
            this.$baseMessage('已经屏蔽' + level + '级以下全部接口', 'success')
          }, 300)
          this.fetchPanic()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .sla-panel {
    line-height: 0;
    text-align: center;
  }

  .sla-desc {
    font-size: 16px;
  }

  .title-danger {
    color: #f56c6c;
    font-size: 55px;
  }

  .title-warning {
    color: #e6a23c;
    font-size: 55px;
  }

  .title-success {
    color: #67c23a;
    font-size: 55px;
  }

  .title-info {
    color: #161616;
    font-size: 55px;
  }
</style>
