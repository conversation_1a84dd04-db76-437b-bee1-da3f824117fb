<script>
  import EChart from '@/views/security/component/echartsModel.vue'
  import TestCard from '@/views/security/dashboard/twoCard.vue'
  import moment from 'moment'
  import { getWafAttackByIp, getWafAttackByType } from '@/api/security'
  import { reject } from 'lodash'
  import { getIPsCity } from '@/api/toolbox'

  export default {
    name: 'SecurityDashboard',
    components: { EChart, TestCard },
    data() {
      return {
        formData: {
          timevalue: 'day',
          group: '',
          keyword: '',
          start_time: '',
          end_time: '',
        },
        DateTimePicker: [],
        timeOptions: [
          { label: '今天', value: 'day' },
          { label: '7天', value: 'week' },
          { label: '30天', value: 'month' },
          { label: '半年', value: 'halfYear' },
        ],
        typeTableData: {
          count: '',
          data: [],
        },
        ipTableData: {
          count: '',
          data: [],
        },
        tableLoading: false,
        tableItems: [
          {
            title: '攻击IP Top10',
            key: 'ip',
            type: {
              count: '',
              data: [],
            },
          },
          {
            title: '拦截类型 Top10',
            key: 'type',
            type: {
              count: '',
              data: [],
            },
          },
        ],
      }
    },
    computed: {
      allSumComputer() {
        if (!this.ipTableData.data || !this.typeTableData.data) {
          return { ipSum: 0, typeSum: 0 }
        }

        const sumReducer = (accumulator, currentValue) =>
          accumulator + (currentValue.sum_attack_count || 0)

        const ipSum = this.ipTableData.data.reduce(sumReducer, 0)
        const typeSum = this.typeTableData.data.reduce(sumReducer, 0)

        return { ipSum, typeSum }
      },
    },
    created() {
      this.handleTimeLabelChange(false)
      this.refreshPage()
    },

    methods: {
      handleTimeLabelChange(refresh) {
        let s, e
        switch (this.formData.timevalue) {
          case 'day':
            s = moment().startOf('day')
            e = moment().endOf('day')
            break
          case 'week':
            s = moment().subtract(6, 'days').startOf('day')
            e = moment().endOf('day')
            break
          case 'month':
            s = moment().subtract(29, 'days').startOf('day')
            e = moment().endOf('day')
            break
          case 'halfYear':
            s = moment().startOf('month').subtract(5, 'month')
            e = moment().endOf('month')
            break
        }
        if (s && e) {
          this.formData.start_time = s.format('yyyy-MM-DD HH:mm:ss')
          this.formData.end_time = e.format('yyyy-MM-DD HH:mm:ss')
          this.DateTimePicker = [
            this.formData.start_time,
            this.formData.end_time,
          ]
        }
        if (refresh) {
          this.refreshPage()
        }
      },
      async changeFormData() {
        this.formData.start_time = this.DateTimePicker[0]
        this.formData.end_time = this.DateTimePicker[1]
        this.formData.timevalue = ''
      },

      async changeData() {
        this.refreshPage()
      },

      async getWafGroupByType(timeoutMs = 5000) {
        try {
          const { data, totalCount } = await getWafAttackByType({
            start_time: this.formData.start_time,
            end_time: this.formData.end_time,
          })
          this.typeTableData.count = totalCount
          this.typeTableData.data = data
          this.updateTableItems('type', data, totalCount)
        } catch (error) {
          console.error('获取数据异常')
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        }

        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
      },

      async getWafGroupByIp(timeoutMs = 5000) {
        try {
          const { data, totalCount } = await getWafAttackByIp({
            start_time: this.formData.start_time,
            end_time: this.formData.end_time,
          })
          this.ipTableData.count = totalCount
          this.ipTableData.data = data
          for (let i = 0; i < data.length; i++) {
            const ip = data[i].ip_address
            const cityData = await getIPsCity('ips1=' + ip)

            const cityitem = {
              country: cityData.data[0].country,
              region: cityData.data[0].continent,
              city: cityData.data[0].city,
              isp: cityData.data[0].isp,
              source: cityData.data[0].source,
            }
            if (cityData) {
              this.ipTableData.data[i] = {
                ...this.ipTableData.data[i],
                ...cityitem,
              }
            } else {
              console.error('Invalid city data received', cityData)
            }
          }
          this.updateTableItems('ip', data, totalCount)
        } catch (error) {
          console.error('获取数据异常')
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
      },

      tableColumns(dataObject) {
        if (dataObject && dataObject.data && dataObject.data.length > 0) {
          return Object.keys(dataObject.data[0]).filter(
            (key) =>
              key !== 'id' && !key.includes('time') && !key.includes('source')
          )
        }
        return []
      },

      getColumnLabel(key) {
        if (key.includes('attack_count')) {
          return '次数'
        }
        if (key.includes('address')) {
          return 'ip'
        }
        if (key.includes('type')) {
          return '类型'
        }
        return key
      },

      refreshPage() {
        this.tableLoading = true
        this.chartLoading = true

        Promise.all([this.getWafGroupByIp(), this.getWafGroupByType()]).finally(
          () => {
            this.tableLoading = false
            this.chartLoading = false
          }
        )
      },

      updateTableItems(key, data, count) {
        const tableItem = this.tableItems.find((item) => item.key === key)
        if (tableItem) {
          tableItem.type.count = count
          tableItem.type.data = data
        }
      },

      initBarOption() {
        if (this.typeTableData.data) {
          const options = {
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
              },
              formatter: function (params) {
                const value = params[0].data
                return `${params[0].name}: ${value}`
              },
            },
            xAxis: {
              type: 'category',
              data: this.typeTableData.data.map((item) => item.attack_type),
              axisLabel: {
                rotate: 15,
                fontSize: 10,
                interval: 0,
              },
            },
            yAxis: {
              type: 'value',
              min: 0,
              max: (value) => value.max * 1.1,
              axisLabel: {
                formatter: '{value}',
                fontSize: 10,
              },
            },
            series: [
              {
                data: this.typeTableData.data.map(
                  (item) => item.sum_attack_count
                ),
                type: 'bar',
                itemStyle: {
                  barBorderRadius: [5, 5, 0, 0],
                },
              },
            ],
          }
          return options
        } else {
          return null
        }
      },

      initPieOption() {
        if (this.typeTableData.data) {
          const options = {
            tooltip: {
              trigger: 'item',
              formatter: '{b} : {c} ({d}%)',
            },

            legend: {
              orient: 'vertical',
              left: 'right',
              top: 'middle',
              data: this.typeTableData.data.map((item) => item.attack_type),
            },
            series: [
              {
                name: '攻击类型',
                type: 'pie',
                radius: '55%',
                center: ['40%', '60%'],
                data: this.typeTableData.data.map((item) => ({
                  value: item.sum_attack_count,
                  name: item.attack_type,
                })),
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          }
          return options
        } else {
          return null
        }
      },
    },
  }
</script>

<template>
  <div class="security-dashboard">
    <el-row :gutter="0" class="mb15">
      <el-col class="base-el-col">
        <div class="timeSelect">
          <el-form ref="formData" :model="formData" @submit="changeData">
            <el-radio-group
              v-model="formData.timevalue"
              size="mini"
              style="float: left; margin-right: 0px"
              @change="handleTimeLabelChange(true)"
            >
              <el-radio-button
                v-for="option in timeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio-button>
            </el-radio-group>
            <el-date-picker
              v-model="DateTimePicker"
              end-placeholder="结束日期"
              range-separator="至"
              size="mini"
              start-placeholder="开始日期"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="changeFormData"
            ></el-date-picker>
            <el-button
              circle
              icon="el-icon-search"
              size="mini"
              style="margin-left: 20px"
              @click="changeData"
            ></el-button>
          </el-form>
        </div>
      </el-col>
    </el-row>

    <el-row
      :gutter="0"
      class="mb15"
      style="display: flex; margin-top: 13px !important"
    >
      <el-col class="base-el-col" style="height: 100%">
        <el-card>
          <div class="title">
            <span>攻击总览</span>
          </div>
          <div class="overview overview-detail">
            <div
              class="flex"
              style="display: flex; justify-content: space-between"
            >
              <div class="item">
                <div class="fl">
                  <svg
                    class="icon"
                    height="60"
                    p-id="12685"
                    t="1717136870051"
                    version="1.1"
                    viewBox="0 0 1024 1024"
                    width="60"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M928 128a32 32 0 0 1 32 32v544a32 32 0 0 1-32 32h-260.608l49.504 115.392a32 32 0 0 1-29.44 44.608H336.544a32 32 0 0 1-29.44-44.608L356.544 736H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h832zM597.792 736h-171.616l-41.12 96h253.888l-41.152-96zM896 192H128v480h768V192zM435.616 303.008V576h-54.72V303.008h54.72z m158.624 0l7.712 0.16c27.744 1.216 49.856 9.152 66.368 23.84 17.984 16 27.008 37.056 27.008 63.168 0 26.144-9.024 47.136-27.008 63.04-16.384 14.4-38.272 22.272-65.664 23.584l-8.416 0.192h-49.664V576h-54.752V303.008h104.416z m0 42.176h-49.664v89.6h49.664c15.264 0 26.816-4.128 34.688-12.448 7.872-8.32 11.84-18.88 11.84-31.776 0-13.12-3.936-23.968-11.744-32.544-6.816-7.488-16.544-11.68-29.184-12.64l-5.6-0.192z"
                      fill="#bfbfbf"
                      p-id="12686"
                    ></path>
                  </svg>
                </div>
                <div class="fl cont">
                  <div class="title">
                    <rs-font>IP总数</rs-font>
                  </div>
                  <p>
                    <span>{{ ipTableData.count }}</span>
                    <rs-font>个</rs-font>
                  </p>
                </div>
              </div>
              <div class="item">
                <div class="fl">
                  <svg
                    class="icon"
                    height="60"
                    p-id="9229"
                    t="1716436672014"
                    version="1.1"
                    viewBox="0 0 1024 1024"
                    width="60"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M346.441879 628.694765V408.019329c0-11.477047 2.611544-20.067651 7.834631-25.771812 5.223087-5.704161 11.958121-8.590604 20.273826-8.590604 8.521879 0 15.463087 2.817718 20.754899 8.521879 5.291812 5.635436 7.903356 14.294765 7.903356 25.909262v220.675436c0 11.614497-2.611544 20.273826-7.903356 25.977987-5.291812 5.704161-12.164295 8.590604-20.754899 8.590604-8.178255 0-14.844564-2.886443-20.136376-8.659329-5.360537-5.841611-7.972081-14.50094-7.972081-25.977987zM575.29557 549.455034h-51.955973v79.308456c0 11.339597-2.680268 19.930201-8.040805 25.771812-5.360537 5.841611-12.09557 8.796779-20.273826 8.796779-8.521879 0-15.394362-2.886443-20.617449-8.659329-5.223087-5.772886-7.834631-14.294765-7.834631-25.496913V413.173691c0-12.507919 2.886443-21.373423 8.590604-26.733959s14.844564-8.040805 27.352483-8.040806h72.779597c21.510872 0 38.073557 1.649396 49.688054 4.948188 11.477047 3.161342 21.373423 8.453154 29.689128 15.875436s14.707114 16.425235 19.036778 27.146309c4.329664 10.721074 6.528859 22.747919 6.528859 36.080537 0 28.520805-8.796779 50.169128-26.390335 64.876242s-43.777718 22.129396-78.552484 22.129396z m-13.744966-128.446712h-38.211007v85.562416h38.211007c13.401342 0 24.534765-1.374497 33.537718-4.192215s15.806711-7.353557 20.548725-13.744966c4.742013-6.391409 7.078658-14.707114 7.078658-25.015839 0-12.37047-3.642416-22.404295-10.858524-30.170201-8.178255-8.24698-24.947114-12.439195-50.306577-12.439195z"
                      fill="#bfbfbf"
                      p-id="9230"
                    ></path>
                    <path
                      d="M734.187383 239.024966c53.33047 0 96.695839 43.365369 96.695838 96.695839v356.338255c0 53.33047-43.365369 96.695839-96.695838 96.695839H281.634362c-53.33047 0-96.695839-43.365369-96.695839-96.695839V335.720805c0-53.33047 43.365369-96.695839 96.695839-96.695839h452.553021m0-41.234899H281.634362c-76.215839 0-137.930738 61.783624-137.930738 137.930738v356.338255c0 76.215839 61.783624 137.930738 137.930738 137.930739h452.553021c76.215839 0 137.930738-61.783624 137.930738-137.930739V335.720805c0.068725-76.215839-61.714899-137.930738-137.930738-137.930738z"
                      fill="#bfbfbf"
                      p-id="9231"
                    ></path>
                  </svg>
                </div>
                <div class="fl cont">
                  <div class="title">
                    <rs-font>IP攻击总数</rs-font>
                  </div>
                  <p>
                    <span class="content">{{ allSumComputer.ipSum }}</span>
                    <rs-font>次</rs-font>
                  </p>
                </div>
              </div>
              <div class="item">
                <div class="fl">
                  <svg
                    class="icon"
                    height="60"
                    p-id="6494"
                    t="1716436292328"
                    viewBox="0 0 1024 1024"
                    width="60"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M939.885714 667.794286a166.034286 166.034286 0 0 1-50.468571-9.508572A401.554286 401.554286 0 0 1 110.445714 549.302857a77.531429 77.531429 0 1 0-62.902857 0A465.188571 465.188571 0 0 0 950.857143 666.331429zM84.114286 356.205714a166.034286 166.034286 0 0 1 50.468571 9.508572 401.554286 401.554286 0 0 1 778.971429 109.714285 77.531429 77.531429 0 1 0 62.902857 0A465.188571 465.188571 0 0 0 73.142857 357.668571z"
                      fill="#bfbfbf"
                      p-id="6495"
                    ></path>
                    <path
                      d="M658.285714 731.428571l-53.394285-97.28L658.285714 431.542857 456.411429 365.714286l-53.394286-92.891429A49.737143 49.737143 0 0 1 408.137143 219.428571a49.005714 49.005714 0 0 1 54.125714-15.36l298.422857 98.011429a92.891429 92.891429 0 0 1 59.977143 107.52l-73.142857 307.2a49.737143 49.737143 0 0 1-41.691429 38.765714A50.468571 50.468571 0 0 1 658.285714 731.428571z m8.777143-104.594285l31.451429 56.32 64.365714-285.257143a34.377143 34.377143 0 0 0-21.942857-39.497143L465.188571 263.314286l31.451429 56.32 183.588571 57.782857a51.2 51.2 0 0 1 34.377143 61.44zM620.982857 751.177143L567.588571 658.285714 365.714286 592.457143l51.2-202.605714L365.714286 292.571429a50.468571 50.468571 0 0 0-50.468572-24.868572 49.737143 49.737143 0 0 0-41.691428 38.765714l-73.142857 307.2a91.428571 91.428571 0 0 0 62.902857 108.251429l298.422857 100.937143a48.274286 48.274286 0 0 0 54.125714-18.285714 49.737143 49.737143 0 0 0 5.12-53.394286z m-93.622857-46.811429l31.451429 56.32-276.48-94.354285a34.377143 34.377143 0 0 1-21.942858-39.497143l64.365715-285.257143 31.451428 56.32L309.394286 585.142857a51.2 51.2 0 0 0 34.377143 61.44z"
                      fill="#bfbfbf"
                      p-id="6496"
                    ></path>
                    <path
                      d="M531.236571 443.026286l49.298286 88.137143-87.478857 48.932571-49.225143-88.137143z"
                      fill="#bfbfbf"
                      p-id="6497"
                    ></path>
                  </svg>
                </div>
                <div class="fl cont">
                  <div class="title">
                    <rs-font>类型攻击总数</rs-font>
                  </div>
                  <p>
                    <span class="content">{{ allSumComputer.typeSum }}</span>
                    <rs-font>次</rs-font>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col
        v-for="(tableItemObject, index) in tableItems"
        :key="index"
        :span="tableItemObject.title.includes('IP') ? 20 : 12"
        class="base-el-col"
      >
        <el-card>
          <div class="title">
            <span>{{ tableItemObject.title }}</span>
          </div>
          <div style="margin-top: 15px">
            <el-table
              v-loading="tableLoading"
              :border="true"
              :data="tableItemObject.type.data"
              :stripe="true"
              class="custom-table"
              element-loading-text="加载中...."
            >
              <el-table-column
                v-for="(key, index) in tableColumns(tableItemObject.type)"
                :key="index"
                :label="getColumnLabel(key)"
                :prop="key"
              ></el-table-column>
            </el-table>
          </div>
          <div
            v-if="
              !tableItemObject.title.includes('IP') && typeTableData.data.length
            "
            class="center-column"
          >
            <div style="margin-bottom: 2px">
              <e-chart :option="initPieOption()"></e-chart>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <test-card></test-card>
  </div>
</template>

<style lang="scss" scoped>
  .overview-detail .flex .item {
    height: auto;
    overflow: hidden;
    margin-top: 30px;
    margin-bottom: 10px;
    margin-right: 10px;
  }

  .overview-detail .flex .item .fl img {
    display: block;
  }

  .overview-detail .flex .item .fl .title {
    font-size: 14px;
    text-align: left;
  }

  .overview-detail .flex .item .cont {
    margin-left: 20px;
  }

  .overview-detail .flex .item .cont p {
    margin-bottom: 0;
    margin-top: 10px;
    font-size: 10px;
    color: #e54545 !important;
  }

  .overview-detail .flex .item .cont p span {
    font-size: 28px;
    font-weight: 700;
  }

  .overview-detail .flex .item .cont .warn {
    font-size: 12px;
  }

  .overview-detail .flex .item .cont .warn i {
    color: red;
  }

  .overview-detail ul li {
    width: 100%;
    line-height: 30px;
    background: #f3f3f3;
    font-size: 12px;
    padding-left: 10px;
  }

  .fl {
    float: left;
  }

  .el-radio-group {
    margin-right: -1px !important;
  }

  .el-date-picker {
    margin-left: -1px !important;
  }

  .el-select .el-tag {
    margin-right: 5px;
    float: none;
    white-space: nowrap;
  }

  .mb15 {
    margin-top: 8px !important;
    margin-bottom: 15px !important;
  }

  .base-el-col {
    padding-left: 8px;
    // padding-right: -8px;
    // border: 1px solid #f1f2f3;
  }

  .title {
    color: #222;
    font-weight: bold;
  }

  .title .span {
    font-size: 12px !important;
    font-family: PingFangSC-Medium, PingFang SC !important;
    margin-bottom: 20px !important;
    margin-top: 20px !important;
  }

  .el-card {
    overflow: hidden;
  }

  .el-card .el-table {
    width: 100%;
    max-width: 100%;
  }

  .security-dashboard {
    overflow-y: auto;
    background-color: #fff;
    margin-left: -20px;
  }

  ::v-deep .el-table th {
    background-color: #eff3f9;
  }

  ::v-deep .el-table td .cell {
    font-size: 13px;
  }

  ::v-deep .el-table th .cell {
    font-size: 15px !important;
    font-weight: normal;
    color: #606266;
  }
</style>
