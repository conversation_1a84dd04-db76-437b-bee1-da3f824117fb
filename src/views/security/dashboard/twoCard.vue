<template>
  <el-row :gutter="0" style="padding-left: 8px">
    <el-card>
      <div class="title">
        <span>实时态势</span>
      </div>
      <div style="margin-bottom: 15px">
        <el-col :span="24" class="base-el-col">
          <el-row
            align="top"
            class="left-column"
            justify="center"
            style="display: flex; flex-wrap: nowrap"
          >
            <el-col :span="12">
              <el-card class="small-box" shadow="never">
                <div class="card-content">
                  <div
                    style="
                      display: inline-block;
                      margin-right: 20px;
                      vertical-align: middle;
                    "
                  >
                    <img
                      src="https://imgcache.qq.com/qcloud/tcloud_dtc/static/static_source_business/f28a68f1-3813-42e0-b097-c33eca704c46.svg"
                      style="width: 60px; height: 60px"
                    />
                  </div>
                  <div
                    class="card-stats"
                    style="display: inline-block; vertical-align: middle"
                  >
                    <div
                      style="
                        color: rgb(136, 136, 136);
                        margin-bottom: 10px;
                        font-size: 13px;
                      "
                    >
                      疑似Bot IP数 | 弹窗验证IP数
                    </div>
                    <span class="frontend-text-success">
                      {{ suspDataCount ? suspDataCount : 0 }}
                    </span>
                    <span
                      style="
                        display: inline-block;
                        border-left: 1px solid rgb(216, 216, 216);
                        height: 30px;
                        margin-left: 5px;
                        margin-right: 10px;
                      "
                    ></span>
                    <span class="" style="font-size: 40px; margin-right: 5px">
                      {{ captchaPassDataCount ? captchaPassDataCount : 0 }}
                    </span>
                    <span>个</span>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card class="small-box" shadow="never">
                <div class="card-content">
                  <div
                    style="
                      display: inline-block;
                      margin-right: 20px;
                      vertical-align: middle;
                    "
                  >
                    <img
                      src="https://imgcache.qq.com/qcloud/tcloud_dtc/static/static_source_business/2db65906-ddf9-468a-9dc0-64254ed04572.svg"
                      style="width: 60px; height: 60px"
                    />
                  </div>
                  <div
                    class="card-stats"
                    style="display: inline-block; vertical-align: middle"
                  >
                    <div
                      style="
                        color: rgb(136, 136, 136);
                        margin-bottom: 10px;
                        font-size: 13px;
                      "
                    >
                      other
                    </div>
                    <span class="frontend-text-success">None</span>
                    <span>个</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </div>
    </el-card>
  </el-row>
</template>

<script>
  import EChart from '@/views/security/component/echartsModel.vue'
  import { getSuspIpList } from '@/api/security'
  import { reject } from 'lodash'

  export default {
    name: 'TwoCard',
    // 由于EChart组件未被使用，暂时注释掉组件注册
    // components: { EChart },
    data() {
      return {
        suspDataCount: null,
        captchaPassDataCount: null,
        suspTableLoading: false,
      }
    },
    mounted() {
      this.initChartOption()
      this.getSuspIpList()
    },
    methods: {
      initChartOption() {
        return {
          xAxis: {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          yAxis: {
            type: 'value',
          },
          series: [
            {
              data: [120, 200, 150, 80, 70, 110, 130],
              type: 'bar',
            },
          ],
        }
      },
      async getSuspIpList(refresh = true, timeoutMs = 5000) {
        if (refresh) {
          this.suspTableLoading = true
          try {
            const fetchPromise = getSuspIpList()
            const timeoutPromise = new Promise((resolve) =>
              setTimeout(resolve, 200)
            )

            const [{ captcha_count, susp_count }] = await Promise.all([
              fetchPromise,
              timeoutPromise,
            ])
            this.suspDataCount = susp_count
            this.captchaPassDataCount = captcha_count
          } catch (error) {
            console.error('获取数据异常')
            this.$baseMessage(`Error fetching data: ${error}`, 'error')
          } finally {
            this.suspTableLoading = false
          }
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .title {
    color: #222;
    font-weight: bold;
  }

  .base-el-col {
    padding-left: 8px;
    // border: 1px solid #f1f2f3;
  }

  .left-column {
    display: flex;
    height: auto;
    margin-top: 20px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .el-col {
      padding-left: 8px;
    }
  }

  .center-column {
    margin-bottom: 20px;
    padding-left: 8px;
    padding-right: -8px;
  }

  .right-column {
    margin-bottom: 20px;
    padding-left: 8px;
    padding-right: -8px;
  }

  .center-column img {
    width: 100%;
    height: 300px;
    object-fit: contain;
  }

  .small-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-content {
    display: flex;
    align-items: center;
  }

  .card-stats {
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px;
  }

  .card-stats .frontend-text-success {
    color: #0abf5b !important;
    font-size: 40px;
    margin-right: 5px;
  }

  .suggestions {
    font-size: 14px;
  }

  .suggestions ul {
    list-style: none;
    padding: 0;
  }

  .suggestions li {
    margin-bottom: 10px;
  }

  .suggestions a {
    color: #409eff;
    text-decoration: none;
  }
</style>
