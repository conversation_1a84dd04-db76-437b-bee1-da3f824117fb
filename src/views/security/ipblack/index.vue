<script>
  import Edit from './component/ipBlackEditor.vue'
  import { fetchIpBlacklist } from '@/api/ipblacklist'

  export default {
    name: 'IpBlackIndex',
    components: {
      Edit,
    },
    data() {
      return {
        queryForm: {
          pageIndex: 1,
          pageSize: 10,
          keyword: '',
          n_status: undefined, // 状态过滤参数
        },
        ipBlacklistData: [], // 存储黑名单数据
        totalCount: 0,
        tableLoading: false,
        lastStatus: undefined, // 用于追踪上次筛选状态
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },
    watch: {
      queryForm: {
        handler() {
          this.refresh()
        },
        deep: true,
      },
    },
    created() {
      this.refresh()
    },
    methods: {
      // 处理清除状态筛选
      handleClearStatus() {
        this.queryForm.n_status = undefined
        this.queryForm.pageIndex = 1
        this.queryForm.pageSize = 10
        this.refresh()
      },

      // 获取IP黑名单数据
      async getIpBlacklist() {
        try {
          this.tableLoading = true
          // 明确处理undefined状态为不传该参数
          const params = {
            ...this.queryForm,
            n_status:
              this.queryForm.n_status === undefined
                ? undefined
                : this.queryForm.n_status,
          }
          const { data, totalCount } = await fetchIpBlacklist(params)
          this.ipBlacklistData = data || []
          this.totalCount = totalCount || 0
        } catch (error) {
          this.$baseMessage(`获取数据失败: ${error}`, 'error')
        } finally {
          this.tableLoading = false
        }
      },

      refresh() {
        if (this.queryForm.n_status !== this.lastStatus) {
          this.queryForm.pageIndex = 1
          this.lastStatus = this.queryForm.n_status
        }
        this.getIpBlacklist()
      },

      // 打开编辑对话框
      handleEdit(row) {
        console.log('Edit button clicked', row) // 调试按钮点击
        console.log('Edit ref:', this.$refs.edit) // 调试ref绑定
        if (this.$refs.edit) {
          console.log('Edit component exists')
          this.$refs.edit.showEdit(row)
          // 添加延迟检查对话框状态
          setTimeout(() => {
            console.log('Dialog visible state:', this.$refs.edit.visible)
          }, 100)
        } else {
          console.error('Edit component ref not found')
        }
      },

      // 状态标签类型映射
      getTagType(status) {
        switch (status) {
          case 0:
            return 'success' // 有效
          case 1:
            return 'info' // 无效
          case 2:
            return 'warning' // 永不过期
          default:
            return ''
        }
      },

      // 状态标签文本映射
      getStatusLabel(status) {
        const statusMap = {
          0: '有效',
          1: '无效',
          2: '永不过期',
        }
        return statusMap[status] || status
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.refresh()
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
        this.refresh()
      },
    },
  }
</script>

<template>
  <div class="security-dashboard">
    <!-- 编辑组件应放置在顶层以确保 ref 正确绑定 -->

    <div class="title">
      <b></b>
      IP黑名单管理
    </div>

    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card>
          <div class="header">
            <el-button type="primary" @click="handleEdit">新建黑名单</el-button>
            <el-input
              v-model="queryForm.keyword"
              class="search-input"
              clearable
              placeholder="IP地址/来源/备注"
              @change="refresh"
            />
            <el-select
              v-model="queryForm.n_status"
              placeholder="状态筛选"
              clearable
              @change="refresh"
              @clear="handleClearStatus"
            >
              <el-option label="有效" :value="0" />
              <el-option label="无效" :value="1" />
              <el-option label="永不过期" :value="2" />
            </el-select>
          </div>
          <div class="content-container">
            <div class="left-content">
              <el-table
                v-loading="tableLoading"
                :border="true"
                :data="ipBlacklistData"
                element-loading-text="加载中..."
                size="mini"
              >
                <el-table-column
                  label="IP地址"
                  prop="c_ip_addr"
                  min-width="120"
                />

                <el-table-column label="状态" prop="n_status" min-width="80">
                  <template #default="scope">
                    <el-tag :type="getTagType(scope.row.n_status)">
                      {{ getStatusLabel(scope.row.n_status) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column
                  label="来源"
                  prop="c_ip_source"
                  min-width="100"
                />

                <el-table-column label="备注" prop="c_remark" min-width="130" />

                <el-table-column
                  label="创建时间"
                  prop="t_create_time"
                  min-width="100"
                />

                <el-table-column header-align="center" label="操作" width="100">
                  <template #default="scope">
                    <el-button
                      plain
                      size="mini"
                      type="text"
                      @click="handleEdit(scope.row)"
                    >
                      编辑
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="queryForm.pageIndex"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :total="totalCount"
                background
                style="margin-top: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </el-card>
        <edit ref="edit" @fetch-data="refresh"></edit>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .title {
    font-size: 18px;
    font-weight: bold;
    height: 50px;
    background: #fff;
    line-height: 50px;
    overflow: hidden;
    margin: -20px -20px 5px;
  }

  .title b {
    width: 3px;
    height: 21px;
    background: #000;
    display: inline-block;
    line-height: 20px;
    position: relative;
    top: 4px;
    margin: 0px 9px 0px 10px;
  }

  .mb15 {
    margin: 8px -20px 15px -20px;
  }

  .base-el-col {
    padding-left: 0px;
    padding-right: -8px;
    border: 5px solid #ffffff;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    > * {
      margin-right: 10px;
    }
  }

  .search-input {
    max-width: 300px;
  }

  ::v-deep .el-table th {
    background-color: #eff3f9;
  }

  ::v-deep .el-table td .cell {
    font-size: 13px;
  }

  .content-container {
    display: flex;
    margin-top: 15px;
  }

  .left-content {
    flex: 3;
    margin-right: 15px;
    margin-top: 8px;
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
