<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="600px"
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      size="small"
    >
      <el-form-item label="IP地址" prop="c_ip_addr">
        <el-input v-model="formData.c_ip_addr" />
      </el-form-item>

      <el-form-item label="来源" prop="c_ip_source">
        <el-input v-model="formData.c_ip_source" />
      </el-form-item>

      <el-form-item label="备注" prop="c_remark">
        <el-input v-model="formData.c_remark" type="textarea" :rows="3" />
      </el-form-item>

      <el-form-item label="状态" prop="n_status">
        <el-select v-model="formData.n_status" placeholder="请选择状态">
          <el-option label="有效" :value="0" />
          <el-option label="无效" :value="1" />
          <el-option label="永不过期" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item
        v-if="formData.n_status === 0"
        label="封禁时间"
        prop="expire_time"
      >
        <el-date-picker
          v-model="formData.expire_time"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="选择封禁截止时间"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
  import { saveIpBlacklist } from '@/api/ipblacklist'

  export default {
    name: 'IpBlackEditor',
    data() {
      return {
        visible: false,
        formData: {
          n_id: null,
          c_ip_addr: '',
          c_ip_source: '',
          c_remark: '',
          n_status: 0,
          n_duration: 10, // 默认10分钟，仅用于后端提交
          expire_time: '',
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() // 不允许选择当前时间之前的日期
          },
          shortcuts: [
            {
              text: '10分钟',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 10 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
            {
              text: '30分钟',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 30 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
            {
              text: '1小时',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 60 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
            {
              text: '12小时',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 12 * 60 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
            {
              text: '1天',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 24 * 60 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
            {
              text: '3天',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 3 * 24 * 60 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
            {
              text: '7天',
              onClick(picker) {
                const date = new Date()
                date.setTime(date.getTime() + 7 * 24 * 60 * 60 * 1000)
                picker.$emit('pick', date)
              },
            },
          ],
        },
        rules: {
          c_ip_addr: [
            { required: true, message: '请输入IP地址', trigger: 'blur' },
            {
              pattern:
                /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
              message: 'IP地址格式不正确',
              trigger: 'blur',
            },
          ],
          n_status: [
            { required: true, message: '请选择状态', trigger: 'change' },
          ],
          expire_time: [
            {
              required: true,
              message: '请选择封禁截止时间',
              trigger: 'change',
              validator: (rule, value, callback) => {
                if (this.formData.n_status !== 2 && !value) {
                  callback(new Error('请选择封禁截止时间'))
                } else {
                  callback()
                }
              },
            },
          ],
        },
      }
    },
    computed: {
      title() {
        return this.formData.n_id ? '编辑黑名单' : '新增黑名单'
      },
    },
    watch: {
      'formData.n_status'(newVal) {
        if (newVal === 2) {
          this.formData.expire_time = ''
        } else if (!this.formData.expire_time) {
          // 当状态不是永不过期且没有设置截止时间时，默认设置为10分钟后
          const date = new Date()
          date.setTime(date.getTime() + 10 * 60 * 1000)
          this.formData.expire_time = this.formatDate(date)
        }
      },
    },
    methods: {
      showEdit(row = {}) {
        const defaultData = this.$options.data().formData
        this.formData = Object.assign({}, defaultData, row)

        // 如果是编辑模式且没有设置截止时间，但有持续时间
        if (
          this.formData.n_status !== 2 &&
          !this.formData.expire_time &&
          this.formData.n_duration
        ) {
          // 根据n_duration计算expire_time
          const date = new Date()
          date.setTime(date.getTime() + this.formData.n_duration * 60 * 1000)
          this.formData.expire_time = this.formatDate(date)
        }

        this.visible = true
      },
      submitForm() {
        this.$refs.formRef.validate(async (valid) => {
          if (valid) {
            try {
              const formDataToSubmit = { ...this.formData }

              // 处理封禁时间
              if (this.formData.n_status !== 2 && this.formData.expire_time) {
                // 获取当前选择的快捷时间对应的分钟数
                const selectedTime = this.getSelectedTimeFromExpireTime(
                  this.formData.expire_time
                )
                if (selectedTime > 0) {
                  // 如果匹配到预设的快捷时间，直接使用对应的分钟数
                  formDataToSubmit.n_duration = selectedTime
                } else {
                  // 否则计算截止时间与当前时间的差值（分钟）
                  const now = new Date()
                  const expireTime = new Date(this.formData.expire_time)
                  const diffMinutes = Math.floor(
                    (expireTime - now) / (60 * 1000)
                  )

                  // 确保时间差为正数
                  if (diffMinutes <= 0) {
                    this.$baseMessage('封禁截止时间必须晚于当前时间', 'error')
                    return
                  }

                  // 更新n_duration为计算出的分钟差值
                  formDataToSubmit.n_duration = diffMinutes
                }
              } else if (this.formData.n_status === 2) {
                // 永不过期状态，设置n_duration为-2表示永久
                formDataToSubmit.n_duration = -2
              }

              // 移除expire_time字段，后端不需要
              delete formDataToSubmit.expire_time

              console.log('formDataToSubmit:', formDataToSubmit)
              const response = await saveIpBlacklist(formDataToSubmit)
              if (response.code === 200) {
                this.$baseMessage(response.msg, 'success')
                this.visible = false
                this.$emit('fetch-data')
              } else {
                this.$baseMessage(response.msg || '保存失败', 'error')
              }
            } catch (error) {
              this.$baseMessage('保存失败: ' + error.message, 'error')
            }
          }
        })
      },
      handleClosed() {
        this.$refs.formRef.resetFields()
      },
      formatDate(date) {
        const pad = (num) => (num < 10 ? '0' + num : num)

        const year = date.getFullYear()
        const month = pad(date.getMonth() + 1)
        const day = pad(date.getDate())
        const hours = pad(date.getHours())
        const minutes = pad(date.getMinutes())
        const seconds = pad(date.getSeconds())

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      },
      // 根据expire_time判断是否是通过快捷方式选择的时间
      getSelectedTimeFromExpireTime(expireTimeStr) {
        // 定义快捷选项对应的分钟数
        const shortcutMinutes = [10, 30, 60, 720, 1440, 4320, 10080]

        try {
          // 解析expire_time字符串为Date对象
          const expireTime = new Date(expireTimeStr)
          // 获取当前时间
          const now = new Date()

          // 计算时间差（毫秒）
          const diffMs = expireTime.getTime() - now.getTime()

          // 将时间差转换为分钟
          const diffMinutes = Math.round(diffMs / (60 * 1000))

          // 检查是否接近预设的快捷时间（允许5秒误差）
          for (const minutes of shortcutMinutes) {
            if (Math.abs(diffMinutes - minutes) < 1) {
              return minutes
            }
          }

          // 如果不匹配任何预设时间，返回-1
          return -1
        } catch (e) {
          return -1
        }
      },
    },
  }
</script>

<style scoped>
  .dialog-footer {
    text-align: right;
  }
</style>
