<script>
  import { postFlowctrlRule } from '@/api/security'

  export default {
    name: 'RestrictedUrisEditor',
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          limit_id: '',
          key: null,
          value: null,
        },
        rules: {
          limit_id: [
            { required: true, message: '请输入：限流ID', trigger: 'blur' },
            {
              type: 'number',
              validator: this.validateNumber,
              message: '限流 ID必须为非负整数',
              trigger: 'blur',
            },
          ],
          key: [
            { required: true, message: '请输入：流控目标', trigger: 'blur' },
          ],
          value: [
            { required: true, message: '请输入：流控目标值', trigger: 'blur' },
          ],
        },
      }
    },

    computed: {},
    watch: {
      filteredData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
    },

    created() {
      this.fetchData()
    },

    methods: {
      validateNumber(rule, value, callback) {
        if (value === '') {
          callback(new Error('请输入：限流ID'))
        } else if (!Number.isInteger(Number(value)) || value < 0) {
          callback(new Error('限流ID必须为非负数字'))
        } else {
          callback()
        }
      },
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true
          this.formData = {
            ...row,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            const formDataToSend = {
              ...this.formData,
            }
            const response = await postFlowctrlRule(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item
            class="label-right-align"
            label="限流ID"
            prop="limit_id"
          >
            <el-input
              v-model.trim="formData.limit_id"
              autocomplete="off"
              clearable
              type="text"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item class="label-right-align" label="流控目标" prop="key">
        <el-input
          v-model.trim="formData.key"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item class="label-right-align" label="流控值" prop="value">
        <el-input
          v-model.trim="formData.value"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :readonly="isedit" type="primary" @click="save">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
</style>
