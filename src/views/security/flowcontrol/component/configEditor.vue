<script>
  import { postFlowctrlConfig } from '@/api/security'

  export default {
    name: 'IpWhileEditor',
    props: {
      choiceActive: {
        type: Object,
        required: true,
        default: () => ({}),
      },
    },
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          rule_name: '',
          active: null,
          request_limit: '',
          time_period: '',
          flow_key: '',
          description: '',
        },
        rules: {
          active: [{ required: true, message: '请选择状态', trigger: 'blur' }],
          rule_name: [
            { required: true, message: '请输入目标URI', trigger: 'blur' },
            {
              type: 'string',
              validator: this.validateString,
              message: '规则名称必须为字符串',
              trigger: 'blur',
            },
          ],
          request_limit: [
            { required: true, message: '请输入请求限制', trigger: 'blur' },
            {
              type: 'string',
              validator: this.validateNumber,
              message: '请求限制必须为非负整数',
              trigger: 'blur',
            },
          ],
          time_period: [
            { required: true, message: '请输入时间段', trigger: 'blur' },
            {
              type: 'number',
              validator: this.validateNumber,
              message: '时间段必须为非负整数',
              trigger: 'blur',
            },
          ],
          flow_key: [
            { required: true, message: '请输入流控key', trigger: 'blur' },
            {
              type: 'string',
              validator: this.validateString,
              message: '流控key必须为字符串',
              trigger: 'blur',
            },
          ],
        },
      }
    },

    computed: {},
    watch: {
      filteredData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
    },

    created() {
      this.fetchData()
    },

    methods: {
      validateNumber(rule, value, callback) {
        if (value === '') {
          callback(new Error('请输入时间段'))
        } else if (!Number.isInteger(Number(value)) || value < 0) {
          callback(new Error('时间段必须为数字'))
        } else {
          callback()
        }
      },
      validateString(rule, value, callback) {
        if (typeof value !== 'string') {
          callback(new Error('流控key必须为字符串'))
        } else {
          callback()
        }
      },
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true
          this.formData = {
            ...row,
          }
          if (typeof this.formData.active === 'number') {
            this.formData.active = this.formData.active.toString()
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            const formDataToSend = {
              ...this.formData,
            }
            const response = await postFlowctrlConfig(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-form-item class="label-right-align" label="状态" prop="active">
        <el-select
          v-model.trim="formData.active"
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="(label, value) in choiceActive"
            :key="value"
            :label="label"
            :value="value"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item class="label-right-align" label="目标URI" prop="rule_name">
        <el-input
          v-model.trim="formData.rule_name"
          :disabled="disabled"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>

      <el-form-item
        class="label-right-align"
        label="请求限制"
        prop="request_limit"
      >
        <el-input
          v-model.trim="formData.request_limit"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>

      <el-form-item class="label-right-align" label="时间段" prop="time_period">
        <el-input
          v-model.trim="formData.time_period"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>

      <el-form-item class="label-right-align" label="流控key" prop="flow_key">
        <el-input
          v-model.trim="formData.flow_key"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>

      <el-form-item class="label-right-align" label="描述" prop="description">
        <el-input
          v-model.trim="formData.description"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :readonly="isedit" type="primary" @click="save">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
</style>
