<script>
  import { getFlowctrlConfig } from '@/api/security'
  import { reject } from 'lodash'
  import Edit from './component/configEditor.vue'

  export default {
    name: 'FlowctrlConfig',
    components: { Edit },
    props: {
      title: {
        type: String,
        required: true,
        default: '流量控制配置',
      },
    },
    data() {
      return {
        queryForm: {
          pageIndex: 1,
          pageSize: 10,
          keyword: '',
        },
        flowctrlConfigData: [],
        totalCount: 0,
        tableLoading: false,
        choiceActive: null,
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },
    watch: {
      queryForm: {
        handler() {
          this.refresh()
        },
        deep: true,
      },
    },
    created() {
      this.refresh()
    },
    methods: {
      async getflowctrlConfigData(timeoutMs = 5000) {
        try {
          this.tableLoading = true
          const { data, totalCount, CHOICE_ACTIVE } = await getFlowctrlConfig(
            this.queryForm
          )
          if (data) {
            this.flowctrlConfigData = data
            this.choiceActive = CHOICE_ACTIVE
            this.totalCount = totalCount
            this.tableLoading = false
          } else {
            this.$baseMessage('暂无数据', 'error')
          }
        } catch (error) {
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
          this.tableLoading = false
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
        this.tableLoading = false
      },

      refresh() {
        this.getflowctrlConfigData()
      },

      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },

      getTagType(value) {
        switch (value) {
          case 1:
            return 'success'
          case 2:
            return 'warning'
          case 0:
            return 'danger'
          default:
            return ''
        }
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.refresh()
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
        this.refresh()
      },

      handleDelete(row) {
        return null
      },
    },
  }
</script>

<template>
  <div class="security-dashboard">
    <div class="title">
      <b></b>
      {{ title }}
    </div>

    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card>
          <div class="header">
            <el-button
              style="background-color: #006eff !important"
              type="primary"
              @click="handleEdit"
            >
              新建配置
            </el-button>
            <el-input
              v-model="queryForm.keyword"
              class="search-input"
              clearable
              placeholder="请输入关键字"
              @change="refresh()"
            />
          </div>
          <div class="content-container">
            <div class="left-content">
              <el-table
                v-loading="tableLoading"
                :border="true"
                :data="flowctrlConfigData"
                element-loading-text="加载中...."
                size="mini"
              >
                <el-table-column
                  label="目标URI"
                  min-width="150"
                  prop="rule_name"
                />
                <el-table-column label="状态" min-width="50" prop="active">
                  <template #default="scope">
                    <el-tag :type="getTagType(scope.row.active)">
                      {{ choiceActive[scope.row.active] }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="描述"
                  min-width="100"
                  prop="description"
                />

                <el-table-column
                  label="请求限制"
                  min-width="60"
                  prop="request_limit"
                />
                <el-table-column
                  label="时间段"
                  min-width="60"
                  prop="time_period"
                />

                <el-table-column
                  label="添加时间"
                  min-width="150"
                  prop="date_added"
                />

                <el-table-column
                  header-align="center"
                  label="操作"
                  max-width="100"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <div class="operation-buttons">
                      <el-button
                        plain
                        size="mini"
                        type="primary"
                        @click="handleEdit(scope.row)"
                      >
                        编辑
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                :current-page="queryForm.pageIndex"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :total="totalCount"
                background
                style="margin-top: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
          </div>
        </el-card>
      </el-col>
      <edit
        ref="edit"
        :choice-active="choiceActive"
        @fetch-data="refresh"
      ></edit>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .title {
    font-size: 18px;
    font-weight: bold;
    height: 50px;
    background: #fff;
    line-height: 50px;
    overflow: hidden;
    margin: -20px -20px 5px;
  }

  .title b {
    width: 3px;
    height: 21px;
    background: #000;
    display: inline-block;

    line-height: 20px;
    position: relative;
    top: 4px;
    margin: 0px 9px 0px 10px;
  }

  .mb15 {
    margin: 8px -20px 15px -20px;
  }

  .base-el-col {
    padding-left: 0px;
    padding-right: -8px;
    border: 1px solid #f1f2f3;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-input {
    max-width: 300px;
  }

  .el-card .el-table {
    width: 100%;
    max-width: 100%;
  }

  ::v-deep .el-table th {
    background-color: #eff3f9;
  }

  ::v-deep .el-table td .cell {
    font-size: 13px;
  }

  .content-container {
    display: flex;
    margin-top: 15px;
  }

  .left-content {
    flex: 3;
    margin-right: 15px;
    margin-top: 8px;
  }

  .right-content {
    flex: 1;
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .operation-buttons .el-button {
    margin: 0 4px;
  }
</style>
