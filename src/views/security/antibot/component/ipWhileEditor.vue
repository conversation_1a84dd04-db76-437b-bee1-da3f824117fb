<script>
  import { postBotIpWhiteList } from '@/api/security'

  export default {
    name: 'IpWhileEditor',
    props: {
      aliasTagName: {
        type: Object,
        required: true,
        default: () => ({}),
      },
    },
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          ip_address: '',
          active: null,
          description: '',
        },
        rules: {
          active: [{ required: true, message: '请选择状态', trigger: 'blur' }],
          ip_address: [
            { required: true, message: '请输入IP地址', trigger: 'blur' },
          ],
        },
      }
    },

    computed: {},
    watch: {
      // 监听 formData.name 的变化
      filteredData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
    },

    created() {
      this.fetchData()
    },

    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true
          this.formData = {
            ...row,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            const formDataToSend = {
              ...this.formData,
            }
            const response = await postBotIpWhiteList(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item class="label-right-align" label="状态" prop="active">
            <el-select
              v-model="formData.active"
              :disabled="disabled"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="(key, value) in aliasTagName"
                :key="key"
                :label="key"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item class="label-right-align" label="IP" prop="ip_address">
        <el-input
          v-model.trim="formData.ip_address"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item class="label-right-align" label="描述" prop="description">
        <el-input
          v-model.trim="formData.description"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :readonly="isedit" type="primary" @click="save">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .custom-label {
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
</style>
