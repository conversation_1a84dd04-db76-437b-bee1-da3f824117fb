<script>
  import { postSuspIp } from '@/api/security'

  export default {
    name: 'IpAddressEditor',
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          ip_address: '',
          active: 1,
          ttl: '',
        },
        rules: {
          ip_address: [
            { required: true, message: '请输入IP地址', trigger: 'blur' },
          ],
          active: [{ required: true, message: '请选择状态', trigger: 'blur' }],
          ttl: [{ required: true, message: '请选择 ttl', trigger: 'blur' }],
        },
        valueOptions: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
        ttlOptions: [
          { label: '1小时', value: 3600 },
          { label: '2小时', value: 7200 },
          { label: '永久', value: -1 },
        ],
      }
    },

    created() {
      this.fetchData()
    },

    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true
          this.formData = {
            ...row,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg

            const body = {
              ...this.formData,
            }
            const response = await postSuspIp(body)
            msg = response.message
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-form-item class="label-right-align" label="IP" prop="ip_address">
        <el-input
          v-model.trim="formData.ip_address"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-row>
        <el-col :span="12" class="grid-cell">
          <el-form-item class="label-right-align" label="状态" prop="active">
            <el-select
              v-model="formData.active"
              :disabled="disabled"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="option in valueOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item class="label-right-align" label="过期时间" prop="ttl">
        <el-select
          v-model="formData.ttl"
          :disabled="disabled"
          clearable
          filterable
          placeholder="请选择"
        >
          <el-option
            v-for="option in ttlOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :readonly="isedit" type="primary" @click="save">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  div.tab-container {
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .custom-label {
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
</style>
