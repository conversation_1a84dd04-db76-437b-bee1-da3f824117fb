<script>
  import { getBotRestrictedUris, postBotRestrictedUris } from '@/api/security'
  import { reject } from 'lodash'
  import Edit from '@/views/security/antibot/component/restrictedUrisEditor.vue'

  export default {
    name: 'RestrictedUris',
    components: { Edit },
    props: {
      title: {
        type: String,
        default: '受限URI管理',
        requird: true,
      },
    },
    data() {
      return {
        queryForm: {
          pageIndex: 1,
          pageSize: 10,
          keyword: '',
        },
        restrictedUrisData: [],
        totalCount: 0,
        tableLoading: false,
        aliasTagName: {
          1: '启用',
          2: '禁用',
          0: '删除',
        },
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },
    watch: {
      queryForm: {
        handler() {
          this.refresh()
        },
        deep: true,
      },
    },

    created() {
      this.refresh()
    },
    methods: {
      async getRestrictedUrisData(timeoutMs = 5000) {
        try {
          this.tableLoading = true
          const { data, totalCount } = await getBotRestrictedUris(
            this.queryForm
          )
          if (data) {
            this.restrictedUrisData = data
            this.totalCount = totalCount
            this.tableLoading = false
          } else {
            this.$baseMessage('暂无数据', 'error')
          }
        } catch (error) {
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
          this.tableLoading = false
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
        this.tableLoading = false
      },
      async updateActive(row, newStatus, timeoutMs = 5000) {
        row.active = newStatus

        try {
          const response = await postBotRestrictedUris(row)
          this.$baseMessage(
            `状态更新为: ${this.aliasTagName[newStatus]}`,
            'success'
          )
        } catch (error) {
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
        this.refresh()
      },

      refresh() {
        this.getRestrictedUrisData()
      },

      handleEdit(row) {
        if (row.uri_id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      tableColumns(dataObject) {
        if (dataObject && typeof dataObject === 'object') {
          return Object.keys(dataObject).filter(
            (key) => key !== 'uri_id' && !key.includes('time')
          )
        }
        return []
      },
      getColumnLabel(key) {
        switch (key) {
          case 'active':
            return '状态'
          case 'description':
            return '描述'
          case 'uri':
            return 'uri'
          case 'date_added':
            return '添加时间'
        }
      },
      getTagType(value) {
        switch (value) {
          case 1:
            return 'success'
          case 2:
            return 'warning'
          case 0:
            return 'info'
          default:
            return ''
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.refresh()
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
        this.refresh()
      },
    },
  }
</script>

<template>
  <div class="security-dashboard">
    <div class="title">
      <b></b>
      {{ title }}
    </div>

    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card>
          <div class="header">
            <el-button
              style="background-color: #006eff !important"
              type="primary"
              @click="handleEdit"
            >
              新建API
            </el-button>
            <el-input
              v-model="queryForm.keyword"
              class="search-input"
              clearable
              placeholder="请输入关键字"
              @change="refresh()"
            />
          </div>
          <div class="content-container">
            <div class="left-content">
              <el-table
                v-loading="tableLoading"
                :border="true"
                :data="restrictedUrisData"
                element-loading-text="加载中...."
                size="mini"
              >
                <el-table-column label="URI" min-width="150" prop="uri" />
                <el-table-column label="状态" min-width="50" prop="active">
                  <template #default="scope">
                    <el-tag :type="getTagType(scope.row.active)">
                      {{ aliasTagName[scope.row.active] }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="描述"
                  min-width="60"
                  prop="description"
                />
                <el-table-column
                  label="添加时间"
                  min-width="80"
                  prop="date_added"
                />
                <el-table-column
                  header-align="center"
                  label="操作"
                  min-width="40"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <div class="operation-buttons">
                      <el-popconfirm
                        v-if="scope.row.active === 1"
                        placement="top"
                        title="确定要关闭吗？"
                        @confirm="updateActive(scope.row, 2)"
                      >
                        <template #reference>
                          <el-button size="mini" type="warning">关闭</el-button>
                        </template>
                      </el-popconfirm>
                      <el-popconfirm
                        v-else-if="scope.row.active === 2"
                        placement="top"
                        title="确定要开启吗？"
                        @confirm="updateActive(scope.row, 1)"
                      >
                        <template #reference>
                          <el-button plain size="mini" type="success">
                            开启
                          </el-button>
                        </template>
                      </el-popconfirm>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="queryForm.pageIndex"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :total="totalCount"
                background
                style="margin-top: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>

            <!-- <div class="right-content">
              <el-empty description="暂无数据" />
            </div> -->
          </div>
        </el-card>
      </el-col>
      <edit
        ref="edit"
        :alias-tag-name="aliasTagName"
        @fetch-data="refresh"
      ></edit>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .title {
    font-size: 18px;
    font-weight: bold;
    height: 50px;
    background: #fff;
    line-height: 50px;
    overflow: hidden;
    margin: -20px -20px 5px;
  }

  .title b {
    width: 3px;
    height: 21px;
    background: #000;
    display: inline-block;

    line-height: 20px;
    position: relative;
    top: 4px;
    margin: 0px 9px 0px 10px;
  }

  .mb15 {
    margin: 8px -20px 15px -20px;
  }

  .base-el-col {
    padding-left: 0px;
    padding-right: -8px;
    boder: 1px solid #f1f2f3;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-input {
    max-width: 300px;
  }

  .el-card .el-table {
    width: 100%;
    max-width: 100%;
  }

  ::v-deep .el-table th {
    background-color: #eff3f9;
  }

  ::v-deep .el-table td .cell {
    font-size: 13px;
  }

  .content-container {
    display: flex;
    margin-top: 15px;
  }

  .left-content {
    flex: 3;
    margin-right: 15px;
    margin-top: 8px;
  }

  .right-content {
    flex: 1;
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .operation-buttons .el-button {
    margin: 0 5px;
  }
</style>
