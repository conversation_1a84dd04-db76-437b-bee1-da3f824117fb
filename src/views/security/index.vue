<template>
  <div class="security-center-container">
    <div :class="{ 'is-collapsed': isCollapse }" class="side-nav">
      <div class="nav-header">
        <h3 class="nav-title">反爬与流控</h3>
        <div class="collapse-btn" @click="toggleCollapse">
          <svg
            :style="{ transform: isCollapse ? 'rotate(180deg)' : 'none' }"
            class="icon"
            viewBox="0 0 1024 1024"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M153.6 166.4m38.4 0l640 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-640 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M153.6 780.8m38.4 0l640 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-640 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M409.6 371.2m38.4 0l384 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-384 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M409.6 576m38.4 0l384 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-384 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M180.8896 545.28a25.6 25.6 0 0 1 0-40.96l136.5504-102.4a25.6 25.6 0 0 1 40.96 20.48v204.8a25.6 25.6 0 0 1-40.96 20.48z"
              fill="#7A88FE"
            ></path>
          </svg>
        </div>
      </div>

      <el-menu
        :collapse="isCollapse"
        :default-active="activeTab"
        active-text-color="#7A88FE"
        background-color="#fff"
        class="el-menu-vertical"
        text-color="#303133"
        @select="handleSelect"
      >
        <el-menu-item index="dashboard">
          <i class="el-icon-monitor"></i>
          <span slot="title">概览</span>
        </el-menu-item>

        <el-menu-item index="baseconfig">
          <i class="el-icon-film"></i>
          <span slot="title">基础配置</span>
        </el-menu-item>

        <el-menu-item index="ipblacklist">
          <span class="custom-menu-icon" v-html="ipBlackIcon"></span>
          <span>IP黑名单</span>
        </el-menu-item>

        <el-submenu index="antibot">
          <template slot="title">
            <i class="el-icon-set-up"></i>
            <span>反爬中心</span>
          </template>
          <el-menu-item index="whitelist">
            <i class="el-icon-unlock"></i>
            <span>爬虫管理</span>
          </el-menu-item>
          <el-menu-item index="restricteduris">
            <i class="el-icon-link"></i>
            <span>核心API</span>
          </el-menu-item>
        </el-submenu>

        <el-submenu index="flowcontrol">
          <template slot="title">
            <i class="el-icon-attract"></i>
            <span>流控中心</span>
          </template>
          <el-menu-item index="flowctrlconfig">
            <span class="custom-menu-icon" v-html="flowCtrlIcon"></span>
            <span>流控配置</span>
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </div>

    <div class="content-container">
      <component :is="currentComponent" :title="title" />
    </div>
  </div>
</template>

<script>
  // import waitDoing from '@/views/security/base/waitDoing.vue'
  import DashBoard from '@/views/security/dashboard/dashboard.vue'
  import BaseIndex from '@/views/security/base/index.vue'
  import AntiBotWhitelist from '@/views/security/antibot/whitelist.vue'
  import AntiRestrictedUris from '@/views/security/antibot/restricteduris.vue'
  import FlowctrlConfig from '@/views/security/flowcontrol/flowctrlConfig.vue'
  import IpBlackIndex from '@/views/security/ipblack/index.vue'

  export default {
    name: 'SecurityIndex',
    components: {
      // waitDoing,
      DashBoard,
      BaseIndex,
      AntiBotWhitelist,
      AntiRestrictedUris,
      FlowctrlConfig,
      IpBlackIndex,
    },
    data() {
      return {
        activeTab: 'dashboard',
        isCollapse: false,
        currentComponent: DashBoard,
        title: '',
        ipBlackIcon: `<svg viewBox="0 0 1024 1024" width="24" height="18"><path d="M512 56.888889C261.688889 56.888889 56.888889 261.688889 56.888889 512s204.8 455.111111 455.111111 455.111111 455.111111-204.8 455.111111-455.111111-204.8-455.111111-455.111111-455.111111zM136.533333 512c0-204.8 170.666667-375.466667 375.466667-375.466667 91.022222 0 176.355556 34.133333 238.933333 85.333334l-108.088889 108.088889H546.133333c-34.133333 0-56.888889 22.755556-56.888889 56.888888v96.711112L403.911111 568.888889V369.777778c0-22.755556-17.066667-39.822222-39.822222-39.822222s-39.822222 17.066667-39.822222 39.822222v278.755555l-102.4 102.4C170.666667 688.355556 136.533333 603.022222 136.533333 512z m585.955556-56.888889c0 56.888889-34.133333 73.955556-96.711111 73.955556h-56.888889v-11.377778L688.355556 398.222222c22.755556 11.377778 34.133333 22.755556 34.133333 56.888889zM568.888889 409.6v-17.066667h17.066667l-17.066667 17.066667z m-56.888889 477.866667c-85.333333 0-164.977778-28.444444-233.244444-79.644445L347.022222 739.555556c5.688889 5.688889 11.377778 5.688889 22.755556 5.688888 22.755556 0 39.822222-17.066667 39.822222-39.822222V682.666667l85.333333-85.333334v113.777778c0 22.755556 17.066667 39.822222 39.822223 39.822222 22.755556 0 39.822222-17.066667 39.822222-39.822222v-119.466667h62.577778c91.022222 0 159.288889-39.822222 159.288888-136.533333 0-51.2-22.755556-85.333333-56.888888-102.4l73.955555-73.955555c45.511111 62.577778 73.955556 147.911111 73.955556 233.244444 0 204.8-170.666667 375.466667-375.466667 375.466667z" fill="currentColor"/></svg>`,
        flowCtrlIcon: `<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M597.632 917.824s-0.128 0 0 0a32 32 0 0 1-31.232-24.768L423.168 282.176 321.216 740.48a32.064 32.064 0 0 1-61.568 3.648L189.76 543.552H32.128a32.128 32.128 0 0 1 0-64.128h180.48c13.696 0 25.792 8.576 30.272 21.504l40.256 115.648 108.032-485.312a31.936 31.936 0 0 1 31.04-25.024h0.256c14.848 0 27.84 10.304 31.232 24.768l144.448 616.32 104.768-434.624a32 32 0 0 1 61.184-3.52l62.912 170.304 165.12 0.704a32.064 32.064 0 0 1-0.128 64.128h-0.128l-187.392-0.768a31.872 31.872 0 0 1-29.888-20.928l-33.984-92.16-111.68 463.168a32.448 32.448 0 0 1-31.296 24.192z" fill="currentColor"/></svg>`,
      }
    },
    watch: {
      $route: {
        immediate: true,
        handler(newRoute) {
          const { tab } = newRoute.query
          if (tab) {
            this.activeTab = tab
            this.handleSelect(tab)
          }
        },
      },
    },
    methods: {
      handleSelect(index) {
        this.activeTab = index
        switch (index) {
          case 'dashboard':
            this.currentComponent = DashBoard
            this.title = '概览'
            break
          case 'baseconfig':
            this.currentComponent = BaseIndex
            this.title = '基础配置'
            break
          case 'ipblacklist':
            this.currentComponent = IpBlackIndex
            this.title = 'IP黑名单'
            break
          case 'whitelist':
            this.currentComponent = AntiBotWhitelist
            this.title = '爬虫管理'
            break
          case 'restricteduris':
            this.currentComponent = AntiRestrictedUris
            this.title = '核心API'
            break
          case 'flowctrlconfig':
            this.currentComponent = FlowctrlConfig
            this.title = '流控配置'
            break
          default:
            this.currentComponent = DashBoard
            this.title = ''
        }

        this.$router
          .push({ query: { ...this.$route.query, tab: index } })
          .catch(() => {})
      },
      toggleCollapse() {
        this.isCollapse = !this.isCollapse
      },
    },
  }
</script>

<style lang="scss" scoped>
  .security-center-container {
    display: flex;
    height: calc(100vh - 50px);
    background-color: #fff;

    .side-nav {
      background-color: #fff;
      transition: width 0.3s;
      width: 200px;
      border-right: 1px solid #e6e6e6;
      display: flex;
      flex-direction: column;

      &.is-collapsed {
        width: 64px;

        .nav-title {
          display: none;
        }
      }

      .nav-header {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        border-bottom: 1px solid #e6e6e6;

        .nav-title {
          margin: 0;
          font-size: 16px;
          color: #303133;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .collapse-btn {
          cursor: pointer;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            width: 20px;
            height: 20px;
            transition: transform 0.3s;
          }
        }
      }

      .el-menu-vertical {
        border-right: none;
        flex: 1;

        &:not(.el-menu--collapse) {
          width: 200px;
        }
      }
    }

    .content-container {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background-color: #fff;
      margin-left: 20px;
    }
  }

  .custom-menu-icon {
    display: inline-flex;
    align-items: center;
    margin-right: 5px;

    svg {
      width: 14px;
      height: 14px;
      fill: currentColor;
      vertical-align: middle;
    }
  }

  .el-menu--collapse {
    .custom-menu-icon {
      margin: 0;
      justify-content: center;
    }
  }

  ::v-deep .el-menu-item {
    .custom-menu-icon {
      color: #909399;
    }

    &:hover,
    &.is-active {
      .custom-menu-icon {
        color: #7a88fe;
      }
    }
  }

  ::v-deep .el-submenu__title i {
    color: #909399;
  }
</style>
