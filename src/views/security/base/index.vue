<script>
  import Edit from './component/baseEditor.vue'
  import { getBaseConfig } from '@/api/security'
  import { reject } from 'lodash'

  export default {
    name: 'BaseIndex',
    components: {
      Edit,
    },
    props: {
      title: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        queryForm: {
          pageIndex: 1,
          pageSize: 10,
          keyword: '',
        },
        baseConfigData: [],
        totalCount: 0,
        tableLoading: false,
        CHOICE_LEVEL_ANTI: null,
        CHOICE_LEVEL_FLOW: null,
        CHOICE_LEVEL_BLACK: null,
      }
    },
    computed: {
      layout() {
        return 'total, sizes, prev, pager, next, jumper'
      },
    },
    watch: {
      queryForm: {
        handler() {
          this.refresh()
        },
        deep: true,
      },
    },

    created() {
      this.refresh()
    },
    methods: {
      async getbaseConfigData(timeoutMs = 5000) {
        try {
          this.tableLoading = true
          const {
            data,
            totalCount,
            CHOICE_LEVEL_ANTI,
            CHOICE_LEVEL_FLOW,
            CHOICE_LEVEL_BLACK,
          } = await getBaseConfig(this.queryForm)
          if (data) {
            this.baseConfigData = data
            this.totalCount = totalCount
            this.CHOICE_LEVEL_ANTI = CHOICE_LEVEL_ANTI
            this.CHOICE_LEVEL_FLOW = CHOICE_LEVEL_FLOW
            this.CHOICE_LEVEL_BLACK = CHOICE_LEVEL_BLACK
          } else {
            this.$baseMessage('暂无数据', 'error')
          }
        } catch (error) {
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
          this.tableLoading = false
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
        this.tableLoading = false
      },

      refresh() {
        this.getbaseConfigData()
      },

      handleEdit(row) {
        if (row.config_name) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },
      getTagType(row) {
        console.log('getTagType:', row)
        const value = row.config_value.toString()
        switch (value) {
          case '0':
            return 'info'
          case '1':
            return 'success'
          case '2':
            return 'warning'
          default:
            return ''
        }
      },
      getTagLabel(row) {
        const configName = row.config_name.toLowerCase()
        if (configName.includes('antibot')) {
          return this.CHOICE_LEVEL_ANTI
            ? this.CHOICE_LEVEL_ANTI[row.config_value] || row.config_value
            : row.config_value
        } else if (configName.includes('flow')) {
          return this.CHOICE_LEVEL_FLOW
            ? this.CHOICE_LEVEL_FLOW[row.config_value] || row.config_value
            : row.config_value
        } else if (configName.includes('black')) {
          return this.CHOICE_LEVEL_BLACK
            ? this.CHOICE_LEVEL_BLACK[row.config_value] || row.config_value
            : row.config_value
        } else {
          return row.config_value
        }
      },
      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.refresh()
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
        this.refresh()
      },
    },
  }
</script>

<template>
  <div class="security-dashboard">
    <div class="title">
      <b></b>
      {{ title }}
    </div>

    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card>
          <div class="header">
            <el-button
              style="background-color: #006eff !important"
              type="primary"
              @click="handleEdit"
            >
              新建规则
            </el-button>
            <el-input
              v-model="queryForm.keyword"
              class="search-input"
              clearable
              placeholder="请输入关键字"
              @change="refresh()"
            />
          </div>
          <div class="content-container">
            <div class="left-content">
              <el-table
                v-loading="tableLoading"
                :border="true"
                :data="baseConfigData"
                element-loading-text="加载中...."
                size="mini"
              >
                <el-table-column
                  label="配置名称"
                  min-width="100"
                  prop="config_name"
                />
                <el-table-column
                  label="配置标题"
                  prop="config_title"
                  width="120"
                />
                <el-table-column
                  label="配置值"
                  min-width="80"
                  prop="config_value"
                >
                  <template #default="scope">
                    <el-tag :type="getTagType(scope.row)">
                      {{ getTagLabel(scope.row) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  label="描述"
                  min-width="130"
                  prop="description"
                />
                <el-table-column
                  label="更新时间"
                  min-width="80"
                  prop="last_updated"
                />
                <el-table-column
                  header-align="center"
                  label="操作"
                  max-width="100"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <div class="operation-buttons">
                      <el-button
                        plain
                        size="mini"
                        type="text"
                        @click="handleEdit(scope.row)"
                      >
                        编辑
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="queryForm.pageIndex"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :total="totalCount"
                background
                style="margin-top: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
            <!-- <div class="right-content">
              <el-empty description="暂无数据" />
            </div> -->
          </div>
        </el-card>
      </el-col>
      <edit
        ref="edit"
        :choice-levels="{
          CHOICE_LEVEL_ANTI: CHOICE_LEVEL_ANTI,
          CHOICE_LEVEL_FLOW: CHOICE_LEVEL_FLOW,
          CHOICE_LEVEL_BLACK: CHOICE_LEVEL_BLACK,
        }"
        @fetch-data="refresh"
      ></edit>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
  .title {
    font-size: 18px;
    font-weight: bold;
    height: 50px;
    background: #fff;
    line-height: 50px;
    overflow: hidden;
    margin: -20px -20px 5px;
  }

  .title b {
    width: 3px;
    height: 21px;
    background: #000;
    display: inline-block;

    line-height: 20px;
    position: relative;
    top: 4px;
    margin: 0px 9px 0px 10px;
  }

  .mb15 {
    margin: 8px -20px 15px -20px;
  }

  .base-el-col {
    padding-left: 0px;
    padding-right: -8px;
    border: 5px solid #ffffff;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-input {
    max-width: 300px;
  }

  .el-card .el-table {
    width: 100%;
    max-width: 100%;
  }

  ::v-deep .el-table th {
    background-color: #eff3f9;
  }

  ::v-deep .el-table td .cell {
    font-size: 13px;
  }

  .content-container {
    display: flex;
    margin-top: 15px;
  }

  .left-content {
    flex: 3;
    margin-right: 15px;
    margin-top: 8px;
  }

  .right-content {
    flex: 1;
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .operation-buttons .el-button {
    margin: 0 5px;
  }
</style>
