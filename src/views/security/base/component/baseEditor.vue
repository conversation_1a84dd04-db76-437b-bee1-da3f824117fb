<script>
  import { postBaseConfig } from '@/api/security'

  export default {
    name: 'BaseEditor',
    props: {
      choiceLevels: {
        type: Object,
        default: () => ({
          CHOICE_LEVEL_ANTI: {},
          CHOICE_LEVEL_FLOW: {},
          CHOICE_LEVEL_BLACK: {},
        }),
      },
    },
    data() {
      return {
        isedit: false,
        disabled: false,
        dialogFormVisible: false,
        title: '',
        formData: {
          config_name: '',
          config_title: '',
          config_value: null,
          description: '',
        },
        rules: {
          config_name: [
            { required: true, message: '请输入配置名称', trigger: 'blur' },
          ],
          config_title: [
            { required: true, message: '请输入配置标题', trigger: 'blur' },
          ],
          config_value: [
            { required: true, message: '请输入配置值', trigger: 'blur' },
          ],
        },
      }
    },

    computed: {
      isSelectOption() {
        const configName = this.formData.config_name.toLowerCase()
        return ['antibot', 'black', 'flow'].some((keyword) =>
          configName.includes(keyword)
        )
      },
      selectOptions() {
        const configName = this.formData.config_name.toLowerCase()
        if (configName.includes('antibot')) {
          return this.choiceLevels.CHOICE_LEVEL_ANTI
        } else if (configName.includes('flow')) {
          return this.choiceLevels.CHOICE_LEVEL_FLOW
        } else if (configName.includes('black')) {
          return this.choiceLevels.CHOICE_LEVEL_BLACK
        } else {
          return {}
        }
      },
    },
    watch: {
      filteredData: {
        immediate: true,
        deep: true,
        handler(newVal) {
          this.fetchData()
        },
      },
    },

    created() {
      this.fetchData()
    },

    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
          this.disabled = false
        } else {
          this.title = '编辑'
          this.disabled = true
          this.isedit = true
          this.formData = {
            ...row,
          }
        }
        this.dialogFormVisible = true
      },

      save() {
        this.$refs['vForm'].validate(async (valid) => {
          if (valid) {
            let msg
            const formDataToSend = {
              ...this.formData,
            }
            const response = await postBaseConfig(formDataToSend)
            msg = response.msg
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },

      close() {
        this.$refs['vForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

      async fetchData() {
        setTimeout(() => {}, 300)
      },
    },
  }
</script>

<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="600px"
    @close="close"
  >
    <el-form ref="vForm" :model="formData" :rules="rules" label-width="80px">
      <el-form-item
        class="label-right-align"
        label="配置标题"
        prop="config_title"
      >
        <el-input
          v-model.trim="formData.config_title"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="label-right-align"
        label="配置名称"
        prop="config_name"
      >
        <el-input
          v-model.trim="formData.config_name"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item
        class="label-right-align"
        label="配置值"
        prop="config_value"
      >
        <el-select
          v-if="isSelectOption"
          v-model="formData.config_value"
          placeholder="请选择"
        >
          <el-option
            v-for="(label, value) in selectOptions"
            :key="value"
            :label="label"
            :value="value"
          />
        </el-select>
        <el-input
          v-else
          v-model.trim="formData.config_value"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
      <el-form-item class="label-right-align" label="描述" prop="description">
        <el-input
          v-model.trim="formData.description"
          autocomplete="off"
          clearable
          type="text"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button :readonly="isedit" type="primary" @click="save">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
  div.table-container {
    table.table-layout {
      width: 100%;
      table-layout: fixed;
      border-collapse: collapse;

      td.table-cell {
        display: table-cell;
        height: 36px;
        border: 1px solid #e1e2e3;
      }
    }
  }

  div.tab-container {
  }

  .label-left-align ::v-deep .el-form-item__label {
    text-align: left;
  }

  .label-center-align ::v-deep .el-form-item__label {
    text-align: center;
  }

  .label-right-align ::v-deep .el-form-item__label {
    text-align: right;
  }

  .custom-label {
  }

  .static-content-item {
    min-height: 20px;
    display: flex;
    align-items: center;

    ::v-deep .el-divider--horizontal {
      margin: 0;
    }
  }
</style>
