<script>
  import { getProDomain } from '@/api/security'
  import { reject } from 'lodash'
  import axios from 'axios'

  export default {
    name: 'DomainDdos',
    props: {
      title: {
        type: String,
        default: '域名DDOS防护',
        requird: true,
      },
    },
    data() {
      return {
        queryForm: {
          pageIndex: 1,
          pageSize: 10,
          keyword: '',
        },
        DomainResData: [],
        totalCount: 0,
        tableLoading: false,
        layout: 'total, sizes, prev, pager, next, jumper',
        AntiDdosData: [],
      }
    },
    computed: {},
    watch: {
      queryForm: {
        handler() {
          this.refresh()
        },
        deep: true,
      },
    },

    mounted() {
      this.refresh()
      this.getAntiDdosDevice()
    },

    methods: {
      getStatusText(value) {
        switch (value) {
          case '1':
            return '封堵状态'
          case '2':
            return '正常状态'
          case '3':
            return '攻击状态'
          default:
            return '未知状态'
        }
      },
      getStatusType(value) {
        switch (value) {
          case '1':
            return 'warning' // 橙色
          case '2':
            return 'success' // 绿色
          case '3':
            return 'danger' // 红色
          default:
            return ''
        }
      },
      getStatusEffect(value) {
        return 'dark' // 标签的效果，可以为 'dark', 'light', 或 'plain'
      },
      getAntiDdosDevice(timeoutMs = 5000) {
        axios
          .get('http://localhost:3000/api/getDeviceStatus') // 调用后端 API
          .then((response) => {
            this.AntiDdosData = response.data.Response.Data
            console.log(this.AntiDdosData)
            this.totalCount = response.data.Response.Data.length
          })
          .catch((error) => {
            console.error('Error fetching device status:', error)
          })
      },
      async getDomainResData(timeoutMs = 5000) {
        try {
          this.tableLoading = true
          const { data, totalCount } = await getProDomain(this.queryForm)
          if (data) {
            this.DomainResData = data
            this.totalCount = totalCount
            this.tableLoading = false
          } else {
            this.$baseMessage('暂无数据', 'error')
          }
        } catch (error) {
          this.$baseMessage(`Error fetching data: ${error}`, 'error')
          this.tableLoading = false
        }
        setTimeout(() => reject(new Error('请求超时')), timeoutMs)
        this.tableLoading = false
      },

      refresh() {
        this.getDomainResData()
      },

      handleEdit(row) {
        if (row.id) {
          this.$refs['edit'].showEdit(row)
        } else {
          this.$refs['edit'].showEdit()
        }
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.queryForm.pageIndex = 1 // 当改变每页显示数时重置页码
      },

      handleCurrentChange(val) {
        this.queryForm.pageIndex = val
      },
    },
  }
</script>

<template>
  <div class="security-dashboard">
    <div class="title">
      <b></b>
      {{ title }}
    </div>

    <el-row :gutter="0" class="mb15" style="display: flex">
      <el-col class="base-el-col">
        <el-card shadow="never">
          <div class="header">
            <el-button
              style="background-color: #006eff !important"
              type="primary"
              @click="handleEdit"
            >
              新建解析
            </el-button>
            <el-input
              v-model="queryForm.keyword"
              class="search-input"
              clearable
              placeholder="请输入关键字"
              @change="refresh()"
            />
          </div>
          <div class="content-container">
            <div class="left-content">
              <el-table
                v-loading="tableLoading"
                :border="true"
                :data="AntiDdosData"
                element-loading-text="加载中...."
                size="mini"
              >
                <el-table-column label="IP" prop="Key" />
                <el-table-column label="状态" prop="Value">
                  <!--                  <template v-slot="scope">-->
                  <!--                    <el-tag-->
                  <!--                      :type="getStatusType(scope.row.Value)"-->
                  <!--                      :effect="getStatusEffect(scope.row.Value)"-->
                  <!--                    >-->
                  <!--                      {{ getStatusText(scope.row.Value) }}-->
                  <!--                    </el-tag>-->
                  <!--                  </template>-->
                </el-table-column>

                <el-table-column
                  header-align="center"
                  label="操作"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <div class="operation-buttons">
                      <el-button
                        plain
                        size="mini"
                        type="text"
                        @click="handleEdit(scope.row)"
                      >
                        编辑
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-pagination
                :current-page="queryForm.pageIndex"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :total="totalCount"
                background
                style="margin-top: 20px"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </div>
            <div class="right-content">
              <el-empty description="暂无数据" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <edit ref="edit" @fetch-data="refresh"></edit>
  </div>
</template>

<style lang="scss" scoped>
  .title {
    font-size: 18px;
    font-weight: bold;
    height: 50px;
    background: #fff;
    line-height: 50px;
    overflow: hidden;
    margin: -20px -20px 5px;
  }

  .title b {
    width: 3px;
    height: 21px;
    background: #000;
    display: inline-block;
    line-height: 20px;
    position: relative;
    top: 4px;
    margin: 0px 9px 0px 10px;
  }

  .mb15 {
    margin: 8px -20px 15px -20px;
  }

  .base-el-col {
    padding-left: 0px;
    padding-right: -8px;
    border: 1px solid #f1f2f3;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .search-input {
    max-width: 300px;
  }

  .el-card .el-table {
    width: 100%;
    max-width: 100%;
  }

  .content-container {
    display: flex;
    margin-top: 15px;
  }

  .left-content {
    flex: 3;
    margin-right: 15px;
    margin-top: 8px;
  }

  .right-content {
    flex: 1;
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .operation-buttons .el-button {
    margin: 0 4px;
  }

  ::v-deep .el-table th {
    background-color: #eff3f9;
  }

  ::v-deep .el-table td .cell {
    font-size: 13px;
  }
</style>
