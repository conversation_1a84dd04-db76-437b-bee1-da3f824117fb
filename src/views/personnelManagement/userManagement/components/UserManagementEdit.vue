<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogFormVisible"
    width="500px"
    @close="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model.trim="form.username"
          autocomplete="off"
          :readonly="isedit"
        ></el-input>
      </el-form-item>
      <el-form-item label="姓名" prop="displayname">
        <el-input v-model.trim="form.displayname" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model.trim="form.email" autocomplete="off"></el-input>
      </el-form-item>
      <el-form-item label="启用状态" prop="status">
        <el-switch v-model="form.status"></el-switch>
      </el-form-item>
      <el-form-item label="角色" prop="role">
        <div style="display: flex">
          <el-checkbox-group v-model="form.role">
            <el-checkbox
              v-for="(role, index) in getRole"
              :key="index"
              style="width: 55px"
              :label="role.role_desc"
            ></el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item label="额外权限" prop="access">
        <el-checkbox-group v-model="form.access">
          <el-checkbox
            v-for="(acc, index1) in getAccess"
            :key="index1"
            style="width: 55px"
            :label="acc.rolename"
          ></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="save">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { doEdit, getRoles } from '@/api/userManagement'

  export default {
    name: 'UserManagementEdit',
    data() {
      return {
        form: {
          username: '',
          displayname: '',
          email: '',
          status: false,
          role: [],
          access: [],
        },
        rules: {
          username: [
            { required: true, trigger: 'blur', message: '请输入用户名' },
          ],
          email: [{ required: true, trigger: 'blur', message: '请输入邮箱' }],
        },
        roleaccess: [],
        title: '',
        isedit: false,
        dialogFormVisible: false,
      }
    },
    computed: {
      getRole() {
        return this.roleaccess.filter((ra) => {
          return ra.type.toLowerCase().includes('role')
        })
      },
      getAccess() {
        return this.roleaccess.filter((ra) => {
          return ra.type.toLowerCase().includes('access')
        })
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '添加'
          this.isedit = false
        } else {
          this.title = '编辑'
          this.isedit = true
          this.form = Object.assign({}, row)
        }
        this.dialogFormVisible = true
      },
      close() {
        this.$refs['form'].resetFields()
        this.form = this.$options.data().form
        this.dialogFormVisible = false
      },
      save() {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            const { msg } = await doEdit(this.form)
            this.$baseMessage(msg, 'success')
            this.$emit('fetch-data')
            this.close()
          } else {
            return false
          }
        })
      },
      async fetchData() {
        const { data } = await getRoles()
        this.roleaccess = data
        setTimeout(() => {}, 300)
      },
    },
  }
</script>
