<template>
  <div class="data-visualization-dashboard">
    <!-- 标题和主题切换 -->
    <div class="dashboard-header">
      <h1>JSON数据可视化工具</h1>
      <button class="theme-toggle" @click="toggleTheme">
        <span class="icon">{{ isDarkMode ? '☀️' : '🌙' }}</span>
      </button>
    </div>

    <!-- 卡片布局 -->
    <div class="dashboard-container">
      <!-- JSON输入卡片 -->
      <div class="card json-input-card">
        <div class="card-header">
          <h2>输入JSON数据</h2>
        </div>
        <div class="card-body">
          <textarea
            v-model="jsonInput"
            placeholder="请在此处粘贴您的JSON数据..."
            @input="debouncedParseJson"
          ></textarea>
          <div class="button-container">
            <button class="btn primary" @click="parseJson">解析JSON</button>
            <button class="btn secondary" @click="clearJson">清空</button>
          </div>
        </div>
      </div>

      <!-- 数据配置卡片 -->
      <div v-if="jsonData" class="card config-card">
        <div class="card-header">
          <h2>数据配置</h2>
        </div>
        <div class="card-body">
          <div class="config-section">
            <label for="array-select">数据数组：</label>
            <div class="select-wrapper">
              <select id="array-select" v-model="selectedArrayKey">
                <option v-for="key in arrayKeys" :key="key" :value="key">
                  {{ key }}
                </option>
              </select>
              <div class="select-arrow"></div>
            </div>
          </div>

          <div v-if="selectedArray.length > 0" class="config-section">
            <label for="time-field">时间轴字段：</label>
            <div class="select-wrapper">
              <select id="time-field" v-model="selectedTimeField">
                <option
                  v-for="field in objectKeys(selectedArray[0])"
                  :key="field"
                  :value="field"
                >
                  {{ field }}
                </option>
              </select>
              <div class="select-arrow"></div>
            </div>
          </div>

          <div v-if="selectedArray.length > 0" class="config-section">
            <label for="value-field">数值字段：</label>
            <div class="select-wrapper">
              <select id="value-field" v-model="selectedValueField">
                <option
                  v-for="field in numericFields(selectedArray[0])"
                  :key="field"
                  :value="field"
                >
                  {{ field }}
                </option>
              </select>
              <div class="select-arrow"></div>
            </div>
          </div>

          <div
            v-if="selectedArray.length > 0"
            class="config-section chart-type-section"
          >
            <label>图表类型：</label>
            <div class="chart-type-buttons">
              <button
                class="chart-type-btn"
                :class="{ active: chartType === 'line' }"
                @click="chartType = 'line'"
              >
                <span class="chart-icon">📈</span>
                折线图
              </button>
              <button
                class="chart-type-btn"
                :class="{ active: chartType === 'bar' }"
                @click="chartType = 'bar'"
              >
                <span class="chart-icon">📊</span>
                柱状图
              </button>
              <button
                class="chart-type-btn"
                :class="{ active: chartType === 'area' }"
                @click="chartType = 'area'"
              >
                <span class="chart-icon">🌊</span>
                面积图
              </button>
            </div>
          </div>

          <button
            v-if="selectedArray.length > 0"
            class="btn primary btn-generate"
            @click="renderChart"
          >
            生成图表
            <span class="icon">📊</span>
          </button>
        </div>
      </div>

      <!-- 图表卡片 -->
      <div v-show="chartRendered" class="card chart-card">
        <div class="card-header">
          <h2>数据可视化</h2>
          <div class="card-actions">
            <button
              class="btn icon-btn"
              title="下载图表"
              @click="downloadChart"
            >
              <span class="icon">💾</span>
            </button>
            <button
              class="btn icon-btn"
              title="全屏查看"
              @click="fullscreenChart"
            >
              <span class="icon">🔍</span>
            </button>
          </div>
        </div>
        <div class="card-body">
          <div
            id="chartContainer"
            ref="chartContainer"
            class="chart-container"
          ></div>
        </div>
      </div>
    </div>

    <!-- 状态提示 -->
    <div
      class="toast"
      :class="{ show: toast.show, success: toast.type === 'success' }"
    >
      {{ toast.message }}
    </div>

    <!-- 全屏模态框 -->
    <div v-if="showFullscreenModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{ chartTitle }}</h2>
          <button class="close-btn" @click="showFullscreenModal = false">
            ×
          </button>
        </div>
        <div class="modal-body">
          <div
            id="fullscreenChartContainer"
            ref="fullscreenChartContainer"
            class="fullscreen-chart-container"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    name: 'JsonDataVisualization',
    data() {
      return {
        jsonInput: '',
        jsonData: null,
        selectedArrayKey: '',
        selectedArray: [],
        selectedTimeField: '',
        selectedValueField: '',
        chartInstance: null,
        fullscreenChartInstance: null,
        chartType: 'line',
        chartRendered: false,
        isDarkMode: false,
        toast: {
          show: false,
          message: '',
          type: 'error',
        },
        showFullscreenModal: false,
        chartTitle: '',
        debouncedParseJson: null,
        chartData: null, // 存储处理后的图表数据
      }
    },
    computed: {
      arrayKeys() {
        // 查找所有数组类型的字段
        if (!this.jsonData) return []
        return Object.keys(this.jsonData).filter(
          (key) =>
            Array.isArray(this.jsonData[key]) && this.jsonData[key].length > 0
        )
      },
    },
    watch: {
      selectedArrayKey(newKey) {
        if (newKey && this.jsonData) {
          this.selectedArray = this.jsonData[newKey]
          this.selectedTimeField = ''
          this.selectedValueField = ''
        }
      },
      isDarkMode(newValue) {
        // 主题变化时重新渲染图表
        if (this.chartRendered && this.chartData) {
          this.$nextTick(this.renderChart)
        }
      },
    },
    created() {
      // 创建一个防抖函数，延迟解析JSON
      this.debouncedParseJson = this.debounce(() => {
        if (this.jsonInput.trim()) {
          this.parseJson()
        }
      }, 1000)
    },
    mounted() {
      // 检测系统主题偏好
      if (
        window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches
      ) {
        this.isDarkMode = true
        document.body.classList.add('dark-theme')
      }

      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', (e) => {
          this.isDarkMode = e.matches
          document.body.classList.toggle('dark-theme', this.isDarkMode)

          // 重新渲染图表
          if (this.chartRendered && this.chartData) {
            this.$nextTick(this.renderChart)
          }
        })
      }
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.resizeChart)
      if (this.chartInstance) {
        this.chartInstance.dispose()
        this.chartInstance = null
      }
      if (this.fullscreenChartInstance) {
        this.fullscreenChartInstance.dispose()
        this.fullscreenChartInstance = null
      }
    },
    methods: {
      // 防抖函数
      debounce(fn, delay) {
        let timeout
        return function () {
          const context = this
          const args = arguments
          clearTimeout(timeout)
          timeout = setTimeout(() => fn.apply(context, args), delay)
        }
      },

      parseJson() {
        if (!this.jsonInput.trim()) {
          this.showToast('请输入JSON数据')
          return
        }

        try {
          this.jsonData = JSON.parse(this.jsonInput)
          if (this.arrayKeys.length > 0) {
            this.selectedArrayKey = this.arrayKeys[0]
            this.selectedArray = this.jsonData[this.selectedArrayKey]
            this.showToast('JSON解析成功!', 'success')

            // 解析成功后不要立即渲染图表
            // 等待用户选择字段并点击"生成图表"按钮
          } else {
            this.showToast('未发现可用的数据数组')
          }
        } catch (err) {
          this.showToast('JSON解析失败，请检查格式')
          console.error('JSON解析错误:', err)
        }
      },

      clearJson() {
        this.jsonInput = ''
        this.jsonData = null
        this.selectedArrayKey = ''
        this.selectedArray = []
        this.selectedTimeField = ''
        this.selectedValueField = ''
        this.chartRendered = false
        this.chartData = null

        // 销毁图表实例，而不是尝试清除
        if (this.chartInstance) {
          this.chartInstance.dispose()
          this.chartInstance = null
        }
      },

      objectKeys(obj) {
        return Object.keys(obj)
      },

      numericFields(obj) {
        return Object.keys(obj).filter((key) => typeof obj[key] === 'number')
      },

      // 准备图表数据
      prepareChartData() {
        if (!this.selectedTimeField || !this.selectedValueField) {
          this.showToast('请选择时间轴和数值字段')
          return false
        }

        try {
          this.chartData = this.selectedArray
            .map((item) => ({
              time: item[this.selectedTimeField],
              value: item[this.selectedValueField],
            }))
            .sort((a, b) => {
              // 尝试按日期排序，如果失败则按字符串排序
              try {
                return new Date(a.time) - new Date(b.time)
              } catch (e) {
                return String(a.time).localeCompare(String(b.time))
              }
            })

          this.chartTitle = `${this.selectedValueField} 随 ${this.selectedTimeField} 变化趋势`
          return true
        } catch (err) {
          this.showToast('数据处理失败')
          console.error('数据处理错误:', err)
          return false
        }
      },

      // 渲染图表 - 重构以解决DOM问题
      renderChart() {
        // 首先准备数据
        if (!this.prepareChartData()) {
          return
        }

        // 设置标志，显示图表容器
        this.chartRendered = true

        // 使用nextTick确保DOM已经更新
        this.$nextTick(() => {
          try {
            // 检查DOM元素是否存在
            const chartContainer = document.getElementById('chartContainer')
            if (!chartContainer) {
              throw new Error('图表容器DOM不存在')
            }

            // 如果已有实例，先销毁
            if (this.chartInstance) {
              this.chartInstance.dispose()
            }

            // 初始化新实例
            this.chartInstance = echarts.init(
              chartContainer,
              this.isDarkMode ? 'dark' : null
            )

            // 设置选项
            const option = this.createChartOption(this.chartData)
            this.chartInstance.setOption(option)

            // 添加图表渲染完成后的动画效果
            chartContainer.classList.add('chart-animated')
            setTimeout(() => {
              chartContainer.classList.remove('chart-animated')
            }, 1000)

            // 添加窗口大小变化监听
            window.addEventListener('resize', this.resizeChart)
          } catch (err) {
            this.chartRendered = false
            this.showToast('图表渲染失败: ' + err.message)
            console.error('图表渲染错误:', err)
          }
        })
      },

      createChartOption(data) {
        // 根据不同图表类型创建不同配置
        const colors = this.isDarkMode
          ? ['#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622']
          : ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']

        const option = {
          title: {
            text: this.chartTitle,
            left: 'center',
            textStyle: {
              fontWeight: 'normal',
              fontSize: 16,
            },
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: colors[0],
              },
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: data.map((d) => d.time),
            boundaryGap: this.chartType === 'bar',
            axisLabel: {
              rotate: 45,
              formatter: function (value) {
                // 如果是日期，尝试格式化它
                const date = new Date(value)
                if (!isNaN(date.getTime())) {
                  return date.toLocaleDateString()
                }
                return value
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          series: [
            {
              name: this.selectedValueField,
              data: data.map((d) => d.value),
              type: this.chartType === 'area' ? 'line' : this.chartType,
              smooth: true,
              itemStyle: {
                color: colors[0],
              },
              areaStyle:
                this.chartType === 'area'
                  ? {
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 0,
                            color: this.isDarkMode
                              ? 'rgba(80, 141, 255, 0.8)'
                              : 'rgba(84, 112, 198, 0.8)',
                          },
                          {
                            offset: 1,
                            color: this.isDarkMode
                              ? 'rgba(80, 141, 255, 0.1)'
                              : 'rgba(84, 112, 198, 0.1)',
                          },
                        ],
                      },
                    }
                  : null,
              animationDuration: 1500,
              animationEasing: 'elasticOut',
            },
          ],
          toolbox: {
            feature: {
              saveAsImage: {
                title: '保存为图片',
                name: 'chart_' + new Date().getTime(),
              },
              dataZoom: {
                title: {
                  zoom: '区域缩放',
                  back: '区域缩放还原',
                },
              },
              dataView: {
                title: '数据视图',
                readOnly: true,
                lang: ['数据视图', '关闭', '刷新'],
              },
            },
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100,
            },
            {
              start: 0,
              end: 100,
            },
          ],
        }

        return option
      },

      resizeChart() {
        if (this.chartInstance) {
          this.chartInstance.resize()
        }
        if (this.fullscreenChartInstance) {
          this.fullscreenChartInstance.resize()
        }
      },

      toggleTheme() {
        this.isDarkMode = !this.isDarkMode
        document.body.classList.toggle('dark-theme', this.isDarkMode)

        // 重新渲染图表
        if (this.chartRendered) {
          this.renderChart()
        }
      },

      showToast(message, type = 'error') {
        this.toast.message = message
        this.toast.show = true
        this.toast.type = type

        setTimeout(() => {
          this.toast.show = false
        }, 3000)
      },

      downloadChart() {
        if (this.chartInstance) {
          // 获取图表的base64编码
          const url = this.chartInstance.getDataURL()

          // 创建下载链接
          const link = document.createElement('a')
          link.download = `chart_${new Date().getTime()}.png`
          link.href = url
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          this.showToast('图表已下载', 'success')
        }
      },

      fullscreenChart() {
        if (!this.prepareChartData()) {
          return
        }

        this.showFullscreenModal = true

        // 延迟渲染全屏图表，等待DOM更新
        this.$nextTick(() => {
          try {
            const fullscreenContainer = document.getElementById(
              'fullscreenChartContainer'
            )
            if (!fullscreenContainer) {
              throw new Error('全屏容器DOM不存在')
            }

            // 如果已有实例，先销毁
            if (this.fullscreenChartInstance) {
              this.fullscreenChartInstance.dispose()
            }

            this.fullscreenChartInstance = echarts.init(
              fullscreenContainer,
              this.isDarkMode ? 'dark' : null
            )

            const option = this.createChartOption(this.chartData)
            this.fullscreenChartInstance.setOption(option)
          } catch (err) {
            this.showToast('全屏图表渲染失败: ' + err.message)
            console.error('全屏图表渲染错误:', err)
          }
        })
      },
    },
  }
</script>

<style>
  /* 基础样式与主题变量 */
  :root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --error-color: #f72585;
    --warning-color: #f8961e;
    --bg-color: #f9f9f9;
    --card-bg: #ffffff;
    --text-color: #333333;
    --border-color: #e0e0e0;
    --input-bg: #ffffff;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --border-radius: 8px;
  }

  .dark-theme {
    --primary-color: #4895ef;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --error-color: #f72585;
    --warning-color: #f8961e;
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --text-color: #f0f0f0;
    --border-color: #333333;
    --input-bg: #2d2d2d;
    --shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  .data-visualization-dashboard {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: var(--bg-color);
    min-height: 100vh;
    padding: 2rem;
    transition: var(--transition);
  }

  /* 头部样式 */
  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }

  .dashboard-header h1 {
    font-weight: 600;
    font-size: 1.8rem;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--secondary-color)
    );
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 3s ease infinite;
    background-size: 200% 200%;
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .theme-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
  }

  .theme-toggle:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .theme-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  /* 卡片容器 */
  .dashboard-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  @media (min-width: 992px) {
    .dashboard-container {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .chart-card {
      grid-column: 1 / -1;
    }
  }

  /* 卡片样式 */
  .card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    transform: translateY(0);
  }

  .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .dark-theme .card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  }

  .card-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .card-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
  }

  .card-body {
    padding: 1.5rem;
  }

  /* JSON输入区域 */
  .json-input-card textarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-family: monospace;
    resize: vertical;
    transition: var(--transition);
  }

  .json-input-card textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  }

  .dark-theme .json-input-card textarea:focus {
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.4);
  }

  .button-container {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    justify-content: flex-end;
  }

  /* 按钮样式 */
  .btn {
    padding: 0.6rem 1.25rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.9rem;
  }

  .btn .icon {
    margin-left: 0.5rem;
  }

  .btn.primary {
    background-color: var(--primary-color);
    color: white;
  }

  .btn.primary:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .dark-theme .btn.primary:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .btn.secondary {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .btn.secondary:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .btn.secondary:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .btn-generate {
    margin-top: 1.5rem;
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
  }

  .icon-btn {
    width: 36px;
    height: 36px;
    padding: 0;
    font-size: 1rem;
    border-radius: 50%;
    background-color: transparent;
  }

  .icon-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .icon-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .card-actions {
    display: flex;
    gap: 0.5rem;
  }

  /* 配置区域样式 */
  .config-section {
    margin-bottom: 1.25rem;
  }

  .config-section label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.9rem;
  }

  .select-wrapper {
    position: relative;
    width: 100%;
  }

  .select-wrapper select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-bg);
    color: var(--text-color);
    appearance: none;
    cursor: pointer;
    transition: var(--transition);
  }

  .select-wrapper select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  }

  .dark-theme .select-wrapper select:focus {
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.4);
  }

  .select-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--text-color);
    pointer-events: none;
  }

  /* 图表类型选择 */
  .chart-type-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .chart-type-btn {
    flex: 1;
    min-width: 80px;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--input-bg);
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
  }

  .chart-type-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .chart-type-btn:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .chart-type-btn.active {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(67, 97, 238, 0.1);
  }

  .chart-icon {
    font-size: 1.2rem;
  }

  /* 图表容器 */
  .chart-container {
    width: 100%;
    height: 400px;
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
  }

  .chart-animated {
    animation: chartFadeIn 1s ease;
  }

  @keyframes chartFadeIn {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fullscreen-chart-container {
    width: 100%;
    height: 70vh;
    border-radius: var(--border-radius);
  }

  /* 提示框 */
  .toast {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    background-color: var(--error-color);
    color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    opacity: 0;
    transform: translateY(100px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    z-index: 1000;
    pointer-events: none;
  }

  .toast.show {
    opacity: 1;
    transform: translateY(0);
  }

  .toast.success {
    background-color: var(--success-color);
  }

  /* 模态框 */
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: modalFadeIn 0.3s ease;
  }

  @keyframes modalFadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  .modal-content {
    background-color: var(--card-bg);
    width: 90%;
    max-width: 1200px;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalContentFadeIn 0.3s ease;
  }

  @keyframes modalContentFadeIn {
    0% {
      opacity: 0;
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  .modal-header {
    padding: 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .modal-header h2 {
    font-size: 1.2rem;
    font-weight: 500;
    margin: 0;
  }

  .close-btn {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .close-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  .dark-theme .close-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .modal-body {
    padding: 1.5rem;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .data-visualization-dashboard {
      padding: 1rem;
    }

    .chart-container {
      height: 300px;
    }

    .btn-generate {
      padding: 0.7rem;
    }

    .dashboard-header h1 {
      font-size: 1.5rem;
    }
  }
</style>
