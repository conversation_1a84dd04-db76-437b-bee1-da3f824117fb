<template>
  <div class="deployment-wizard-container">
    <el-card class="wizard-card">
      <div slot="header" class="wizard-header">
        <h2>
          <i class="el-icon-share"></i>
          向导式版本发布流程
        </h2>
        <div class="wizard-header-actions">
          <el-button
            type="primary"
            plain
            size="small"
            icon="el-icon-view"
            @click="showReleaseHistory"
          >
            查看发版历史
          </el-button>
          <el-button
            type="success"
            plain
            size="small"
            icon="el-icon-refresh"
            @click="resetWizard"
          >
            新建发版流程
          </el-button>
        </div>
      </div>

      <el-steps :active="activeStep" finish-status="success" align-center>
        <el-step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :icon="step.icon"
        >
          <template #description>
            <span>{{ step.description }}</span>
          </template>
        </el-step>
      </el-steps>

      <div class="wizard-content">
        <transition name="fade-transform" mode="out-in">
          <component
            :is="steps[activeStep].component"
            :key="activeStep"
            @next="goToNextStep"
            @prev="goToPrevStep"
            @goto="goToStep"
          >
            >
          </component>
        </transition>
      </div>
    </el-card>

    <el-dialog
      title="发版历史记录"
      :visible.sync="historyDialogVisible"
      width="80%"
      top="5vh"
    >
      <release-history></release-history>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex'
  import BasicInfo from './steps/BasicInfo'
  import ServiceSelection from './steps/ServiceSelection'
  import BuildPackage from './steps/BuildPackage'
  import PreReleaseTesting from './steps/PreReleaseTesting'
  import ReleaseQueue from './steps/ReleaseQueue'
  import Production from './steps/Production'
  import GrayscaleConfig from './steps/GrayscaleConfig'
  import GrayscaleRelease from './steps/GrayscaleRelease'
  import ReleaseHistory from './steps/ReleaseHistory'

  export default {
    name: 'DeploymentWizard',
    components: {
      BasicInfo,
      ServiceSelection,
      BuildPackage,
      PreReleaseTesting,
      ReleaseQueue,
      Production,
      GrayscaleConfig,
      GrayscaleRelease,
      ReleaseHistory,
    },
    data() {
      return {
        historyDialogVisible: false,
        steps: [
          {
            title: '基本信息',
            description: '设置发版基本信息',
            icon: 'el-icon-document',
            component: 'BasicInfo',
          },
          {
            title: '服务选择',
            description: '选择发布服务',
            icon: 'el-icon-s-grid',
            component: 'ServiceSelection',
          },
          {
            title: '构建打包',
            description: '构建Docker镜像',
            icon: 'el-icon-box',
            component: 'BuildPackage',
          },
          {
            title: '预发验证',
            description: '预发环境测试',
            icon: 'el-icon-discover',
            component: 'PreReleaseTesting',
          },
          {
            title: '发版队列',
            description: '等待发版审批',
            icon: 'el-icon-tickets',
            component: 'ReleaseQueue',
          },
        ],
        standardSteps: [
          {
            title: '正式发版',
            description: '部署到生产环境',
            icon: 'el-icon-s-promotion',
            component: 'Production',
          },
        ],
        grayscaleSteps: [
          {
            title: '灰度配置',
            description: '配置灰度策略',
            icon: 'el-icon-s-tools',
            component: 'GrayscaleConfig',
          },
          {
            title: '灰度发布',
            description: '灰度发布与完全发布',
            icon: 'el-icon-data-analysis',
            component: 'GrayscaleRelease',
          },
        ],
      }
    },
    computed: {
      ...mapState('deployment', ['activeStep', 'deploymentType']),
      completedSteps() {
        let finalSteps = [...this.steps]
        if (this.deploymentType === 'standard') {
          finalSteps = finalSteps.concat(this.standardSteps)
        } else {
          finalSteps = finalSteps.concat(this.grayscaleSteps)
        }
        return finalSteps
      },
    },
    watch: {
      deploymentType: {
        handler(newType) {
          this.updateSteps(newType)
        },
        immediate: true,
      },
    },
    created() {
      this.fetchPersonnel()
      this.fetchServices()
    },
    methods: {
      ...mapActions('deployment', [
        'setActiveStep',
        'fetchPersonnel',
        'fetchServices',
        'setDeploymentType',
      ]),
      goToNextStep() {
        if (this.activeStep < this.completedSteps.length - 1) {
          this.setActiveStep(this.activeStep + 1)
        }
      },
      goToPrevStep() {
        if (this.activeStep > 0) {
          this.setActiveStep(this.activeStep - 1)
        }
      },
      goToStep(stepIndex) {
        if (stepIndex >= 0 && stepIndex < this.completedSteps.length) {
          this.setActiveStep(stepIndex)
        }
      },
      showReleaseHistory() {
        this.historyDialogVisible = true
      },
      resetWizard() {
        this.$confirm('确定要重新开始一个新的发版流程吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.$store.commit('deployment/SET_DEPLOYMENT_ID', null)
            this.$store.commit('deployment/SET_BASIC_INFO', {
              releaseName: '',
              expectedReleaseDate: '',
              personnel: [],
              changelog: '',
              bugfixes: [],
            })
            this.$store.commit('deployment/SET_SELECTED_SERVICES', [])
            this.$store.commit('deployment/SET_SERVICE_ORDER', [])
            this.$store.commit('deployment/SET_BUILD_INFO', {
              buildId: null,
              status: '',
              progress: 0,
              logs: '',
              images: [],
            })
            this.$store.commit('deployment/SET_PRE_RELEASE', {
              deploymentId: null,
              status: '',
              progress: 0,
              logs: '',
            })
            this.$store.commit('deployment/SET_PRODUCTION_DEPLOYMENT', {
              deploymentId: null,
              status: '',
              progress: 0,
              logs: '',
            })
            this.setActiveStep(0)
            this.$message.success('已重置发版流程')
          })
          .catch(() => {})
      },
      updateSteps(type) {
        this.steps = [...this.steps.slice(0, 5)]
        if (type === 'standard') {
          this.steps = this.steps.concat(this.standardSteps)
        } else {
          this.steps = this.steps.concat(this.grayscaleSteps)
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .deployment-wizard-container {
    padding: 20px;

    .wizard-card {
      margin-bottom: 20px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

      .wizard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        h2 {
          margin: 0;
          font-size: 20px;
          display: flex;
          align-items: center;

          i {
            margin-right: 10px;
            color: #409eff;
          }
        }

        .wizard-header-actions {
          display: flex;
          gap: 10px;
        }
      }

      .wizard-content {
        padding: 30px 0;
        min-height: 400px;
      }
    }
  }

  .fade-transform-enter-active,
  .fade-transform-leave-active {
    transition: all 0.3s;
  }

  .fade-transform-enter {
    opacity: 0;
    transform: translateX(20px);
  }

  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(-20px);
  }
</style>
