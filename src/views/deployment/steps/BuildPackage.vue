<template>
  <div class="build-package-container">
    <div class="step-title">
      <h3>项目打包和Docker镜像构建</h3>
      <p class="step-description">合并代码并构建Docker镜像</p>
    </div>

    <div class="build-stages">
      <el-steps :active="activeStage" align-center finish-status="success">
        <el-step title="代码合并" icon="el-icon-document"></el-step>
        <el-step title="项目构建" icon="el-icon-s-promotion"></el-step>
        <el-step title="Docker镜像" icon="el-icon-box"></el-step>
      </el-steps>
    </div>

    <div class="build-content">
      <transition name="fade-transform" mode="out-in">
        <div v-if="activeStage === 0" key="merge" class="merge-stage">
          <el-card class="stage-card">
            <div slot="header">
              <h4>代码合并配置</h4>
            </div>

            <el-form ref="mergeForm" :model="mergeConfig" label-width="120px">
              <el-form-item label="目标分支" required>
                <el-select
                  v-model="mergeConfig.targetBranch"
                  placeholder="选择合并的目标分支"
                >
                  <el-option label="master" value="master"></el-option>
                  <el-option label="release" value="release"></el-option>
                  <el-option label="develop" value="develop"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="来源分支" required>
                <el-table
                  :data="sourceServices"
                  border
                  style="width: 100%"
                  max-height="300"
                >
                  <el-table-column
                    prop="name"
                    label="服务名称"
                    width="150"
                  ></el-table-column>
                  <el-table-column label="分支选择" width="250">
                    <template slot-scope="scope">
                      <el-select
                        v-model="scope.row.sourceBranch"
                        placeholder="选择来源分支"
                        style="width: 100%"
                      >
                        <el-option label="master" value="master"></el-option>
                        <el-option label="develop" value="develop"></el-option>
                        <el-option
                          label="feature/v1.0"
                          value="feature/v1.0"
                        ></el-option>
                        <el-option
                          label="bugfix/v1.0"
                          value="bugfix/v1.0"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="提交ID" width="250">
                    <template slot-scope="scope">
                      <el-input
                        v-model="scope.row.commitId"
                        placeholder="可选，指定提交ID"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="仓库地址">
                    <template slot-scope="scope">
                      <el-link type="primary" :href="'#'" target="_blank">
                        {{
                          `https://github.com/your-org/${scope.row.name}.git`
                        }}
                      </el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="confirmMerge">
                  确认合并配置
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <div v-else-if="activeStage === 1" key="build" class="build-stage">
          <el-card class="stage-card">
            <div slot="header">
              <h4>项目构建</h4>
            </div>

            <div class="build-progress">
              <div
                v-for="(service, index) in sourceServices"
                :key="service.id"
                class="service-build-progress"
              >
                <div class="service-info">
                  <div class="service-name">{{ service.name }}</div>
                  <div class="build-status">
                    <el-tag :type="getBuildStatusTag(service)">
                      {{ getBuildStatusText(service) }}
                    </el-tag>
                  </div>
                </div>

                <el-progress
                  :percentage="service.buildProgress || 0"
                  :status="service.buildStatus === 'failed' ? 'exception' : ''"
                  :stroke-width="15"
                ></el-progress>

                <div class="build-info">
                  <div v-if="service.buildStartTime" class="build-time">
                    开始时间: {{ service.buildStartTime }}
                  </div>
                  <div v-if="service.buildEndTime" class="build-time">
                    结束时间: {{ service.buildEndTime }}
                  </div>
                </div>

                <div
                  v-if="service.buildStatus === 'failed'"
                  class="build-error"
                >
                  <el-alert
                    title="构建失败"
                    type="error"
                    description="请查看构建日志了解详细错误信息"
                    show-icon
                  ></el-alert>
                </div>
              </div>

              <div class="build-actions">
                <el-button
                  type="primary"
                  :disabled="isBuildStarted"
                  @click="startBuild"
                >
                  开始构建
                </el-button>
                <el-button
                  type="warning"
                  :disabled="!hasFailedBuilds"
                  @click="retryFailedBuilds"
                >
                  重试失败的构建
                </el-button>
              </div>
            </div>

            <div class="build-logs">
              <el-collapse v-model="activeLogPanel" accordion>
                <el-collapse-item
                  v-for="service in sourceServices"
                  :key="service.id"
                  :title="`${service.name} 构建日志`"
                  :name="service.id"
                >
                  <div class="log-content">
                    <pre>{{ service.buildLog || '暂无构建日志' }}</pre>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </el-card>
        </div>

        <div v-else-if="activeStage === 2" key="docker" class="docker-stage">
          <el-card class="stage-card">
            <div slot="header">
              <h4>Docker镜像构建</h4>
            </div>

            <div class="docker-images">
              <el-table :data="dockerImages" border style="width: 100%">
                <el-table-column
                  prop="serviceName"
                  label="服务名称"
                  width="150"
                ></el-table-column>
                <el-table-column
                  prop="imageName"
                  label="镜像名称"
                  width="250"
                ></el-table-column>
                <el-table-column
                  prop="imageTag"
                  label="镜像标签"
                  width="150"
                ></el-table-column>
                <el-table-column
                  prop="size"
                  label="大小"
                  width="100"
                ></el-table-column>
                <el-table-column
                  prop="buildTime"
                  label="构建时间"
                  width="180"
                ></el-table-column>
                <el-table-column label="状态" width="100">
                  <template slot-scope="scope">
                    <el-tag
                      :type="
                        scope.row.status === 'success' ? 'success' : 'danger'
                      "
                    >
                      {{ scope.row.status === 'success' ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button
                      size="mini"
                      type="text"
                      icon="el-icon-view"
                      @click="viewDockerfile(scope.row)"
                    >
                      查看Dockerfile
                    </el-button>
                    <el-button
                      v-if="scope.row.status !== 'success'"
                      size="mini"
                      type="text"
                      icon="el-icon-refresh"
                      @click="rebuildImage(scope.row)"
                    >
                      重新构建
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div class="docker-actions">
                <el-button
                  type="success"
                  :disabled="!allImagesBuilt"
                  @click="completeBuild"
                >
                  完成构建
                </el-button>
              </div>
            </div>
          </el-card>
        </div>
      </transition>
    </div>

    <el-dialog
      title="Dockerfile 详情"
      :visible.sync="dockerfileDialogVisible"
      width="60%"
    >
      <div v-if="currentDockerfile" class="dockerfile-content">
        <pre><code>{{ currentDockerfile }}</code></pre>
      </div>
    </el-dialog>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
      <el-button type="primary" :disabled="!canGoNext" @click="nextStep">
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'BuildPackage',
    data() {
      return {
        activeStage: 0,
        mergeConfig: {
          targetBranch: 'release',
        },
        sourceServices: [],
        activeLogPanel: null,
        isBuildStarted: false,
        dockerfileDialogVisible: false,
        currentDockerfile: '',
        buildInterval: null,
        dockerImages: [],
      }
    },
    computed: {
      ...mapState('deployment', [
        'selectedServices',
        'servicesList',
        'buildInfo',
      ]),

      hasFailedBuilds() {
        return this.sourceServices.some(
          (service) => service.buildStatus === 'failed'
        )
      },

      allImagesBuilt() {
        return (
          this.dockerImages.length > 0 &&
          this.dockerImages.every((image) => image.status === 'success')
        )
      },

      canGoNext() {
        return this.activeStage === 2 && this.allImagesBuilt
      },
    },
    watch: {
      'buildInfo.status': {
        handler(newStatus) {
          if (newStatus === 'completed') {
            this.prepareDockerImages()
          }
        },
      },
    },
    created() {
      this.initializeServices()
    },
    beforeDestroy() {
      if (this.buildInterval) {
        clearInterval(this.buildInterval)
      }
    },
    methods: {
      ...mapActions('deployment', ['startBuild', 'checkBuildStatus']),
      ...mapMutations('deployment', ['SET_BUILD_INFO', 'UPDATE_BUILD_STATUS']),

      initializeServices() {
        this.sourceServices = this.selectedServices.map((id) => {
          const service = this.servicesList.find((s) => s.id === id)
          return {
            ...service,
            sourceBranch: 'develop',
            commitId: '',
            buildStatus: 'pending',
            buildProgress: 0,
            buildStartTime: null,
            buildEndTime: null,
            buildLog: '',
          }
        })
      },

      confirmMerge() {
        this.$confirm('确认上述代码合并配置信息?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 模拟异步操作
            this.$message({
              type: 'success',
              message: '代码合并配置已确认!',
            })

            // 切换到第二阶段
            setTimeout(() => {
              this.activeStage = 1
            }, 800)
          })
          .catch(() => {})
      },

      async startBuildProcess() {
        this.isBuildStarted = true

        try {
          // 模拟异步构建过程
          for (let i = 0; i < this.sourceServices.length; i++) {
            const service = this.sourceServices[i]
            service.buildStatus = 'in-progress'
            service.buildStartTime = new Date().toLocaleString()

            // 模拟构建进度
            const buildPromise = new Promise((resolve, reject) => {
              let progress = 0
              const interval = setInterval(() => {
                progress += Math.floor(Math.random() * 10)
                if (progress >= 100) {
                  progress = 100
                  clearInterval(interval)

                  // 随机模拟失败情况
                  const isSuccess = Math.random() > 0.2
                  if (isSuccess) {
                    resolve()
                  } else {
                    reject(new Error('构建失败'))
                  }
                }

                service.buildProgress = progress
                service.buildLog += `[${new Date().toLocaleTimeString()}] 构建进度: ${progress}%\n`

                // 强制更新视图
                this.sourceServices = [...this.sourceServices]
              }, 500)
            })

            try {
              await buildPromise
              service.buildStatus = 'success'
              service.buildEndTime = new Date().toLocaleString()
              service.buildLog += `[${new Date().toLocaleTimeString()}] 构建成功!\n`
            } catch (error) {
              service.buildStatus = 'failed'
              service.buildEndTime = new Date().toLocaleString()
              service.buildLog += `[${new Date().toLocaleTimeString()}] 构建失败: ${
                error.message
              }\n`
            }
          }

          // 更新构建状态到store
          this.UPDATE_BUILD_STATUS({
            status: 'completed',
            progress: 100,
            logs: this.sourceServices
              .map((s) => `${s.name}: ${s.buildStatus}`)
              .join('\n'),
          })

          // 如果全部成功，自动进入下一阶段
          if (!this.hasFailedBuilds) {
            setTimeout(() => {
              this.activeStage = 2
              this.prepareDockerImages()
            }, 1000)
          }
        } catch (error) {
          console.error('构建过程出错:', error)
          this.$message.error('构建过程中出现错误')
        }
      },

      retryFailedBuilds() {
        const failedServices = this.sourceServices.filter(
          (service) => service.buildStatus === 'failed'
        )

        failedServices.forEach((service) => {
          service.buildStatus = 'pending'
          service.buildProgress = 0
          service.buildStartTime = null
          service.buildEndTime = null
          service.buildLog = ''
        })

        // 强制更新视图
        this.sourceServices = [...this.sourceServices]

        this.startBuildProcess()
      },

      prepareDockerImages() {
        this.dockerImages = this.sourceServices.map((service) => {
          const imageTag = `v1.0.${Math.floor(Math.random() * 100)}`
          const size = `${(Math.random() * 500 + 100).toFixed(2)}MB`

          return {
            serviceId: service.id,
            serviceName: service.name,
            imageName: `registry.example.com/${service.name}`,
            imageTag,
            size,
            buildTime: new Date().toLocaleString(),
            status: service.buildStatus === 'success' ? 'success' : 'failed',
          }
        })

        // 更新构建信息到store
        this.UPDATE_BUILD_STATUS({
          status: 'completed',
          progress: 100,
          logs: 'Docker镜像构建完成',
          images: this.dockerImages,
        })
      },

      viewDockerfile(image) {
        this.currentDockerfile = `FROM node:14-alpine as builder

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]`

        this.dockerfileDialogVisible = true
      },

      rebuildImage(image) {
        this.$confirm('确定要重新构建该镜像吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 模拟重建过程
            this.$message({
              type: 'info',
              message: '镜像重建中...',
            })

            setTimeout(() => {
              const index = this.dockerImages.findIndex(
                (i) => i.serviceId === image.serviceId
              )
              if (index !== -1) {
                this.dockerImages[index].status = 'success'
                this.dockerImages[index].buildTime = new Date().toLocaleString()
                this.$message.success('镜像重建成功')
              }
            }, 2000)
          })
          .catch(() => {})
      },

      completeBuild() {
        this.$confirm('确认所有镜像构建完成，继续下一步操作?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        })
          .then(() => {
            this.$message.success('构建阶段完成')
          })
          .catch(() => {})
      },

      getBuildStatusTag(service) {
        switch (service.buildStatus) {
          case 'success':
            return 'success'
          case 'failed':
            return 'danger'
          case 'in-progress':
            return ''
          default:
            return 'info'
        }
      },

      getBuildStatusText(service) {
        switch (service.buildStatus) {
          case 'success':
            return '成功'
          case 'failed':
            return '失败'
          case 'in-progress':
            return '构建中'
          default:
            return '等待中'
        }
      },

      startBuild() {
        this.$confirm('确定要开始构建所有选中的服务吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        })
          .then(() => {
            this.startBuildProcess()
          })
          .catch(() => {})
      },

      nextStep() {
        if (this.canGoNext) {
          this.$emit('next')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .build-package-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .build-stages {
      margin-bottom: 30px;
    }

    .build-content {
      min-height: 400px;

      .stage-card {
        margin-bottom: 20px;

        h4 {
          margin: 0;
          font-size: 16px;
        }
      }
    }

    .merge-stage {
      padding: 10px 0;
    }

    .build-stage {
      .service-build-progress {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px dashed #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .service-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .service-name {
            font-weight: 500;
            font-size: 16px;
          }
        }

        .build-info {
          margin-top: 10px;
          display: flex;
          justify-content: space-between;
          color: #909399;
          font-size: 14px;
        }

        .build-error {
          margin-top: 15px;
        }
      }

      .build-actions {
        margin: 20px 0;
        display: flex;
        justify-content: center;
        gap: 20px;
      }

      .build-logs {
        margin-top: 30px;

        .log-content {
          background-color: #f5f5f5;
          padding: 15px;
          border-radius: 4px;
          max-height: 300px;
          overflow-y: auto;

          pre {
            margin: 0;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            color: #606266;
            font-size: 14px;
          }
        }
      }
    }

    .docker-stage {
      .docker-actions {
        margin: 20px 0;
        display: flex;
        justify-content: center;
      }
    }

    .dockerfile-content {
      pre {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        max-height: 400px;
        overflow-y: auto;
        margin: 0;

        code {
          font-family: monospace;
          white-space: pre-wrap;
          word-break: break-all;
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }

  .fade-transform-enter-active,
  .fade-transform-leave-active {
    transition: all 0.3s;
  }

  .fade-transform-enter {
    opacity: 0;
    transform: translateX(20px);
  }

  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(-20px);
  }
</style>
