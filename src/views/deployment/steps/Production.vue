<template>
  <div class="production-container">
    <div class="step-title">
      <h3>标准正式发版</h3>
      <p class="step-description">将服务按顺序部署到生产环境</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="deployment-card">
          <div slot="header" class="card-header">
            <div class="header-title">
              <span>发版进度</span>
              <el-tag v-if="deploymentStatus">
                {{ deploymentStatusText }}
              </el-tag>
            </div>
            <div class="header-actions">
              <el-button
                v-if="!isDeploying && !isDeployed"
                type="primary"
                @click="startDeployment"
              >
                开始发布
              </el-button>
              <el-button
                v-if="isDeploying"
                type="warning"
                :disabled="isPaused"
                @click="pauseDeployment"
              >
                暂停
              </el-button>
              <el-button
                v-if="isDeploying && isPaused"
                type="success"
                @click="resumeDeployment"
              >
                继续
              </el-button>
              <el-button
                v-if="isDeploying"
                type="danger"
                @click="rollbackDeployment"
              >
                回滚
              </el-button>
              <el-button
                v-if="isDeployed"
                type="success"
                @click="completeDeployment"
              >
                完成发布
              </el-button>
            </div>
          </div>

          <div v-if="!isDeploying && !isDeployed" class="empty-state">
            <i class="el-icon-upload"></i>
            <p>准备就绪，点击"开始发布"进行生产环境部署</p>
          </div>

          <div v-else class="deployment-status">
            <div class="status-header">
              <el-progress
                :percentage="overallProgress"
                :status="isAnyFailed ? 'exception' : isPaused ? 'warning' : ''"
                :stroke-width="20"
                class="overall-progress"
              >
                <span>{{ overallProgressText }}</span>
              </el-progress>

              <div class="deployment-timeline">
                <div
                  class="timeline-item"
                  :class="{ active: deploymentPhase === 'canary' }"
                >
                  <div class="timeline-phase">
                    <i class="el-icon-s-flag"></i>
                    <span>Canary 环境</span>
                  </div>
                  <div class="timeline-progress">
                    <el-progress
                      :percentage="canaryProgress"
                      :status="canaryPhaseStatus"
                      :stroke-width="8"
                    ></el-progress>
                  </div>
                </div>
                <div class="timeline-arrow">
                  <i class="el-icon-arrow-right"></i>
                </div>
                <div
                  class="timeline-item"
                  :class="{ active: deploymentPhase === 'production' }"
                >
                  <div class="timeline-phase">
                    <i class="el-icon-s-home"></i>
                    <span>生产环境</span>
                  </div>
                  <div class="timeline-progress">
                    <el-progress
                      :percentage="productionProgress"
                      :status="productionPhaseStatus"
                      :stroke-width="8"
                    ></el-progress>
                  </div>
                </div>
              </div>
            </div>

            <div class="service-status">
              <h4>服务发布状态</h4>
              <div class="service-list">
                <div
                  v-for="service in deploymentServices"
                  :key="service.id"
                  class="service-item"
                >
                  <div class="service-info">
                    <div class="service-name">
                      <el-tag
                        :type="getServiceTypeTag(service.type)"
                        size="small"
                      >
                        {{ getServiceTypeName(service.type) }}
                      </el-tag>
                      {{ service.name }}
                    </div>
                    <div class="service-phase">
                      <el-tag
                        :type="getPhaseTagType(service.phase, service.status)"
                        size="small"
                      >
                        {{ getPhaseText(service.phase) }}
                      </el-tag>
                    </div>
                    <div class="service-deployment-status">
                      <el-tag
                        :type="getStatusTagType(service.status)"
                        size="small"
                      >
                        {{ getStatusText(service.status) }}
                      </el-tag>
                    </div>
                  </div>

                  <el-progress
                    :percentage="service.progress || 0"
                    :status="getProgressStatus(service)"
                    :stroke-width="10"
                  ></el-progress>

                  <div class="service-actions">
                    <el-button
                      type="text"
                      icon="el-icon-view"
                      @click="viewServiceLogs(service)"
                    >
                      查看日志
                    </el-button>
                    <el-button
                      v-if="service.status === 'failed'"
                      type="text"
                      icon="el-icon-refresh"
                      @click="retryService(service)"
                    >
                      重试
                    </el-button>
                    <el-button
                      v-if="
                        service.status === 'success' &&
                        service.phase === 'canary'
                      "
                      type="text"
                      icon="el-icon-s-promotion"
                      @click="promoteToProduction(service)"
                    >
                      提升到生产
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row
      v-if="isDeploying || isDeployed"
      :gutter="20"
      style="margin-top: 20px"
    >
      <el-col :span="24">
        <el-card class="logs-card">
          <div slot="header" class="card-header">
            <span>发布日志</span>
            <el-button type="text" @click="clearLogs">清空日志</el-button>
          </div>

          <div class="logs-content">
            <el-tabs v-model="activeLogTab" type="card">
              <el-tab-pane label="全局日志" name="global">
                <div class="log-viewer">
                  <pre>{{ globalLogs }}</pre>
                </div>
              </el-tab-pane>
              <el-tab-pane label="Canary日志" name="canary">
                <div class="log-viewer">
                  <pre>{{ canaryLogs }}</pre>
                </div>
              </el-tab-pane>
              <el-tab-pane label="生产日志" name="production">
                <div class="log-viewer">
                  <pre>{{ productionLogs }}</pre>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      title="服务部署日志"
      :visible.sync="serviceLogsVisible"
      width="60%"
    >
      <div v-if="currentServiceLogs" class="service-logs">
        <div class="logs-header">
          <span class="service-name">
            {{ currentService && currentService.name }}
          </span>
          <el-tag
            :type="
              getPhaseTagType(
                currentService && currentService.phase,
                currentService && currentService.status
              )
            "
          >
            {{ getPhaseText(currentService && currentService.phase) }}
          </el-tag>
          <el-tag
            :type="getStatusTagType(currentService && currentService.status)"
          >
            {{ getStatusText(currentService && currentService.status) }}
          </el-tag>
        </div>
        <pre>{{ currentServiceLogs }}</pre>
      </div>
    </el-dialog>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'Production',
    data() {
      return {
        isDeploying: false,
        isDeployed: false,
        isPaused: false,
        overallProgress: 0,
        deploymentStatus: '',
        deploymentPhase: '',
        canaryProgress: 0,
        productionProgress: 0,
        deploymentServices: [],
        deploymentTimer: null,
        globalLogs: '',
        canaryLogs: '',
        productionLogs: '',
        serviceLogsVisible: false,
        currentServiceLogs: '',
        currentService: null,
        activeLogTab: 'global',
      }
    },
    computed: {
      ...mapState('deployment', [
        'servicesList',
        'serviceOrder',
        'selectedServices',
        'buildInfo',
      ]),

      deploymentStatusText() {
        switch (this.deploymentStatus) {
          case 'pending':
            return '等待中'
          case 'deploying':
            return this.isPaused ? '已暂停' : '部署中'
          case 'success':
            return '发布成功'
          case 'failed':
            return '发布失败'
          case 'rolling-back':
            return '正在回滚'
          case 'rolled-back':
            return '已回滚'
          default:
            return '未知状态'
        }
      },

      overallProgressText() {
        if (this.isPaused) {
          return `已暂停 (${this.overallProgress}%)`
        }
        return `总体进度 ${this.overallProgress}%`
      },

      isAnyFailed() {
        return this.deploymentServices.some(
          (service) => service.status === 'failed'
        )
      },

      canaryPhaseStatus() {
        if (this.deploymentPhase !== 'canary' && this.canaryProgress < 100) {
          return ''
        }

        const canaryServices = this.deploymentServices.filter(
          (s) => s.phase === 'canary'
        )
        if (canaryServices.some((s) => s.status === 'failed')) {
          return 'exception'
        }
        if (this.isPaused) {
          return 'warning'
        }
        return this.canaryProgress >= 100 ? 'success' : ''
      },

      productionPhaseStatus() {
        if (this.deploymentPhase !== 'production') {
          return ''
        }

        const prodServices = this.deploymentServices.filter(
          (s) => s.phase === 'production'
        )
        if (prodServices.some((s) => s.status === 'failed')) {
          return 'exception'
        }
        if (this.isPaused) {
          return 'warning'
        }
        return this.productionProgress >= 100 ? 'success' : ''
      },
    },
    beforeDestroy() {
      if (this.deploymentTimer) {
        clearInterval(this.deploymentTimer)
      }
    },
    methods: {
      ...mapActions('deployment', [
        'deployToProduction',
        'checkProductionDeploymentStatus',
        'pauseProductionDeployment',
        'resumeProductionDeployment',
        'rollbackProductionDeployment',
      ]),

      initializeDeploymentServices() {
        // 基于服务顺序初始化部署服务列表
        this.deploymentServices = this.serviceOrder.map((service) => ({
          ...service,
          phase: 'canary', // 初始阶段为canary
          status: 'pending',
          progress: 0,
          logs: '',
          deploymentStarted: false,
        }))
      },

      startDeployment() {
        this.$confirm('确定要开始部署到生产环境吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.initializeDeploymentServices()
            this.isDeploying = true
            this.isPaused = false
            this.deploymentStatus = 'deploying'
            this.deploymentPhase = 'canary'
            this.overallProgress = 0
            this.canaryProgress = 0
            this.productionProgress = 0

            this.addLog('global', '[INFO] 开始生产环境部署流程')
            this.addLog('global', '[INFO] 第一阶段：部署到Canary环境')
            this.addLog('canary', '[INFO] 开始Canary环境部署')

            // 开始模拟部署过程
            this.simulateDeployment()
          })
          .catch(() => {})
      },

      simulateDeployment() {
        if (this.deploymentTimer) {
          clearInterval(this.deploymentTimer)
        }

        let currentServiceIndex = 0

        this.deploymentTimer = setInterval(() => {
          // 如果暂停，不执行部署逻辑
          if (this.isPaused) return

          const currentService = this.deploymentServices[currentServiceIndex]

          // 如果当前服务未开始部署
          if (!currentService.deploymentStarted) {
            currentService.deploymentStarted = true
            currentService.status = 'deploying'

            // 记录部署开始日志
            this.addLog(
              'global',
              `[INFO] 开始部署服务: ${
                currentService.name
              } 到${this.getPhaseText(currentService.phase)}环境`
            )
            this.addLog(
              currentService.phase === 'canary' ? 'canary' : 'production',
              `[INFO] 开始部署服务: ${currentService.name}`
            )

            currentService.logs += `[${new Date().toLocaleTimeString()}] 开始部署到${this.getPhaseText(
              currentService.phase
            )}环境\n`
          }

          // 更新服务部署进度
          if (currentService.status === 'deploying') {
            currentService.progress += Math.floor(Math.random() * 10) + 1

            if (currentService.progress >= 100) {
              currentService.progress = 100

              // 有15%的概率部署失败
              const deployFailed = Math.random() < 0.15

              if (deployFailed) {
                currentService.status = 'failed'
                this.addLog(
                  'global',
                  `[ERROR] 服务 ${
                    currentService.name
                  } 部署到${this.getPhaseText(currentService.phase)}环境失败`
                )
                this.addLog(
                  currentService.phase === 'canary' ? 'canary' : 'production',
                  `[ERROR] 服务 ${currentService.name} 部署失败: 容器启动超时`
                )

                currentService.logs += `[${new Date().toLocaleTimeString()}] 部署失败: 容器启动超时\n`

                // 如果有服务失败，暂停部署
                this.pauseDeployment()
              } else {
                currentService.status = 'success'
                this.addLog(
                  'global',
                  `[INFO] 服务 ${currentService.name} 部署到${this.getPhaseText(
                    currentService.phase
                  )}环境成功`
                )
                this.addLog(
                  currentService.phase === 'canary' ? 'canary' : 'production',
                  `[INFO] 服务 ${currentService.name} 部署成功`
                )

                currentService.logs += `[${new Date().toLocaleTimeString()}] 部署成功，服务已启动并通过健康检查\n`

                // 如果全部canary服务部署完成，更新阶段
                if (
                  this.deploymentPhase === 'canary' &&
                  this.deploymentServices.every(
                    (s) =>
                      s.phase === 'canary' &&
                      (s.status === 'success' || s.status === 'skipped')
                  )
                ) {
                  this.addLog(
                    'global',
                    '[INFO] Canary环境部署完成，将逐步推进到生产环境'
                  )
                  this.addLog('canary', '[INFO] Canary环境所有服务部署完成')
                  this.addLog('production', '[INFO] 开始生产环境部署')

                  // 自动推进到下一个服务
                  currentServiceIndex = 0
                  return
                }

                // 推进到下一个服务
                currentServiceIndex =
                  (currentServiceIndex + 1) % this.deploymentServices.length
              }
            } else {
              // 更新日志
              if (currentService.progress % 20 === 0) {
                this.addLog(
                  currentService.phase === 'canary' ? 'canary' : 'production',
                  `[INFO] 服务 ${currentService.name} 部署进度: ${currentService.progress}%`
                )

                currentService.logs += `[${new Date().toLocaleTimeString()}] 部署进度: ${
                  currentService.progress
                }%\n`
              }
            }
          }

          // 更新总体进度
          this.updateOverallProgress()
        }, 500)
      },

      updateOverallProgress() {
        // 计算canary和production阶段的进度
        let totalCanaryProgress = 0
        let totalProductionProgress = 0
        let canaryServiceCount = 0
        let productionServiceCount = 0

        this.deploymentServices.forEach((service) => {
          if (service.phase === 'canary') {
            totalCanaryProgress += service.progress || 0
            canaryServiceCount++
          } else if (service.phase === 'production') {
            totalProductionProgress += service.progress || 0
            productionServiceCount++
          }
        })

        this.canaryProgress =
          canaryServiceCount > 0
            ? Math.floor(totalCanaryProgress / canaryServiceCount)
            : 0
        this.productionProgress =
          productionServiceCount > 0
            ? Math.floor(totalProductionProgress / productionServiceCount)
            : 0

        // 计算总体进度：canary占30%，production占70%
        const weightedCanaryProgress = this.canaryProgress * 0.3
        const weightedProductionProgress = this.productionProgress * 0.7

        this.overallProgress = Math.floor(
          weightedCanaryProgress + weightedProductionProgress
        )

        // 检查是否全部完成
        if (this.overallProgress >= 100) {
          this.deploymentStatus = this.isAnyFailed ? 'failed' : 'success'
          this.isDeployed = true
          this.isDeploying = false

          if (this.deploymentTimer) {
            clearInterval(this.deploymentTimer)
            this.deploymentTimer = null
          }

          if (!this.isAnyFailed) {
            this.addLog('global', '[INFO] 所有服务已成功部署到生产环境')
            this.addLog('production', '[INFO] 生产环境部署完成')
          } else {
            this.addLog(
              'global',
              '[WARNING] 部署过程中有服务失败，请检查并进行处理'
            )
          }
        }
      },

      pauseDeployment() {
        this.isPaused = true
        this.addLog('global', '[WARNING] 部署流程已暂停')

        if (this.deploymentPhase === 'canary') {
          this.addLog('canary', '[WARNING] Canary环境部署已暂停')
        } else {
          this.addLog('production', '[WARNING] 生产环境部署已暂停')
        }
      },

      resumeDeployment() {
        this.isPaused = false
        this.addLog('global', '[INFO] 部署流程已恢复')

        if (this.deploymentPhase === 'canary') {
          this.addLog('canary', '[INFO] Canary环境部署已恢复')
        } else {
          this.addLog('production', '[INFO] 生产环境部署已恢复')
        }
      },

      rollbackDeployment() {
        this.$confirm('确定要回滚所有已部署的服务吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 暂停当前部署
            this.pauseDeployment()

            // 更新状态
            this.deploymentStatus = 'rolling-back'
            this.addLog('global', '[WARNING] 开始回滚所有已部署的服务')

            // 模拟回滚操作
            setTimeout(() => {
              this.deploymentStatus = 'rolled-back'
              this.isDeploying = false

              if (this.deploymentTimer) {
                clearInterval(this.deploymentTimer)
                this.deploymentTimer = null
              }

              this.addLog('global', '[INFO] 所有服务已成功回滚到之前版本')

              // 将已部署的服务状态设置为已回滚
              this.deploymentServices.forEach((service) => {
                if (service.status === 'success') {
                  service.status = 'rolled-back'
                  service.logs += `[${new Date().toLocaleTimeString()}] 服务已回滚到之前版本\n`
                }
              })
            }, 3000)
          })
          .catch(() => {})
      },

      retryService(service) {
        service.status = 'pending'
        service.progress = 0
        service.deploymentStarted = false
        service.logs += `[${new Date().toLocaleTimeString()}] 重新开始部署\n`

        this.addLog('global', `[INFO] 重新部署服务: ${service.name}`)
        this.addLog(
          service.phase === 'canary' ? 'canary' : 'production',
          `[INFO] 重新部署服务: ${service.name}`
        )

        // 如果部署暂停了，自动恢复
        if (this.isPaused) {
          this.resumeDeployment()
        }
      },

      promoteToProduction(service) {
        this.$confirm(
          `确定要将服务 ${service.name} 从Canary环境推进到生产环境吗?`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info',
          }
        )
          .then(() => {
            // 设置阶段为production
            this.deploymentPhase = 'production'

            // 复制一个新的服务对象用于生产环境
            const prodService = {
              ...service,
              phase: 'production',
              status: 'pending',
              progress: 0,
              logs: '',
              deploymentStarted: false,
            }

            // 添加到服务列表
            this.deploymentServices.push(prodService)

            this.addLog(
              'global',
              `[INFO] 服务 ${service.name} 开始从Canary环境推进到生产环境`
            )
            this.addLog(
              'production',
              `[INFO] 服务 ${service.name} 从Canary环境推进`
            )
          })
          .catch(() => {})
      },

      completeDeployment() {
        this.$confirm('确认所有服务已成功部署到生产环境?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success',
        })
          .then(() => {
            this.$message.success('发版流程完成!')
          })
          .catch(() => {})
      },

      viewServiceLogs(service) {
        this.currentService = service
        this.currentServiceLogs = service.logs || '暂无部署日志'
        this.serviceLogsVisible = true
      },

      addLog(type, message) {
        const timestamp = `[${new Date().toLocaleTimeString()}]`
        const logMessage = `${timestamp} ${message}\n`

        this.globalLogs += logMessage

        if (type === 'canary') {
          this.canaryLogs += logMessage
        } else if (type === 'production') {
          this.productionLogs += logMessage
        }
      },

      clearLogs() {
        if (this.activeLogTab === 'global') {
          this.globalLogs = ''
        } else if (this.activeLogTab === 'canary') {
          this.canaryLogs = ''
        } else if (this.activeLogTab === 'production') {
          this.productionLogs = ''
        }
      },

      getProgressStatus(service) {
        if (service.status === 'failed') {
          return 'exception'
        }
        if (service.status === 'deploying' && this.isPaused) {
          return 'warning'
        }
        return ''
      },

      getServiceTypeTag(type) {
        switch (type) {
          case 'backend':
            return 'primary'
          case 'frontend':
            return 'success'
          case 'half-popup':
            return 'warning'
          default:
            return 'info'
        }
      },

      getServiceTypeName(type) {
        switch (type) {
          case 'backend':
            return '后端'
          case 'frontend':
            return '前端'
          case 'half-popup':
            return '半弹窗'
          default:
            return '未知'
        }
      },

      getPhaseText(phase) {
        return phase === 'canary' ? 'Canary环境' : '生产环境'
      },

      getPhaseTagType(phase, status) {
        if (status === 'failed') return 'danger'
        return phase === 'canary' ? 'warning' : 'success'
      },

      getStatusText(status) {
        switch (status) {
          case 'pending':
            return '等待中'
          case 'deploying':
            return '部署中'
          case 'success':
            return '成功'
          case 'failed':
            return '失败'
          case 'rolled-back':
            return '已回滚'
          case 'skipped':
            return '已跳过'
          default:
            return '未知'
        }
      },

      getStatusTagType(status) {
        switch (status) {
          case 'success':
            return 'success'
          case 'failed':
            return 'danger'
          case 'deploying':
            return this.isPaused ? 'warning' : ''
          case 'rolled-back':
            return 'info'
          default:
            return 'info'
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .production-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 16px;

        .el-tag {
          margin-left: 10px;
        }
      }

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .empty-state {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }
    }

    .deployment-status {
      .status-header {
        margin-bottom: 30px;

        .overall-progress {
          margin-bottom: 20px;
        }

        .deployment-timeline {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 20px;

          .timeline-item {
            flex: 1;
            padding: 15px;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            background-color: #f5f7fa;
            transition: all 0.3s;

            &.active {
              background-color: #ecf5ff;
              border-color: #c6e2ff;
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }

            .timeline-phase {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 10px;

              i {
                margin-right: 8px;
                font-size: 18px;
                color: #409eff;
              }

              span {
                font-weight: 500;
                font-size: 16px;
              }
            }
          }

          .timeline-arrow {
            padding: 0 20px;

            i {
              font-size: 24px;
              color: #909399;
            }
          }
        }
      }

      .service-status {
        h4 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 16px;
        }

        .service-list {
          .service-item {
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            transition: all 0.3s;

            &:hover {
              box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            }

            .service-info {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;

              .service-name {
                display: flex;
                align-items: center;
                font-weight: 500;

                .el-tag {
                  margin-right: 8px;
                }
              }
            }

            .service-actions {
              margin-top: 10px;
              text-align: right;
            }
          }
        }
      }
    }

    .logs-card {
      .logs-content {
        .log-viewer {
          height: 300px;
          background-color: #f5f5f5;
          padding: 15px;
          border-radius: 4px;
          overflow-y: auto;

          pre {
            margin: 0;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 14px;
          }
        }
      }
    }

    .service-logs {
      .logs-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .service-name {
          font-weight: 500;
          font-size: 16px;
          margin-right: 10px;
        }

        .el-tag {
          margin-right: 8px;
        }
      }

      pre {
        height: 300px;
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        overflow-y: auto;
        margin: 0;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
        font-size: 14px;
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
</style>
