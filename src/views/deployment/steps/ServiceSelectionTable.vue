<template>
  <div class="service-selection-table">
    <el-table
      ref="serviceTable"
      :data="services"
      border
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column
        prop="name"
        label="服务名称"
        min-width="150"
      ></el-table-column>
      <el-table-column
        prop="description"
        label="描述"
        min-width="200"
      ></el-table-column>
      <el-table-column label="依赖关系" min-width="200">
        <template slot-scope="scope">
          <el-tag
            v-for="depId in scope.row.dependencies"
            :key="depId"
            size="mini"
            type="info"
            style="margin-right: 5px; margin-bottom: 5px"
          >
            {{ getServiceNameById(depId) }}
          </el-tag>
          <span v-if="scope.row.dependencies.length === 0">无依赖</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-tooltip content="查看详情" placement="top">
            <el-button
              type="text"
              icon="el-icon-view"
              @click="showServiceDetail(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="服务详情" :visible.sync="detailDialogVisible" width="50%">
      <div v-if="currentService" class="service-detail">
        <h3>{{ currentService.name }}</h3>
        <p class="service-description">{{ currentService.description }}</p>

        <el-divider content-position="left">依赖关系</el-divider>
        <div class="dependency-list">
          <el-tag
            v-for="depId in currentService.dependencies"
            :key="depId"
            type="info"
            style="margin-right: 10px; margin-bottom: 10px"
          >
            {{ getServiceNameById(depId) }}
          </el-tag>
          <div
            v-if="currentService.dependencies.length === 0"
            class="no-dependency"
          >
            该服务没有依赖其他服务
          </div>
        </div>

        <el-divider content-position="left">被依赖情况</el-divider>
        <div class="dependents-list">
          <el-tag
            v-for="service in getDependents(currentService.id)"
            :key="service.id"
            type="warning"
            style="margin-right: 10px; margin-bottom: 10px"
          >
            {{ service.name }}
          </el-tag>
          <div
            v-if="getDependents(currentService.id).length === 0"
            class="no-dependency"
          >
            没有其他服务依赖该服务
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState } from 'vuex'

  export default {
    name: 'ServiceSelectionTable',
    props: {
      services: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        detailDialogVisible: false,
        currentService: null,
      }
    },
    computed: {
      ...mapState('deployment', ['servicesList', 'selectedServices']),
    },
    mounted() {
      // 预选已选择的服务
      this.preSelectServices()
    },
    methods: {
      handleSelectionChange(selection) {
        const selectedIds = selection.map((item) => item.id)
        this.$emit('select-change', selectedIds)
      },

      preSelectServices() {
        this.services.forEach((service) => {
          if (this.selectedServices.includes(service.id)) {
            this.$refs.serviceTable.toggleRowSelection(service, true)
          }
        })
      },

      getServiceNameById(id) {
        const service = this.servicesList.find((s) => s.id === id)
        return service ? service.name : `未知服务(${id})`
      },

      getDependents(serviceId) {
        return this.servicesList.filter((service) =>
          service.dependencies.includes(serviceId)
        )
      },

      showServiceDetail(service) {
        this.currentService = service
        this.detailDialogVisible = true
      },
    },
  }
</script>

<style lang="scss" scoped>
  .service-selection-table {
    .service-detail {
      h3 {
        margin-top: 0;
        color: #303133;
      }

      .service-description {
        color: #606266;
        margin-bottom: 20px;
      }

      .no-dependency {
        color: #909399;
        font-style: italic;
        padding: 10px 0;
      }
    }
  }
</style>
