<template>
  <div class="release-history">
    <div class="history-filters">
      <el-form :inline="true" :model="filters" class="filter-form">
        <el-form-item label="发版类型">
          <el-select v-model="filters.releaseType" placeholder="全部" clearable>
            <el-option label="标准发版" value="standard"></el-option>
            <el-option label="灰度发版" value="grayscale"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filters.status" placeholder="全部" clearable>
            <el-option label="成功" value="success"></el-option>
            <el-option label="失败" value="failed"></el-option>
            <el-option label="回滚" value="rollback"></el-option>
            <el-option label="进行中" value="in-progress"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchHistory">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="history-timeline">
      <el-timeline>
        <el-timeline-item
          v-for="item in filteredReleaseHistory"
          :key="item.id"
          :timestamp="item.releaseDate"
          :type="getTimelineItemType(item.status)"
          :icon="getTimelineItemIcon(item.status)"
          placement="top"
        >
          <el-card>
            <div class="release-header">
              <h4>{{ item.version }}</h4>
              <el-tag :type="getStatusType(item.status)" size="medium">
                {{ getStatusText(item.status) }}
              </el-tag>
            </div>

            <div class="release-info">
              <div class="release-type">
                <span class="label">发版类型:</span>
                <el-tag
                  size="small"
                  :type="
                    item.releaseType === 'standard' ? 'primary' : 'warning'
                  "
                >
                  {{
                    item.releaseType === 'standard' ? '标准发版' : '灰度发版'
                  }}
                </el-tag>
              </div>

              <div class="release-by">
                <span class="label">发版人员:</span>
                <span>{{ item.releasedBy }}</span>
              </div>
            </div>

            <div class="release-services">
              <span class="label">发布服务:</span>
              <el-tag
                v-for="(service, index) in item.services"
                :key="index"
                size="small"
                type="info"
                style="margin-right: 5px; margin-bottom: 5px"
              >
                {{ service }}
              </el-tag>
            </div>

            <div class="release-changelog">
              <span class="label">变更内容:</span>
              <p>{{ item.changeLog }}</p>
            </div>

            <div class="release-actions">
              <el-button type="text" @click="viewReleaseDetail(item)">
                查看详情
              </el-button>
              <el-button
                v-if="item.status === 'success'"
                type="text"
                @click="rollbackRelease(item)"
              >
                回滚
              </el-button>
              <el-button type="text" @click="compareRelease(item)">
                比较差异
              </el-button>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <el-dialog title="发版详情" :visible.sync="detailDialogVisible" width="70%">
      <div v-if="currentReleaseDetail" class="release-detail">
        <h3>{{ currentReleaseDetail.version }} 详细信息</h3>

        <el-tabs type="border-card">
          <el-tab-pane label="基本信息">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="发版ID">
                {{ currentReleaseDetail.id }}
              </el-descriptions-item>
              <el-descriptions-item label="发版状态">
                <el-tag :type="getStatusType(currentReleaseDetail.status)">
                  {{ getStatusText(currentReleaseDetail.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="发版类型">
                <el-tag
                  :type="
                    currentReleaseDetail.releaseType === 'standard'
                      ? 'primary'
                      : 'warning'
                  "
                >
                  {{
                    currentReleaseDetail.releaseType === 'standard'
                      ? '标准发版'
                      : '灰度发版'
                  }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="发版日期">
                {{ currentReleaseDetail.releaseDate }}
              </el-descriptions-item>
              <el-descriptions-item label="发版人员">
                {{ currentReleaseDetail.releasedBy }}
              </el-descriptions-item>
              <el-descriptions-item label="完成时间">
                {{
                  new Date(
                    new Date(currentReleaseDetail.releaseDate).getTime() +
                      3600000
                  ).toLocaleString()
                }}
              </el-descriptions-item>
              <el-descriptions-item label="变更日志" :span="2">
                {{ currentReleaseDetail.changeLog }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <el-tab-pane label="服务列表">
            <el-table :data="getReleaseServices()" border style="width: 100%">
              <el-table-column
                prop="name"
                label="服务名称"
                width="180"
              ></el-table-column>
              <el-table-column prop="type" label="服务类型" width="120">
                <template slot-scope="scope">
                  <el-tag :type="getServiceTypeTag(scope.row.type)">
                    {{ getServiceTypeName(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="imageTag"
                label="镜像标签"
                width="150"
              ></el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template slot-scope="scope">
                  <el-tag
                    :type="
                      scope.row.status === 'success' ? 'success' : 'danger'
                    "
                  >
                    {{ scope.row.status === 'success' ? '成功' : '失败' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="deployTime"
                label="部署时间"
                width="180"
              ></el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button type="text" @click="viewServiceLogs(scope.row)">
                    部署日志
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane label="部署日志">
            <div class="deployment-logs">
              <div class="log-filters">
                <el-radio-group v-model="logLevel" size="small">
                  <el-radio-button label="all">全部</el-radio-button>
                  <el-radio-button label="info">信息</el-radio-button>
                  <el-radio-button label="warning">警告</el-radio-button>
                  <el-radio-button label="error">错误</el-radio-button>
                </el-radio-group>
                <el-input
                  v-model="logSearch"
                  placeholder="搜索日志内容"
                  suffix-icon="el-icon-search"
                  size="small"
                  style="width: 200px; margin-left: 10px"
                ></el-input>
              </div>

              <div class="log-viewer">
                <pre>{{ getFilteredLogs() }}</pre>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <el-dialog
      title="服务部署日志"
      :visible.sync="serviceLogDialogVisible"
      width="70%"
    >
      <div v-if="currentServiceLog" class="service-log-viewer">
        <pre>{{ currentServiceLog }}</pre>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { mapState, mapActions } from 'vuex'

  export default {
    name: 'ReleaseHistory',
    data() {
      return {
        filters: {
          releaseType: '',
          status: '',
          dateRange: null,
        },
        detailDialogVisible: false,
        currentReleaseDetail: null,
        serviceLogDialogVisible: false,
        currentServiceLog: '',
        logLevel: 'all',
        logSearch: '',
      }
    },
    computed: {
      ...mapState('deployment', ['releaseHistory', 'servicesList']),

      filteredReleaseHistory() {
        let result = [...this.releaseHistory]

        if (this.filters.releaseType) {
          result = result.filter(
            (item) => item.releaseType === this.filters.releaseType
          )
        }

        if (this.filters.status) {
          result = result.filter((item) => item.status === this.filters.status)
        }

        if (this.filters.dateRange && this.filters.dateRange.length === 2) {
          const startDate = new Date(this.filters.dateRange[0])
          const endDate = new Date(this.filters.dateRange[1])
          endDate.setHours(23, 59, 59)

          result = result.filter((item) => {
            const itemDate = new Date(item.releaseDate)
            return itemDate >= startDate && itemDate <= endDate
          })
        }

        return result
      },
    },
    created() {
      this.fetchReleaseHistory()
    },
    methods: {
      ...mapActions('deployment', ['fetchReleaseHistory']),

      searchHistory() {
        this.fetchReleaseHistory({
          releaseType: this.filters.releaseType,
          status: this.filters.status,
          startDate: this.filters.dateRange ? this.filters.dateRange[0] : null,
          endDate: this.filters.dateRange ? this.filters.dateRange[1] : null,
        })
      },

      resetFilters() {
        this.filters = {
          releaseType: '',
          status: '',
          dateRange: null,
        }
        this.searchHistory()
      },

      getStatusType(status) {
        switch (status) {
          case 'success':
            return 'success'
          case 'failed':
            return 'danger'
          case 'rollback':
            return 'warning'
          case 'in-progress':
            return 'info'
          default:
            return 'info'
        }
      },

      getStatusText(status) {
        switch (status) {
          case 'success':
            return '成功'
          case 'failed':
            return '失败'
          case 'rollback':
            return '已回滚'
          case 'in-progress':
            return '进行中'
          default:
            return '未知'
        }
      },

      getTimelineItemType(status) {
        switch (status) {
          case 'success':
            return 'success'
          case 'failed':
            return 'danger'
          case 'rollback':
            return 'warning'
          case 'in-progress':
            return 'primary'
          default:
            return 'info'
        }
      },

      getTimelineItemIcon(status) {
        switch (status) {
          case 'success':
            return 'el-icon-check'
          case 'failed':
            return 'el-icon-close'
          case 'rollback':
            return 'el-icon-back'
          case 'in-progress':
            return 'el-icon-loading'
          default:
            return 'el-icon-info'
        }
      },

      viewReleaseDetail(release) {
        this.currentReleaseDetail = { ...release }
        this.detailDialogVisible = true
      },

      rollbackRelease(release) {
        this.$confirm(`确定要回滚到版本 ${release.version} 吗?`, '回滚确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 模拟回滚操作
            this.$message({
              type: 'success',
              message: `已发起回滚到版本 ${release.version} 的操作`,
            })
          })
          .catch(() => {})
      },

      compareRelease(release) {
        this.$message({
          type: 'info',
          message: '对比功能正在开发中',
        })
      },

      getServiceTypeTag(type) {
        switch (type) {
          case 'backend':
            return 'primary'
          case 'frontend':
            return 'success'
          case 'half-popup':
            return 'warning'
          default:
            return 'info'
        }
      },

      getServiceTypeName(type) {
        switch (type) {
          case 'backend':
            return '后端服务'
          case 'frontend':
            return '前端项目'
          case 'half-popup':
            return '半弹窗项目'
          default:
            return '未知类型'
        }
      },

      viewServiceLogs(service) {
        this.currentServiceLog = this.generateMockServiceLog(service)
        this.serviceLogDialogVisible = true
      },

      generateMockServiceLog(service) {
        return `[2023-05-01 10:15:23] INFO: 开始部署服务 ${service.name}
[2023-05-01 10:15:25] INFO: 拉取镜像 ${service.name}:${service.imageTag}
[2023-05-01 10:15:45] INFO: 镜像拉取成功
[2023-05-01 10:15:46] INFO: 停止旧容器
[2023-05-01 10:15:48] INFO: 启动新容器
[2023-05-01 10:15:55] INFO: 容器启动成功
[2023-05-01 10:15:57] INFO: 健康检查通过
[2023-05-01 10:16:00] INFO: 服务部署完成
`
      },

      getReleaseServices() {
        if (!this.currentReleaseDetail) return []

        // 模拟服务详情数据
        return this.currentReleaseDetail.services.map((serviceName) => {
          const serviceType = this.getServiceTypeFromName(serviceName)
          return {
            name: serviceName,
            type: serviceType,
            imageTag: `v1.0.${Math.floor(Math.random() * 100)}`,
            status: 'success',
            deployTime: new Date(
              new Date(this.currentReleaseDetail.releaseDate).getTime() +
                Math.random() * 3600000
            ).toLocaleString(),
          }
        })
      },

      getServiceTypeFromName(name) {
        if (name.includes('service')) return 'backend'
        if (name.includes('portal')) return 'frontend'
        if (name.includes('popup')) return 'half-popup'
        return 'backend'
      },

      getFilteredLogs() {
        // 模拟部署日志
        const logs = `[2023-05-01 10:15:00] INFO: 开始发版流程
[2023-05-01 10:15:05] INFO: 准备部署服务 user-service
[2023-05-01 10:15:23] INFO: user-service 部署完成
[2023-05-01 10:15:25] INFO: 准备部署服务 order-service
[2023-05-01 10:15:45] WARNING: order-service 健康检查响应缓慢
[2023-05-01 10:15:55] INFO: order-service 健康检查通过
[2023-05-01 10:16:00] INFO: order-service 部署完成
[2023-05-01 10:16:05] ERROR: payment-service 部署失败，连接超时
[2023-05-01 10:16:15] WARNING: 尝试重新部署 payment-service
[2023-05-01 10:16:30] INFO: payment-service 部署完成
[2023-05-01 10:16:45] INFO: 准备部署前端服务 admin-portal
[2023-05-01 10:17:00] INFO: admin-portal 部署完成
[2023-05-01 10:17:15] INFO: 发版流程完成
`

        let filteredLines = logs.split('\n')

        if (this.logLevel !== 'all') {
          const levelMap = {
            info: 'INFO',
            warning: 'WARNING',
            error: 'ERROR',
          }

          filteredLines = filteredLines.filter((line) =>
            line.includes(levelMap[this.logLevel])
          )
        }

        if (this.logSearch) {
          filteredLines = filteredLines.filter((line) =>
            line.toLowerCase().includes(this.logSearch.toLowerCase())
          )
        }

        return filteredLines.join('\n')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .release-history {
    .history-filters {
      margin-bottom: 30px;

      .filter-form {
        display: flex;
        flex-wrap: wrap;
      }
    }

    .history-timeline {
      .release-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;

        h4 {
          margin: 0;
          font-size: 18px;
        }
      }

      .release-info {
        display: flex;
        margin-bottom: 10px;

        .release-type,
        .release-by {
          margin-right: 30px;
          display: flex;
          align-items: center;
        }

        .label {
          margin-right: 10px;
          color: #606266;
        }
      }

      .release-services,
      .release-changelog {
        margin-bottom: 10px;

        .label {
          color: #606266;
          margin-right: 10px;
        }

        p {
          margin: 5px 0 0;
          color: #606266;
        }
      }

      .release-actions {
        margin-top: 15px;
        text-align: right;
      }
    }

    .release-detail {
      h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: #303133;
      }
    }

    .deployment-logs {
      .log-filters {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
      }

      .log-viewer {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        height: 400px;
        overflow-y: auto;

        pre {
          margin: 0;
          font-family: monospace;
          white-space: pre-wrap;
          word-break: break-all;
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .service-log-viewer {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      max-height: 400px;
      overflow-y: auto;

      pre {
        margin: 0;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
        color: #606266;
        font-size: 14px;
      }
    }
  }
</style>
