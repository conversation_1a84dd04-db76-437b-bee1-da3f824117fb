<template>
  <div class="grayscale-config-container">
    <div class="step-title">
      <h3>灰度策略配置</h3>
      <p class="step-description">配置灰度发布策略，控制灰度用户范围</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="strategy-card">
          <div slot="header" class="card-header">
            <span>灰度策略配置</span>
            <div class="header-actions">
              <el-button
                type="primary"
                size="small"
                icon="el-icon-plus"
                @click="createNewStrategy"
              >
                新建策略
              </el-button>
              <el-button
                type="primary"
                plain
                size="small"
                icon="el-icon-refresh"
                @click="refreshStrategies"
              >
                刷新列表
              </el-button>
            </div>
          </div>

          <el-tabs v-model="activeTab" type="border-card">
            <el-tab-pane label="已有策略" name="existing">
              <div
                v-if="grayscaleStrategies.length === 0"
                class="empty-strategies"
              >
                <i class="el-icon-data-analysis"></i>
                <p>暂无灰度策略</p>
                <p class="sub-text">点击"新建策略"创建一个新的灰度策略</p>
              </div>

              <div v-else class="strategies-list">
                <el-radio-group
                  v-model="selectedStrategyId"
                  class="strategy-radio-group"
                >
                  <div
                    v-for="strategy in grayscaleStrategies"
                    :key="strategy.id"
                    class="strategy-item"
                  >
                    <el-radio :label="strategy.id">
                      <div class="strategy-info">
                        <div class="strategy-header">
                          <span class="strategy-name">{{ strategy.name }}</span>
                          <el-tag size="small">
                            {{ getStrategyTypeText(strategy.type) }}
                          </el-tag>
                        </div>
                        <div class="strategy-rules">
                          <div v-if="strategy.type === 'ip'" class="rule-item">
                            <span class="rule-label">IP范围:</span>
                            <el-tag
                              v-for="(rule, index) in strategy.rules"
                              :key="index"
                              size="mini"
                              type="info"
                              style="margin-right: 5px; margin-bottom: 5px"
                            >
                              {{ rule.ipRange }} ({{ rule.percentage }}%)
                            </el-tag>
                          </div>

                          <div
                            v-else-if="strategy.type === 'userId'"
                            class="rule-item"
                          >
                            <span class="rule-label">用户ID:</span>
                            <span>
                              {{ strategy.rules[0].userIds.length }} 位用户 ({{
                                strategy.rules[0].percentage
                              }}%)
                            </span>
                          </div>

                          <div
                            v-else-if="strategy.type === 'percentage'"
                            class="rule-item"
                          >
                            <span class="rule-label">流量比例:</span>
                            <span>{{ strategy.rules[0].percentage }}%</span>
                          </div>
                        </div>
                        <div class="strategy-actions">
                          <el-button
                            type="text"
                            size="small"
                            @click.stop="viewStrategy(strategy)"
                          >
                            详情
                          </el-button>
                          <el-button
                            type="text"
                            size="small"
                            @click.stop="editStrategy(strategy)"
                          >
                            编辑
                          </el-button>
                        </div>
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
            </el-tab-pane>

            <el-tab-pane label="创建新策略" name="new">
              <div class="strategy-form">
                <el-form
                  ref="strategyForm"
                  :model="strategyForm"
                  :rules="strategyRules"
                  label-width="120px"
                >
                  <el-form-item label="策略名称" prop="name">
                    <el-input
                      v-model="strategyForm.name"
                      placeholder="请输入策略名称"
                    ></el-input>
                  </el-form-item>

                  <el-form-item label="策略类型" prop="type">
                    <el-select
                      v-model="strategyForm.type"
                      placeholder="请选择策略类型"
                      style="width: 100%"
                    >
                      <el-option label="IP地址段" value="ip"></el-option>
                      <el-option label="用户ID" value="userId"></el-option>
                      <el-option
                        label="流量百分比"
                        value="percentage"
                      ></el-option>
                    </el-select>
                  </el-form-item>

                  <template v-if="strategyForm.type === 'ip'">
                    <el-form-item
                      v-for="(rule, index) in strategyForm.ipRules"
                      :key="index"
                      :label="index === 0 ? 'IP规则' : ''"
                      :prop="'ipRules.' + index + '.ipRange'"
                      :rules="{
                        required: true,
                        message: '请输入IP范围',
                        trigger: 'blur',
                      }"
                    >
                      <div class="rule-input-group">
                        <el-input
                          v-model="rule.ipRange"
                          placeholder="IP范围，如：***********-*************"
                          style="width: 60%"
                        ></el-input>
                        <el-input-number
                          v-model="rule.percentage"
                          :min="1"
                          :max="100"
                          placeholder="百分比"
                          style="width: 30%; margin: 0 10px"
                        ></el-input-number>
                        <el-button
                          type="danger"
                          icon="el-icon-delete"
                          :disabled="strategyForm.ipRules.length <= 1"
                          @click="removeIpRule(index)"
                        >
                          >
                        </el-button>
                      </div>
                    </el-form-item>

                    <el-form-item>
                      <el-button
                        type="primary"
                        plain
                        icon="el-icon-plus"
                        @click="addIpRule"
                      >
                        添加IP规则
                      </el-button>
                    </el-form-item>
                  </template>

                  <template v-if="strategyForm.type === 'userId'">
                    <el-form-item label="用户ID列表" prop="userIds">
                      <el-select
                        v-model="strategyForm.userIds"
                        multiple
                        filterable
                        allow-create
                        default-first-option
                        placeholder="请输入用户ID，回车添加"
                        style="width: 100%"
                      ></el-select>
                    </el-form-item>

                    <el-form-item label="用户百分比" prop="userPercentage">
                      <el-input-number
                        v-model="strategyForm.userPercentage"
                        :min="1"
                        :max="100"
                        style="width: 100%"
                      ></el-input-number>
                    </el-form-item>
                  </template>

                  <template v-if="strategyForm.type === 'percentage'">
                    <el-form-item label="流量百分比" prop="trafficPercentage">
                      <el-slider
                        v-model="strategyForm.trafficPercentage"
                        :min="1"
                        :max="100"
                        :format-tooltip="formatTooltip"
                        show-input
                      ></el-slider>
                    </el-form-item>
                  </template>

                  <el-form-item>
                    <el-button type="primary" @click="submitStrategyForm">
                      创建策略
                    </el-button>
                    <el-button @click="resetStrategyForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="24">
        <el-card class="selected-strategy-card">
          <div slot="header" class="card-header">
            <span>当前选中的灰度策略</span>
          </div>

          <div v-if="selectedStrategy" class="selected-strategy-info">
            <div class="strategy-header">
              <h3>{{ selectedStrategy.name }}</h3>
              <el-tag size="medium">
                {{ getStrategyTypeText(selectedStrategy.type) }}
              </el-tag>
            </div>

            <el-divider content-position="left">策略规则</el-divider>

            <div class="strategy-rules-detail">
              <template v-if="selectedStrategy.type === 'ip'">
                <div class="rules-table">
                  <el-table
                    :data="selectedStrategy.rules"
                    border
                    style="width: 100%"
                  >
                    <el-table-column
                      prop="ipRange"
                      label="IP范围"
                    ></el-table-column>
                    <el-table-column
                      prop="percentage"
                      label="百分比"
                      width="120"
                    >
                      <template slot-scope="scope">
                        {{ scope.row.percentage }}%
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>

              <template v-else-if="selectedStrategy.type === 'userId'">
                <div class="user-ids-list">
                  <div class="user-ids-header">
                    <span>
                      用户ID列表 ({{
                        selectedStrategy.rules[0].userIds.length
                      }}个)
                    </span>
                    <span>
                      百分比: {{ selectedStrategy.rules[0].percentage }}%
                    </span>
                  </div>
                  <el-tag
                    v-for="(userId, index) in selectedStrategy.rules[0].userIds"
                    :key="index"
                    type="info"
                    style="margin-right: 5px; margin-bottom: 5px"
                  >
                    {{ userId }}
                  </el-tag>
                </div>
              </template>

              <template v-else-if="selectedStrategy.type === 'percentage'">
                <div class="percentage-info">
                  <el-progress
                    :percentage="selectedStrategy.rules[0].percentage"
                    :stroke-width="18"
                  >
                    <span>
                      {{ selectedStrategy.rules[0].percentage }}% 的用户流量
                    </span>
                  </el-progress>
                </div>
              </template>
            </div>

            <el-divider content-position="left">影响分析</el-divider>

            <div class="impact-analysis">
              <div class="impact-item">
                <div class="impact-icon">
                  <i class="el-icon-user"></i>
                </div>
                <div class="impact-info">
                  <div class="impact-title">覆盖用户数</div>
                  <div class="impact-value">
                    约 {{ getImpactUserCount() }} 位用户
                  </div>
                </div>
              </div>

              <div class="impact-item">
                <div class="impact-icon">
                  <i class="el-icon-data-line"></i>
                </div>
                <div class="impact-info">
                  <div class="impact-title">预计影响流量</div>
                  <div class="impact-value">{{ getImpactTraffic() }}</div>
                </div>
              </div>

              <div class="impact-item">
                <div class="impact-icon">
                  <i class="el-icon-warning-outline"></i>
                </div>
                <div class="impact-info">
                  <div class="impact-title">风险级别</div>
                  <div class="impact-value">
                    <el-tag size="medium" :type="getRiskLevelType()">
                      {{ getRiskLevel() }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="no-strategy-selected">
            <i class="el-icon-select"></i>
            <p>请从上方选择一个灰度策略</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      title="灰度策略详情"
      :visible.sync="strategyDetailVisible"
      width="60%"
    >
      <div v-if="strategyDetail" class="strategy-detail">
        <h3>{{ strategyDetail.name }}</h3>
        <p class="strategy-type">
          策略类型: {{ getStrategyTypeText(strategyDetail.type) }}
        </p>

        <el-divider content-position="left">规则详情</el-divider>

        <div class="rules-detail">
          <template v-if="strategyDetail.type === 'ip'">
            <el-table :data="strategyDetail.rules" border style="width: 100%">
              <el-table-column prop="ipRange" label="IP范围"></el-table-column>
              <el-table-column prop="percentage" label="百分比" width="120">
                <template slot-scope="scope">
                  {{ scope.row.percentage }}%
                </template>
              </el-table-column>
            </el-table>
          </template>

          <template v-else-if="strategyDetail.type === 'userId'">
            <div class="user-ids-section">
              <div class="section-header">
                <span>用户百分比</span>
                <span>{{ strategyDetail.rules[0].percentage }}%</span>
              </div>
              <div class="user-ids-list">
                <div class="list-title">用户ID列表:</div>
                <div class="tag-list">
                  <el-tag
                    v-for="(userId, index) in strategyDetail.rules[0].userIds"
                    :key="index"
                    type="info"
                    style="margin-right: 5px; margin-bottom: 5px"
                  >
                    {{ userId }}
                  </el-tag>
                </div>
              </div>
            </div>
          </template>

          <template v-else-if="strategyDetail.type === 'percentage'">
            <div class="percentage-section">
              <div class="percentage-title">流量百分比:</div>
              <el-progress
                :percentage="strategyDetail.rules[0].percentage"
                :stroke-width="18"
              >
                <span>
                  {{ strategyDetail.rules[0].percentage }}% 的用户流量
                </span>
              </el-progress>
            </div>
          </template>
        </div>
      </div>
    </el-dialog>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
      <el-button type="primary" :disabled="!selectedStrategyId" @click="goNext">
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'GrayscaleConfig',
    data() {
      return {
        activeTab: 'existing',
        selectedStrategyId: null,
        strategyForm: {
          name: '',
          type: 'percentage',
          ipRules: [{ ipRange: '', percentage: 10 }],
          userIds: [],
          userPercentage: 10,
          trafficPercentage: 10,
        },
        strategyRules: {
          name: [
            { required: true, message: '请输入策略名称', trigger: 'blur' },
            {
              min: 2,
              max: 50,
              message: '长度在 2 到 50 个字符',
              trigger: 'blur',
            },
          ],
          type: [
            { required: true, message: '请选择策略类型', trigger: 'change' },
          ],
          userIds: [
            {
              type: 'array',
              required: true,
              message: '请输入至少一个用户ID',
              trigger: 'change',
            },
          ],
        },
        strategyDetailVisible: false,
        strategyDetail: null,
      }
    },
    computed: {
      ...mapState('deployment', ['grayscale']),

      grayscaleStrategies() {
        return this.grayscale.strategies || []
      },

      selectedStrategy() {
        if (!this.selectedStrategyId) return null
        return this.grayscaleStrategies.find(
          (s) => s.id === this.selectedStrategyId
        )
      },
    },
    watch: {
      selectedStrategyId(newVal) {
        if (newVal) {
          this.SET_SELECTED_GRAYSCALE_STRATEGY(this.selectedStrategy)
        } else {
          this.SET_SELECTED_GRAYSCALE_STRATEGY(null)
        }
      },
    },
    created() {
      this.fetchGrayscaleStrategies()

      // 如果已经选择了策略，初始化选中状态
      if (this.grayscale.selectedStrategy) {
        this.selectedStrategyId = this.grayscale.selectedStrategy.id
      }
    },
    methods: {
      ...mapActions('deployment', [
        'fetchGrayscaleStrategies',
        'saveGrayscaleStrategy',
      ]),
      ...mapMutations('deployment', ['SET_SELECTED_GRAYSCALE_STRATEGY']),

      refreshStrategies() {
        this.fetchGrayscaleStrategies()
        this.$message.success('灰度策略列表已刷新')
      },

      createNewStrategy() {
        this.activeTab = 'new'
      },

      viewStrategy(strategy) {
        this.strategyDetail = strategy
        this.strategyDetailVisible = true
      },

      editStrategy(strategy) {
        this.$message.info('编辑功能开发中')
      },

      getStrategyTypeText(type) {
        switch (type) {
          case 'ip':
            return 'IP地址段'
          case 'userId':
            return '用户ID'
          case 'percentage':
            return '流量百分比'
          default:
            return '未知类型'
        }
      },

      addIpRule() {
        this.strategyForm.ipRules.push({ ipRange: '', percentage: 10 })
      },

      removeIpRule(index) {
        this.strategyForm.ipRules.splice(index, 1)
      },

      formatTooltip(val) {
        return val + '%'
      },

      submitStrategyForm() {
        this.$refs.strategyForm.validate(async (valid) => {
          if (valid) {
            try {
              // 构造策略对象
              let strategy = {
                name: this.strategyForm.name,
                type: this.strategyForm.type,
                rules: [],
              }

              // 根据不同类型构造规则
              if (this.strategyForm.type === 'ip') {
                strategy.rules = this.strategyForm.ipRules
              } else if (this.strategyForm.type === 'userId') {
                strategy.rules = [
                  {
                    userIds: this.strategyForm.userIds,
                    percentage: this.strategyForm.userPercentage,
                  },
                ]
              } else if (this.strategyForm.type === 'percentage') {
                strategy.rules = [
                  {
                    percentage: this.strategyForm.trafficPercentage,
                  },
                ]
              }

              // 模拟后端保存，生成ID
              strategy.id = this.grayscaleStrategies.length + 1

              // 将新策略添加到列表
              this.grayscale.strategies.push(strategy)

              // 选中新创建的策略
              this.selectedStrategyId = strategy.id

              this.$message.success('灰度策略创建成功')
              this.activeTab = 'existing'
              this.resetStrategyForm()
            } catch (error) {
              this.$message.error('创建灰度策略失败')
            }
          } else {
            return false
          }
        })
      },

      resetStrategyForm() {
        this.$refs.strategyForm.resetFields()
        this.strategyForm = {
          name: '',
          type: 'percentage',
          ipRules: [{ ipRange: '', percentage: 10 }],
          userIds: [],
          userPercentage: 10,
          trafficPercentage: 10,
        }
      },

      getImpactUserCount() {
        if (!this.selectedStrategy) return '0'

        // 这里应该是根据不同策略类型计算影响的用户数
        // 模拟数据
        if (this.selectedStrategy.type === 'userId') {
          return this.selectedStrategy.rules[0].userIds.length.toString()
        } else if (this.selectedStrategy.type === 'percentage') {
          return `${(this.selectedStrategy.rules[0].percentage / 100) * 10000}`
        } else if (this.selectedStrategy.type === 'ip') {
          let totalPercentage = 0
          this.selectedStrategy.rules.forEach((rule) => {
            totalPercentage += rule.percentage
          })
          return `${(totalPercentage / 100) * 5000}`
        }

        return '0'
      },

      getImpactTraffic() {
        if (!this.selectedStrategy) return '0 QPS'

        // 模拟数据
        if (this.selectedStrategy.type === 'userId') {
          return `约 ${this.selectedStrategy.rules[0].percentage * 2} QPS`
        } else if (this.selectedStrategy.type === 'percentage') {
          return `约 ${this.selectedStrategy.rules[0].percentage * 10} QPS`
        } else if (this.selectedStrategy.type === 'ip') {
          let totalPercentage = 0
          this.selectedStrategy.rules.forEach((rule) => {
            totalPercentage += rule.percentage
          })
          return `约 ${totalPercentage * 5} QPS`
        }

        return '0 QPS'
      },

      getRiskLevel() {
        if (!this.selectedStrategy) return '未知'

        let percentage = 0

        if (this.selectedStrategy.type === 'userId') {
          percentage = this.selectedStrategy.rules[0].percentage
        } else if (this.selectedStrategy.type === 'percentage') {
          percentage = this.selectedStrategy.rules[0].percentage
        } else if (this.selectedStrategy.type === 'ip') {
          this.selectedStrategy.rules.forEach((rule) => {
            percentage += rule.percentage
          })
          percentage = Math.min(percentage, 100)
        }

        if (percentage <= 10) {
          return '低'
        } else if (percentage <= 30) {
          return '中'
        } else {
          return '高'
        }
      },

      getRiskLevelType() {
        const level = this.getRiskLevel()
        if (level === '低') return 'success'
        if (level === '中') return 'warning'
        if (level === '高') return 'danger'
        return 'info'
      },

      goNext() {
        if (this.selectedStrategyId) {
          this.$emit('next')
        } else {
          this.$message.warning('请先选择一个灰度策略')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .grayscale-config-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 16px;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .empty-strategies {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }

      .sub-text {
        margin-top: 10px;
        color: #909399;
        font-size: 14px;
      }
    }

    .strategies-list {
      .strategy-radio-group {
        width: 100%;
        display: block;
      }

      .strategy-item {
        margin-bottom: 15px;
        padding: 15px;
        border-radius: 4px;
        border: 1px solid #ebeef5;
        transition: all 0.3s;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          background-color: #f5f7fa;
        }

        .strategy-info {
          margin-left: 25px;

          .strategy-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            .strategy-name {
              font-weight: 500;
              font-size: 16px;
              margin-right: 10px;
            }
          }

          .strategy-rules {
            margin-bottom: 10px;

            .rule-item {
              margin-bottom: 5px;

              .rule-label {
                font-weight: 500;
                margin-right: 10px;
                color: #606266;
              }
            }
          }

          .strategy-actions {
            text-align: right;
          }
        }
      }
    }

    .strategy-form {
      .rule-input-group {
        display: flex;
        align-items: center;
      }
    }

    .selected-strategy-info {
      .strategy-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          margin-right: 10px;
        }
      }

      .strategy-rules-detail {
        margin-bottom: 20px;

        .user-ids-list {
          .user-ids-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-weight: 500;
          }
        }
      }

      .impact-analysis {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;

        .impact-item {
          flex: 1;
          min-width: 200px;
          display: flex;
          align-items: center;
          padding: 15px;
          border-radius: 4px;
          background-color: #f5f7fa;

          .impact-icon {
            font-size: 32px;
            color: #409eff;
            margin-right: 15px;
          }

          .impact-info {
            .impact-title {
              color: #606266;
              margin-bottom: 5px;
            }

            .impact-value {
              font-weight: 500;
              font-size: 16px;
            }
          }
        }
      }
    }

    .no-strategy-selected {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }
    }

    .strategy-detail {
      h3 {
        margin-top: 0;
        margin-bottom: 5px;
      }

      .strategy-type {
        color: #606266;
        margin-bottom: 20px;
      }

      .rules-detail {
        .user-ids-section {
          .section-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ebeef5;
            font-weight: 500;
          }

          .user-ids-list {
            .list-title {
              margin-bottom: 10px;
              font-weight: 500;
            }
          }
        }

        .percentage-section {
          .percentage-title {
            margin-bottom: 10px;
            font-weight: 500;
          }
        }
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
</style>
