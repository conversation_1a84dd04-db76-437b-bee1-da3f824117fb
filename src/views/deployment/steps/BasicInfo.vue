<template>
  <div class="basic-info-container">
    <div class="step-title">
      <h3>基本信息维护</h3>
      <p class="step-description">
        设置发版的基本信息，包括人员选择、变更日志和预期时间等
      </p>
    </div>

    <el-form
      ref="basicInfoForm"
      :model="basicInfo"
      :rules="rules"
      label-width="120px"
      class="form-container"
    >
      <el-form-item label="发版名称" prop="releaseName">
        <el-input
          v-model="basicInfo.releaseName"
          placeholder="请输入发版名称"
        ></el-input>
      </el-form-item>

      <el-form-item label="预期时间" prop="expectedReleaseDate">
        <el-date-picker
          v-model="basicInfo.expectedReleaseDate"
          type="datetime"
          placeholder="选择预期发版时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="相关人员" prop="personnel">
        <div class="personnel-selection">
          <el-tabs type="border-card">
            <el-tab-pane label="开发/测试人员">
              <el-transfer
                v-model="developmentPersonnel"
                :data="filteredPersonnel('技术部', '测试部')"
                :titles="['可选人员', '已选人员']"
                :button-texts="['移除', '添加']"
                @change="handlePersonnelChange"
              >
                <template #left-footer>
                  <el-button size="small" @click="selectAllDevelopers">
                    选择全部
                  </el-button>
                </template>
              </el-transfer>
            </el-tab-pane>
            <el-tab-pane label="运维人员">
              <el-transfer
                v-model="opsPersonnel"
                :data="filteredPersonnel('运维部')"
                :titles="['可选人员', '已选人员']"
                :button-texts="['移除', '添加']"
                @change="handlePersonnelChange"
              ></el-transfer>
            </el-tab-pane>
            <el-tab-pane label="产品/项目经理">
              <el-transfer
                v-model="productPersonnel"
                :data="filteredPersonnel('产品部')"
                :titles="['可选人员', '已选人员']"
                :button-texts="['移除', '添加']"
                @change="handlePersonnelChange"
              ></el-transfer>
            </el-tab-pane>
          </el-tabs>

          <div class="selected-personnel-summary">
            <h4>已选择人员:</h4>
            <el-tag
              v-for="id in basicInfo.personnel"
              :key="id"
              closable
              style="margin-right: 5px; margin-bottom: 5px"
              @close="removePersonnel(id)"
            >
              {{ getPersonnelName(id) }}
            </el-tag>
            <div v-if="basicInfo.personnel.length === 0" class="empty-tip">
              尚未选择任何人员
            </div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="变更日志" prop="changelog">
        <el-input
          v-model="basicInfo.changelog"
          type="textarea"
          :rows="4"
          placeholder="请输入本次发版的主要变更内容"
        ></el-input>
      </el-form-item>

      <el-form-item label="Bug修复列表">
        <div class="bugfix-list">
          <div
            v-for="(bug, index) in basicInfo.bugfixes"
            :key="index"
            class="bugfix-item"
          >
            <el-input
              v-model="basicInfo.bugfixes[index]"
              placeholder="Bug修复项"
            ></el-input>
            <el-button
              type="danger"
              icon="el-icon-delete"
              circle
              @click="removeBugfix(index)"
            ></el-button>
          </div>
          <el-button type="primary" icon="el-icon-plus" @click="addBugfix">
            添加Bug修复项
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="发版类型" prop="deploymentType">
        <el-radio-group v-model="deploymentType">
          <el-radio-button label="standard">标准发版</el-radio-button>
          <el-radio-button label="grayscale">灰度发版</el-radio-button>
        </el-radio-group>
        <div class="deployment-type-description">
          <template v-if="deploymentType === 'standard'">
            标准发版：先发布到Canary环境，观察一段时间后再推送到生产环境
          </template>
          <template v-else>
            灰度发版：先配置灰度策略，发布到部分用户，完成灰度后再全量发布
          </template>
        </div>
      </el-form-item>
    </el-form>

    <div class="step-actions">
      <el-button type="primary" @click="submitForm">下一步</el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'BasicInfo',
    data() {
      return {
        developmentPersonnel: [],
        opsPersonnel: [],
        productPersonnel: [],
        rules: {
          releaseName: [
            { required: true, message: '请输入发版名称', trigger: 'blur' },
            {
              min: 3,
              max: 50,
              message: '长度在 3 到 50 个字符',
              trigger: 'blur',
            },
          ],
          expectedReleaseDate: [
            {
              required: true,
              message: '请选择预期发版时间',
              trigger: 'change',
            },
          ],
          personnel: [
            {
              type: 'array',
              required: true,
              message: '请至少选择一名相关人员',
              trigger: 'change',
            },
          ],
          changelog: [
            { required: true, message: '请输入变更日志', trigger: 'blur' },
          ],
        },
      }
    },
    computed: {
      ...mapState('deployment', [
        'basicInfo',
        'personnelList',
        'deploymentType',
      ]),

      deploymentType: {
        get() {
          return this.$store.state.deployment.deploymentType
        },
        set(value) {
          this.setDeploymentType(value)
        },
      },
    },
    methods: {
      ...mapActions('deployment', ['submitBasicInfo', 'setDeploymentType']),
      ...mapMutations('deployment', ['UPDATE_BASIC_INFO']),

      filteredPersonnel(...departments) {
        return this.personnelList
          .filter((person) => departments.includes(person.department))
          .map((person) => ({
            key: person.id,
            label: `${person.name} (${person.role})`,
            disabled: false,
          }))
      },

      handlePersonnelChange() {
        const allPersonnel = [
          ...this.developmentPersonnel,
          ...this.opsPersonnel,
          ...this.productPersonnel,
        ]

        // 去重
        const uniquePersonnel = [...new Set(allPersonnel)]
        this.UPDATE_BASIC_INFO({ key: 'personnel', value: uniquePersonnel })
      },

      selectAllDevelopers() {
        this.developmentPersonnel = this.filteredPersonnel(
          '技术部',
          '测试部'
        ).map((item) => item.key)
        this.handlePersonnelChange()
      },

      removePersonnel(id) {
        this.developmentPersonnel = this.developmentPersonnel.filter(
          (item) => item !== id
        )
        this.opsPersonnel = this.opsPersonnel.filter((item) => item !== id)
        this.productPersonnel = this.productPersonnel.filter(
          (item) => item !== id
        )
        this.handlePersonnelChange()
      },

      getPersonnelName(id) {
        const person = this.personnelList.find((p) => p.id === id)
        return person ? `${person.name} (${person.role})` : id
      },

      addBugfix() {
        const bugfixes = [...this.basicInfo.bugfixes]
        bugfixes.push('')
        this.UPDATE_BASIC_INFO({ key: 'bugfixes', value: bugfixes })
      },

      removeBugfix(index) {
        const bugfixes = [...this.basicInfo.bugfixes]
        bugfixes.splice(index, 1)
        this.UPDATE_BASIC_INFO({ key: 'bugfixes', value: bugfixes })
      },

      submitForm() {
        this.$refs.basicInfoForm.validate(async (valid) => {
          if (valid) {
            try {
              await this.submitBasicInfo()
              this.$notify({
                title: '成功',
                message: '基本信息保存成功',
                type: 'success',
                duration: 2000,
              })
              this.$emit('next')
            } catch (error) {
              this.$notify.error({
                title: '错误',
                message: '保存基本信息时发生错误',
              })
            }
          } else {
            this.$message.error('请完善表单信息')
            return false
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .basic-info-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .form-container {
      max-width: 900px;
      margin: 0 auto;
    }

    .personnel-selection {
      margin-bottom: 20px;

      .selected-personnel-summary {
        margin-top: 20px;
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        padding: 15px;

        h4 {
          margin-top: 0;
          margin-bottom: 10px;
        }

        .empty-tip {
          color: #909399;
          font-style: italic;
          padding: 10px 0;
        }
      }
    }

    .bugfix-list {
      .bugfix-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;

        .el-input {
          margin-right: 10px;
        }
      }
    }

    .deployment-type-description {
      margin-top: 10px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      color: #606266;
      font-size: 14px;
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
</style>
