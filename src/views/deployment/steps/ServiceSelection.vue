<template>
  <div class="service-selection-container">
    <div class="step-title">
      <h3>服务选择和配置发布顺序</h3>
      <p class="step-description">选择需要发布的服务并配置它们的发布顺序</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <div class="service-categories">
          <el-tabs v-model="activeServiceTab" type="card">
            <el-tab-pane label="后端服务" name="backend">
              <service-selection-table
                :services="filteredServices('backend')"
                @select-change="handleServiceSelection"
              ></service-selection-table>
            </el-tab-pane>

            <el-tab-pane label="前端项目" name="frontend">
              <service-selection-table
                :services="filteredServices('frontend')"
                @select-change="handleServiceSelection"
              ></service-selection-table>
            </el-tab-pane>

            <el-tab-pane label="半弹窗项目" name="half-popup">
              <service-selection-table
                :services="filteredServices('half-popup')"
                @select-change="handleServiceSelection"
              ></service-selection-table>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>

    <el-divider content-position="center">发布顺序配置</el-divider>

    <el-row>
      <el-col :span="24">
        <div v-if="selectedServices.length > 0" class="release-order-container">
          <h4>拖拽服务调整发布顺序</h4>
          <p class="helper-text">系统将按照此顺序依次发布服务</p>

          <el-alert
            title="注意：请确保依赖关系正确，被依赖的服务应该先发布"
            type="warning"
            :closable="false"
            show-icon
          ></el-alert>

          <div class="order-list-container">
            <draggable
              v-model="serviceOrder"
              animation="300"
              handle=".drag-handle"
              ghost-class="ghost"
              @end="updateDependencyWarnings"
            >
              <transition-group type="transition" name="flip-list">
                <div
                  v-for="service in serviceOrder"
                  :key="service.id"
                  class="order-item"
                  :class="{ 'has-warning': service.hasWarning }"
                >
                  <div class="drag-handle">
                    <i class="el-icon-rank"></i>
                  </div>
                  <div class="order-item-content">
                    <div class="service-name">
                      <el-tag :type="getServiceTypeTag(service.type)">
                        {{ getServiceTypeName(service.type) }}
                      </el-tag>
                      {{ service.name }}
                    </div>
                    <div class="service-description">
                      {{ service.description }}
                    </div>
                    <div v-if="service.hasWarning" class="dependency-warning">
                      <i class="el-icon-warning-outline"></i>
                      依赖关系可能有误，此服务依赖于：
                      <template v-for="(depId, index) in service.dependencies">
                        <el-tag :key="depId" size="mini" type="danger">
                          {{ getServiceNameById(depId) }}
                        </el-tag>
                        <span
                          v-if="index < service.dependencies.length - 1"
                          :key="`sep-${index}`"
                        >
                          ,
                        </span>
                      </template>
                    </div>
                  </div>
                  <div class="order-actions">
                    <el-tooltip content="上移" placement="top">
                      <el-button
                        type="text"
                        icon="el-icon-top"
                        :disabled="isFirstService(service)"
                        @click="moveServiceUp(service)"
                      ></el-button>
                    </el-tooltip>
                    <el-tooltip content="下移" placement="top">
                      <el-button
                        type="text"
                        icon="el-icon-bottom"
                        :disabled="isLastService(service)"
                        @click="moveServiceDown(service)"
                      ></el-button>
                    </el-tooltip>
                    <el-tooltip content="移除" placement="top">
                      <el-button
                        type="text"
                        icon="el-icon-delete"
                        @click="removeService(service)"
                      ></el-button>
                    </el-tooltip>
                  </div>
                </div>
              </transition-group>
            </draggable>

            <div v-if="serviceOrder.length === 0" class="empty-order-list">
              请从上方选择要发布的服务
            </div>
          </div>
        </div>
        <div v-else class="no-services-selected">
          <i class="el-icon-box"></i>
          <p>尚未选择任何服务</p>
          <p class="sub-text">请在上方选择需要发布的服务</p>
        </div>
      </el-col>
    </el-row>

    <div v-if="selectedServices.length > 0" class="dependency-graph">
      <h4>依赖关系图</h4>
      <p class="helper-text">查看所选服务的依赖关系</p>
      <div ref="graphContainer" class="graph-container">
        <!-- 依赖关系图将在mounted中使用D3.js渲染 -->
      </div>
    </div>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
      <el-button
        type="primary"
        :disabled="selectedServices.length === 0"
        @click="submitServiceOrderAndNext"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'
  import draggable from 'vuedraggable'
  import ServiceSelectionTable from './ServiceSelectionTable'

  export default {
    name: 'ServiceSelection',
    components: {
      draggable,
      ServiceSelectionTable,
    },
    data() {
      return {
        activeServiceTab: 'backend',
        localSelectedServices: [],
        serviceOrder: [],
      }
    },
    computed: {
      ...mapState('deployment', ['servicesList', 'selectedServices']),

      filteredServices() {
        return (type) => {
          return this.servicesList.filter((service) => service.type === type)
        }
      },
    },
    watch: {
      selectedServices: {
        handler(newVal) {
          // 当选择的服务变化时，更新发布顺序
          this.updateServiceOrder()
        },
        immediate: true,
      },
    },
    mounted() {
      this.updateServiceOrder()
    },
    methods: {
      ...mapActions('deployment', ['submitServiceOrder']),
      ...mapMutations('deployment', [
        'SET_SELECTED_SERVICES',
        'SET_SERVICE_ORDER',
      ]),

      handleServiceSelection(selection) {
        const currentTab = this.activeServiceTab

        // 移除当前类型的所有服务
        const otherTypesServices = this.selectedServices.filter((id) => {
          const service = this.servicesList.find((s) => s.id === id)
          return service && service.type !== currentTab
        })

        // 合并所选的服务
        const newSelection = [...otherTypesServices, ...selection]
        this.SET_SELECTED_SERVICES(newSelection)
      },

      updateServiceOrder() {
        // 根据selectedServices更新serviceOrder
        const orderList = this.selectedServices
          .map((id) => {
            return this.servicesList.find((service) => service.id === id)
          })
          .filter(Boolean) // 过滤掉undefined

        // 尝试基于依赖关系自动排序
        const sortedList = this.sortByDependencies(orderList)
        this.serviceOrder = sortedList.map((service) => ({
          ...service,
          hasWarning: false,
        }))

        this.updateDependencyWarnings()
        this.SET_SERVICE_ORDER(this.serviceOrder)
      },

      sortByDependencies(services) {
        // 简单的拓扑排序算法
        const result = []
        const visited = new Set()
        const temp = new Set()

        const visit = (serviceId) => {
          if (temp.has(serviceId)) {
            // 检测到循环依赖，直接返回
            return
          }

          if (visited.has(serviceId)) {
            return
          }

          temp.add(serviceId)

          const service = services.find((s) => s.id === serviceId)
          if (service) {
            service.dependencies.forEach((depId) => {
              const depService = services.find((s) => s.id === depId)
              if (depService) {
                visit(depId)
              }
            })

            visited.add(serviceId)
            temp.delete(serviceId)
            result.push(service)
          }
        }

        services.forEach((service) => {
          if (!visited.has(service.id)) {
            visit(service.id)
          }
        })

        return result.reverse()
      },

      updateDependencyWarnings() {
        const serviceOrderIds = this.serviceOrder.map((s) => s.id)

        this.serviceOrder.forEach((service, index) => {
          // 检查每个服务的依赖是否在它之前已经发布
          service.hasWarning = service.dependencies.some((depId) => {
            const depIndex = serviceOrderIds.indexOf(depId)
            return depIndex > index || depIndex === -1
          })
        })

        this.SET_SERVICE_ORDER(this.serviceOrder)
      },

      moveServiceUp(service) {
        const index = this.serviceOrder.findIndex((s) => s.id === service.id)
        if (index > 0) {
          this.serviceOrder.splice(index, 1)
          this.serviceOrder.splice(index - 1, 0, service)
          this.updateDependencyWarnings()
        }
      },

      moveServiceDown(service) {
        const index = this.serviceOrder.findIndex((s) => s.id === service.id)
        if (index < this.serviceOrder.length - 1) {
          this.serviceOrder.splice(index, 1)
          this.serviceOrder.splice(index + 1, 0, service)
          this.updateDependencyWarnings()
        }
      },

      removeService(service) {
        const newSelectedServices = this.selectedServices.filter(
          (id) => id !== service.id
        )
        this.SET_SELECTED_SERVICES(newSelectedServices)
      },

      isFirstService(service) {
        return this.serviceOrder.indexOf(service) === 0
      },

      isLastService(service) {
        return (
          this.serviceOrder.indexOf(service) === this.serviceOrder.length - 1
        )
      },

      getServiceNameById(id) {
        const service = this.servicesList.find((s) => s.id === id)
        return service ? service.name : `未知服务(${id})`
      },

      getServiceTypeTag(type) {
        switch (type) {
          case 'backend':
            return 'primary'
          case 'frontend':
            return 'success'
          case 'half-popup':
            return 'warning'
          default:
            return 'info'
        }
      },

      getServiceTypeName(type) {
        switch (type) {
          case 'backend':
            return '后端'
          case 'frontend':
            return '前端'
          case 'half-popup':
            return '半弹窗'
          default:
            return '未知'
        }
      },

      async submitServiceOrderAndNext() {
        try {
          await this.submitServiceOrder()
          this.$notify({
            title: '成功',
            message: '服务发布顺序保存成功',
            type: 'success',
            duration: 2000,
          })
          this.$emit('next')
        } catch (error) {
          this.$notify.error({
            title: '错误',
            message: '保存服务发布顺序时发生错误',
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .service-selection-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .helper-text {
      color: #909399;
      font-size: 14px;
      margin-top: 5px;
    }

    .service-categories {
      margin-bottom: 30px;
    }

    .release-order-container {
      h4 {
        margin-top: 0;
        margin-bottom: 10px;
      }

      .el-alert {
        margin: 15px 0;
      }

      .order-list-container {
        margin-top: 20px;

        .order-item {
          display: flex;
          align-items: center;
          padding: 15px;
          margin-bottom: 10px;
          border-radius: 4px;
          background-color: #f5f7fa;
          border: 1px solid #e4e7ed;
          transition: all 0.3s;

          &.has-warning {
            border-color: #e6a23c;
            background-color: #fdf6ec;
          }

          &:hover {
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
          }

          .drag-handle {
            cursor: move;
            margin-right: 15px;
            color: #909399;

            i {
              font-size: 20px;
            }
          }

          .order-item-content {
            flex: 1;

            .service-name {
              font-size: 16px;
              font-weight: 500;
              margin-bottom: 5px;
              display: flex;
              align-items: center;

              .el-tag {
                margin-right: 10px;
              }
            }

            .service-description {
              color: #606266;
              font-size: 14px;
              margin-bottom: 5px;
            }

            .dependency-warning {
              color: #e6a23c;
              font-size: 13px;
              margin-top: 8px;

              i {
                margin-right: 5px;
              }

              .el-tag {
                margin: 0 3px;
              }
            }
          }

          .order-actions {
            display: flex;
            align-items: center;

            .el-button {
              margin-left: 5px;
              font-size: 16px;
            }
          }
        }

        .empty-order-list {
          padding: 30px;
          text-align: center;
          color: #909399;
          background-color: #f5f7fa;
          border-radius: 4px;
        }
      }
    }

    .no-services-selected {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }

      .sub-text {
        margin-top: 10px;
        color: #909399;
        font-size: 14px;
      }
    }

    .dependency-graph {
      margin-top: 30px;

      h4 {
        margin-top: 0;
        margin-bottom: 10px;
      }

      .graph-container {
        height: 300px;
        background-color: #f5f7fa;
        border-radius: 4px;
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }

  .flip-list-move {
    transition: transform 0.5s;
  }

  .ghost {
    opacity: 0.5;
    background: #c8ebfb !important;
  }
</style>
