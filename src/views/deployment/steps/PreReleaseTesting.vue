<template>
  <div class="pre-release-testing-container">
    <div class="step-title">
      <h3>预发环境部署与验证</h3>
      <p class="step-description">将构建好的镜像部署到预发环境进行验证</p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="deployment-card">
          <div slot="header" class="card-header">
            <span>预发环境部署状态</span>
            <el-button
              v-if="!isDeploying && !isDeployed"
              type="primary"
              size="small"
              @click="deployToPreRelease"
            >
              开始部署
            </el-button>
            <el-button
              v-if="isDeploying"
              type="danger"
              size="small"
              @click="cancelDeployment"
            >
              取消部署
            </el-button>
            <el-button
              v-if="isDeployed && hasFailures"
              type="warning"
              size="small"
              @click="retryFailedDeployments"
            >
              重试失败项
            </el-button>
          </div>

          <div v-if="!isDeploying && !isDeployed" class="empty-state">
            <i class="el-icon-upload"></i>
            <p>点击"开始部署"将镜像部署到预发环境</p>
          </div>

          <div v-else class="deployment-status">
            <el-progress
              :percentage="overallProgress"
              :status="deploymentStatus === 'failed' ? 'exception' : ''"
              :stroke-width="18"
              class="overall-progress"
            >
              <span>{{ deploymentStatusText }}</span>
            </el-progress>

            <div class="service-deployments">
              <div
                v-for="service in deploymentServices"
                :key="service.id"
                class="service-deployment-item"
              >
                <div class="service-info">
                  <span class="service-name">{{ service.name }}</span>
                  <el-tag :type="getStatusType(service.status)">
                    {{ getStatusText(service.status) }}
                  </el-tag>
                </div>

                <el-progress
                  :percentage="service.progress || 0"
                  :status="service.status === 'failed' ? 'exception' : ''"
                  :stroke-width="15"
                ></el-progress>

                <div class="service-actions">
                  <el-button
                    type="text"
                    icon="el-icon-view"
                    @click="viewServiceLogs(service)"
                  >
                    查看日志
                  </el-button>
                  <el-button
                    v-if="service.status === 'success'"
                    type="text"
                    icon="el-icon-position"
                    @click="openServiceEndpoint(service)"
                  >
                    访问服务
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="24">
        <el-card class="validation-card">
          <div slot="header" class="card-header">
            <span>预发环境验证</span>
            <div class="header-actions">
              <el-button
                type="success"
                size="small"
                :disabled="!isDeployed || !allValidated"
                @click="completeValidation"
              >
                完成验证
              </el-button>
              <el-button
                type="primary"
                plain
                size="small"
                :disabled="!isDeployed"
                @click="validateAll"
              >
                全部验证通过
              </el-button>
            </div>
          </div>

          <div v-if="!isDeployed" class="empty-state">
            <i class="el-icon-check"></i>
            <p>部署完成后进行验证</p>
          </div>

          <div v-else class="validation-content">
            <el-collapse v-model="activeValidation" accordion>
              <el-collapse-item
                v-for="(test, index) in validationTests"
                :key="index"
                :title="test.name"
                :name="index"
              >
                <template slot="title">
                  <div class="test-title">
                    <span>{{ test.name }}</span>
                    <el-tag
                      v-if="test.status"
                      :type="getValidationStatusType(test.status)"
                      size="small"
                    >
                      {{ getValidationStatusText(test.status) }}
                    </el-tag>
                  </div>
                </template>

                <div class="test-content">
                  <div class="test-description">
                    <p>{{ test.description }}</p>
                  </div>

                  <div class="test-steps">
                    <h4>验证步骤：</h4>
                    <ol>
                      <li
                        v-for="(step, stepIndex) in test.steps"
                        :key="stepIndex"
                      >
                        {{ step }}
                      </li>
                    </ol>
                  </div>

                  <div v-if="test.status === 'completed'" class="test-result">
                    <el-form label-width="100px">
                      <el-form-item label="验证结果">
                        <el-radio-group
                          v-model="test.result"
                          @change="updateTestStatus(index)"
                        >
                          <el-radio :label="'pass'">通过</el-radio>
                          <el-radio :label="'fail'">不通过</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item v-if="test.result === 'fail'" label="备注">
                        <el-input
                          v-model="test.note"
                          type="textarea"
                          :rows="3"
                          placeholder="请输入验证失败的原因"
                        >
                          >
                        </el-input>
                      </el-form-item>
                    </el-form>
                  </div>

                  <div class="test-actions">
                    <el-button
                      type="primary"
                      size="small"
                      :disabled="test.status === 'completed'"
                      @click="completeTest(index)"
                    >
                      完成验证
                    </el-button>
                    <el-button
                      type="info"
                      size="small"
                      :disabled="test.status === 'pending'"
                      @click="resetTest(index)"
                    >
                      重置
                    </el-button>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      title="服务部署日志"
      :visible.sync="logsDialogVisible"
      width="60%"
    >
      <div v-if="currentServiceLogs" class="service-logs">
        <pre>{{ currentServiceLogs }}</pre>
      </div>
    </el-dialog>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
      <el-button type="primary" :disabled="!canProceed" @click="goNext">
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'PreReleaseTesting',
    data() {
      return {
        isDeploying: false,
        isDeployed: false,
        overallProgress: 0,
        deploymentStatus: 'pending',
        deploymentServices: [],
        logsDialogVisible: false,
        currentServiceLogs: '',
        validationTests: [
          {
            name: '基础功能验证',
            description: '验证核心功能是否正常运行',
            status: 'pending',
            result: null,
            note: '',
            steps: [
              '登录系统检查是否正常',
              '创建测试数据并验证',
              '验证核心业务流程',
            ],
          },
          {
            name: '性能测试',
            description: '验证系统性能是否满足要求',
            status: 'pending',
            result: null,
            note: '',
            steps: [
              '执行并发用户测试',
              '验证响应时间是否在可接受范围',
              '检查系统资源使用情况',
            ],
          },
          {
            name: '接口兼容性测试',
            description: '验证API接口是否向后兼容',
            status: 'pending',
            result: null,
            note: '',
            steps: [
              '使用旧版客户端调用新接口',
              '验证是否有兼容性问题',
              '检查接口返回格式是否符合预期',
            ],
          },
          {
            name: 'UI兼容性检查',
            description: '验证UI在各浏览器中的表现',
            status: 'pending',
            result: null,
            note: '',
            steps: [
              '在Chrome、Firefox、Safari等浏览器中打开',
              '检查布局是否正常显示',
              '验证交互功能是否正常',
            ],
          },
        ],
        activeValidation: 0,
        deployTimer: null,
      }
    },
    computed: {
      ...mapState('deployment', [
        'buildInfo',
        'selectedServices',
        'servicesList',
      ]),

      deploymentStatusText() {
        switch (this.deploymentStatus) {
          case 'pending':
            return '等待部署'
          case 'deploying':
            return `部署中 (${this.overallProgress}%)`
          case 'success':
            return '部署成功'
          case 'failed':
            return '部署失败'
          default:
            return '未知状态'
        }
      },

      hasFailures() {
        return this.deploymentServices.some(
          (service) => service.status === 'failed'
        )
      },

      allValidated() {
        return this.validationTests.every(
          (test) => test.status === 'completed' && test.result === 'pass'
        )
      },

      canProceed() {
        return this.isDeployed && this.allValidated
      },
    },
    beforeDestroy() {
      if (this.deployTimer) {
        clearInterval(this.deployTimer)
      }
    },
    methods: {
      ...mapActions('deployment', [
        'deployToPreRelease',
        'checkPreReleaseStatus',
      ]),
      ...mapMutations('deployment', [
        'SET_PRE_RELEASE',
        'UPDATE_PRE_RELEASE_STATUS',
      ]),

      deployToPreRelease() {
        this.isDeploying = true
        this.deploymentStatus = 'deploying'
        this.overallProgress = 0

        // 初始化服务部署状态
        this.deploymentServices = this.selectedServices.map((id) => {
          const service = this.servicesList.find((s) => s.id === id)
          return {
            ...service,
            status: 'pending',
            progress: 0,
            logs: '',
            endpoint: `https://pre-${service.name}.example.com`,
          }
        })

        // 模拟部署过程
        this.simulateDeployment()
      },

      simulateDeployment() {
        this.deployTimer = setInterval(() => {
          let allCompleted = true
          let totalProgress = 0

          // 更新每个服务的部署进度
          this.deploymentServices.forEach((service) => {
            if (
              service.status === 'pending' ||
              service.status === 'deploying'
            ) {
              service.status = 'deploying'
              service.progress += Math.floor(Math.random() * 10)

              if (service.progress >= 100) {
                service.progress = 100
                // 有10%的概率部署失败
                service.status = Math.random() < 0.1 ? 'failed' : 'success'

                if (service.status === 'failed') {
                  service.logs += '\n[ERROR] 部署失败: 无法拉取镜像'
                } else {
                  service.logs += '\n[INFO] 部署成功，服务已启动'
                }
              } else {
                allCompleted = false
                service.logs += `\n[INFO] 部署进度: ${service.progress}%`
              }
            }

            totalProgress += service.progress
          })

          // 更新总体进度
          this.overallProgress = Math.floor(
            totalProgress / this.deploymentServices.length
          )

          // 如果所有服务都完成部署，停止计时器
          if (allCompleted) {
            clearInterval(this.deployTimer)
            this.deployTimer = null
            this.isDeploying = false
            this.isDeployed = true

            // 检查是否有任何服务部署失败
            const hasFailure = this.deploymentServices.some(
              (s) => s.status === 'failed'
            )
            this.deploymentStatus = hasFailure ? 'failed' : 'success'

            // 更新状态到store
            this.UPDATE_PRE_RELEASE_STATUS({
              status: this.deploymentStatus,
              progress: this.overallProgress,
              logs: this.generateDeploymentLogs(),
            })
          }
        }, 500)
      },

      generateDeploymentLogs() {
        return this.deploymentServices
          .map((service) => `${service.name}: ${service.status.toUpperCase()}`)
          .join('\n')
      },

      cancelDeployment() {
        if (this.deployTimer) {
          clearInterval(this.deployTimer)
          this.deployTimer = null
        }

        this.isDeploying = false
        this.deploymentStatus = 'cancelled'

        this.$message.warning('部署已取消')
      },

      retryFailedDeployments() {
        const failedServices = this.deploymentServices.filter(
          (s) => s.status === 'failed'
        )

        failedServices.forEach((service) => {
          service.status = 'pending'
          service.progress = 0
          service.logs += '\n[INFO] 重新开始部署'
        })

        this.isDeploying = true
        this.deploymentStatus = 'deploying'

        // 重新模拟部署过程
        this.simulateDeployment()
      },

      viewServiceLogs(service) {
        this.currentServiceLogs = service.logs || this.generateMockLogs(service)
        this.logsDialogVisible = true
      },

      generateMockLogs(service) {
        return `[INFO] 开始部署 ${service.name}
[INFO] 拉取镜像: registry.example.com/${service.name}:latest
[INFO] 镜像拉取成功
[INFO] 创建部署配置...
[INFO] 应用部署配置...
[INFO] 等待Pod启动...
[INFO] Pod已启动
[INFO] 执行健康检查...
[INFO] 健康检查通过
[INFO] 服务路由配置成功
[INFO] 部署完成，服务地址: https://pre-${service.name}.example.com`
      },

      openServiceEndpoint(service) {
        // 在实际应用中应该打开预发环境的服务URL
        this.$message.info(`正在打开服务: ${service.endpoint}`)
        window.open(service.endpoint, '_blank')
      },

      getStatusType(status) {
        switch (status) {
          case 'success':
            return 'success'
          case 'failed':
            return 'danger'
          case 'deploying':
            return 'warning'
          default:
            return 'info'
        }
      },

      getStatusText(status) {
        switch (status) {
          case 'success':
            return '成功'
          case 'failed':
            return '失败'
          case 'deploying':
            return '部署中'
          case 'pending':
            return '等待中'
          default:
            return '未知'
        }
      },

      getValidationStatusType(status) {
        switch (status) {
          case 'completed':
            return 'success'
          case 'in-progress':
            return 'warning'
          default:
            return 'info'
        }
      },

      getValidationStatusText(status) {
        switch (status) {
          case 'completed':
            return '已验证'
          case 'in-progress':
            return '验证中'
          case 'pending':
            return '待验证'
          default:
            return '未知'
        }
      },

      completeTest(index) {
        this.validationTests[index].status = 'completed'
        if (!this.validationTests[index].result) {
          this.validationTests[index].result = 'pass'
        }
      },

      resetTest(index) {
        this.validationTests[index].status = 'pending'
        this.validationTests[index].result = null
        this.validationTests[index].note = ''
      },

      updateTestStatus(index) {
        // 已经在UI中通过v-model绑定了result，这里可以做一些额外处理
        if (
          this.validationTests[index].result === 'fail' &&
          !this.validationTests[index].note
        ) {
          this.$message.warning('请填写验证失败的原因')
        }
      },

      validateAll() {
        this.validationTests.forEach((test, index) => {
          test.status = 'completed'
          test.result = 'pass'
        })

        this.$message.success('已将所有验证项标记为通过')
      },

      completeValidation() {
        this.$confirm('确认完成预发环境验证，进入发版队列?', '确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success',
        })
          .then(() => {
            this.$message.success('验证完成！')
            this.goNext()
          })
          .catch(() => {})
      },

      goNext() {
        if (this.canProceed) {
          this.$emit('next')
        } else {
          this.$message.warning('请先完成所有验证项')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .pre-release-testing-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .empty-state {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }
    }

    .deployment-status {
      .overall-progress {
        margin-bottom: 30px;
      }

      .service-deployments {
        .service-deployment-item {
          margin-bottom: 20px;
          padding-bottom: 20px;
          border-bottom: 1px dashed #ebeef5;

          &:last-child {
            border-bottom: none;
            padding-bottom: 0;
          }

          .service-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;

            .service-name {
              font-weight: 500;
              font-size: 16px;
            }
          }

          .service-actions {
            margin-top: 10px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
          }
        }
      }
    }

    .validation-content {
      .test-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .test-content {
        padding: 10px 0;

        .test-description {
          margin-bottom: 15px;
          color: #606266;
        }

        .test-steps {
          margin-bottom: 20px;

          h4 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 14px;
            color: #303133;
          }

          ol {
            margin: 0;
            padding-left: 20px;

            li {
              margin-bottom: 5px;
              color: #606266;
            }
          }
        }

        .test-result {
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f5f7fa;
          border-radius: 4px;
        }

        .test-actions {
          display: flex;
          justify-content: flex-end;
          gap: 10px;
        }
      }
    }

    .service-logs {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      height: 300px;
      overflow-y: auto;

      pre {
        margin: 0;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
        font-size: 14px;
        color: #606266;
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
</style>
