<template>
  <div class="grayscale-release-container">
    <div class="step-title">
      <h3>灰度发布</h3>
      <p class="step-description">
        逐步将服务推送给灰度用户，观察效果后再全量发布
      </p>
    </div>

    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="strategy-card">
          <div slot="header" class="card-header">
            <span>灰度策略</span>
          </div>

          <div v-if="selectedStrategy" class="selected-strategy">
            <div class="strategy-header">
              <h3>{{ selectedStrategy.name }}</h3>
              <el-tag size="medium">
                {{ getStrategyTypeText(selectedStrategy.type) }}
              </el-tag>
            </div>

            <div class="strategy-rules">
              <template v-if="selectedStrategy.type === 'ip'">
                <el-table
                  :data="selectedStrategy.rules"
                  border
                  style="width: 100%"
                >
                  <el-table-column
                    prop="ipRange"
                    label="IP范围"
                  ></el-table-column>
                  <el-table-column prop="percentage" label="百分比" width="120">
                    <template slot-scope="scope">
                      {{ scope.row.percentage }}%
                    </template>
                  </el-table-column>
                </el-table>
              </template>

              <template v-else-if="selectedStrategy.type === 'userId'">
                <div class="user-ids-section">
                  <div class="section-header">
                    <span>
                      用户百分比: {{ selectedStrategy.rules[0].percentage }}%
                    </span>
                    <span>
                      用户数量: {{ selectedStrategy.rules[0].userIds.length }}
                    </span>
                  </div>
                </div>
              </template>

              <template v-else-if="selectedStrategy.type === 'percentage'">
                <div class="percentage-section">
                  <el-progress
                    :percentage="selectedStrategy.rules[0].percentage"
                    :stroke-width="18"
                  >
                    <span>
                      {{ selectedStrategy.rules[0].percentage }}% 的用户流量
                    </span>
                  </el-progress>
                </div>
              </template>
            </div>
          </div>

          <div v-else class="no-strategy">
            <i class="el-icon-warning-outline"></i>
            <p>尚未选择灰度策略</p>
            <p class="sub-text">请返回上一步选择灰度策略</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <el-col :span="24">
        <el-card class="deployment-card">
          <div slot="header" class="card-header">
            <div class="header-title">
              <span>灰度发布流程</span>
              <el-tag v-if="deploymentStatus">
                {{ deploymentStatusText }}
              </el-tag>
            </div>
            <div class="header-actions">
              <el-button
                v-if="!isDeploying && !isDeployed && selectedStrategy"
                type="primary"
                @click="startGrayscaleDeployment"
              >
                开始灰度发布
              </el-button>
              <el-button
                v-if="isDeploying"
                type="warning"
                :disabled="isPaused"
                @click="pauseDeployment"
              >
                暂停
              </el-button>
              <el-button
                v-if="isDeploying && isPaused"
                type="success"
                @click="resumeDeployment"
              >
                继续
              </el-button>
              <el-button
                v-if="isDeploying || isDeployed"
                type="danger"
                @click="rollbackDeployment"
              >
                回滚
              </el-button>
              <el-button
                v-if="canPromoteToProd"
                type="success"
                @click="promoteToProd"
              >
                推广到全量
              </el-button>
            </div>
          </div>

          <div
            v-if="!isDeploying && !isDeployed && selectedStrategy"
            class="empty-state"
          >
            <i class="el-icon-upload"></i>
            <p>准备就绪，点击"开始灰度发布"将服务部署到灰度环境</p>
          </div>

          <div v-else-if="!selectedStrategy" class="empty-state">
            <i class="el-icon-warning-outline"></i>
            <p>请先选择灰度策略</p>
          </div>

          <div v-else class="deployment-status">
            <div class="phases-progress">
              <el-steps
                :active="activePhaseIndex"
                finish-status="success"
                align-center
              >
                <el-step
                  v-for="(phase, index) in deploymentPhases"
                  :key="index"
                  :title="phase.title"
                  :icon="phase.icon"
                >
                  <template #description>
                    <span>{{ phase.description }}</span>
                  </template>
                </el-step>
              </el-steps>
            </div>

            <div class="current-phase">
              <h4>当前阶段: {{ currentPhase.title }}</h4>
              <p class="phase-description">{{ currentPhase.description }}</p>

              <el-progress
                :percentage="phaseProgress"
                :status="getPhaseProgressStatus()"
                :stroke-width="18"
                class="phase-progress"
              >
                <span>{{ getPhaseProgressText() }}</span>
              </el-progress>

              <div
                v-if="currentPhase.key === 'monitoring'"
                class="monitoring-stats"
              >
                <div class="stats-header">
                  <h5>灰度监控指标</h5>
                  <el-button type="text" @click="refreshStats">刷新</el-button>
                </div>

                <div class="stats-grid">
                  <div
                    class="stat-item"
                    :class="{ 'has-alert': errorRate > 1 }"
                  >
                    <div class="stat-title">错误率</div>
                    <div class="stat-value">{{ errorRate }}%</div>
                    <div class="stat-trend">
                      <i
                        :class="
                          errorRateTrend === 'up'
                            ? 'el-icon-top'
                            : 'el-icon-bottom'
                        "
                      ></i>
                      <span>
                        {{ errorRateTrend === 'up' ? '上升' : '下降' }}
                      </span>
                    </div>
                  </div>

                  <div class="stat-item">
                    <div class="stat-title">请求量</div>
                    <div class="stat-value">{{ requestCount }}</div>
                    <div class="stat-trend">
                      <i class="el-icon-top"></i>
                      <span>增长</span>
                    </div>
                  </div>

                  <div
                    class="stat-item"
                    :class="{ 'has-alert': responseTime > 500 }"
                  >
                    <div class="stat-title">平均响应时间</div>
                    <div class="stat-value">{{ responseTime }}ms</div>
                    <div class="stat-trend">
                      <i
                        :class="
                          responseTimeTrend === 'up'
                            ? 'el-icon-top'
                            : 'el-icon-bottom'
                        "
                      ></i>
                      <span>
                        {{ responseTimeTrend === 'up' ? '上升' : '下降' }}
                      </span>
                    </div>
                  </div>

                  <div class="stat-item">
                    <div class="stat-title">CPU使用率</div>
                    <div class="stat-value">{{ cpuUsage }}%</div>
                    <div class="stat-trend">
                      <i
                        :class="
                          cpuTrend === 'up' ? 'el-icon-top' : 'el-icon-bottom'
                        "
                      ></i>
                      <span>{{ cpuTrend === 'up' ? '上升' : '下降' }}</span>
                    </div>
                  </div>
                </div>

                <div class="monitoring-actions">
                  <el-button type="primary" @click="completeMonitoring">
                    监控通过，推广到全量
                  </el-button>
                  <el-button type="danger" @click="rollbackDueToIssues">
                    发现问题，回滚
                  </el-button>
                </div>
              </div>

              <div
                v-if="currentPhase.key === 'stg-deployment'"
                class="service-status"
              >
                <h4>服务发布状态</h4>
                <div class="service-list">
                  <div
                    v-for="service in deploymentServices"
                    :key="service.id"
                    class="service-item"
                  >
                    <div class="service-info">
                      <div class="service-name">
                        <el-tag
                          :type="getServiceTypeTag(service.type)"
                          size="small"
                        >
                          {{ getServiceTypeName(service.type) }}
                        </el-tag>
                        {{ service.name }}
                      </div>
                      <div class="service-deployment-status">
                        <el-tag
                          :type="getStatusTagType(service.status)"
                          size="small"
                        >
                          {{ getStatusText(service.status) }}
                        </el-tag>
                      </div>
                    </div>

                    <el-progress
                      :percentage="service.progress || 0"
                      :status="getProgressStatus(service)"
                      :stroke-width="10"
                    ></el-progress>

                    <div class="service-actions">
                      <el-button
                        type="text"
                        icon="el-icon-view"
                        @click="viewServiceLogs(service)"
                      >
                        查看日志
                      </el-button>
                      <el-button
                        v-if="service.status === 'failed'"
                        type="text"
                        icon="el-icon-refresh"
                        @click="retryService(service)"
                      >
                        重试
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>

              <div
                v-if="currentPhase.key === 'promotion'"
                class="promotion-progress"
              >
                <h4>生产环境部署进度</h4>
                <div class="progress-container">
                  <el-progress
                    :percentage="productionProgress"
                    :status="productionStatus"
                    :stroke-width="18"
                  >
                    <span>{{ getProductionProgressText() }}</span>
                  </el-progress>
                </div>

                <div
                  v-if="
                    productionProgress === 100 && productionStatus === 'success'
                  "
                  class="completion-message"
                >
                  <i class="el-icon-success"></i>
                  <p>恭喜！灰度发布流程已全部完成</p>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row
      v-if="isDeploying || isDeployed"
      :gutter="20"
      style="margin-top: 20px"
    >
      <el-col :span="24">
        <el-card class="logs-card">
          <div slot="header" class="card-header">
            <span>灰度发布日志</span>
            <el-button type="text" @click="clearLogs">清空日志</el-button>
          </div>

          <div class="logs-content">
            <el-tabs v-model="activeLogTab" type="card">
              <el-tab-pane label="全局日志" name="global">
                <div class="log-viewer">
                  <pre>{{ globalLogs }}</pre>
                </div>
              </el-tab-pane>
              <el-tab-pane label="灰度环境日志" name="stg">
                <div class="log-viewer">
                  <pre>{{ stgLogs }}</pre>
                </div>
              </el-tab-pane>
              <el-tab-pane label="生产环境日志" name="production">
                <div class="log-viewer">
                  <pre>{{ productionLogs }}</pre>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-dialog
      title="服务部署日志"
      :visible.sync="serviceLogsVisible"
      width="60%"
    >
      <div v-if="currentServiceLogs" class="service-logs">
        <div class="logs-header">
          <span class="service-name">
            {{ currentService && currentService.name }}
          </span>
          <el-tag
            :type="getStatusTagType(currentService && currentService.status)"
          >
            {{ getStatusText(currentService && currentService.status) }}
          </el-tag>
        </div>
        <pre>{{ currentServiceLogs }}</pre>
      </div>
    </el-dialog>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'GrayscaleRelease',
    data() {
      return {
        isDeploying: false,
        isDeployed: false,
        isPaused: false,
        deploymentStatus: '',
        deploymentPhases: [
          {
            key: 'stg-deployment',
            title: '灰度环境部署',
            description: '部署到灰度环境(stg)',
            icon: 'el-icon-upload',
          },
          {
            key: 'monitoring',
            title: '灰度监控',
            description: '监控灰度环境运行情况',
            icon: 'el-icon-data-line',
          },
          {
            key: 'promotion',
            title: '全量发布',
            description: '推广到生产环境',
            icon: 'el-icon-s-promotion',
          },
        ],
        activePhaseIndex: 0,
        phaseProgress: 0,
        deploymentServices: [],
        deploymentTimer: null,
        monitoringTimer: null,
        globalLogs: '',
        stgLogs: '',
        productionLogs: '',
        serviceLogsVisible: false,
        currentServiceLogs: '',
        currentService: null,
        activeLogTab: 'global',
        // 监控指标
        errorRate: 0.5,
        errorRateTrend: 'down',
        requestCount: 1354,
        responseTime: 230,
        responseTimeTrend: 'down',
        cpuUsage: 35,
        cpuTrend: 'up',
        // 生产环境部署
        productionProgress: 0,
        productionStatus: '',
      }
    },
    computed: {
      ...mapState('deployment', ['grayscale', 'servicesList', 'serviceOrder']),

      selectedStrategy() {
        return this.grayscale.selectedStrategy
      },

      deploymentStatusText() {
        switch (this.deploymentStatus) {
          case 'pending':
            return '等待中'
          case 'deploying':
            return this.isPaused ? '已暂停' : '部署中'
          case 'monitoring':
            return '监控中'
          case 'promoting':
            return '正在推广到全量'
          case 'success':
            return '发布成功'
          case 'failed':
            return '发布失败'
          case 'rolling-back':
            return '正在回滚'
          case 'rolled-back':
            return '已回滚'
          default:
            return '未知状态'
        }
      },

      currentPhase() {
        return this.deploymentPhases[this.activePhaseIndex]
      },

      canPromoteToProd() {
        return (
          this.isDeployed &&
          this.activePhaseIndex === 1 &&
          this.phaseProgress >= 100
        )
      },

      isAnyFailed() {
        return this.deploymentServices.some(
          (service) => service.status === 'failed'
        )
      },
    },
    beforeDestroy() {
      if (this.deploymentTimer) {
        clearInterval(this.deploymentTimer)
      }
      if (this.monitoringTimer) {
        clearInterval(this.monitoringTimer)
      }
    },
    methods: {
      ...mapActions('deployment', [
        'deployToGrayscale',
        'promoteGrayscaleToProd',
      ]),

      getStrategyTypeText(type) {
        switch (type) {
          case 'ip':
            return 'IP地址段'
          case 'userId':
            return '用户ID'
          case 'percentage':
            return '流量百分比'
          default:
            return '未知类型'
        }
      },

      startGrayscaleDeployment() {
        this.$confirm('确定要开始灰度发布吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.initializeDeploymentServices()
            this.isDeploying = true
            this.isPaused = false
            this.deploymentStatus = 'deploying'
            this.activePhaseIndex = 0
            this.phaseProgress = 0

            this.addLog('global', '[INFO] 开始灰度发布流程')
            this.addLog('global', '[INFO] 第一阶段：部署到灰度环境(stg)')
            this.addLog('stg', '[INFO] 开始灰度环境部署')

            // 开始模拟部署过程
            this.simulateStgDeployment()
          })
          .catch(() => {})
      },

      initializeDeploymentServices() {
        // 基于服务顺序初始化部署服务列表
        this.deploymentServices = this.serviceOrder.map((service) => ({
          ...service,
          status: 'pending',
          progress: 0,
          logs: '',
          deploymentStarted: false,
        }))
      },

      simulateStgDeployment() {
        if (this.deploymentTimer) {
          clearInterval(this.deploymentTimer)
        }

        let currentServiceIndex = 0

        this.deploymentTimer = setInterval(() => {
          // 如果暂停，不执行部署逻辑
          if (this.isPaused) return

          const currentService = this.deploymentServices[currentServiceIndex]

          // 如果当前服务未开始部署
          if (!currentService.deploymentStarted) {
            currentService.deploymentStarted = true
            currentService.status = 'deploying'

            // 记录部署开始日志
            this.addLog(
              'global',
              `[INFO] 开始部署服务: ${currentService.name} 到灰度环境`
            )
            this.addLog('stg', `[INFO] 开始部署服务: ${currentService.name}`)

            currentService.logs += `[${new Date().toLocaleTimeString()}] 开始部署到灰度环境\n`
          }

          // 更新服务部署进度
          if (currentService.status === 'deploying') {
            currentService.progress += Math.floor(Math.random() * 10) + 1

            if (currentService.progress >= 100) {
              currentService.progress = 100

              // 有5%的概率部署失败
              const deployFailed = Math.random() < 0.05

              if (deployFailed) {
                currentService.status = 'failed'
                this.addLog(
                  'global',
                  `[ERROR] 服务 ${currentService.name} 部署到灰度环境失败`
                )
                this.addLog(
                  'stg',
                  `[ERROR] 服务 ${currentService.name} 部署失败: 容器启动超时`
                )

                currentService.logs += `[${new Date().toLocaleTimeString()}] 部署失败: 容器启动超时\n`

                // 如果有服务失败，暂停部署
                this.pauseDeployment()
              } else {
                currentService.status = 'success'
                this.addLog(
                  'global',
                  `[INFO] 服务 ${currentService.name} 部署到灰度环境成功`
                )
                this.addLog(
                  'stg',
                  `[INFO] 服务 ${currentService.name} 部署成功`
                )

                currentService.logs += `[${new Date().toLocaleTimeString()}] 部署成功，服务已启动并通过健康检查\n`

                // 推进到下一个服务
                currentServiceIndex =
                  (currentServiceIndex + 1) % this.deploymentServices.length

                // 如果所有服务都部署完成
                if (
                  this.deploymentServices.every(
                    (s) => s.status === 'success' || s.status === 'skipped'
                  )
                ) {
                  clearInterval(this.deploymentTimer)
                  this.deploymentTimer = null
                  this.phaseProgress = 100

                  this.addLog('global', '[INFO] 所有服务已成功部署到灰度环境')
                  this.addLog('stg', '[INFO] 灰度环境部署完成')

                  // 进入监控阶段
                  setTimeout(() => {
                    this.startMonitoringPhase()
                  }, 1500)

                  return
                }
              }
            } else {
              // 更新日志
              if (currentService.progress % 20 === 0) {
                this.addLog(
                  'stg',
                  `[INFO] 服务 ${currentService.name} 部署进度: ${currentService.progress}%`
                )

                currentService.logs += `[${new Date().toLocaleTimeString()}] 部署进度: ${
                  currentService.progress
                }%\n`
              }
            }
          }

          // 更新阶段进度
          this.updatePhaseProgress()
        }, 500)
      },

      updatePhaseProgress() {
        if (this.activePhaseIndex === 0) {
          // 灰度环境部署阶段
          let totalProgress = 0
          this.deploymentServices.forEach((service) => {
            totalProgress += service.progress || 0
          })

          this.phaseProgress = Math.floor(
            totalProgress / this.deploymentServices.length
          )
        }
      },

      startMonitoringPhase() {
        this.activePhaseIndex = 1
        this.phaseProgress = 0
        this.deploymentStatus = 'monitoring'

        this.addLog('global', '[INFO] 进入灰度监控阶段')
        this.addLog('global', '[INFO] 开始监控灰度环境运行指标')

        // 模拟监控进度
        this.monitoringTimer = setInterval(() => {
          // 如果暂停，不执行监控逻辑
          if (this.isPaused) return

          this.phaseProgress += 5
          if (this.phaseProgress > 100) {
            this.phaseProgress = 100
            clearInterval(this.monitoringTimer)
            this.monitoringTimer = null

            this.addLog('global', '[INFO] 灰度监控完成，可以决定是否推广到全量')
          }

          // 模拟监控数据变化
          this.updateMonitoringStats()
        }, 1500)
      },

      updateMonitoringStats() {
        // 随机波动错误率
        const errorDelta = (Math.random() - 0.5) * 0.3
        this.errorRate = Math.max(0, this.errorRate + errorDelta).toFixed(1)
        this.errorRateTrend = errorDelta > 0 ? 'up' : 'down'

        // 随机波动请求量
        const requestDelta = Math.floor((Math.random() - 0.3) * 100)
        this.requestCount = Math.max(1000, this.requestCount + requestDelta)

        // 随机波动响应时间
        const responseDelta = Math.floor((Math.random() - 0.5) * 20)
        this.responseTime = Math.max(100, this.responseTime + responseDelta)
        this.responseTimeTrend = responseDelta > 0 ? 'up' : 'down'

        // 随机波动CPU使用率
        const cpuDelta = Math.floor((Math.random() - 0.5) * 5)
        this.cpuUsage = Math.min(100, Math.max(20, this.cpuUsage + cpuDelta))
        this.cpuTrend = cpuDelta > 0 ? 'up' : 'down'

        // 记录日志
        if (this.phaseProgress % 20 === 0) {
          this.addLog('global', `[INFO] 灰度监控进度: ${this.phaseProgress}%`)
          this.addLog(
            'global',
            `[STATS] 错误率: ${this.errorRate}%, 请求量: ${this.requestCount}, 响应时间: ${this.responseTime}ms`
          )
        }

        // 如果指标异常，添加警告日志
        if (this.errorRate > 1) {
          this.addLog('global', `[WARNING] 错误率异常: ${this.errorRate}%`)
        }
        if (this.responseTime > 500) {
          this.addLog(
            'global',
            `[WARNING] 响应时间异常: ${this.responseTime}ms`
          )
        }
      },

      refreshStats() {
        this.updateMonitoringStats()
        this.$message.success('监控指标已刷新')
      },

      completeMonitoring() {
        this.$confirm('确认灰度验证通过，是否推广到全量生产环境?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info',
        })
          .then(() => {
            this.promoteToProd()
          })
          .catch(() => {})
      },

      rollbackDueToIssues() {
        this.$confirm('确认灰度验证发现问题，是否回滚?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.rollbackDeployment()
          })
          .catch(() => {})
      },

      promoteToProd() {
        this.activePhaseIndex = 2
        this.phaseProgress = 0
        this.productionProgress = 0
        this.productionStatus = ''
        this.deploymentStatus = 'promoting'

        this.addLog('global', '[INFO] 开始将灰度版本推广到生产环境')
        this.addLog('production', '[INFO] 开始生产环境部署')

        // 模拟生产环境部署
        const prodTimer = setInterval(() => {
          // 如果暂停，不执行部署逻辑
          if (this.isPaused) return

          this.productionProgress += 5
          this.phaseProgress = this.productionProgress

          if (this.productionProgress >= 100) {
            this.productionProgress = 100
            this.phaseProgress = 100
            this.productionStatus = 'success'
            this.deploymentStatus = 'success'
            this.isDeployed = true
            this.isDeploying = false

            clearInterval(prodTimer)

            this.addLog('global', '[INFO] 灰度版本已成功推广到生产环境')
            this.addLog('production', '[INFO] 生产环境部署完成')
            this.addLog('global', '[INFO] 灰度发布流程全部完成')
          } else {
            // 更新日志
            if (this.productionProgress % 20 === 0) {
              this.addLog(
                'production',
                `[INFO] 生产环境部署进度: ${this.productionProgress}%`
              )
            }
          }
        }, 800)
      },

      pauseDeployment() {
        this.isPaused = true
        this.addLog('global', '[WARNING] 部署流程已暂停')

        if (this.activePhaseIndex === 0) {
          this.addLog('stg', '[WARNING] 灰度环境部署已暂停')
        } else if (this.activePhaseIndex === 1) {
          this.addLog('global', '[WARNING] 灰度监控已暂停')
        } else {
          this.addLog('production', '[WARNING] 生产环境部署已暂停')
        }
      },

      resumeDeployment() {
        this.isPaused = false
        this.addLog('global', '[INFO] 部署流程已恢复')

        if (this.activePhaseIndex === 0) {
          this.addLog('stg', '[INFO] 灰度环境部署已恢复')
        } else if (this.activePhaseIndex === 1) {
          this.addLog('global', '[INFO] 灰度监控已恢复')
        } else {
          this.addLog('production', '[INFO] 生产环境部署已恢复')
        }
      },

      rollbackDeployment() {
        this.$confirm('确定要回滚所有已部署的服务吗?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            // 暂停当前部署
            this.pauseDeployment()

            // 更新状态
            this.deploymentStatus = 'rolling-back'
            this.addLog('global', '[WARNING] 开始回滚所有已部署的服务')

            // 模拟回滚操作
            setTimeout(() => {
              this.deploymentStatus = 'rolled-back'
              this.isDeploying = false

              if (this.deploymentTimer) {
                clearInterval(this.deploymentTimer)
                this.deploymentTimer = null
              }

              if (this.monitoringTimer) {
                clearInterval(this.monitoringTimer)
                this.monitoringTimer = null
              }

              this.addLog('global', '[INFO] 所有服务已成功回滚到之前版本')

              // 将已部署的服务状态设置为已回滚
              this.deploymentServices.forEach((service) => {
                if (service.status === 'success') {
                  service.status = 'rolled-back'
                  service.logs += `[${new Date().toLocaleTimeString()}] 服务已回滚到之前版本\n`
                }
              })
            }, 3000)
          })
          .catch(() => {})
      },

      retryService(service) {
        service.status = 'pending'
        service.progress = 0
        service.deploymentStarted = false
        service.logs += `[${new Date().toLocaleTimeString()}] 重新开始部署\n`

        this.addLog('global', `[INFO] 重新部署服务: ${service.name}`)
        this.addLog('stg', `[INFO] 重新部署服务: ${service.name}`)

        // 如果部署暂停了，自动恢复
        if (this.isPaused) {
          this.resumeDeployment()
        }

        // 重新开始部署模拟
        if (!this.deploymentTimer) {
          this.simulateStgDeployment()
        }
      },

      viewServiceLogs(service) {
        this.currentService = service
        this.currentServiceLogs = service.logs || '暂无部署日志'
        this.serviceLogsVisible = true
      },

      addLog(type, message) {
        const timestamp = `[${new Date().toLocaleTimeString()}]`
        const logMessage = `${timestamp} ${message}\n`

        this.globalLogs += logMessage

        if (type === 'stg') {
          this.stgLogs += logMessage
        } else if (type === 'production') {
          this.productionLogs += logMessage
        }
      },

      clearLogs() {
        if (this.activeLogTab === 'global') {
          this.globalLogs = ''
        } else if (this.activeLogTab === 'stg') {
          this.stgLogs = ''
        } else if (this.activeLogTab === 'production') {
          this.productionLogs = ''
        }
      },

      getProgressStatus(service) {
        if (service.status === 'failed') {
          return 'exception'
        }
        if (service.status === 'deploying' && this.isPaused) {
          return 'warning'
        }
        return ''
      },

      getPhaseProgressStatus() {
        if (this.deploymentStatus === 'failed' || this.isAnyFailed) {
          return 'exception'
        }
        if (this.isPaused) {
          return 'warning'
        }
        return ''
      },

      getPhaseProgressText() {
        if (this.isPaused) {
          return `已暂停 (${this.phaseProgress}%)`
        }
        return `进度 ${this.phaseProgress}%`
      },

      getProductionProgressText() {
        if (this.isPaused) {
          return `已暂停 (${this.productionProgress}%)`
        }
        return `进度 ${this.productionProgress}%`
      },

      getServiceTypeTag(type) {
        switch (type) {
          case 'backend':
            return 'primary'
          case 'frontend':
            return 'success'
          case 'half-popup':
            return 'warning'
          default:
            return 'info'
        }
      },

      getServiceTypeName(type) {
        switch (type) {
          case 'backend':
            return '后端'
          case 'frontend':
            return '前端'
          case 'half-popup':
            return '半弹窗'
          default:
            return '未知'
        }
      },

      getStatusText(status) {
        switch (status) {
          case 'pending':
            return '等待中'
          case 'deploying':
            return '部署中'
          case 'success':
            return '成功'
          case 'failed':
            return '失败'
          case 'rolled-back':
            return '已回滚'
          case 'skipped':
            return '已跳过'
          default:
            return '未知'
        }
      },

      getStatusTagType(status) {
        switch (status) {
          case 'success':
            return 'success'
          case 'failed':
            return 'danger'
          case 'deploying':
            return this.isPaused ? 'warning' : ''
          case 'rolled-back':
            return 'info'
          default:
            return 'info'
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .grayscale-release-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
      font-size: 16px;

      .header-title {
        display: flex;
        align-items: center;

        .el-tag {
          margin-left: 10px;
        }
      }

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .selected-strategy {
      .strategy-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        h3 {
          margin: 0;
          margin-right: 10px;
        }
      }

      .strategy-rules {
        .user-ids-section {
          .section-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-weight: 500;
          }
        }
      }
    }

    .no-strategy {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }

      .sub-text {
        margin-top: 10px;
        color: #909399;
        font-size: 14px;
      }
    }

    .empty-state {
      padding: 40px;
      text-align: center;
      background-color: #f5f7fa;
      border-radius: 4px;

      i {
        font-size: 48px;
        color: #909399;
        margin-bottom: 15px;
      }

      p {
        margin: 0;
        color: #606266;
        font-size: 16px;
      }
    }

    .deployment-status {
      .phases-progress {
        margin-bottom: 30px;
      }

      .current-phase {
        h4 {
          margin-top: 0;
          margin-bottom: 10px;
          font-size: 18px;
        }

        .phase-description {
          color: #606266;
          margin-bottom: 20px;
        }

        .phase-progress {
          margin-bottom: 30px;
        }

        .monitoring-stats {
          margin-top: 30px;

          .stats-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;

            h5 {
              margin: 0;
              font-size: 16px;
            }
          }

          .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;

            .stat-item {
              padding: 15px;
              border-radius: 4px;
              background-color: #f5f7fa;
              transition: all 0.3s;

              &.has-alert {
                background-color: #fef0f0;
                border: 1px solid #fbc4c4;
              }

              .stat-title {
                font-size: 14px;
                color: #606266;
                margin-bottom: 10px;
              }

              .stat-value {
                font-size: 24px;
                font-weight: 500;
                margin-bottom: 10px;
              }

              .stat-trend {
                font-size: 14px;
                color: #909399;

                i {
                  margin-right: 5px;
                }

                .el-icon-top {
                  color: #f56c6c;
                }

                .el-icon-bottom {
                  color: #67c23a;
                }
              }
            }
          }

          .monitoring-actions {
            text-align: center;
            margin-top: 20px;
          }
        }

        .service-status {
          h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
          }

          .service-list {
            .service-item {
              padding: 15px;
              margin-bottom: 15px;
              border-radius: 4px;
              border: 1px solid #ebeef5;
              transition: all 0.3s;

              &:hover {
                box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
              }

              .service-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;

                .service-name {
                  display: flex;
                  align-items: center;
                  font-weight: 500;

                  .el-tag {
                    margin-right: 8px;
                  }
                }
              }

              .service-actions {
                margin-top: 10px;
                text-align: right;
              }
            }
          }
        }

        .promotion-progress {
          h4 {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
          }

          .progress-container {
            margin-bottom: 30px;
          }

          .completion-message {
            text-align: center;
            padding: 30px;
            background-color: #f0f9eb;
            border-radius: 4px;

            i {
              font-size: 48px;
              color: #67c23a;
              margin-bottom: 15px;
            }

            p {
              margin: 0;
              font-size: 18px;
              color: #67c23a;
              font-weight: 500;
            }
          }
        }
      }
    }

    .logs-card {
      .logs-content {
        .log-viewer {
          height: 300px;
          background-color: #f5f5f5;
          padding: 15px;
          border-radius: 4px;
          overflow-y: auto;

          pre {
            margin: 0;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 14px;
          }
        }
      }
    }

    .service-logs {
      .logs-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;

        .service-name {
          font-weight: 500;
          font-size: 16px;
          margin-right: 10px;
        }
      }

      pre {
        height: 300px;
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 4px;
        overflow-y: auto;
        margin: 0;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
        font-size: 14px;
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
</style>
