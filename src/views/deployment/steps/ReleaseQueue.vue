<template>
  <div class="release-queue-container">
    <div class="step-title">
      <h3>发版队列管理</h3>
      <p class="step-description">管理等待发布的版本队列，进行运维审批</p>
    </div>

    <el-card class="queue-card">
      <div slot="header" class="card-header">
        <span>发版队列</span>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-refresh"
          @click="refreshQueue"
        >
          刷新队列
        </el-button>
      </div>

      <div class="queue-filter">
        <el-form :inline="true" :model="filter" class="filter-form">
          <el-form-item label="状态">
            <el-select v-model="filter.status" placeholder="全部" clearable>
              <el-option label="待审批" value="pending"></el-option>
              <el-option label="已审批" value="approved"></el-option>
              <el-option label="已拒绝" value="rejected"></el-option>
              <el-option label="发布中" value="in-progress"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="申请人">
            <el-input
              v-model="filter.requestedBy"
              placeholder="申请人"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilter">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table
        :data="filteredQueueItems"
        style="width: 100%"
        row-key="id"
        border
        :default-sort="{ prop: 'requestDate', order: 'descending' }"
        @row-click="handleRowClick"
      >
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column
          prop="name"
          label="发版名称"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="requestedBy"
          label="申请人"
          width="120"
        ></el-table-column>
        <el-table-column
          prop="requestDate"
          label="申请时间"
          width="150"
          sortable
        ></el-table-column>
        <el-table-column
          prop="scheduledDate"
          label="计划发版时间"
          width="150"
          sortable
        ></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-tooltip content="查看详情" placement="top">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-view"
                @click.stop="viewDetail(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.status === 'pending'"
              content="审批"
              placement="top"
            >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-check"
                @click.stop="approveItem(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.status === 'pending'"
              content="拒绝"
              placement="top"
            >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-close"
                @click.stop="rejectItem(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.status === 'approved'"
              content="发布"
              placement="top"
            >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-upload2"
                @click.stop="releaseItem(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="filteredQueueItems.length === 0" class="queue-empty">
        <i class="el-icon-tickets"></i>
        <p>暂无发版队列数据</p>
      </div>

      <div v-if="filteredQueueItems.length > 0" class="queue-pagination">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredQueueItems.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>

    <el-divider content-position="center">当前发版状态</el-divider>

    <el-card class="current-release-card">
      <div slot="header" class="card-header">
        <span>当前发版信息</span>
      </div>

      <div v-if="currentRelease" class="current-release-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发版名称">
            {{ currentRelease.name }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentRelease.status)">
              {{ getStatusText(currentRelease.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ currentRelease.requestedBy }}
          </el-descriptions-item>
          <el-descriptions-item label="审批人">
            {{ currentRelease.approvedBy }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ currentRelease.requestDate }}
          </el-descriptions-item>
          <el-descriptions-item label="审批时间">
            {{ currentRelease.approvalDate }}
          </el-descriptions-item>
          <el-descriptions-item label="计划发版时间">
            {{ currentRelease.scheduledDate }}
          </el-descriptions-item>
          <el-descriptions-item label="当前位置">
            发版队列第 {{ currentReleasePosition }} 位
          </el-descriptions-item>
        </el-descriptions>

        <el-collapse style="margin-top: 20px">
          <el-collapse-item title="服务列表" name="1">
            <el-table
              :data="currentRelease.services"
              border
              style="width: 100%"
            >
              <el-table-column prop="name" label="服务名称"></el-table-column>
              <el-table-column
                prop="order"
                label="发布顺序"
                width="100"
              ></el-table-column>
            </el-table>
          </el-collapse-item>
          <el-collapse-item title="变更日志" name="2">
            <div class="changelog">
              {{ currentRelease.changeLog }}
            </div>
          </el-collapse-item>
        </el-collapse>

        <div class="release-actions">
          <el-button
            v-if="canProceedToNextStep"
            type="primary"
            @click="proceedToNextStep"
          >
            进入发版流程
          </el-button>
          <el-button type="warning" @click="updateReleaseInfo">
            更新发版信息
          </el-button>
        </div>
      </div>

      <div v-else class="no-current-release">
        <i class="el-icon-info"></i>
        <p>您当前没有正在准备发布的版本</p>
        <p class="sub-tip">请从上方队列中选择一个版本，或者重新开始发版流程</p>
      </div>
    </el-card>

    <el-dialog title="发版详情" :visible.sync="detailDialogVisible" width="60%">
      <div v-if="selectedItem" class="item-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发版名称">
            {{ selectedItem.name }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedItem.status)">
              {{ getStatusText(selectedItem.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请人">
            {{ selectedItem.requestedBy }}
          </el-descriptions-item>
          <el-descriptions-item label="审批人">
            {{ selectedItem.approvedBy || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">
            {{ selectedItem.requestDate }}
          </el-descriptions-item>
          <el-descriptions-item label="审批时间">
            {{ selectedItem.approvalDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="计划发版时间">
            {{ selectedItem.scheduledDate }}
          </el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="left">服务列表</el-divider>
        <el-table :data="selectedItem.services" border style="width: 100%">
          <el-table-column prop="name" label="服务名称"></el-table-column>
          <el-table-column
            prop="order"
            label="发布顺序"
            width="100"
          ></el-table-column>
        </el-table>

        <el-divider content-position="left">变更日志</el-divider>
        <div class="changelog">
          {{ selectedItem.changeLog }}
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="selectedItem && selectedItem.status === 'approved'"
          type="primary"
          @click="setAsCurrentRelease"
        >
          设为当前发版
        </el-button>
      </span>
    </el-dialog>

    <el-dialog
      title="审批发版"
      :visible.sync="approveDialogVisible"
      width="50%"
    >
      <el-form :model="approveForm" label-width="120px">
        <el-form-item label="审批意见">
          <el-radio-group v-model="approveForm.decision">
            <el-radio :label="'approve'">通过</el-radio>
            <el-radio :label="'reject'">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="approveForm.decision === 'reject'" label="审批备注">
          <el-input
            v-model="approveForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          >
            >
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="approveForm.decision === 'approve'"
          label="计划发版时间"
        >
          <el-date-picker
            v-model="approveForm.scheduledDate"
            type="datetime"
            placeholder="选择发版时间"
            style="width: 100%"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="approveDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitApproval">确认</el-button>
      </span>
    </el-dialog>

    <div class="step-actions">
      <el-button @click="$emit('prev')">上一步</el-button>
      <el-button
        type="primary"
        :disabled="!canProceedToNextStep"
        @click="goNext"
      >
        下一步
      </el-button>
    </div>
  </div>
</template>

<script>
  import { mapState, mapActions, mapMutations } from 'vuex'

  export default {
    name: 'ReleaseQueue',
    data() {
      return {
        filter: {
          status: '',
          requestedBy: '',
        },
        currentPage: 1,
        pageSize: 10,
        detailDialogVisible: false,
        approveDialogVisible: false,
        selectedItem: null,
        approveForm: {
          decision: 'approve',
          comment: '',
          scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 默认明天
        },
        currentRelease: null,
        currentReleasePosition: 1,
      }
    },
    computed: {
      ...mapState('deployment', ['releaseQueue']),

      filteredQueueItems() {
        let result = [...this.releaseQueue]

        if (this.filter.status) {
          result = result.filter((item) => item.status === this.filter.status)
        }

        if (this.filter.requestedBy) {
          result = result.filter((item) =>
            item.requestedBy
              .toLowerCase()
              .includes(this.filter.requestedBy.toLowerCase())
          )
        }

        return result
      },

      canProceedToNextStep() {
        return this.currentRelease && this.currentRelease.status === 'approved'
      },
    },
    created() {
      this.fetchReleaseQueue()
    },
    methods: {
      ...mapActions('deployment', [
        'fetchReleaseQueue',
        'updateQueueItemStatus',
      ]),

      refreshQueue() {
        this.fetchReleaseQueue()
        this.$message.success('发版队列已刷新')
      },

      applyFilter() {
        // 过滤已经通过计算属性实现
        this.currentPage = 1
      },

      resetFilter() {
        this.filter = {
          status: '',
          requestedBy: '',
        }
        this.currentPage = 1
      },

      handleSizeChange(val) {
        this.pageSize = val
      },

      handleCurrentChange(val) {
        this.currentPage = val
      },

      getStatusType(status) {
        switch (status) {
          case 'approved':
            return 'success'
          case 'rejected':
            return 'danger'
          case 'in-progress':
            return 'warning'
          default:
            return 'info'
        }
      },

      getStatusText(status) {
        switch (status) {
          case 'pending':
            return '待审批'
          case 'approved':
            return '已审批'
          case 'rejected':
            return '已拒绝'
          case 'in-progress':
            return '发布中'
          default:
            return '未知'
        }
      },

      handleRowClick(row) {
        this.viewDetail(row)
      },

      viewDetail(item) {
        this.selectedItem = item
        this.detailDialogVisible = true
      },

      approveItem(item) {
        this.selectedItem = item
        this.approveForm = {
          decision: 'approve',
          comment: '',
          scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        }
        this.approveDialogVisible = true
      },

      rejectItem(item) {
        this.selectedItem = item
        this.approveForm = {
          decision: 'reject',
          comment: '',
          scheduledDate: null,
        }
        this.approveDialogVisible = true
      },

      async submitApproval() {
        if (
          this.approveForm.decision === 'reject' &&
          !this.approveForm.comment
        ) {
          this.$message.warning('请输入拒绝原因')
          return
        }

        if (
          this.approveForm.decision === 'approve' &&
          !this.approveForm.scheduledDate
        ) {
          this.$message.warning('请选择计划发版时间')
          return
        }

        try {
          const status =
            this.approveForm.decision === 'approve' ? 'approved' : 'rejected'
          await this.updateQueueItemStatus({
            id: this.selectedItem.id,
            data: {
              status,
              approvedBy: '钱七', // 模拟当前用户
              approvalDate: new Date().toISOString().split('T')[0],
              scheduledDate: this.approveForm.scheduledDate
                ? this.approveForm.scheduledDate.toISOString().split('T')[0]
                : null,
              comment: this.approveForm.comment,
            },
          })

          this.$message.success(
            `发版申请已${status === 'approved' ? '通过' : '拒绝'}`
          )
          this.approveDialogVisible = false

          // 更新本地数据
          const index = this.releaseQueue.findIndex(
            (item) => item.id === this.selectedItem.id
          )
          if (index !== -1) {
            this.releaseQueue[index].status = status
            this.releaseQueue[index].approvedBy = '钱七'
            this.releaseQueue[index].approvalDate = new Date()
              .toISOString()
              .split('T')[0]
            if (status === 'approved') {
              this.releaseQueue[index].scheduledDate =
                this.approveForm.scheduledDate.toISOString().split('T')[0]
            }
          }

          // 更新选中项
          this.selectedItem = { ...this.releaseQueue[index] }
        } catch (error) {
          this.$message.error('审批操作失败')
        }
      },

      releaseItem(item) {
        this.$confirm(`确定要将"${item.name}"设置为当前发版吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.setAsCurrentRelease()
          })
          .catch(() => {})
      },

      setAsCurrentRelease() {
        this.currentRelease = this.selectedItem
        this.currentReleasePosition =
          this.releaseQueue
            .filter((item) => item.status === 'approved')
            .findIndex((item) => item.id === this.selectedItem.id) + 1

        this.detailDialogVisible = false
        this.$message.success('已设置为当前发版')
      },

      updateReleaseInfo() {
        this.$message.info('更新发版信息功能开发中')
      },

      proceedToNextStep() {
        if (this.canProceedToNextStep) {
          this.goNext()
        } else {
          this.$message.warning('请先选择一个已审批的发版')
        }
      },

      goNext() {
        if (this.canProceedToNextStep) {
          this.$emit('next')
        } else {
          this.$message.warning('请先选择一个已审批的发版')
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .release-queue-container {
    .step-title {
      margin-bottom: 30px;
      border-left: 4px solid #409eff;
      padding-left: 15px;

      h3 {
        margin: 0;
        font-size: 20px;
        color: #303133;
      }

      .step-description {
        margin: 5px 0 0;
        color: #909399;
      }
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
      font-size: 16px;
    }

    .queue-card {
      margin-bottom: 20px;

      .queue-filter {
        margin-bottom: 20px;
      }

      .queue-empty {
        padding: 40px;
        text-align: center;
        background-color: #f5f7fa;
        border-radius: 4px;
        margin-top: 20px;

        i {
          font-size: 48px;
          color: #909399;
          margin-bottom: 15px;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 16px;
        }
      }

      .queue-pagination {
        margin-top: 20px;
        text-align: right;
      }
    }

    .current-release-card {
      .current-release-info {
        .changelog {
          padding: 15px;
          background-color: #f5f7fa;
          border-radius: 4px;
          margin-top: 10px;
          white-space: pre-line;
          color: #606266;
        }

        .release-actions {
          margin-top: 20px;
          text-align: center;
        }
      }

      .no-current-release {
        padding: 40px;
        text-align: center;
        background-color: #f5f7fa;
        border-radius: 4px;

        i {
          font-size: 48px;
          color: #909399;
          margin-bottom: 15px;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 16px;
        }

        .sub-tip {
          margin-top: 10px;
          color: #909399;
          font-size: 14px;
        }
      }
    }

    .item-detail {
      .changelog {
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
        margin-top: 10px;
        white-space: pre-line;
        color: #606266;
      }
    }

    .step-actions {
      margin-top: 40px;
      text-align: center;
    }
  }
</style>
