<!-- LogTrackingSystem.vue -->
<template>
  <div class="log-tracking-system">
    <!-- 顶部搜索区域 -->
    <TopSearchBar
      :statistics="globalStatistics"
      :loading="searchLoading"
      @search="handleSearch"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧请求列表 -->
      <RequestList
        :requests="filteredRequests"
        :selected-request="selectedRequest"
        :loading="searchLoading"
        :total="totalRequests"
        :current-page="currentPage"
        :page-size="pageSize"
        @select-request="handleSelectRequest"
        @filter-change="handleFilterChange"
        @page-change="handlePageChange"
      />

      <!-- 右侧内容区域 -->
      <div class="right-panel">
        <!-- 统计面板 -->
        <StatisticsPanel
          v-if="!selectedRequest"
          :statistics-data="statisticsData"
          :album-id="searchParams.albumId"
          :loading="searchLoading"
        />

        <!-- 请求详情 -->
        <RequestDetail
          v-else
          :request="selectedRequest"
          :service-logs="serviceLogs"
          :service-logs-loading="serviceLogsLoading"
          :show-chain-graph="showChainGraph"
          @close="handleCloseDetail"
          @toggle-chain-graph="handleToggleChainGraph"
          @view-raw-log="handleViewRawLog"
          @view-in-kibana="handleViewInKibana"
          @refresh-service-logs="handleRefreshServiceLogs"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import TopSearchBar from './components/TopSearchBar.vue'
  import RequestList from './components/RequestList.vue'
  import StatisticsPanel from './components/StatisticsPanel.vue'
  import RequestDetail from './components/RequestDetail.vue'
  import {
    getAccessLogs,
    getLogStatistics,
    getRawLog,
    getServiceLogs,
  } from '@/api/logcenter'

  export default {
    name: 'LogTrackingSystem',
    components: {
      TopSearchBar,
      RequestList,
      StatisticsPanel,
      RequestDetail,
    },
    data() {
      return {
        selectedRequest: null,
        showChainGraph: false,
        searchLoading: false,
        serviceLogsLoading: false,
        searchParams: {
          albumId: '',
          startTime: '',
          endTime: '',
          userId: '',
        },
        filterText: '',

        // 分页相关
        currentPage: 1,
        pageSize: 50,
        totalRequests: 0,

        // 数据
        currentPageRequests: [],
        serviceLogs: [], // 服务日志数据
        globalStatistics: {
          totalRequests: 0,
          successRate: 0,
          avgResponseTime: '0ms',
        },
        statisticsData: {
          totalRequests: 0,
          successRate: 0,
          avgResponseTime: '0ms',
          errorCount: 0,
          statusDistribution: [],
          responseTimeDistribution: [],
          topEndpoints: [],
          hourlyTrend: [],
          recentErrors: [],
        },
      }
    },
    computed: {
      filteredRequests() {
        if (!this.filterText) {
          return this.currentPageRequests
        }

        return this.currentPageRequests.filter(
          (req) =>
            req.url.toLowerCase().includes(this.filterText.toLowerCase()) ||
            req.status.toString().includes(this.filterText) ||
            req.method.toLowerCase().includes(this.filterText.toLowerCase())
        )
      },

      filteredTotal() {
        if (!this.filterText) {
          return this.totalRequests
        }
        return this.filteredRequests.length
      },
    },
    methods: {
      async handleSearch(params) {
        this.searchParams = { ...params }
        this.searchLoading = true
        this.currentPage = 1
        this.filterText = ''

        try {
          await this.fetchDataFromES()
        } catch (error) {
          this.$message.error('搜索失败：' + error.message)
          console.error('搜索错误:', error)
        } finally {
          this.searchLoading = false
        }
      },

      async fetchDataFromES() {
        const query = this.buildAccessLogQuery()
        const indices = this.generateAccessLogIndices()

        const from = (this.currentPage - 1) * this.pageSize
        const size = this.pageSize

        try {
          const [listResponse, statsResponse] = await Promise.all([
            getAccessLogs({
              query,
              indices,
              size: size,
              from: from,
            }),
            getLogStatistics({
              query,
              indices,
            }),
          ])

          this.currentPageRequests = this.transformRequestData(
            listResponse.hits.hits
          )
          this.totalRequests =
            listResponse.hits.total.value || listResponse.hits.total

          this.statisticsData = this.transformStatisticsData(
            statsResponse.aggregations
          )
          this.globalStatistics = {
            totalRequests: this.totalRequests,
            successRate: this.calculateSuccessRate(statsResponse.aggregations),
            avgResponseTime: this.calculateAvgResponseTime(
              statsResponse.aggregations
            ),
          }
        } catch (error) {
          console.error('ES查询失败:', error)
          throw new Error('数据获取失败')
        }
      },

      // 用户点击某条日志时的处理
      async handleSelectRequest(request) {
        this.selectedRequest = request
        this.showChainGraph = false
        this.serviceLogs = []

        // 如果有request_id，自动加载对应的服务日志
        if (request.requestId) {
          await this.loadServiceLogs(request.requestId)
        } else {
          console.warn('该请求缺少request_id，无法加载服务日志')
        }
      },

      // 加载服务日志的核心方法
      async loadServiceLogs(requestId) {
        if (!requestId) {
          this.$message.warning('缺少请求ID，无法获取服务日志')
          return
        }

        this.serviceLogsLoading = true
        this.serviceLogs = []

        try {
          const query = this.buildServiceLogQuery(requestId)
          const indices = this.generateServiceLogIndices()

          console.log('查询服务日志:', {
            requestId,
            query: JSON.stringify(query, null, 2),
            indices,
          })

          const response = await getServiceLogs({
            query,
            indices,
            size: 1000, // 获取更多服务日志
            sort: [
              { '@timestamp': { order: 'asc' } }, // 按时间升序排列
            ],
          })

          this.serviceLogs = this.transformServiceLogData(response.hits.hits)

          if (this.serviceLogs.length === 0) {
            //this.$message.info(`未找到request_id为 ${requestId} 的服务日志`)
          } else {
            console.log(`找到 ${this.serviceLogs.length} 条服务日志`)
          }
        } catch (error) {
          console.error('获取服务日志失败:', error)
          this.$message.error('获取服务日志失败：' + error.message)
        } finally {
          this.serviceLogsLoading = false
        }
      },

      // 构建服务日志查询 - 核心查询逻辑
      buildServiceLogQuery(requestId) {
        const must = [
          // 环境过滤
          {
            term: {
              'fields.env.keyword': 'prod-a',
            },
          },
          // 命名空间过滤
          {
            term: {
              'kubernetes.namespace.keyword': 'prod',
            },
          },
          // 时间范围过滤 - 基于搜索的时间范围
          {
            range: {
              '@timestamp': {
                gte: this.searchParams.startTime.toISOString(),
                lte: this.searchParams.endTime.toISOString(),
              },
            },
          },
          // 核心过滤条件：message字段包含request_id
          {
            match_phrase_prefix: {
              x_req_id: `${requestId}`,
            },
          },
        ]

        return {
          bool: {
            must,
          },
        }
      },

      // 生成服务日志索引名称
      generateServiceLogIndices() {
        const indices = []
        const start = new Date(this.searchParams.startTime)
        const end = new Date(this.searchParams.endTime)

        for (let d = new Date(start); d <= end; d.setHours(d.getHours() + 1)) {
          const year = d.getFullYear()
          const month = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          const hour = String(d.getHours()).padStart(2, '0')
          //const indexName = `tomcat_catalina_${year}.${month}.${day}.${hour}`
          const indexName = `tomcat_catalina_${year}.${month}.${day}.*`
          if (!indices.includes(indexName)) {
            indices.push(indexName)
          }
        }

        return indices.join(',')
      },

      // 转换服务日志数据
      // 在主组件中修改 transformServiceLogData 方法
      transformServiceLogData(hits) {
        console.log('转换服务日志数据:', hits) // 添加调试日志

        return hits.map((hit) => {
          const source = hit._source

          // 解析日志消息，tomcat日志通常格式为：
          // 2025-06-26 18:51:22.494 ERROR [requestId] [thread] logger:line - message
          const message = source.message || ''
          let level = 'INFO'
          let thread = ''
          let logger = ''
          let actualMessage = message

          // 尝试解析日志级别
          const levelMatch = message.match(
            /\b(TRACE|DEBUG|INFO|WARN|ERROR|FATAL)\b/
          )
          if (levelMatch) {
            level = levelMatch[1]
          }

          // 尝试解析线程名
          const threadMatch = message.match(
            /\[([^\]]+)\]\s*$|(\[http-nio-\d+-exec-\d+\])/g
          )
          if (threadMatch && threadMatch.length > 0) {
            thread = threadMatch[threadMatch.length - 1].replace(/\[|\]/g, '')
          }

          // 尝试解析logger和实际消息
          const loggerMatch = message.match(/([a-zA-Z0-9.]+):(\d+)\s*-\s*(.+)$/)
          if (loggerMatch) {
            logger = loggerMatch[1]
            actualMessage = loggerMatch[3]
          }

          return {
            id: hit._id,
            timestamp: source['@timestamp'],
            level: level,
            service: source.kubernetes?.container?.name || '未知服务',
            namespace: source.kubernetes?.namespace || '',
            pod: source.kubernetes?.pod?.name || '',
            node: source.kubernetes?.node?.name || '',
            message: actualMessage.trim(),
            fullMessage: message, // 保留完整消息
            logger: logger,
            thread: thread,
            exception: source.stack_trace || source.exception || '',
            fields: source.fields || {},
            kubernetes: source.kubernetes || {},
            // 原始数据，用于调试
            _source: source,
          }
        })
      },

      // 刷新服务日志
      async handleRefreshServiceLogs() {
        if (this.selectedRequest && this.selectedRequest.requestId) {
          await this.loadServiceLogs(this.selectedRequest.requestId)
        }
      },

      handleFilterChange(filterText) {
        this.filterText = filterText

        if (filterText && this.currentPage !== 1) {
          this.currentPage = 1
        }
      },

      async handlePageChange(pageInfo) {
        console.log('分页变化:', pageInfo)

        let needRefetch = false

        if (pageInfo.page !== undefined && pageInfo.page !== this.currentPage) {
          this.currentPage = pageInfo.page
          needRefetch = true
        }

        if (pageInfo.size !== undefined && pageInfo.size !== this.pageSize) {
          this.pageSize = pageInfo.size
          this.currentPage = 1
          needRefetch = true
        }

        if (needRefetch) {
          this.searchLoading = true
          try {
            await this.fetchDataFromES()
          } catch (error) {
            this.$message.error('获取数据失败：' + error.message)
          } finally {
            this.searchLoading = false
          }
        }

        console.log('更新后的分页参数:', {
          currentPage: this.currentPage,
          pageSize: this.pageSize,
          totalRequests: this.totalRequests,
        })
      },

      // 构建访问日志查询
      buildAccessLogQuery() {
        const must = [
          {
            term: {
              'x_albumid.keyword': this.searchParams.albumId,
            },
          },
          {
            range: {
              '@timestamp': {
                gte: this.searchParams.startTime.toISOString(),
                lte: this.searchParams.endTime.toISOString(),
              },
            },
          },
        ]

        if (this.searchParams.userId) {
          must.push({
            term: {
              'x_userid.keyword': this.searchParams.userId,
            },
          })
        }

        if (this.filterText) {
          must.push({
            bool: {
              should: [
                {
                  wildcard: {
                    npath: `*${this.filterText}*`,
                  },
                },
                {
                  term: {
                    status: this.filterText,
                  },
                },
                {
                  term: {
                    'verb.keyword': this.filterText.toUpperCase(),
                  },
                },
              ],
              minimum_should_match: 1,
            },
          })
        }

        return {
          bool: {
            must,
          },
        }
      },

      generateAccessLogIndices() {
        const indices = []
        const start = new Date(this.searchParams.startTime)
        const end = new Date(this.searchParams.endTime)

        for (let d = new Date(start); d <= end; d.setHours(d.getHours() + 1)) {
          const year = d.getFullYear()
          const month = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          const hour = String(d.getHours()).padStart(2, '0')
          const indexName = `nginx_access_${year}.${month}.${day}.${hour}`

          if (!indices.includes(indexName)) {
            indices.push(indexName)
          }
        }

        return indices.join(',')
      },

      transformRequestData(hits) {
        return hits.map((hit) => {
          const source = hit._source
          return {
            id: hit._id,
            method: source.verb || 'GET',
            url: source.npath || '',
            status: source.status || 0,
            duration: `${Math.round((source.request_time || 0) * 1000)}ms`,
            timestamp: new Date(source['@timestamp']).toLocaleString(),
            ip: source.client_ip || '',
            userAgent: source.useragent || '',
            albumId: source.x_albumid || '',
            requestId: source.x_req_id || '', // 这个是关键字段
            request_body: source.request_body || '',
            txId: source.txid || '',
            responseSize: this.formatBytes(source.body_bytes_sent || 0),
            headers: {},
            queryParams: source.nparam || '',
          }
        })
      },

      parseQueryParams(uri) {
        try {
          const url = new URL('http://example.com' + uri)
          const params = {}
          url.searchParams.forEach((value, key) => {
            params[key] = value
          })
          return params
        } catch {
          return {}
        }
      },

      transformStatisticsData(aggs) {
        if (!aggs) return this.statisticsData

        return {
          totalRequests: this.totalRequests,
          successRate: this.calculateSuccessRate(aggs),
          avgResponseTime: this.calculateAvgResponseTime(aggs),
          errorCount: this.calculateErrorCount(aggs),
          statusDistribution: this.transformStatusDistribution(
            aggs.status_stats
          ),
          responseTimeDistribution: this.transformResponseTimeDistribution(
            aggs.response_time_stats
          ),
          topEndpoints: this.transformTopEndpoints(aggs.top_endpoints),
          hourlyTrend: this.transformHourlyTrend(aggs.hourly_trend),
          recentErrors: this.transformRecentErrors(aggs.recent_errors),
        }
      },

      calculateSuccessRate(aggs) {
        if (!aggs.status_stats || !aggs.status_stats.buckets) return 0

        const statusBuckets = aggs.status_stats.buckets
        const totalRequests = statusBuckets.reduce(
          (sum, bucket) => sum + bucket.doc_count,
          0
        )
        const successRequests = statusBuckets
          .filter((bucket) => bucket.key >= 200 && bucket.key < 400)
          .reduce((sum, bucket) => sum + bucket.doc_count, 0)

        return totalRequests > 0
          ? ((successRequests / totalRequests) * 100).toFixed(1)
          : 0
      },

      calculateAvgResponseTime(aggs) {
        if (!aggs.avg_response_time || !aggs.avg_response_time.value)
          return '0ms'
        const avgTime = aggs.avg_response_time.value
        return `${Math.round(avgTime * 1000)}ms`
      },

      calculateErrorCount(aggs) {
        return aggs.recent_errors ? aggs.recent_errors.doc_count : 0
      },

      transformStatusDistribution(statusAgg) {
        if (!statusAgg || !statusAgg.buckets) return []

        const total = statusAgg.buckets.reduce(
          (sum, bucket) => sum + bucket.doc_count,
          0
        )
        return statusAgg.buckets.map((bucket) => ({
          status: bucket.key.toString(),
          count: bucket.doc_count,
          percentage:
            total > 0 ? ((bucket.doc_count / total) * 100).toFixed(1) : 0,
        }))
      },

      transformResponseTimeDistribution(timeAgg) {
        if (!timeAgg || !timeAgg.buckets) return []

        const total = timeAgg.buckets.reduce(
          (sum, bucket) => sum + bucket.doc_count,
          0
        )
        return timeAgg.buckets.map((bucket) => ({
          range: `${bucket.key.toFixed(1)}s-${(bucket.key + 0.1).toFixed(1)}s`,
          count: bucket.doc_count,
          percentage:
            total > 0 ? ((bucket.doc_count / total) * 100).toFixed(1) : 0,
        }))
      },

      transformTopEndpoints(endpointsAgg) {
        if (!endpointsAgg || !endpointsAgg.buckets) return []

        return endpointsAgg.buckets.map((bucket) => ({
          url: bucket.key,
          count: bucket.doc_count,
          avgTime: bucket.avg_response_time
            ? `${Math.round(bucket.avg_response_time.value * 1000)}ms`
            : '0ms',
        }))
      },

      transformHourlyTrend(hourlyAgg) {
        if (!hourlyAgg || !hourlyAgg.buckets) return []

        return hourlyAgg.buckets.map((bucket) => ({
          hour: new Date(bucket.key).getHours() + ':00',
          requests: bucket.doc_count,
        }))
      },

      transformRecentErrors(errorsAgg) {
        if (
          !errorsAgg ||
          !errorsAgg.latest_errors ||
          !errorsAgg.latest_errors.hits
        )
          return []

        return errorsAgg.latest_errors.hits.hits.map((hit) => {
          const source = hit._source
          return {
            id: hit._id,
            url: source.request_uri || '',
            status: source.status || 0,
            timestamp: new Date(source['@timestamp']).toLocaleString(),
            duration: `${Math.round((source.request_time || 0) * 1000)}ms`,
          }
        })
      },

      formatBytes(bytes) {
        if (bytes === 0) return '0 B'
        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      },

      handleCloseDetail() {
        this.selectedRequest = null
        this.showChainGraph = false
        this.serviceLogs = []
      },

      handleToggleChainGraph() {
        this.showChainGraph = !this.showChainGraph
      },

      async handleViewRawLog(request) {
        try {
          const response = await getRawLog(request.id)
          this.$alert(response.data, '原始日志', {
            dangerouslyUseHTMLString: true,
            customClass: 'raw-log-dialog',
          })
        } catch (error) {
          this.$message.error('获取原始日志失败')
        }
      },

      handleViewInKibana(request) {
        const startTime = this.searchParams.startTime.toISOString()
        const endTime = this.searchParams.endTime.toISOString()
        const query = `x_request_id:"${request.requestId}"`

        const kibanaUrl = `/kibana/app/discover#/?_g=(time:(from:'${startTime}',to:'${endTime}'))&_a=(query:(language:kuery,query:'${query}'))`
        window.open(kibanaUrl, '_blank')
      },
    },
  }
</script>

<style scoped>
  .log-tracking-system {
    height: 100vh;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
  }

  .main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  :global(.raw-log-dialog) {
    width: 80%;
    max-width: 1000px;
  }

  :global(.raw-log-dialog .el-message-box__message) {
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    white-space: pre-wrap;
    background: #1e1e1e;
    color: #52c41a;
    padding: 16px;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
  }
</style>
