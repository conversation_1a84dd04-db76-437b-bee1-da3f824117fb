<!-- RequestDetail.vue -->
<template>
  <div class="request-detail">
    <!-- 请求详情头部 - 可滚动区域 -->
    <div class="detail-header">
      <!-- ... 保持原有内容 ... -->
      <!-- 这里保持所有原有的头部内容，只修改样式 -->
      <div class="main-info">
        <div class="request-summary">
          <el-tag :type="getStatusType(request.status)" size="medium">
            {{ request.method }} {{ request.status }}
          </el-tag>
          <span class="request-url">{{ request.url }}</span>
        </div>
        <div class="detail-actions">
          <span :class="['duration', getDurationClass(request.duration)]">
            {{ request.duration }}
          </span>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleClose"
          />
        </div>
      </div>

      <!-- 核心指标卡片 -->
      <div class="metrics-cards">
        <div class="metric-card blue">
          <i class="el-icon-user"></i>
          <div class="metric-info">
            <div class="metric-label">用户ID</div>
            <div class="metric-value">{{ request.albumId }}</div>
          </div>
        </div>
        <div class="metric-card green">
          <i class="el-icon-location"></i>
          <div class="metric-info">
            <div class="metric-label">客户端IP</div>
            <div class="metric-value">{{ request.ip }}</div>
          </div>
        </div>
        <div class="metric-card purple">
          <i class="el-icon-time"></i>
          <div class="metric-info">
            <div class="metric-label">请求时间</div>
            <div class="metric-value">{{ request.timestamp }}</div>
          </div>
        </div>
        <div class="metric-card orange">
          <i class="el-icon-document"></i>
          <div class="metric-info">
            <div class="metric-label">响应大小</div>
            <div class="metric-value">{{ request.responseSize }}</div>
          </div>
        </div>
      </div>

      <!-- 跟踪ID -->
      <div class="trace-ids">
        <div class="trace-item">
          <span class="trace-label">Request ID:</span>
          <el-tag size="small" type="info">{{ request.requestId }}</el-tag>
        </div>
        <div v-if="request.txId" class="trace-item">
          <span class="trace-label">Transaction ID:</span>
          <el-tag size="small" type="primary">{{ request.txId }}</el-tag>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-share"
          @click="handleToggleChainGraph"
        >
          {{ showChainGraph ? '隐藏' : '显示' }}链路图
        </el-button>
        <el-button
          type="default"
          size="small"
          icon="el-icon-document"
          @click="handleViewRawLog"
        >
          原始日志
        </el-button>
        <el-button
          type="success"
          size="small"
          icon="el-icon-link"
          @click="handleViewInKibana"
        >
          在Kibana中查看
        </el-button>
        <el-button
          type="warning"
          size="small"
          icon="el-icon-refresh"
          @click="handleRefreshServiceLogs"
        >
          刷新服务日志
        </el-button>
      </div>

      <!-- 详细信息 - 简化版本，减少高度占用 -->
      <div class="detail-info-compact">
        <div class="info-section">
          <h3 class="section-title">
            <i class="el-icon-upload"></i>
            关键信息
          </h3>
          <div class="info-grid">
            <div v-if="userAgentText" class="info-item-compact">
              <span class="info-label">User Agent:</span>
              <span class="info-value-compact" :title="userAgentText">
                {{ getDisplayText(userAgentText, false) }}
              </span>
            </div>
            <div v-if="requestBodyText" class="info-item-compact">
              <span class="info-label">Request Body:</span>
              <span class="info-value-compact" :title="requestBodyText">
                {{ getDisplayText(requestBodyText, false) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 链路图区域 -->
    <ChainGraph
      v-if="showChainGraph"
      :request="request"
      class="chain-graph-section"
    />

    <!-- 服务日志区域 - 关键：占用剩余空间 -->
    <div class="service-logs-section">
      <ServiceLogs
        :request="request"
        :service-logs="serviceLogs"
        :service-logs-loading="serviceLogsLoading"
        @refresh="handleRefreshServiceLogs"
      />
    </div>
  </div>
</template>

<script>
  import ChainGraph from './ChainGraph.vue'
  import ServiceLogs from './ServiceLogs.vue'

  export default {
    name: 'RequestDetail',
    components: {
      ChainGraph,
      ServiceLogs,
    },
    props: {
      request: {
        type: Object,
        required: true,
      },
      serviceLogs: {
        type: Array,
        default: () => [],
      },
      serviceLogsLoading: {
        type: Boolean,
        default: false,
      },
      showChainGraph: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        expandedUserAgent: false,
        expandedQueryParams: false,
        expandedRequestBody: false,
        maxPreviewLength: 100, // 减少预览长度
        maxPreviewLines: 8,
      }
    },
    computed: {
      // 安全获取 UserAgent 文本
      userAgentText() {
        if (!this.request.userAgent) return ''

        // 如果是字符串，直接返回
        if (typeof this.request.userAgent === 'string') {
          return this.request.userAgent
        }

        // 如果是对象，尝试获取 original 属性
        if (typeof this.request.userAgent === 'object') {
          return (
            this.request.userAgent.original ||
            this.request.userAgent.value ||
            JSON.stringify(this.request.userAgent)
          )
        }

        // 其他情况转为字符串
        return String(this.request.userAgent)
      },

      // 安全获取 Request Body 文本
      requestBodyText() {
        if (!this.request.request_body) return ''

        // 如果是字符串，直接返回
        if (typeof this.request.request_body === 'string') {
          return this.request.request_body
        }

        // 如果是对象，转为 JSON 字符串
        if (typeof this.request.request_body === 'object') {
          try {
            return JSON.stringify(this.request.request_body, null, 2)
          } catch (e) {
            return String(this.request.request_body)
          }
        }

        // 其他情况转为字符串
        return String(this.request.request_body)
      },

      hasQueryParams() {
        return (
          this.request.queryParams &&
          Object.keys(this.request.queryParams).length > 0
        )
      },

      isUserAgentLong() {
        return (
          this.userAgentText &&
          this.userAgentText.length > this.maxPreviewLength
        )
      },

      isQueryParamsLong() {
        if (!this.hasQueryParams) return false
        const jsonStr = JSON.stringify(this.request.queryParams, null, 2)
        return (
          jsonStr.length > this.maxPreviewLength ||
          jsonStr.split('\n').length > this.maxPreviewLines
        )
      },

      isRequestBodyLong() {
        return (
          this.requestBodyText &&
          this.requestBodyText.length > this.maxPreviewLength
        )
      },
    },
    methods: {
      handleClose() {
        this.$emit('close')
      },
      handleToggleChainGraph() {
        this.$emit('toggle-chain-graph')
      },
      handleViewRawLog() {
        this.$emit('view-raw-log', this.request)
      },
      handleViewInKibana() {
        this.$emit('view-in-kibana', this.request)
      },
      handleRefreshServiceLogs() {
        this.$emit('refresh-service-logs')
      },
      getStatusType(status) {
        if (status >= 200 && status < 300) return 'success'
        if (status >= 400 && status < 500) return 'warning'
        if (status >= 500) return 'danger'
        return 'info'
      },
      getDurationClass(duration) {
        const time = parseFloat(duration)
        if (time < 200) return 'fast'
        if (time < 1000) return 'normal'
        return 'slow'
      },

      // 安全的文本显示方法
      getDisplayText(text, expanded) {
        // 确保 text 是字符串
        if (!text) return ''

        const textStr = String(text) // 强制转换为字符串

        if (expanded || textStr.length <= this.maxPreviewLength) {
          return textStr
        }
        return textStr.substring(0, this.maxPreviewLength) + '...'
      },

      // 安全的 JSON 显示方法
      getDisplayJson(obj, expanded) {
        if (!obj) return ''

        let jsonStr = ''
        try {
          jsonStr = JSON.stringify(obj, null, 2)
        } catch (e) {
          jsonStr = String(obj)
        }

        if (expanded) {
          return jsonStr
        }

        const lines = jsonStr.split('\n')
        if (lines.length > this.maxPreviewLines) {
          return lines.slice(0, this.maxPreviewLines).join('\n') + '\n...'
        }

        if (jsonStr.length > this.maxPreviewLength) {
          return jsonStr.substring(0, this.maxPreviewLength) + '...'
        }

        return jsonStr
      },

      // 安全的复制到剪贴板方法
      async copyToClipboard(text) {
        try {
          const textToCopy = String(text || '') // 确保是字符串
          await navigator.clipboard.writeText(textToCopy)
          this.$message.success('复制成功')
        } catch (err) {
          // 降级处理
          try {
            const textToCopy = String(text || '') // 确保是字符串
            const textarea = document.createElement('textarea')
            textarea.value = textToCopy
            document.body.appendChild(textarea)
            textarea.select()
            document.execCommand('copy')
            document.body.removeChild(textarea)
            this.$message.success('复制成功')
          } catch (e) {
            this.$message.error('复制失败')
          }
        }
      },
    },
  }
</script>

<style scoped>
  .request-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .detail-header {
    background: white;
    border-bottom: 1px solid #e8e8e8;
    padding: 16px;
    max-height: 50vh; /* 限制头部最大高度为视口的50% */
    overflow-y: auto;
    flex-shrink: 0;
  }

  .main-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .request-summary {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .request-url {
    font-size: 16px; /* 减小字体 */
    font-family: monospace;
    color: #333;
    word-break: break-all;
  }

  .detail-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .duration {
    font-size: 16px; /* 减小字体 */
    font-weight: bold;
  }

  .duration.fast {
    color: #52c41a;
  }
  .duration.normal {
    color: #faad14;
  }
  .duration.slow {
    color: #ff4d4f;
  }

  .metrics-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px; /* 减小间距 */
    margin-bottom: 12px;
  }

  .metric-card {
    display: flex;
    align-items: center;
    gap: 6px; /* 减小间距 */
    padding: 8px; /* 减小内边距 */
    border-radius: 6px;
  }

  .metric-card.blue {
    background: #f0f9ff;
    color: #1890ff;
  }
  .metric-card.green {
    background: #f6ffed;
    color: #52c41a;
  }
  .metric-card.purple {
    background: #f9f0ff;
    color: #722ed1;
  }
  .metric-card.orange {
    background: #fff7e6;
    color: #fa8c16;
  }

  .metric-card i {
    font-size: 14px; /* 减小图标 */
  }

  .metric-label {
    font-size: 11px; /* 减小字体 */
    opacity: 0.8;
  }

  .metric-value {
    font-size: 12px; /* 减小字体 */
    font-weight: 500;
  }

  .trace-ids {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
  }

  .trace-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .trace-label {
    font-size: 12px;
    color: #666;
  }

  .action-buttons {
    display: flex;
    gap: 6px; /* 减小间距 */
    margin-bottom: 12px; /* 减小底部间距 */
    flex-wrap: wrap;
  }

  /* 简化的详细信息区域 */
  .detail-info-compact {
    margin-bottom: 8px;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 13px; /* 减小字体 */
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .info-item-compact {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .info-label {
    color: #666;
    font-weight: 500;
    font-size: 11px; /* 减小字体 */
  }

  .info-value-compact {
    color: #333;
    font-size: 11px; /* 减小字体 */
    font-family: monospace;
    background: #f5f5f5;
    padding: 4px 6px; /* 减小内边距 */
    border-radius: 3px;
    border: 1px solid #e8e8e8;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 链路图区域 */
  .chain-graph-section {
    flex-shrink: 0;
    max-height: 300px;
    overflow: hidden;
    border-bottom: 1px solid #e8e8e8;
  }

  /* 服务日志区域 - 关键样式 */
  .service-logs-section {
    flex: 1;
    overflow: hidden;
    min-height: 0; /* 关键：允许子元素收缩 */
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .info-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .metrics-cards {
      grid-template-columns: repeat(2, 1fr);
    }

    .main-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .detail-actions {
      align-self: flex-end;
    }

    .action-buttons {
      justify-content: flex-start;
    }
  }
</style>
