<!-- ServiceLogs.vue -->
<template>
  <div class="service-logs">
    <!-- 加载状态 -->
    <div
      v-if="serviceLogsLoading"
      v-loading="true"
      class="loading-wrapper"
      element-loading-text="正在加载服务日志..."
    ></div>

    <!-- 无数据状态 -->
    <div v-else-if="!hasLogs" class="no-data">
      <el-empty description="未找到相关服务日志">
        <template slot="description">
          <div class="empty-description">
            <p>
              未找到request_id为
              <strong>{{ request.requestId }}</strong>
              的服务日志
            </p>
            <p class="empty-tip">请确认日志时间范围和request_id是否正确</p>
          </div>
        </template>
        <el-button type="primary" @click="$emit('refresh')">重新加载</el-button>
      </el-empty>
    </div>

    <!-- 服务页签 -->
    <div v-else class="service-tabs-container">
      <el-tabs v-model="activeTab" type="border-card" class="full-height-tabs">
        <el-tab-pane
          v-for="(logs, service) in groupedLogs"
          :key="service"
          :name="service"
          class="full-height-pane"
        >
          <span slot="label">
            {{ service.toUpperCase() }}
            <el-badge :value="logs.length" :max="999" />
          </span>

          <!-- 日志内容 -->
          <div class="log-content">
            <div class="log-viewer">
              <div
                v-for="(log, index) in logs"
                :key="log.id || index"
                class="log-line"
                :class="{ highlight: isHighlighted(log) }"
              >
                <span class="log-time">{{ formatTime(log.timestamp) }}</span>
                <span
                  :class="['log-level', `level-${log.level.toLowerCase()}`]"
                >
                  [{{ log.level }}]
                </span>
                <span class="log-service">[{{ log.service }}]</span>
                <span v-if="log.thread" class="log-thread">
                  [{{ log.thread }}]
                </span>
                <span v-if="log.logger" class="log-logger">
                  [{{ log.logger }}]
                </span>
                <span class="log-message">{{ log.message }}</span>

                <!-- 异常堆栈 -->
                <div v-if="log.exception" class="log-exception">
                  <pre>{{ log.exception }}</pre>
                </div>
              </div>

              <!-- 如果日志太多，显示加载更多 -->
              <div v-if="logs.length >= 1000" class="log-line more">
                <span class="log-more">
                  已显示前1000条日志，可能还有更多...
                </span>
              </div>
            </div>

            <div class="log-footer">
              <span class="log-count">共 {{ logs.length }} 条记录</span>
              <div class="log-actions">
                <el-button size="mini" plain @click="exportLogs(service)">
                  导出
                </el-button>
                <el-button size="mini" plain @click="$emit('refresh')">
                  刷新
                </el-button>
                <el-button size="mini" plain @click="viewInKibana(service)">
                  在Kibana中查看
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ServiceLogs',
    props: {
      request: {
        type: Object,
        required: true,
      },
      serviceLogs: {
        type: Array,
        default: () => [],
      },
      serviceLogsLoading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        activeTab: '',
      }
    },
    computed: {
      // 按服务分组日志
      groupedLogs() {
        const groups = {}

        this.serviceLogs.forEach((log) => {
          const service = log.service || '未知服务'
          if (!groups[service]) {
            groups[service] = []
          }
          groups[service].push(log)
        })

        // 按时间排序每个服务的日志
        Object.keys(groups).forEach((service) => {
          groups[service].sort(
            (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
          )
        })

        return groups
      },

      // 是否有日志数据
      hasLogs() {
        return this.serviceLogs && this.serviceLogs.length > 0
      },

      // 服务列表
      services() {
        return Object.keys(this.groupedLogs)
      },
    },
    watch: {
      // 当日志数据变化时，自动选择第一个有数据的服务
      groupedLogs: {
        handler(newGroups) {
          if (Object.keys(newGroups).length > 0 && !this.activeTab) {
            this.activeTab = Object.keys(newGroups)[0]
          }
        },
        immediate: true,
      },
    },
    methods: {
      // 格式化时间显示
      formatTime(timestamp) {
        const date = new Date(timestamp)
        return date.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          fractionalSecondDigits: 3,
        })
      },

      // 判断是否需要高亮显示（比如包含错误的日志）
      isHighlighted(log) {
        return log.level === 'ERROR' || log.level === 'WARN' || log.exception
      },

      // 导出日志
      exportLogs(service) {
        const logs = this.groupedLogs[service] || []
        if (logs.length === 0) {
          this.$message.warning('没有可导出的日志')
          return
        }

        const content = logs
          .map((log) => {
            let line = `${log.timestamp} [${log.level}] [${log.service}]`
            if (log.thread) line += ` [${log.thread}]`
            if (log.logger) line += ` [${log.logger}]`
            line += ` ${log.message}`
            if (log.exception) line += `\n${log.exception}`
            return line
          })
          .join('\n')

        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${service}-logs-${this.request.requestId}.txt`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        this.$message.success('日志导出成功')
      },

      // 在Kibana中查看
      viewInKibana(service) {
        const requestId = this.request.requestId
        if (!requestId) {
          this.$message.warning('缺少请求ID')
          return
        }

        // 构造Kibana查询URL
        const query = `message:"${requestId}" AND kubernetes.container.name:"${service}"`
        const startTime = new Date(
          new Date().getTime() - 30 * 60 * 1000
        ).toISOString() // 30分钟前
        const endTime = new Date().toISOString()

        const kibanaUrl = `/kibana/app/discover#/?_g=(time:(from:'${startTime}',to:'${endTime}'))&_a=(query:(language:kuery,query:'${encodeURIComponent(
          query
        )}'))`
        window.open(kibanaUrl, '_blank')
      },
    },
  }
</script>

<style scoped>
  .service-logs {
    height: 100%;
    background: white;
    display: flex;
    flex-direction: column;
  }

  .loading-wrapper {
    flex: 1;
    position: relative;
    background: white;
  }

  .no-data {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .empty-description {
    text-align: center;
  }

  .empty-description p {
    margin: 4px 0;
    color: #666;
  }

  .empty-tip {
    font-size: 12px;
    color: #999;
  }

  .service-tabs-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 关键样式：让tabs占满全部高度 */
  .full-height-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .full-height-tabs :deep(.el-tabs__content) {
    flex: 1;
    overflow: hidden;
  }

  .full-height-pane {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .log-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 12px;
  }

  .log-viewer {
    flex: 1;
    background: #1e1e1e;
    color: #52c41a;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    padding: 12px;
    border-radius: 4px;
    overflow-y: auto;
    line-height: 1.4;
    min-height: 0; /* 关键：允许收缩 */
  }

  .log-line {
    margin-bottom: 2px;
    display: flex;
    gap: 8px;
    align-items: flex-start;
  }

  .log-line.highlight {
    background-color: rgba(255, 77, 79, 0.1);
    border-left: 3px solid #ff4d4f;
    padding-left: 8px;
    margin-left: -8px;
  }

  .log-time {
    color: #d9d9d9;
    min-width: 80px;
    flex-shrink: 0;
  }

  .log-level {
    min-width: 60px;
    flex-shrink: 0;
  }

  .level-info {
    color: #faad14;
  }
  .level-debug {
    color: #8c8c8c;
  }
  .level-warn {
    color: #fa8c16;
  }
  .level-error {
    color: #ff4d4f;
  }

  .log-service {
    color: #1890ff;
    min-width: 80px;
    flex-shrink: 0;
  }

  .log-thread {
    color: #722ed1;
    min-width: 100px;
    flex-shrink: 0;
  }

  .log-logger {
    color: #13c2c2;
    min-width: 120px;
    flex-shrink: 0;
  }

  .log-message {
    color: #52c41a;
    flex: 1;
    word-break: break-all;
  }

  .log-exception {
    width: 100%;
    margin-top: 4px;
    padding: 8px;
    background-color: rgba(255, 77, 79, 0.1);
    border-radius: 4px;
  }

  .log-exception pre {
    color: #ff4d4f;
    font-size: 11px;
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .log-line.more {
    color: #8c8c8c;
    font-style: italic;
    justify-content: center;
  }

  .log-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e8e8e8;
    font-size: 12px;
    flex-shrink: 0;
  }

  .log-count {
    color: #666;
  }

  .log-actions {
    display: flex;
    gap: 8px;
  }

  /* 自定义滚动条 */
  .log-viewer::-webkit-scrollbar {
    width: 8px;
  }

  .log-viewer::-webkit-scrollbar-track {
    background: #2c2c2c;
    border-radius: 4px;
  }

  .log-viewer::-webkit-scrollbar-thumb {
    background: #666;
    border-radius: 4px;
  }

  .log-viewer::-webkit-scrollbar-thumb:hover {
    background: #888;
  }
</style>
