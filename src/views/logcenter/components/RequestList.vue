<!-- RequestList.vue -->
<template>
  <div class="request-list">
    <!-- 列表头部 -->
    <div class="list-header">
      <div class="header-content">
        <div class="header-info">
          <span class="title">访问日志</span>
          <el-badge :value="total" :max="9999" class="total-badge" />
        </div>
        <el-tooltip content="过滤列表" placement="top">
          <i class="el-icon-filter filter-icon"></i>
        </el-tooltip>
      </div>
      <div class="search-box">
        <el-input
          v-model="filterText"
          placeholder="过滤 URL、状态码..."
          size="small"
          clearable
          @input="handleFilterChange"
        >
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
    </div>

    <!-- 列表内容区域 - 固定高度，可滚动 -->
    <div v-loading="loading" class="list-content">
      <div class="content-wrapper">
        <div
          v-for="request in requests"
          :key="request.id"
          :class="[
            'request-item',
            {
              active: selectedRequest && selectedRequest.id === request.id,
              'status-error': request.status >= 400,
              'status-success': request.status >= 200 && request.status < 300,
            },
          ]"
          @click="handleSelectRequest(request)"
        >
          <!-- 请求头部信息 -->
          <div class="request-header">
            <div class="method-status">
              <el-tag
                :type="getMethodType(request.method)"
                size="mini"
                class="method-tag"
              >
                {{ request.method }}
              </el-tag>
              <el-tag
                :type="getStatusType(request.status)"
                size="mini"
                class="status-tag"
              >
                {{ request.status }}
              </el-tag>
            </div>
            <div class="timing-info">
              <span :class="['duration', getDurationClass(request.duration)]">
                {{ request.duration }}
              </span>
            </div>
          </div>

          <!-- URL信息 -->
          <div class="request-url" :title="request.url">
            {{ request.url }}
          </div>

          <!-- 请求底部信息 -->
          <div class="request-footer">
            <div class="request-meta">
              <span class="timestamp">{{ formatTime(request.timestamp) }}</span>
              <span v-if="request.userId" class="user-id">
                {{ request.userId }}
              </span>
            </div>
            <i class="el-icon-arrow-right arrow-icon"></i>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && requests.length === 0" class="empty-state">
          <i class="el-icon-document"></i>
          <p>暂无匹配的日志数据</p>
          <p class="empty-tip">尝试调整搜索条件或时间范围</p>
        </div>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="list-pagination">
      <div class="pagination-info">
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        <span class="total-info">
          显示第 {{ getDisplayRange().start }}-{{ getDisplayRange().end }} 条，
          共 {{ total }} 条记录
        </span>
      </div>

      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[100, 500]"
        :pager-count="5"
        layout="sizes, prev, pager, next"
        class="custom-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
  export default {
    name: 'RequestList',
    props: {
      requests: {
        type: Array,
        required: true,
      },
      selectedRequest: {
        type: Object,
        default: null,
      },
      loading: {
        type: Boolean,
        default: false,
      },
      total: {
        type: Number,
        default: 0,
      },
      currentPage: {
        type: Number,
        default: 1,
      },
      pageSize: {
        type: Number,
        default: 50,
      },
    },
    data() {
      return {
        filterText: '',
      }
    },
    computed: {
      totalPages() {
        return Math.ceil(this.total / this.pageSize) || 1
      },
      filteredTotal() {
        // 如果有过滤，需要重新计算总数
        return this.filterText ? this.requests.length : this.total
      },
    },
    methods: {
      handleSelectRequest(request) {
        this.$emit('select-request', request)
      },

      handleFilterChange() {
        this.$emit('filter-change', this.filterText)
      },

      handleSizeChange(size) {
        this.$emit('page-change', { page: 1, size })
      },

      handleCurrentChange(page) {
        this.$emit('page-change', { page, size: this.pageSize })
      },

      getDisplayRange() {
        const start = (this.currentPage - 1) * this.pageSize + 1
        const end = Math.min(
          this.currentPage * this.pageSize,
          this.filteredTotal
        )
        return { start, end }
      },

      formatTime(timestamp) {
        // 格式化时间显示，只显示时分秒
        const date = new Date(timestamp)
        return date.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
      },

      getDurationClass(duration) {
        const time = parseFloat(duration)
        if (time < 200) return 'fast'
        if (time < 1000) return 'normal'
        return 'slow'
      },

      getMethodType(method) {
        const types = {
          GET: 'success',
          POST: 'primary',
          PUT: 'warning',
          DELETE: 'danger',
          PATCH: 'info',
        }
        return types[method] || 'info'
      },

      getStatusType(status) {
        if (status >= 200 && status < 300) return 'success'
        if (status >= 300 && status < 400) return 'info'
        if (status >= 400 && status < 500) return 'warning'
        if (status >= 500) return 'danger'
        return 'info'
      },
    },
  }
</script>

<style scoped>
  .request-list {
    width: 380px;
    background: white;
    border-right: 1px solid #e8e8e8;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* 列表头部 */
  .list-header {
    padding: 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;
    flex-shrink: 0;
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }

  .header-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .total-badge {
    line-height: 1;
  }

  .filter-icon {
    font-size: 16px;
    color: #666;
    cursor: pointer;
    transition: color 0.3s;
  }

  .filter-icon:hover {
    color: #1890ff;
  }

  .search-box {
    margin: 0;
  }

  /* 列表内容区域 - 关键：固定高度和滚动 */
  .list-content {
    flex: 1;
    overflow: hidden;
    min-height: 400px;
    max-height: calc(100vh - 300px); /* 动态高度，减去头部和分页的高度 */
  }

  .content-wrapper {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* 自定义滚动条 */
  .content-wrapper::-webkit-scrollbar {
    width: 6px;
  }

  .content-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .content-wrapper::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .content-wrapper::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
  }

  /* 请求项样式 */
  .request-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
  }

  .request-item:hover {
    background-color: #f8f9fa;
  }

  .request-item.active {
    background-color: #e6f7ff;
    border-left: 3px solid #1890ff;
  }

  .request-item.status-error {
    border-left: 3px solid transparent;
    border-left-color: #ff4d4f;
  }

  .request-item.status-success {
    border-left: 3px solid transparent;
    border-left-color: #52c41a;
  }

  .request-item.active.status-error {
    border-left-color: #1890ff;
  }

  .request-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
  }

  .method-status {
    display: flex;
    gap: 6px;
  }

  .method-tag,
  .status-tag {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 2px;
  }

  .timing-info {
    display: flex;
    align-items: center;
  }

  .duration {
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 2px;
  }

  .duration.fast {
    color: #52c41a;
    background: #f6ffed;
  }
  .duration.normal {
    color: #faad14;
    background: #fffbe6;
  }
  .duration.slow {
    color: #ff4d4f;
    background: #fff2f0;
  }

  .request-url {
    font-size: 13px;
    color: #333;
    margin-bottom: 6px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.4;
  }

  .request-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .request-meta {
    display: flex;
    flex-direction: column;
    gap: 2px;
  }

  .timestamp {
    font-size: 11px;
    color: #999;
  }

  .user-id {
    font-size: 11px;
    color: #666;
    background: #f0f0f0;
    padding: 1px 4px;
    border-radius: 2px;
  }

  .arrow-icon {
    font-size: 12px;
    color: #ccc;
    transition: color 0.2s;
  }

  .request-item:hover .arrow-icon {
    color: #1890ff;
  }

  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #999;
    text-align: center;
  }

  .empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    color: #ddd;
  }

  .empty-state p {
    margin: 4px 0;
    font-size: 14px;
  }

  .empty-tip {
    font-size: 12px;
    color: #bbb;
  }

  /* 分页区域 */
  .list-pagination {
    border-top: 1px solid #e8e8e8;
    background: #fafafa;
    padding: 12px 16px;
    flex-shrink: 0;
  }

  .pagination-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    color: #666;
  }

  .page-info {
    font-weight: 500;
  }

  .total-info {
    color: #999;
  }

  /* 自定义分页样式 */
  .custom-pagination {
    display: flex;
    justify-content: center;
  }

  :deep(.custom-pagination .el-pagination__sizes) {
    margin-right: 12px;
  }

  :deep(.custom-pagination .el-pagination__jump) {
    margin-left: 12px;
  }

  :deep(.custom-pagination .el-pager li) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
  }

  :deep(.custom-pagination .el-pagination__prev),
  :deep(.custom-pagination .el-pagination__next) {
    padding: 0 8px;
    font-size: 12px;
  }

  /* 响应式设计 */
  @media (max-height: 800px) {
    .list-content {
      max-height: calc(100vh - 280px);
    }
  }

  @media (max-height: 600px) {
    .list-content {
      max-height: calc(100vh - 250px);
    }
  }
</style>
