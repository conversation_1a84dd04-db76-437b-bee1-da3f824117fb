<template>
  <div class="chain-graph">
    <div class="graph-header">
      <h3>调用链路图</h3>
    </div>
    <div class="graph-content">
      <div class="graph-placeholder">
        <i class="el-icon-share"></i>
        <p>链路图可视化组件区域</p>
        <p class="graph-info">
          基于 {{ request.requestId }}
          <span v-if="request.txId">和 {{ request.txId }}</span>
          的调用链路
        </p>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ChainGraph',
    props: {
      request: {
        type: Object,
        required: true,
      },
    },
  }
</script>

<style scoped>
  .chain-graph {
    background: white;
    border-bottom: 1px solid #e8e8e8;
    padding: 12px;
  }

  .graph-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
  }

  .graph-content {
    background: #f5f5f5;
    border-radius: 6px;
    padding: 32px;
    text-align: center;
  }

  .graph-placeholder i {
    font-size: 64px;
    color: #ccc;
    display: block;
    margin-bottom: 16px;
  }

  .graph-placeholder p {
    color: #666;
    margin: 8px 0;
  }

  .graph-info {
    font-size: 14px;
    color: #999;
  }
</style>
