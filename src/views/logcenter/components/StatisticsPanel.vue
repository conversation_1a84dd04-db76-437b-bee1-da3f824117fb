<template>
  <div class="statistics-panel">
    <div class="panel-content">
      <h2 class="panel-title">用户 {{ albumId }} 访问统计</h2>

      <!-- 快速指标 -->
      <div class="metrics-grid">
        <el-card class="metric-card blue">
          <div class="metric-content">
            <div class="metric-info">
              <p class="metric-label">总请求数</p>
              <p class="metric-value">{{ statisticsData.totalRequests }}</p>
            </div>
            <i class="el-icon-data-line metric-icon"></i>
          </div>
        </el-card>

        <el-card class="metric-card green">
          <div class="metric-content">
            <div class="metric-info">
              <p class="metric-label">成功率</p>
              <p class="metric-value">{{ statisticsData.successRate }}%</p>
            </div>
            <i class="el-icon-circle-check metric-icon"></i>
          </div>
        </el-card>

        <el-card class="metric-card yellow">
          <div class="metric-content">
            <div class="metric-info">
              <p class="metric-label">平均响应</p>
              <p class="metric-value">{{ statisticsData.avgResponseTime }}</p>
            </div>
            <i class="el-icon-time metric-icon"></i>
          </div>
        </el-card>

        <el-card class="metric-card red">
          <div class="metric-content">
            <div class="metric-info">
              <p class="metric-label">错误数</p>
              <p class="metric-value">{{ statisticsData.errorCount }}</p>
            </div>
            <i class="el-icon-warning metric-icon"></i>
          </div>
        </el-card>
      </div>

      <!-- 统计图表区域 -->
      <div class="charts-grid">
        <!-- 状态码分布 -->
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>状态码分布</span>
          </div>
          <div class="status-distribution">
            <div
              v-for="item in statisticsData.statusDistribution"
              :key="item.status"
              class="status-item"
            >
              <div class="status-info">
                <div :class="['status-dot', getStatusClass(item.status)]"></div>
                <span class="status-code">{{ item.status }}</span>
              </div>
              <div class="status-stats">
                <span class="status-count">{{ item.count }}</span>
                <span class="status-percentage">({{ item.percentage }}%)</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 响应时间分布 -->
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>响应时间分布</span>
          </div>
          <div class="response-time-distribution">
            <div
              v-for="item in statisticsData.responseTimeDistribution"
              :key="item.range"
              class="time-item"
            >
              <span class="time-range">{{ item.range }}</span>
              <div class="time-stats">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: item.percentage + '%' }"
                  ></div>
                </div>
                <span class="time-count">{{ item.count }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 热门API端点 -->
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>热门API端点</span>
          </div>
          <div class="top-endpoints">
            <div
              v-for="(item, index) in statisticsData.topEndpoints"
              :key="item.url"
              class="endpoint-item"
            >
              <div class="endpoint-info">
                <el-tag size="mini" type="primary">{{ index + 1 }}</el-tag>
                <span class="endpoint-url">{{ item.url }}</span>
              </div>
              <div class="endpoint-stats">
                <span class="endpoint-count">{{ item.count }}次</span>
                <span class="endpoint-time">{{ item.avgTime }}</span>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 时间趋势 -->
        <el-card class="chart-card">
          <div slot="header" class="chart-header">
            <span>小时请求趋势</span>
          </div>
          <div class="hourly-trend">
            <div
              v-for="item in statisticsData.hourlyTrend"
              :key="item.hour"
              class="trend-item"
            >
              <span class="trend-hour">{{ item.hour }}</span>
              <div class="trend-stats">
                <div class="trend-bar">
                  <div
                    class="trend-fill"
                    :style="{ width: (item.requests / 156) * 100 + '%' }"
                  ></div>
                </div>
                <span class="trend-count">{{ item.requests }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 最近错误 -->
      <el-card class="error-card">
        <div slot="header" class="error-header">
          <i class="el-icon-warning"></i>
          <span>最近错误请求</span>
        </div>
        <div class="error-list">
          <div
            v-for="error in statisticsData.recentErrors"
            :key="error.id"
            class="error-item"
          >
            <div class="error-info">
              <el-tag :type="getStatusType(error.status)" size="mini">
                {{ error.status }}
              </el-tag>
              <span class="error-url">{{ error.url }}</span>
              <span class="error-time">{{ error.timestamp }}</span>
            </div>
            <span class="error-duration">{{ error.duration }}</span>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'StatisticsPanel',
    props: {
      statisticsData: {
        type: Object,
        required: true,
      },
      albumId: {
        type: String,
        required: true,
      },
    },
    methods: {
      getStatusClass(status) {
        if (status === '200') return 'green'
        if (status === '404') return 'yellow'
        if (status === '500') return 'red'
        return 'gray'
      },
      getStatusType(status) {
        if (status >= 200 && status < 300) return 'success'
        if (status >= 400 && status < 500) return 'warning'
        if (status >= 500) return 'danger'
        return 'info'
      },
    },
  }
</script>

<style scoped>
  .statistics-panel {
    flex: 1;
    background: white;
    padding: 24px;
    overflow-y: auto;
  }

  .panel-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .panel-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 24px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 24px;
  }

  .metric-card {
    border: none;
  }

  .metric-card.blue {
    background: #f0f9ff;
  }
  .metric-card.green {
    background: #f0fff4;
  }
  .metric-card.yellow {
    background: #fffbf0;
  }
  .metric-card.red {
    background: #fff1f0;
  }

  .metric-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .metric-label {
    font-size: 14px;
    color: #666;
    margin: 0 0 4px 0;
  }

  .metric-value {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
  }

  .metric-icon {
    font-size: 32px;
    opacity: 0.6;
  }

  .metric-trend {
    font-size: 12px;
    margin: 8px 0 0 0;
    color: #52c41a;
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 24px;
  }

  .chart-card {
    border: 1px solid #f0f0f0;
  }

  .chart-header {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .status-distribution,
  .response-time-distribution,
  .top-endpoints,
  .hourly-trend {
    padding: 8px 0;
  }

  .status-item,
  .time-item,
  .endpoint-item,
  .trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
  }

  .status-info,
  .endpoint-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
  }

  .status-dot.green {
    background: #52c41a;
  }
  .status-dot.yellow {
    background: #faad14;
  }
  .status-dot.red {
    background: #ff4d4f;
  }

  .progress-bar,
  .trend-bar {
    width: 80px;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: #1890ff;
    transition: width 0.3s;
  }

  .trend-fill {
    height: 100%;
    background: #52c41a;
    transition: width 0.3s;
  }

  .error-card {
    background: #fff1f0;
    border: 1px solid #ffccc7;
  }

  .error-header {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #cf1322;
  }

  .error-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  .error-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .error-url {
    font-family: monospace;
    font-size: 14px;
  }

  .error-time {
    font-size: 12px;
    color: #999;
  }
</style>
