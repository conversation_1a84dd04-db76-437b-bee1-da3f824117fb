<!-- TopSearchBar.vue -->
<template>
  <div class="top-search-bar">
    <div class="header-content">
      <div class="header-top">
        <h1 class="title">全链路日志跟踪</h1>
        <div class="statistics">
          <div class="stat-item">
            <div class="stat-dot green"></div>
            <span>{{ statistics.totalRequests }} 请求</span>
          </div>
          <div class="stat-item">
            <div class="stat-dot blue"></div>
            <span>{{ statistics.successRate }}% 成功</span>
          </div>
          <div class="stat-item">
            <div class="stat-dot yellow"></div>
            <span>{{ statistics.avgResponseTime }} 平均</span>
          </div>
        </div>
      </div>

      <div class="search-controls">
        <div class="search-form">
          <div class="form-row">
            <div class="form-item album-id-item">
              <label>相册ID</label>
              <el-input
                v-model="formData.albumId"
                placeholder="x_albumid"
                prefix-icon="el-icon-search"
              />
            </div>
            <div class="form-item time-item">
              <label>开始时间</label>
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="开始时间"
                format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
              />
            </div>
            <div class="form-item time-item">
              <label>结束时间</label>
              <el-date-picker
                v-model="formData.endTime"
                type="datetime"
                placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                :picker-options="pickerOptions"
              />
            </div>
          </div>

          <div class="form-actions">
            <el-button-group class="action-group">
              <el-button
                type="primary"
                icon="el-icon-search"
                :loading="loading"
                @click="handleSearch"
              >
                查询
              </el-button>
              <el-button type="default" @click="handleQuickSearch('5m')">
                近5分钟
              </el-button>
              <el-button type="default" @click="handleQuickSearch('1h')">
                近1小时
              </el-button>
              <el-button type="default" @click="handleQuickSearch('1d')">
                近1天
              </el-button>
            </el-button-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'TopSearchBar',
    props: {
      statistics: {
        type: Object,
        required: true,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        formData: {
          albumId: 'A201907101144303730207631',
          userId: '',
          startTime: '',
          endTime: '',
        },
        pickerOptions: {
          disabledDate: (time) => {
            // 限制只能选择7天内的时间
            const now = Date.now()
            const sevenDaysAgo = now - 7 * 24 * 60 * 60 * 1000
            return time.getTime() < sevenDaysAgo || time.getTime() > now
          },
        },
      }
    },
    mounted() {
      // 初始化为近5分钟
      this.handleQuickSearch('5m')
    },
    methods: {
      handleSearch() {
        if (!this.formData.albumId) {
          this.$message.warning('请输入Album ID')
          return
        }
        if (!this.formData.startTime || !this.formData.endTime) {
          this.$message.warning('请选择时间范围')
          return
        }
        this.$emit('search', { ...this.formData })
      },
      handleQuickSearch(range) {
        const now = new Date()
        const endTime = new Date(now)
        let startTime

        switch (range) {
          case '5m':
            startTime = new Date(now.getTime() - 5 * 60 * 1000)
            break
          case '1h':
            startTime = new Date(now.getTime() - 60 * 60 * 1000)
            break
          case '1d':
            startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000)
            break
        }

        this.formData.startTime = startTime
        this.formData.endTime = endTime

        // 自动触发搜索
        this.handleSearch()
      },
    },
  }
</script>

<style scoped>
  .top-search-bar {
    background: white;
    border-bottom: 1px solid #e8e8e8;
    padding: 16px;
  }

  .header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .title {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    color: #333;
  }

  .statistics {
    display: flex;
    gap: 16px;
    font-size: 14px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    color: #666;
  }

  .stat-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .stat-dot.green {
    background-color: #52c41a;
  }
  .stat-dot.blue {
    background-color: #1890ff;
  }
  .stat-dot.yellow {
    background-color: #faad14;
  }

  .search-controls {
    width: 100%;
  }

  .search-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
  }

  .form-row {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    flex-wrap: wrap;
  }

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  /* 针对不同输入框设置不同宽度 */
  .album-id-item {
    min-width: 280px; /* 相册ID输入框更宽 */
    flex: 1;
    max-width: 400px;
  }

  .time-item {
    min-width: 170px; /* 时间选择器稍微窄一些 */
    flex: 0 0 auto;
  }

  .form-item label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
  }

  .form-item :deep(.el-input) {
    width: 100%;
  }

  .form-item :deep(.el-date-editor) {
    width: 100%;
  }

  /* 时间选择器的特殊样式调整 */
  .time-item :deep(.el-date-editor.el-input) {
    width: 100%;
  }

  .time-item :deep(.el-date-editor .el-range-input) {
    width: auto;
  }

  .form-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .action-group {
    display: flex;
    gap: 0;
  }

  .action-group .el-button {
    margin-left: 0;
  }

  /* 响应式设计 */
  @media (max-width: 1400px) {
    .form-row {
      flex-wrap: wrap;
    }

    .album-id-item {
      min-width: 250px;
      max-width: 350px;
    }

    .time-item {
      min-width: 160px;
    }
  }

  @media (max-width: 1200px) {
    .form-row {
      flex-direction: column;
      align-items: stretch;
    }

    .form-item {
      min-width: 100%;
    }

    .album-id-item,
    .time-item {
      min-width: 100%;
      max-width: none;
    }
  }

  @media (max-width: 768px) {
    .header-top {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }

    .statistics {
      align-self: stretch;
      justify-content: space-between;
    }

    .action-group {
      flex-wrap: wrap;
    }

    .action-group .el-button {
      margin-bottom: 4px;
    }
  }

  /* 确保输入框内容完全可见 */
  .album-id-item :deep(.el-input__inner) {
    padding-left: 30px; /* 为搜索图标留出空间 */
    padding-right: 8px;
  }

  .time-item :deep(.el-input__inner) {
    padding-left: 8px;
    padding-right: 30px; /* 为日期图标留出空间 */
  }
</style>
