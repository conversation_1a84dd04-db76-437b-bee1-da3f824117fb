<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthPanel API 参数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e6e6e6;
            border-radius: 6px;
            background: #f9f9f9;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .param-table th, .param-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .param-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>HealthPanel API 参数修改验证</h1>
        
        <div class="success test-case">
            <h3>✅ 修改完成</h3>
            <p>HealthPanel.vue 组件的 API 请求参数已成功修改为7天数据范围。</p>
        </div>

        <h2>修改前后对比</h2>
        
        <div class="test-case">
            <h4>修改前 - 单日数据请求</h4>
            <div class="code-block">
const params = {
  date: this.selectedDate,
  start_time: dayjs(this.selectedDate).startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  end_time: dayjs(this.selectedDate).endOf('day').format('YYYY-MM-DD HH:mm:ss'),
}

// 示例：选定日期 2024-01-07
// start_time: "2024-01-07 00:00:00"
// end_time: "2024-01-07 23:59:59"
// 数据范围：仅1天
            </div>
        </div>

        <div class="test-case success">
            <h4>修改后 - 7天数据范围请求</h4>
            <div class="code-block">
// 计算7天数据范围：从选定日期往前推6天到选定日期
const endDate = dayjs(this.selectedDate)
const startDate = endDate.subtract(6, 'day')

const params = {
  date: this.selectedDate,
  start_time: startDate.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  end_time: endDate.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
}

// 示例：选定日期 2024-01-07
// start_time: "2024-01-01 00:00:00"
// end_time: "2024-01-07 23:59:59"
// 数据范围：7天 (2024-01-01 到 2024-01-07)
            </div>
        </div>

        <h2>参数测试用例</h2>

        <div class="test-case">
            <h4>测试用例 1：选定日期 2024-01-07</h4>
            <table class="param-table">
                <tr>
                    <th>参数</th>
                    <th>值</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>date</td>
                    <td>2024-01-07</td>
                    <td>选定的基准日期</td>
                </tr>
                <tr>
                    <td>start_time</td>
                    <td>2024-01-01 00:00:00</td>
                    <td>7天前的开始时间</td>
                </tr>
                <tr>
                    <td>end_time</td>
                    <td>2024-01-07 23:59:59</td>
                    <td>选定日期的结束时间</td>
                </tr>
            </table>
            
            <h5>对应的日期列显示：</h5>
            <div class="code-block">
day_0: 01/07 (2024-01-07) - 选定日期
day_1: 01/06 (2024-01-06)
day_2: 01/05 (2024-01-05)
day_3: 01/04 (2024-01-04)
day_4: 01/03 (2024-01-03)
day_5: 01/02 (2024-01-02)
day_6: 01/01 (2024-01-01) - 开始日期
            </div>
        </div>

        <div class="test-case">
            <h4>测试用例 2：选定今天 (假设今天是 2024-01-15)</h4>
            <table class="param-table">
                <tr>
                    <th>参数</th>
                    <th>值</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>date</td>
                    <td>2024-01-15</td>
                    <td>今天的日期</td>
                </tr>
                <tr>
                    <td>start_time</td>
                    <td>2024-01-09 00:00:00</td>
                    <td>7天前的开始时间</td>
                </tr>
                <tr>
                    <td>end_time</td>
                    <td>2024-01-15 23:59:59</td>
                    <td>今天的结束时间</td>
                </tr>
            </table>
        </div>

        <h2>功能验证清单</h2>

        <div class="test-case">
            <h4>✅ 已实现的功能</h4>
            <ul>
                <li>✅ API 请求参数修改为7天范围</li>
                <li>✅ 日期列初始化逻辑与API参数保持一致</li>
                <li>✅ 日期变化时自动更新日期列和API参数</li>
                <li>✅ 日期导航功能正常工作</li>
                <li>✅ 保持原有的日期格式和参数结构</li>
                <li>✅ 添加调试日志便于验证</li>
            </ul>
        </div>

        <h2>调试信息</h2>

        <div class="test-case">
            <h4>控制台输出示例</h4>
            <div class="code-block">
请求参数 (7天范围): {
  date: "2024-01-07",
  start_time: "2024-01-01 00:00:00",
  end_time: "2024-01-07 23:59:59"
}

数据范围: 2024-01-01 到 2024-01-07

日期列初始化完成: [
  "2024-01-07", "2024-01-06", "2024-01-05", 
  "2024-01-04", "2024-01-03", "2024-01-02", "2024-01-01"
]
            </div>
        </div>

        <h2>测试建议</h2>

        <div class="test-case">
            <h4>手动测试步骤</h4>
            <ol>
                <li>打开浏览器开发者工具的控制台</li>
                <li>加载 HealthPanel 组件</li>
                <li>观察控制台输出的请求参数和日期范围</li>
                <li>使用日期选择器选择不同日期</li>
                <li>使用左右箭头进行日期导航</li>
                <li>验证每次操作后的API参数是否正确</li>
            </ol>
        </div>

        <div class="success test-case">
            <h3>🎯 修改总结</h3>
            <p>
                HealthPanel.vue 组件现在能够正确请求7天范围的数据，
                确保API请求的数据范围与界面显示的日期列完全匹配，
                提供了更好的数据一致性和用户体验。
            </p>
        </div>
    </div>
</body>
</html>
