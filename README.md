# 运维平台

## 地址

- <https://ops.szwego.com>

## 技术栈

- echarts:5.1.2
- vue:2.6.14
- element-ui:2.15.14

## 版本日志

<details>
<summary>迭代日志</summary>

### 2025-03-12
- 优化：监控配置页面整体页面重构
- 新增：监控各配置，添加指定飞书群组告警功能
- 其他 bug 优化

### 2025-03-05

- 拆分：预发布主页面，预发布环境管理页面，用户规则管理页面, 灰度规则管理页面
- 优化：
  - 预发布环境管理页面布局
  - 逻辑优化，模块优化
  - 数据获取优化，样式渲染优化，分页分状态获取数据，提高性能，减少开销

### 2025-03-03

- 新增：
  - 规则，URI 删除接口 @董鑫
  - 预发布 URI 灰度规则管理 @董鑫
  - 工具箱查询办公室 IP 地址 @董鑫
  - 研发项目增加空间限制 @董鑫

- 优化：
  - 预发布环境管理页面布局
  - 修改 index.js 缺少 meta 信息引发报错的 bug @董鑫：

### 2025-01-13

- 优化：
  - 构建后搜索条件问题
  - 规则备注默认取值相册名
  - 规则添加提示信息

### 2024-11-28

- 优化：预发布环境批量发布功能

### 2024-10-31

- 优化：xlog 日志详情页 操作栏添加 kibanaUrl 连接
- 新增：xlog 日志详情页 添加筛选条件

### 2024-10-29

- 新增：xlog 日志详情页

### 2024-10-22

- 新增：
  - 预发布环境管理
  - xlog 日志分析

### 2024-08-29

- V2.0版本
  - 页面变更：海外流量
  - 优化：
    - 事件上报：新增 radio-group 标签，修复 bug，查询功能新增 details 过滤
    - 错误码：告警忽略，告警查询，页面调整
    - 安全中心：优化白名单
  - 新增：预发布环境管理 2 级网关

### 2024-07-15

- 优化：
  - 工具箱-maven 缓存清理
  - 流控中心：
    - 删除流控规则页面
    - 优化流控配置页面，变更规则名称->目标 URI，取消流控 key显示

### 2024-07-03

- 新增：Waf IP 白名单，添加疑似 IP 地址

### 2024-07-02

- 新增：
  - SQL 慢日志统计信息
  - SqlTemplate 统计信息

### 2024-06-28

- 优化业务监控关闭告警视窗
- 优化告警视窗默认关闭，有告警时可进行跳转
- 优化关闭调试log

### 2024-06-27

- 新增：业务数据监控
  - 主要内容:
    - 默认 3 天对比率超过30%的数据情况
    - 默认没有选择对比时，显示当前时间的数据视图
    - 当选择对比时，显示比对视图
    - 监听 biz_id，发生变化就更新图形

### 2024-06-25

- 优化：
  - 错误码页面：Echarts 图形标题信息更改：显示错误码和错误消息
  - SQL 慢日志分析：调整默认时间区间为 day
  - SQL 慢日志分析：查询条件中删除 5s 过滤，直接返回所有，然后根据总耗时进行 desc 排序
  - 调整 SQL 慢日志分析的样式信息 使其紧凑

### 2024-06-24

- 新增监控-> SQL 分析

### 2024-06-19

- 修复错误码中 echarts 图形数据异常修复

### 2024-06-12

- 新增kafka 事件监控

### 2024-06-05

安全中心

- 新增：反爬中心-IP 白名单中新增 IP 查询功能

### 2024-06-04

安全中心

- 移动：将流控规则，Redis 记录信息，BotIP 信息 变更到反爬中心-IP 白名单中

### 2024-05-31

安全中心

- 新增：
  - 概览-实时态势
  - 流控规则，Redis 记录信息，BotIP 信息
- 修改：
  - [黑白名单] -> 改成 [IP白名单]
  - [URI列表] -> 改成 [核心API]

### 2024-05-28

- 新增：
  - 基础配置
  - 反爬中心-黑白名单，URI 列表

### 2024-05-27

- 优化：细节名称优化
- 变动：路由 uri 接口变动

### 2024-05-24

- 新增：
  - 安全中心组：反爬与流控
  - 概览页面组
- 移动：熔断降级页面移动到安全中心组
- 修改：将`[灰度与发版]`更名为`[版本控制]`

### 2024-05-21

- 优化：成本周报空字符串异常处理

### 2024-05-11

- 优化：工具箱权限页面分类
- 更新：文档中心嵌入地址更改

### 2024-05-10

- 优化：周费用数据中心页面结构

### 2024-05-07

- 新增：工具箱页面->投诉信息推送数据下载 小工具

### 2024-04-29

- 图表更新错误码监控图表添加：日期选择，趋势图，code&url 分组变化率
- 图表更新提供趋势图片下载

### 2024-04-26

- 优化
  - 服务名称获取不到异常修复
  - 错误码监控排版问题

### 2024-04-23

- 新增：
  - bug 服务名称获取不到异常修复
  - 页面加载配置文件对比 apollo 添加超时遮罩关闭

### 2024-04-15

- 新增：错误码监控页面添加忽略告警规则功能

### 2024-04-09

- 优化错误码告警列表
- 调整：错误码配置移动到监控配置管理

### 2024-04-08

- 优化
  - 错误码告警列表
  - 用户管理页面，去处非必要字段
  - api 接口请求参数
- 新增：
  - 错误码忽略规则
  - 错误码告警规则

### 2024-04-02

- 优化
  - 用户管理页面
  - 错误码告警列表

### 2024-03-26 版本更新

- 错误码 code 新增图表趋势

### 2024-03-21 版本更新

- 新增：
  - PP配置添加指定消息通知人
- 优化
  - 页面布局

### 2024-03-15 版本更新

- 新增：
  - 错误码添加数据显示信息
- 优化：
  - 页面样式优化

### 2024-03-08 版本更新

- 新增：
  - 告警列表可视化窗口
  - 告警列表告警状态操作
- 优化
  - 修改路由结构
  - 错误码信息添加参数化查询和 url 修改
  - 浏览器参数读取异常
  - 搜索图标更换
  - ErrAlertFire表格逻辑修改

### 2024-03-06 版本更新

- 修改路由结构

### 2024-03-05 版本更新

- 变更目录结构
  - report 周报报表，从 item/report 移动到 monitoring/report 目录
  - 监控相关 components，移动到 monitoring/components=
- 修改路由文件(route/index.js)变更目录结构，创建`[监控配置管理]`页面
- 新增 Pinpoint 页面
- 将 `[MQ 监控配置]`与`[Pinpoint监控配置页]`整合到`[监控配置管理]`页面
- 优化`[MQ 监控配置]`
- 修改路由结构

</details>

## 图示

<details>
<summary>图示</summary>

- 流程图

```mermaid
graph LR
  A("/") -->|重定向| B("首页 /index")
  A -->|子路由| C("工具箱 /toolbox")
  A -->|子路由| D("ITSM /itsm")
  D -->|子路由| D1("事件管理 /itsm/events")
  D -->|子路由| D2("SLA统计 /itsm/eventlist")
  A -->|子路由| E("DevOps /devops")
  E -->|子路由| E1("版本工作流 /devops/workFlows/:flow_uuid")
  E -->|子路由| E2("变更工作流 /devops/upgradeFlow")
  E -->|子路由| E3("灰度和发版 /devops/grayevent")
  A -->|子路由| F("运维监控 /devmonitor")
  F -->|子路由| F1("熔断和降级 /devmonitor/panicManage")
  F -->|子路由| F2("错误码监控 /devmonitor/errCode")
  F -->|子路由| F3("海外用户数据 /devmonitor/countryside")
  F -->|子路由| F4("监控配置管理 /devmonitor/manageconfig")
  F4 -->|子页面| F4-1("RocketMQ监控配置")
  F4 -->|子页面| F4-2("PP监控配置")
  F -->|子路由| F5("成本周报 /devmonitor/panicManage")
  A -->|子路由| G("配置 /personnelManagement")
  G -->|子路由| G1("用户管理 /personnelManagement/userManagement")
  G -->|子路由| G2("角色管理 /personnelManagement/roleManagement")
  A -->|子路由| H("文档中心 /opsdocs")
  A -->|404重定向| I("404页面 /404")
```

- 序列图

```mermaid
sequenceDiagram
  participant 用户 as User
  participant 根路由 as Root
  participant 首页 as Index
  participant 工具箱 as Toolbox
  participant 事件管理 as ITSM
  participant DevOps as DevOps
  participant 运维监控 as DevMonitor
  participant 配置 as Config
  用户 ->>+ 根路由: 访问应用
  根路由 ->>+ 首页: 重定向到"/index"
  用户 ->>+ 工具箱: 点击工具箱
  工具箱 ->>+ 事件管理: 导航到 ITSM
  事件管理 ->> DevOps: 切换到 DevOps
  用户 ->>+ 运维监控: 查看运维监控
  运维监控 ->>+ 配置: 跳转到配置管理
```

</details>
  
