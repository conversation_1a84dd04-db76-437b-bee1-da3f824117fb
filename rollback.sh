#!/bin/bash
# Author: wa<PERSON><PERSON><PERSON>
# Date: 2025-03-03
# Description: 回滚ops-admin

# 定义变量
# 远程主机地址
remote_host="**********"

# 远程目录路径
remote_dir="/data/"

# 本地保存路径
local_dir="/data/wanglh/ops-admin/back"

# 获取最新的备份文件
latest_backup=$(ls -t $local_dir/*.zip | head -n 1)

if [ -z "$latest_backup" ]; then
    echo "未找到备份文件，无法进行回滚操作。"
    exit 1
fi

# 回滚步骤

# 1. 在远程主机上创建临时目录
echo "正在在远程主机上创建临时目录..."
ssh "${remote_host}" "mkdir -p ${remote_dir}/ops-admin-temp"

# 2. 将最新的备份文件复制到远程主机的临时目录
echo "正在将最新的备份文件复制到远程主机的临时目录..."
scp "$latest_backup" "${remote_host}:${remote_dir}/ops-admin-temp/"

# 3. 在远程主机的临时目录中解压备份文件
echo "正在在远程主机的临时目录中解压备份文件..."
ssh "${remote_host}" "cd ${remote_dir}/ops-admin-temp && unzip -q ${latest_backup##*/}"

# 4. 使用 rsync 增量同步临时目录中的内容到 /data/ops-admin 目录
echo "正在使用 rsync 增量同步临时目录中的内容到 /data/ops-admin 目录..."
ssh "${remote_host}" "rsync -avz --delete ${remote_dir}/ops-admin-temp/ops-admin/ ${remote_dir}/ops-admin/"

# 5. 删除远程主机上的临时目录和备份文件
echo "正在删除远程主机上的临时目录和备份文件..."
ssh "${remote_host}" "rm -rf ${remote_dir}/ops-admin-temp"

echo "回滚操作完成，已恢复到最新备份时的状态。"
