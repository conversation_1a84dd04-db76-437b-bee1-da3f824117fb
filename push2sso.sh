#!/bin/bash
# Author: wa<PERSON><PERSON><PERSON>
# Date: 2025-03-03
# Description: 推送ops-admin到sso

# 定义变量
# 远程主机地址
remote_host="**********"

# 远程目录路径
remote_dir="/data/"

# 本地保存路径
local_dir="/data/wanglh/ops-admin/back"

# 获取当前日期（格式：年-月-日）
current_date=$(date "+%Y-%m-%d_%H%M%S")

# 压缩文件名
zip_filename="ops-admin-${current_date}.zip"


# 指定命令
# 构建vue项目
echo "正在构建vue项目..."
npm run build

# 连接远程主机并执行压缩命令
echo "执行远程压缩..."
ssh "${remote_host}" "cd ${remote_dir} && zip -r ${zip_filename} ./ops-admin/*"

# 将压缩文件从远程主机复制到本地
echo "执行scp复制..."
scp "${remote_host}:${remote_dir}/${zip_filename}" "${local_dir}/"

# 提示操作完成
echo "压缩完成并保存为 ${local_dir}/${zip_filename}"

# 运行传输任务
echo "执行传输任务"
scp -r dist/* "${remote_host}:/data/ops-admin/"

#scp -r dist/* **********:/data/ops-admin/
