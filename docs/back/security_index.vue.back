<template>
  <div class="alert-center-container">
    <div :class="{ 'is-collapsed': isCollapse }" class="side-nav">
      <div class="nav-header">
        <h3 class="nav-title">告警中心</h3>
        <div class="collapse-btn" @click="toggleCollapse">
          <svg
            class="icon"
            viewBox="0 0 1024 1024"
            xmlns="http://www.w3.org/2000/svg"
            :style="{ transform: isCollapse ? 'rotate(180deg)' : 'none' }"
          >
            <path
              d="M153.6 166.4m38.4 0l640 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-640 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M153.6 780.8m38.4 0l640 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-640 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M409.6 371.2m38.4 0l384 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-384 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M409.6 576m38.4 0l384 0q38.4 0 38.4 38.4l0 0q0 38.4-38.4 38.4l-384 0q-38.4 0-38.4-38.4l0 0q0-38.4 38.4-38.4Z"
              fill="#7A88FE"
            ></path>
            <path
              d="M180.8896 545.28a25.6 25.6 0 0 1 0-40.96l136.5504-102.4a25.6 25.6 0 0 1 40.96 20.48v204.8a25.6 25.6 0 0 1-40.96 20.48z"
              fill="#7A88FE"
            ></path>
          </svg>
        </div>
      </div>

      <el-menu
        :collapse="isCollapse"
        background-color="#fff"
        text-color="#303133"
        active-text-color="#7A88FE"
        class="el-menu-vertical"
        :default-active="activeTab"
        @select="handleSelect"
      >
        <el-menu-item index="health">
          <i class="el-icon-monitor"></i>
          <span slot="title">健康看板</span>
        </el-menu-item>

        <el-menu-item index="active">
          <i class="el-icon-bell"></i>
          <span slot="title">实时告警</span>
        </el-menu-item>

        <el-menu-item index="history">
          <i class="el-icon-time"></i>
          <span slot="title">历史告警</span>
        </el-menu-item>

        <el-menu-item index="namespace">
          <i class="el-icon-s-grid"></i>
          <span slot="title">命名空间</span>
        </el-menu-item>

        <el-submenu index="config">
          <template slot="title">
            <i class="el-icon-setting"></i>
            <span>告警配置</span>
          </template>
          <el-menu-item index="mq">
            <span class="custom-menu-icon" v-html="rocketMqIcon"></span>
            <span>MQ告警</span>
          </el-menu-item>
          <el-menu-item index="pinpoint">
            <span class="custom-menu-icon" v-html="pinpointIcon"></span>
            <span>PP告警</span>
          </el-menu-item>
          <el-menu-item index="errcode">
            <span class="custom-menu-icon" v-html="errCodeIcon"></span>
            <span>错误码告警</span>
          </el-menu-item>
          <el-menu-item index="http">
            <span class="custom-menu-icon" v-html="httpMonitorIcon"></span>
            <span>HTTP告警</span>
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </div>

    <div class="content-container">
      <active-alerts
        v-if="activeTab === 'active'"
        ref="activeAlerts"
        @show-detail="handleDetail"
      />
      <history-alerts
        v-if="activeTab === 'history'"
        @show-detail="handleDetail"
      />

      <health-board v-if="activeTab === 'health'" ref="healthBoard" />
      <namespace-stats
        v-if="activeTab === 'namespace'"
        ref="namespaceStats"
        @namespace-click="handleNamespaceClick"
      />

      <component
        :is="currentConfigComponent"
        v-if="['mq', 'pinpoint', 'errcode', 'http'].includes(activeTab)"
      />

      <el-drawer
        :visible.sync="drawerVisible"
        direction="rtl"
        size="70%"
        :title="drawerTitle"
        :before-close="handleDrawerClose"
        :destroy-on-close="true"
      >
        <alert-detail
          v-if="drawerVisible && selectedAlert"
          :alert="selectedAlert"
        />
      </el-drawer>
    </div>
  </div>
</template>

<script>
  import ActiveAlerts from './components/ActiveAlerts.vue'
  import HistoryAlerts from './components/HistoryAlerts.vue'
  import AlertDetail from './components/AlertDetail.vue'
  import HealthBoard from './components/HealthBoard.vue'
  import NamespaceStats from './components/NamespaceStats.vue'
  import MqIndex from '@/views/monitoring/rocketMQ/index.vue'
  import PpIndex from '@/views/monitoring/pinpoint/index.vue'
  import ErrIndex from '@/views/monitoring/kafka/ErrConfig.vue'
  import HttpIndex from '@/views/monitoring/elk/elkMonitor.vue'

  export default {
    name: 'AlertCenter',
    components: {
      ActiveAlerts,
      HistoryAlerts,
      AlertDetail,
      HealthBoard,
      NamespaceStats,
      MqIndex,
      PpIndex,
      ErrIndex,
      HttpIndex,
    },
    data() {
      return {
        activeTab: 'health',
        isCollapse: true,
        drawerVisible: false,
        drawerTitle: '告警详情',
        selectedAlert: null,
        currentConfigComponent: null,
        rocketMqIcon:
          '<svg  class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="M514.784 1000.736c-76.992-71.488-73.824-152.448-73.824-152.448h147.712s3.136 80.96-73.856 152.448z m0-896.48c-64.96 85.76-191.104 280.736-191.104 500.576 0 63.616 11.936 116.896 23.968 154.464h334.304a507.947 507.947 0 0 0 23.968-154.464c0-219.84-126.208-414.816-191.136-500.608v0.032zM757.12 604.832c0 144.8-55.52 240.416-55.52 240.416l-59.36-34.72H387.36l-59.392 34.72S272.48 749.696 272.48 604.8c0-319.04 242.24-581.44 242.304-581.568 0.096 0.096 242.336 262.528 242.336 581.568z m33.536 95.072c0 55.52-24.64 115.872-41.632 141.344h117.664l-13.056-80-62.976-61.344z m-614.72 61.344l-13.024 80.032h117.664c-17.024-25.44-41.664-85.792-41.664-141.344l-62.976 61.344zM468.64 530.24a11.392 11.392 0 1 0-2.496 22.688 11.392 11.392 0 0 0 2.496-22.688z m36.48 0a11.392 11.392 0 1 0-2.528 22.688 11.392 11.392 0 0 0 2.528-22.688z m36.448 0a11.392 11.392 0 1 0-2.528 22.688 11.392 11.392 0 0 0 2.528-22.688z m124.48 143.84a58.88 58.88 0 1 1-109.92-29.248l-14.688-20.096a90.4 90.4 0 0 1-95.104-14.048l-16.384 14.272a35.2 35.2 0 1 1-34.4-27.84c4.224 0 8.192 0.832 11.968 2.208l17.472-15.168a90.336 90.336 0 0 1 7.904-97.312l-18.368-19.968c-1.312 0.16-2.656 0.384-4 0.384a29.92 29.92 0 1 1 29.184-23.36l18.56 20.16a90.24 90.24 0 0 1 91.52-1.44l15.648-15.68a46.592 46.592 0 1 1 28.896 28.896l-14.4 14.4a90.4 90.4 0 0 1-5.6 110.112l13.408 18.304c6.144-2.176 12.704-3.424 19.584-3.424a58.912 58.912 0 0 1 58.784 58.944v-0.096h-0.032z m-160.96-61.024a71.456 71.456 0 1 0 2.976-142.88 71.456 71.456 0 0 0-2.944 142.88z" fill="#bfbfbf" p-id="1320"></path></svg>',
        pinpointIcon:
          '<svg class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="14" height="14"><path d="M512 0c282.7648 0 512 229.2352 512 512S794.7648 1024 512 1024 0 794.7648 0 512 229.2352 0 512 0z m0 76.8C271.6416 76.8 76.8 271.6416 76.8 512s194.8416 435.2 435.2 435.2 435.2-194.8416 435.2-435.2S752.3584 76.8 512 76.8z m0 358.4a76.8 76.8 0 1 1 0 153.6 76.8 76.8 0 0 1 0-153.6z" fill="#bfbfbf" p-id="2754"></path></svg>',

        errCodeIcon: `<svg viewBox="0 0 1024 1024" width="14" height="14">
        <path d="M512 960c-249.6 0-448-198.4-448-448s198.4-448 448-448 448 198.4 448 448-198.4 448-448 448z m57.6-672c0-32-25.6-57.6-57.6-57.6s-57.6 25.6-57.6 57.6v339.2c0 32 25.6 57.6 57.6 57.6s57.6-25.6 57.6-57.6V288z m-57.6 448c-32 0-57.6 25.6-57.6 57.6s25.6 57.6 57.6 57.6 57.6-25.6 57.6-57.6-25.6-57.6-57.6-57.6z" fill="currentColor"/>
      </svg>`,
        httpMonitorIcon: `<svg viewBox="0 0 1024 1024" width="14" height="14">
        <path d="M896 128H128c-35.2 0-64 28.8-64 64v640c0 35.2 28.8 64 64 64h768c35.2 0 64-28.8 64-64V192c0-35.2-28.8-64-64-64zM128 832V192h768l0.1 640H128z" fill="currentColor"/>
        <path d="M256 448h512v64H256z" fill="currentColor"/>
      </svg>`,
      }
    },
    watch: {
      // 监听路由变化
      $route: {
        immediate: true,
        handler(newRoute) {
          const { tab, severity, source, status, start_time, end_time } =
            newRoute.query

          if (tab) {
            this.activeTab = tab
            this.handleSelect(tab)
          }

          if (
            tab === 'active' &&
            (severity || source || status || start_time || end_time)
          ) {
            this.$nextTick(() => {
              const activeAlertsRef = this.$refs.activeAlerts
              if (activeAlertsRef) {
                activeAlertsRef.setInitialFilters({
                  severity: severity || '',
                  source: source || '',
                  status: status || 'ALARM',
                  start_time: start_time || '',
                  end_time: end_time || '',
                })
              }
            })
          }
        },
      },
    },
    methods: {
      handleSelect(index) {
        this.activeTab = index
        switch (index) {
          case 'mq':
            this.currentConfigComponent = 'MqIndex'
            break
          case 'pinpoint':
            this.currentConfigComponent = 'PpIndex'
            break
          case 'errcode':
            this.currentConfigComponent = 'ErrIndex'
            break
          case 'http':
            this.currentConfigComponent = 'HttpIndex'
            break
          case 'namespace':
            this.currentConfigComponent = 'NamespaceStats'
            break
          default:
            this.currentConfigComponent = null
        }
      },
      toggleCollapse() {
        this.isCollapse = !this.isCollapse
      },
      handleDetail(row) {
        this.selectedAlert = row
        let titlePrefix = '告警详情'
        if (row.severity) {
          const severityMap = {
            critical: '严重',
            warning: '警告',
            resolved: '已恢复',
          }
          titlePrefix = `[${
            severityMap[row.severity] || row.severity
          }] 告警详情`
        }
        this.drawerTitle = `${titlePrefix}: ${row.summary || '无概要信息'}`
        this.drawerVisible = true
      },
      handleDrawerClose(done) {
        this.selectedAlert = null
        this.drawerVisible = false
        if (done) {
          done()
        }
      },
      handleNamespaceClick(namespace) {
        this.$refs.namespaceStats.showNamespaceDetail(namespace)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .custom-menu-icon {
    display: inline-flex;
    align-items: center;
    margin-right: 5px;

    svg {
      width: 14px;
      height: 14px;
      fill: currentColor;
      vertical-align: middle;
    }
  }

  // 确保图标在菜单折叠时居中显示
  .el-menu--collapse {
    .custom-menu-icon {
      margin: 0;
      justify-content: center;
    }
  }

  :deep(.el-menu-item) {
    .custom-menu-icon {
      color: #909399;
    }

    &:hover,
    &.is-active {
      .custom-menu-icon {
        color: #7a88fe;
      }
    }
  }

  .alert-center-container {
    display: flex;
    height: calc(100vh - 50px);
    background-color: #fff;

    .side-nav {
      background-color: #fff;
      transition: width 0.3s;
      width: 200px;
      border-right: 1px solid #e6e6e6;
      display: flex;
      flex-direction: column;

      &.is-collapsed {
        width: 64px;

        .nav-title {
          display: none;
        }
      }

      .nav-header {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        border-bottom: 1px solid #e6e6e6;

        .nav-title {
          margin: 0;
          font-size: 16px;
          color: #303133;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .collapse-btn {
          cursor: pointer;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            width: 20px;
            height: 20px;
            transition: transform 0.3s;
          }
        }
      }

      .el-menu-vertical {
        border-right: none;
        flex: 1;

        &:not(.el-menu--collapse) {
          width: 200px;
        }
      }
    }

    .content-container {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      background-color: #fff;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0;
    padding: 10px 20px;
  }

  :deep(.el-submenu__title i) {
    color: #909399;
  }

  :deep(.el-drawer__body) {
    padding: 0;
  }
</style>
