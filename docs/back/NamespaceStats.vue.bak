<template>
  <div class="namespace-stats-view">
    <div>
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <div class="filter-row">
          <el-form-item label="" class="time-range-item">
            <div class="time-range-container">
              <el-date-picker
                v-model="timeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                class="custom-date-picker"
                @change="handleTimeChange"
              />
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
          </el-form-item>
        </div>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      style="width: 100%; margin-top: 20px"
      height="calc(100vh - 350px)"
      @row-click="handleRowClick"
      @sort-change="handleSortChange"
    >
      <el-table-column prop="name" label="命名空间/产品" min-width="200">
        <template #default="scope">
          <div class="name-cell">
            <span>{{ scope.row.name }}</span>
            <el-tag v-if="scope.row.source_count > 1" size="mini">
              {{ scope.row.source_count }}个来源
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="告警级别" min-width="220">
        <template #default="scope">
          <div class="severity-bars">
            <el-progress
              v-if="scope.row.severity_items.critical"
              :percentage="
                (scope.row.severity_items.critical / scope.row.total) * 100
              "
              :show-text="false"
              status="exception"
              :stroke-width="12"
            />
            <el-progress
              v-if="scope.row.severity_items.warning"
              :percentage="
                (scope.row.severity_items.warning / scope.row.total) * 100
              "
              :show-text="false"
              status="warning"
              :stroke-width="12"
            />
            <div class="severity-counts">
              <el-tag
                v-if="scope.row.severity_items.critical"
                type="danger"
                size="small"
              >
                严重: {{ scope.row.severity_items.critical }}
              </el-tag>
              <el-tag
                v-if="scope.row.severity_items.warning"
                type="warning"
                size="small"
              >
                警告: {{ scope.row.severity_items.warning }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="来源分布" min-width="220">
        <template #default="scope">
          <div class="source-distribution-container">
            <div ref="chartContainer" class="chart-container">
              <div
                :id="`chart-${scope.row.nameId}`"
                class="echarts-container"
              ></div>
            </div>
            <div class="source-details">
              <!-- 当只有一个来源时 -->
              <template
                v-if="Object.keys(scope.row.sources_items).length === 1"
              >
                <div
                  v-for="(count, source) in scope.row.sources_items"
                  :key="source"
                  class="source-item"
                >
                  <el-tag size="small" :type="getSourceTagType(source)">
                    {{ formatSource(source) }} {{ count }}
                  </el-tag>
                </div>
              </template>

              <!-- 当有多个来源时 -->
              <template v-else>
                <div
                  v-for="(count, source) in scope.row.sources_items"
                  :key="source"
                  class="source-item"
                >
                  <el-tag size="small" :type="getSourceTagType(source)">
                    {{ formatSource(source) }} {{ count }}
                  </el-tag>
                </div>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="total" label="总数" min-width="100" sortable>
        <template #default="scope">
          <span class="total-count">{{ scope.row.total }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import { getNamespaceStats } from '@/api/monitoring'
  import dayjs from 'dayjs'
  import * as echarts from 'echarts/core'
  import { GaugeChart } from 'echarts/charts'
  import { CanvasRenderer } from 'echarts/renderers'
  import { TitleComponent, TooltipComponent } from 'echarts/components'

  echarts.use([GaugeChart, CanvasRenderer, TitleComponent, TooltipComponent])

  export default {
    name: 'NamespaceStats',
    data() {
      return {
        loading: false,
        timeRange: [
          dayjs().subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss'),
          dayjs().format('YYYY-MM-DD HH:mm:ss'),
        ],
        queryParams: {},
        tableData: [],
        chartInstances: [],
        totalStats: {
          critical: 0,
          warning: 0,
          total: 0,
        },
        sortTimer: null,
      }
    },
    mounted() {
      console.log('NamespaceStats mounted')
      this.handleQuery()
    },
    updated() {
      this.$nextTick(() => {
        this.initCharts()
      })
    },
    beforeDestroy() {
      // 清理所有图表实例
      if (this.chartInstances && this.chartInstances.length) {
        this.chartInstances.forEach((chart) => {
          chart.dispose()
        })
      }

      // 清理定时器
      if (this.sortTimer) {
        clearTimeout(this.sortTimer)
      }
    },
    methods: {
      handleSortChange() {
        // 排序后重新初始化图表
        // 减少延时时间，避免多次触发
        if (this.sortTimer) {
          clearTimeout(this.sortTimer)
        }
        this.sortTimer = setTimeout(() => {
          this.initCharts()
        }, 300)
      },
      getSourceTagType(source) {
        if (source.includes('PunkSong')) {
          return 'success'
        }
        if (source.includes('微购科技2')) {
          return 'info'
        }
        if (source.includes('custom')) {
          return 'success'
        }
        if (source.includes('rocketmq')) {
          return 'info'
        }
        if (source.includes('pinpoint')) {
          return 'success'
        }

        // 对于其他source，可以设置默认类型
        return 'info'
      },
      formatSource(source) {
        if (source.includes('|')) {
          return source.split('|')[0]
        }
        // 对于其他source，可以设置默认显示
        return source
      },
      handleTimeChange(val) {
        console.log('Time range changed:', val)
        this.timeRange = val
        this.handleQuery()
      },
      initCharts() {
        // 清除旧的图表实例
        this.chartInstances.forEach((chart) => {
          chart.dispose()
        })
        this.chartInstances = []

        // 为每行数据创建图表
        this.$nextTick(() => {
          if (!this.tableData || this.tableData.length === 0) {
            console.log('No data available for charts')
            return
          }

          this.tableData.forEach((row) => {
            const chartId = `chart-${row.nameId}`
            const chartDom = document.getElementById(chartId)
            if (!chartDom) {
              console.warn(`Chart DOM not found for: ${chartId}`)
              return
            }

            try {
              // 检查DOM元素上是否已经有ECharts实例
              const existingInstance = echarts.getInstanceByDom(chartDom)
              if (existingInstance) {
                console.log(`重用已存在的图表实例: ${chartId}`)
                existingInstance.dispose() // 先销毁已有实例
              }

              const chart = echarts.init(chartDom)
              this.chartInstances.push(chart)

              // 直接使用后端返回的 total 值
              const total = row.total || 1 // 防止除以零错误

              // 计算异常和正常数量
              let abnormalCount = 0

              if (row.severity_items.critical) {
                abnormalCount += row.severity_items.critical
              }
              if (row.severity_items.major) {
                abnormalCount += row.severity_items.major
              }
              if (row.severity_items.warning) {
                abnormalCount += row.severity_items.warning
              }

              const option = {
                series: [
                  {
                    type: 'gauge',
                    startAngle: 180,
                    endAngle: 0,
                    center: ['50%', '75%'],
                    radius: '90%',
                    min: 0,
                    max: total,
                    splitNumber: 1,
                    axisLine: {
                      lineStyle: {
                        width: 6,
                        color: [
                          [abnormalCount / total, '#FF6E76'],
                          [1, '#67C23A'],
                        ],
                      },
                    },
                    pointer: {
                      show: false,
                    },
                    axisTick: {
                      show: false,
                    },
                    splitLine: {
                      show: false,
                    },
                    axisLabel: {
                      show: false,
                    },
                    detail: {
                      valueAnimation: true,
                      width: '60%',
                      lineHeight: 40,
                      borderRadius: 8,
                      offsetCenter: [0, '-15%'],
                      fontSize: 20,
                      fontWeight: 'bold',
                      formatter: '{value}',
                      color: 'inherit',
                    },
                    title: {
                      show: true,
                      fontSize: 12,
                      offsetCenter: [0, '30%'],
                    },
                    data: [
                      {
                        value: total,
                        name: '总数',
                      },
                    ],
                  },
                ],
              }

              chart.setOption(option)
            } catch (error) {
              console.error('初始化图表失败:', error, chartId)
            }
          })
        })
      },

      async handleQuery() {
        console.log('handleQuery triggered')
        this.loading = true
        try {
          if (!this.timeRange || !this.timeRange[0] || !this.timeRange[1]) {
            this.$message.warning('请选择有效的时间范围')
            this.loading = false
            return
          }

          const params = {
            start_time: this.timeRange[0],
            end_time: this.timeRange[1],
          }
          console.log('NamespaceStats.vue:handleQuery: ', params)
          const res = await getNamespaceStats(params)
          console.log('API response:', res)

          if (res.status === 'success') {
            this.tableData = res.data.stats || []

            // 计算总统计数据
            this.calculateTotalStats()

            // 使用 setTimeout 确保 DOM 已完全更新
            setTimeout(() => {
              this.initCharts()
            }, 200)
          } else {
            this.$message.error(res.message || '获取数据失败')
          }
        } catch (error) {
          console.error('获取命名空间统计失败:', error)
          this.$message.error(
            '获取命名空间统计失败: ' + (error.message || '未知错误')
          )
        } finally {
          this.loading = false
        }
      },

      calculateTotalStats() {
        // 重置统计
        this.totalStats = {
          critical: 0,
          warning: 0,
          total: 0,
        }

        // 计算各级别告警总数
        this.tableData.forEach((item) => {
          if (item.severity_items) {
            this.totalStats.critical += item.severity_items.critical || 0
            this.totalStats.warning += item.severity_items.warning || 0
          }
          this.totalStats.total += item.total || 0
        })
      },

      handleRowClick(row) {
        this.$emit('namespace-click', row)
      },

      showNamespaceDetail(namespace) {
        // 实现命名空间详情展示逻辑
        console.log('显示命名空间详情:', namespace)
        // 这里可以添加详情展示逻辑，如打开抽屉或对话框
      },
    },
  }
</script>

<style lang="scss" scoped>
  .namespace-stats-view {
    padding: 8px 5px;
  }

  .filter-form .filter-row {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .time-range-item .el-date-editor {
    width: 380px !important;
    margin-left: 10px;
  }

  .stats-summary {
    margin-bottom: 20px;

    .stat-content {
      display: flex;
      align-items: center;

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;

        i {
          font-size: 24px;
          color: white;
        }

        &.critical {
          background-color: #f56c6c;
        }

        &.warning {
          background-color: #e6a23c;
        }

        &.total {
          background-color: #409eff;
        }
      }

      .stat-info {
        .stat-title {
          font-size: 14px;
          color: #909399;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
        }
      }
    }
  }

  .name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .severity-bars {
    .el-progress {
      margin-bottom: 4px;
    }

    .severity-counts {
      display: flex;
      gap: 8px;
      margin-top: 4px;
    }
  }

  .source-distribution-container {
    display: flex;
    align-items: center;
  }

  .chart-container {
    width: 100px;
    height: 80px;
    margin-right: 10px;
  }

  .echarts-container {
    width: 100%;
    height: 100%;
  }

  .source-details {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .source-item {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .circle-chart-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 12px;

    &.small {
      margin-right: 8px;
    }

    &:last-child {
      margin-right: 0;
    }
  }

  .circle-chart {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f0f0f0;
    border: 4px solid var(--el-color-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 4px;

    &.small {
      width: 50px;
      height: 50px;
      border-width: 3px;
    }
  }

  .circle-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .circle-number {
    font-size: 20px;
    font-weight: bold;
    color: var(--el-color-primary);

    .small & {
      font-size: 14px;
    }
  }

  .circle-label {
    font-size: 12px;
    color: #909399;

    .small & {
      display: none;
    }
  }

  .source-name {
    margin-top: 4px;
    text-align: center;
  }

  .multi-source-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .source-counts {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .source-item {
      margin-bottom: 2px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .source-details {
    .source-item {
      display: flex;
      justify-content: space-between;
      padding: 4px 0;
      border-bottom: 1px solid #eee;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .total-count {
    font-weight: bold;
    color: var(--el-color-primary);
  }
</style>
