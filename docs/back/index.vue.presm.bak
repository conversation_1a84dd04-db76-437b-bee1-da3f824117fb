<template>
  <div class="env-management">
    <el-row :gutter="24">
      <el-col :span="6">
        <el-card ref="dynamicsCard" class="latest-dynamics">
          <div slot="header" class="card-header">
            <span>最新动态</span>
          </div>
          <div ref="scrollContent" class="scroll-content">
            <seamless-scroll
              :data="formattedActives"
              :scroll-options="scrollOptions"
              :hover-stop="true"
              :is-reset="false"
              class="dynamic-info"
            >
              <template #default="">
                <el-timeline>
                  <el-timeline-item
                    v-for="(activity, index) in formattedActives"
                    :key="index"
                    :color="activity.color"
                    :timestamp="activity.create_time"
                    placement="top"
                  >
                    <div class="activity-line">
                      <div>操作者：{{ activity.applicant }}</div>
                      <div>
                        事件名：{{ activity.step_name || '' }}
                        {{ activity.pre_alias }}
                      </div>
                      <div>
                        结果：
                        <span :style="{ color: activity.color }">
                          {{ activity.step_result }}
                        </span>
                      </div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </template>
            </seamless-scroll>
          </div>
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-alert type="info" style="margin-bottom: 10px; padding: 0">
          <el-card>
            <div class="flow-image-container">
              <img
                src="@/assets/images/presminfo.jpeg"
                alt="预发布环境流程图"
                class="flow-image"
              />
            </div>
          </el-card>
        </el-alert>

        <el-card shadow="never" style="padding: 0px">
          <vab-query-form>
            <vab-query-form-left-panel :span="4">
              <el-button
                icon="el-icon-plus"
                type="primary"
                @click="handleEnvEdit"
              >
                环境
              </el-button>
            </vab-query-form-left-panel>
            <vab-query-form-left-panel :span="10">
              <el-radio-group
                v-model="selectedOption"
                size="mini"
                style="margin-top: 5px"
                @change="handleOptionChange"
              >
                <el-radio-button label="running" name="运行中">
                  运行中
                </el-radio-button>
                <el-radio-button label="creating" name="创建中">
                  创建中
                </el-radio-button>
                <el-radio-button label="archived" name="已封存">
                  已封存
                </el-radio-button>
                <el-radio-button label="deleted" name="已销毁">
                  已销毁
                </el-radio-button>
              </el-radio-group>
            </vab-query-form-left-panel>
            <vab-query-form-right-panel :span="10">
              <el-form :inline="true" :model="queryForm" @submit.native.prevent>
                <el-form-item>
                  <el-input
                    v-model.trim="queryForm.keyword"
                    clearable
                    placeholder="环境ID/别名/迭代版本"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    type="primary"
                    @click="queryData"
                  >
                    查询
                  </el-button>
                </el-form-item>
              </el-form>
            </vab-query-form-right-panel>
          </vab-query-form>

          <el-table
            :data="formattedApplicantList"
            :element-loading-text="elementLoadingText"
            :loading="listLoading"
            @selection-change="setSelectRows"
          >
            <el-table-column
              label="别名"
              prop="pre_alias"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div v-if="row.status !== 4">
                  <el-link
                    :underline="false"
                    style="color: #3f9eff"
                    type="primary"
                    @click="handleView(row)"
                  >
                    {{ row.pre_alias || row.pre_uuid }}
                  </el-link>
                </div>
                <div v-else>
                  {{ row.pre_alias || row.pre_uuid }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="业务线"
              prop="bizline"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="版本分支"
              min-width="100"
              prop="version_code"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="申请人"
              prop="applicant"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column
              label="状态"
              min-width="50"
              prop="status"
              :align="center"
              header-align="center"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div class="status-cell">
                  <el-tag :type="row.status | statusType">
                    {{ row.status | statusMap }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              v-if="queryForm.status !== 1 && queryForm.status !== 4"
              header-align="center"
              label="操作"
              min-width="160"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div class="presm-operation-buttons">
                  <el-button
                    v-if="row.status === 2"
                    size="mini"
                    type="text"
                    @click="handleView(row)"
                  >
                    管理服务
                  </el-button>
                  <el-divider
                    v-if="row.status === 2"
                    direction="vertical"
                  ></el-divider>
                  <el-button
                    v-if="row.status === 2 && row.pre_uuid !== 'preprod'"
                    type="text"
                    size="mini"
                    @click="handleArchive(row)"
                  >
                    封存环境
                  </el-button>

                  <el-button
                    v-if="row.status === 3"
                    size="mini"
                    type="text"
                    @click="showExtensionDialog(row)"
                  >
                    申请延期
                  </el-button>
                  <el-divider
                    v-if="row.status === 3"
                    direction="vertical"
                  ></el-divider>

                  <el-button
                    v-if="row.status === 3"
                    size="mini"
                    type="text"
                    @click="handleDelete(row)"
                  >
                    销毁环境
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :current-page="queryForm.pageNo"
            :layout="layout"
            :page-size="queryForm.pageSize"
            :page-sizes="[15, 30, 50, 100]"
            :total="total"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-card>
      </el-col>
    </el-row>

    <!-- 延期对话框 -->
    <el-dialog
      :before-close="closeExtensionDialog"
      :visible.sync="extensionDialogVisible"
      title="申请延期"
      width="30%"
    >
      <el-form>
        <el-form-item label="延期时间">
          <el-date-picker
            v-model="valid_time"
            align="right"
            placeholder="计划截止时间"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="extensionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleApplyForExtension">
          确定
        </el-button>
      </span>
    </el-dialog>

    <!-- 环境编辑组件 -->
    <env-edit
      ref="envedit"
      :all-user-list="allUserList"
      @fetch-data="fetchData"
    ></env-edit>
  </div>
</template>

<script>
  import { getUserInfo } from '@/api/user'
  import { actionEnv, deleteEnv, getEnvs } from '@/api/presm.js'
  import EnvEdit from './PreManagementEdit.vue'
  import SeamlessScroll from 'vue-seamless-scroll'

  export default {
    name: 'EnvManagement',
    components: {
      EnvEdit,
      SeamlessScroll,
    },
    filters: {
      statusMap(status) {
        const statusMap = {
          0: '申请中',
          1: '创建中',
          2: '运行中',
          3: '已封存',
          4: '已销毁',
          5: '创建失败',
        }
        return statusMap[status]
      },
      statusType(status) {
        const statusMap = {
          0: 'warning',
          1: 'warning',
          2: 'success',
          3: 'danger',
          4: 'info',
          5: 'danger',
        }
        return statusMap[status]
      },
    },
    props: {
      allUserList: {
        type: Array,
        required: true,
      },
      applicantDisplayNameMap: {
        type: Object,
        required: true,
      },
      activities: {
        type: Array,
        required: true,
      },
    },
    data() {
      return {
        selectedOption: 'running',
        scrollOptions: {
          step: 0.5,
          limitMoveNum: 5,
          hoverStop: true,
          direction: 1,
          openWatch: true,
          waitTime: 1000,
          switchOffset: 30,
        },
        queryForm: {
          pageNo: 1,
          pageSize: 15,
          keyword: '',
          status: 2,
        },
        listLoading: false,
        total: 0,
        layout: 'total, sizes, prev, pager, next, jumper',
        elementLoadingText: '正在加载...',
        extensionDialogVisible: false,
        valid_time: null,
        currentRow: null,
        filteredlist: [],
        allEnvData: [],
        resizeTimer: null,
        currentUser: null,
        isAdmin: false,
      }
    },
    computed: {
      formattedApplicantList() {
        if (!this.applicantDisplayNameMap || !this.filteredlist) {
          return []
        }

        return this.filteredlist.map((item) => {
          const displayName = this.applicantDisplayNameMap?.[item.applicant]
          return {
            ...item,
            applicant: displayName || item.applicant,
          }
        })
      },
      formattedActives() {
        if (!this.applicantDisplayNameMap || !this.activities) {
          return []
        }

        const filteredActivities = this.activities.filter((item) => {
          const stepName = item.step_name || ''
          const hasRuleKeyword =
            typeof stepName === 'string' && stepName.includes('规则')
          return !hasRuleKeyword
        })

        return filteredActivities.map((item) => {
          const displayName = this.applicantDisplayNameMap?.[item.applicant]
          const color = item.color || '#67C23A'
          return {
            ...item,
            applicant: displayName || item.applicant,
            color,
          }
        })
      },
    },
    watch: {
      activities: {
        handler(newVal) {
          if (newVal) {
            this.$nextTick(() => {
              this.initDynamicsHeight()
            })
          }
        },
        immediate: true,
      },
      'queryForm.status': {
        handler() {
          this.$nextTick(() => {
            this.initDynamicsHeight()
          })
        },
      },
    },
    mounted() {
      this.fetchData()
      this.$nextTick(() => {
        setTimeout(() => {
          this.initDynamicsHeight()
        }, 100)
      })
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      window.removeEventListener('resize', this.handleResize)
    },
    methods: {
      handleEnvEdit(row) {
        if (row?.pre_uuid) {
          this.$refs.envedit.showEdit(row)
        } else {
          this.$refs.envedit.showEdit()
        }
      },

      async fetchData() {
        try {
          this.listLoading = true
          const queryForm = {
            pageNo: 1,
            pageSize: 15,
          }
          const { data } = await getEnvs(queryForm)
          this.allEnvData = data
          await this.applyFilters()
          this.$emit('update:env-data', data)
          this.$nextTick(() => {
            this.initDynamicsHeight()
          })
        } catch (error) {
          console.error('Error fetching data:', error)
          this.$baseMessage('数据加载失败', 'error')
        } finally {
          this.listLoading = false
        }
      },

      async handleView(row) {
        if (!row.pre_uuid) {
          this.$baseMessage('没有找到对应环境', 'warning')
          return
        }

        if (row.status === 2) {
          if (row.pre_uuid === 'preprod') {
            try {
              const { user, groups } = await getUserInfo()
              this.currentUser = user

              if (groups.indexOf('admins') === -1) {
                this.$baseMessage(
                  '基线环境只能管理员操作，请联系管理员',
                  'warning'
                )
                return
              } else {
                this.isAdmin = groups.indexOf('admin') !== -1
              }
            } catch (error) {
              console.error('Error getting user info:', error)
              this.$baseMessage('获取用户信息失败', 'error')
              return
            }
          }

          // 更新 Vuex store
          this.$store.dispatch('consts/updateAllUserList', this.allUserList)
          this.$store.dispatch(
            'consts/updateDisplayNameMap',
            this.applicantDisplayNameMap
          )

          // 路由跳转
          this.$router.push({
            name: 'preManagement',
            params: {
              pre_uuid: row.pre_uuid,
              pre_alias: row.pre_alias,
            },
          })
        } else {
          this.$baseMessage('只能对运行中的环境进行操作', 'warning')
        }
      },

      async handleApplyForExtension() {
        this.listLoading = true
        try {
          const params = { ...this.currentRow, valid_time: this.valid_time }
          const { msg } = await actionEnv(params.pre_uuid, params, 'extension')
          this.$baseMessage(msg, 'success')
          await this.fetchData()
        } catch (error) {
          console.error('延长申请失败:', error)
          this.$baseMessage('延长申请失败', 'error')
        } finally {
          this.listLoading = false
          this.closeExtensionDialog()
        }
      },

      applyFilters(filteredData = null) {
        try {
          const dataToFilter =
            filteredData ||
            this.allEnvData.filter(
              (item) => item.status === this.queryForm.status
            )

          this.filteredlist = dataToFilter.slice(
            (this.queryForm.pageNo - 1) * this.queryForm.pageSize,
            this.queryForm.pageNo * this.queryForm.pageSize
          )
          this.total = dataToFilter.length
        } catch (error) {
          console.error('过滤组件异常：', error)
          this.$baseMessage('过滤失败', 'error')
        } finally {
          this.listLoading = false
        }
        this.$nextTick(() => {
          this.handleDataChange()
        })
      },

      async handleArchive(row) {
        this.$baseConfirm('确认封存该环境吗？', null, async () => {
          try {
            const { msg } = await actionEnv(row.pre_uuid, row, 'archive')
            this.$baseMessage(msg, 'success')
            await this.fetchData()
          } catch (error) {
            console.error('封存失败:', error)
            this.$baseMessage('封存失败', 'error')
          }
        })
      },

      showExtensionDialog(row) {
        this.currentRow = row
        this.extensionDialogVisible = true
      },

      closeExtensionDialog() {
        this.extensionDialogVisible = false
      },

      async handleDelete(row) {
        this.$baseConfirm('确认销毁该环境吗？', null, async () => {
          try {
            const { msg } = await deleteEnv(row.pre_uuid)
            this.$baseMessage(msg, 'success')
            await this.fetchData()
          } catch (error) {
            console.error('销毁失败:', error)
            this.$baseMessage('销毁失败', 'error')
          }
        })
      },

      handleOptionChange(val) {
        const statusMap = {
          running: 2,
          creating: 1,
          archived: 3,
          deleted: 4,
        }
        this.queryForm.status = statusMap[val]
        this.queryForm.pageNo = 1
        this.applyFilters()
        this.handleDataChange()
      },

      queryData() {
        this.queryForm.pageNo = 1
        this.applyFilters()
        this.handleDataChange()
      },

      handleSizeChange(val) {
        this.queryForm.pageSize = val
        this.queryForm.pageNo = 1
        this.applyFilters()
        this.handleDataChange()
      },

      handleCurrentChange(val) {
        this.queryForm.pageNo = val
        this.applyFilters()
        this.handleDataChange()
      },

      handleResize() {
        if (this.resizeTimer) clearTimeout(this.resizeTimer)
        this.resizeTimer = setTimeout(() => {
          this.initDynamicsHeight()
        }, 200)
      },

      initDynamicsHeight() {
        this.$nextTick(() => {
          const dynamicsCard = this.$refs.dynamicsCard?.$el
          const scrollContent = this.$refs.scrollContent
          if (!dynamicsCard || !scrollContent) return

          requestAnimationFrame(() => {
            const alertEl = this.$el.querySelector('.el-alert')
            const tableCardEl = this.$el.querySelector(
              '.el-card.is-never-shadow'
            )
            if (!tableCardEl) return

            const alertRect = alertEl?.getBoundingClientRect()
            const tableRect = tableCardEl.getBoundingClientRect()

            const alertHeight = alertRect ? alertRect.height : 0
            const tableHeight = tableRect.height
            const rightSideHeight = Math.ceil(alertHeight + tableHeight)

            dynamicsCard.style.height = `${rightSideHeight}px`

            const headerEl = dynamicsCard.querySelector('.el-card__header')
            const headerHeight = headerEl
              ? headerEl.getBoundingClientRect().height
              : 0
            const contentHeight = rightSideHeight - headerHeight - 20

            scrollContent.style.height = `${Math.max(contentHeight, 100)}px`
          })
        })
      },

      handleDataChange() {
        this.$nextTick(() => {
          this.initDynamicsHeight()
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .latest-dynamics {
    margin-bottom: 20px;
    transition: height 0.3s ease;
    position: relative;
    min-height: 500px;

    .el-card__body {
      padding: 10px;
      height: calc(100% - 47px);
      display: flex;
      flex-direction: column;
      position: relative;
    }

    .el-card__header {
      padding: 10px;
      height: 47px;
      box-sizing: border-box;

      .card-header {
        padding: 0 5px 0 0;
        font-size: 14px;
        line-height: 27px;
      }
    }

    .scroll-content {
      flex: 1;
      transition: height 0.3s ease;
      overflow: hidden;
      position: relative;
      display: flex;
      min-height: 500px;

      .dynamic-info {
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        .el-timeline {
          padding-left: 10px;
          margin: 0;
          height: auto;

          .el-timeline-item {
            padding-bottom: 15px;
            margin-bottom: 0;

            .activity-line {
              font-size: 12px;
              line-height: 1;
              white-space: normal;
              display: -webkit-box;
              line-clamp: 6;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              max-height: 7.2em;

              div {
                margin-bottom: 3px;
              }

              span {
                display: inline-block;
                margin: 2px 0;
              }
            }
          }
        }
      }
    }
  }

  .flow-image-container {
    padding: 0;
    text-align: center;

    .flow-image {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
  }

  .presm-operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;

    .el-button {
      padding: 0 8px;
      margin: 0;
      height: 28px;
      line-height: 28px;

      &:nth-of-type(1) {
        color: #3f9eff;
      }

      &:nth-of-type(2) {
        color: #ebb563;
      }

      &:nth-of-type(3) {
        color: #67c23a;
      }

      &:last-child {
        color: #f68989;
      }

      &:hover {
        opacity: 0.8;
      }
    }

    .el-divider--vertical {
      height: 1em;
      margin: 0 8px;
    }
  }

  .status-cell {
    display: flex;
    justify-content: center;
    align-items: center;

    .el-tag {
      margin: 0;
      min-width: 64px;
      text-align: center;
    }
  }

  .env-table {
    margin-bottom: 20px;
  }

  .el-pagination {
    margin-top: 15px;
    padding: 0;
  }
</style>
