<template>
  <div class="premain-container">
    <el-row :gutter="24">
      <el-col :lg="6" :md="6" :sm="24" :xl="6" :xs="24">
        <!-- 根据页签显示不同的提示信息 -->
        <div v-if="activeTab === 'premange'">
          <el-alert title="环境管理说明" type="info">
            <div class="ci-alert-list">
              ○ <span style="color: red">[创建中]</span> 的环境，会自动变更状态
            </div>
            <p />
          </el-alert>
        </div>
        <div v-else-if="activeTab === 'grays'">
          <el-alert title="用户规则说明" type="info">
            <div class="ci-alert-list">
              ○ 只列出环境状态为
              <span style="color: red">[创建中]</span> 和
              <span style="color: red">[运行中]</span>
            </div>
            <div class="ci-alert-list">
              ○ 点击
              <span style="color: red">[albumid]</span> 可查看具体信息
            </div>
            <div class="ci-alert-list">
              ○ <span style="color: red">[albumid]</span> 可拖拽到
              <span style="color: red">[环境名]</span> 实现
              <span style="color: red">[更新]</span>
            </div>
            <p />
          </el-alert>
        </div>
        <div v-else-if="activeTab === 'uri'">
          <el-alert title="URI灰度规则说明" type="info">
            <div class="ci-alert-list">
              ○ 列出所有有效的 URI 规则（状态为有效，即 0）
            </div>
            <div class="ci-alert-list">
              ○ 点击规则可进行编辑操作
            </div>
            <div class="ci-alert-list">
              ○ URI 规则支持精准匹配和前缀匹配
            </div>
            <p />
          </el-alert>
        </div>

        <el-card>
          <div slot="header" class="card-header">
            <span>动态信息</span>
          </div>
          <div class="scroll-content">
            <seamless-scroll
              :data="formattedActives"
              :scroll-options="scrollOptions"
              class="dynamic-info"
            >
              <template #default="activity">
                <el-timeline>
                  <el-timeline-item
                    v-for="(activity, index) in formattedActives"
                    :key="index"
                    :color="activity.color"
                    :timestamp="activity.create_time"
                    placement="top"
                  >
                    <div class="activity-line">
                      {{ activity.applicant }}：{{ activity.step_name }} -
                      {{ activity.pre_alias }}
                      <span :style="{ color: activity.color }">
                        {{ activity.step_result }}
                      </span>
                    </div>
                  </el-timeline-item>
                </el-timeline>
              </template>
            </seamless-scroll>
          </div>
        </el-card>
      </el-col>

      <el-col :lg="17" :md="17" :sm="24" :xl="17" :xs="24">
        <el-alert title="使用步骤说明" type="info">
          <p />
          <el-steps align-center class="custom-steps" finish-status="success">
            <el-step>
              <template #description>
                <b>创建环境</b>
              </template>
            </el-step>
            <el-step>
              <template #description>
                <b>添加服务</b>
              </template>
            </el-step>
            <el-step>
              <template #description>
                配置用户规则，指定的 <b>albumid</b> 会走到对应环境已存在的服务
              </template>
            </el-step>
            <el-step>
              <template #description>
                未登录、未匹配到规则、或规则指向环境不存在对应服务的，会转发到 <b>基线环境</b>
              </template>
            </el-step>
            <el-step>
              <template #description>
                所有环境域名均为<br />
                <b><a href="https://www.tapbizz.cn">www.tapbizz.cn</a></b>
              </template>
            </el-step>
            <el-step>
              <template #description>
                nacos地址<br />
                <b>
                  <a
                    href="https://nacos-pre.in.szwego.com"
                    style="white-space: nowrap"
                  >
                    https://nacos-pre.in.szwego.com
                  </a>
                </b>
              </template>
            </el-step>
          </el-steps>
        </el-alert>

        <el-tabs v-model="activeTab" type="card" @tab-click="handleClick">
          <!-- 环境管理页签 -->
          <el-tab-pane name="premange">
            <span slot="label">
              <i class="el-icon-folder"></i>
              <b v-text="tabText_1"></b>
            </span>

            <el-card shadow="never">
              <vab-query-form>
                <vab-query-form-left-panel :span="4">
                  <el-button icon="el-icon-plus" type="primary" @click="handleEnvEdit">
                    环境
                  </el-button>
                </vab-query-form-left-panel>
                <vab-query-form-left-panel :span="10">
                  <el-radio-group
                    v-model="selectedOption"
                    size="mini"
                    style="margin-top: 5px"
                    @change="handleOptionChange"
                  >
                    <el-radio-button label="running" name="运行中">
                      运行中
                    </el-radio-button>
                    <el-radio-button label="creating" name="创建中">
                      创建中
                    </el-radio-button>
                    <el-radio-button label="archived" name="已封存">
                      已封存
                    </el-radio-button>
                    <el-radio-button label="deleted" name="已销毁">
                      已销毁
                    </el-radio-button>
                  </el-radio-group>
                </vab-query-form-left-panel>
                <vab-query-form-right-panel :span="10">
                  <el-form :inline="true" :model="queryForm" @submit.native.prevent>
                    <el-form-item>
                      <el-input
                        v-model.trim="queryForm.keyword"
                        clearable
                        placeholder="环境ID/别名/迭代版本"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button icon="el-icon-search" type="primary" @click="queryData">
                        查询
                      </el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-right-panel>
              </vab-query-form>

              <el-table
                :data="formattedApplicantList"
                :element-loading-text="elementLoadingText"
                :loading="listLoading"
                @selection-change="setSelectRows"
              >
                <el-table-column label="别名" prop="pre_alias" show-overflow-tooltip>
                  <template #default="{ row }">
                    <div v-if="row.status !== 4">
                      <el-link
                        :underline="false"
                        style="color: #3f9eff"
                        type="primary"
                        @click="handleView(row)"
                      >
                        {{ row.pre_alias || row.pre_uuid }}
                      </el-link>
                    </div>
                    <div v-else>
                      {{ row.pre_alias || row.pre_uuid }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="业务线" prop="bizline" show-overflow-tooltip></el-table-column>

                <el-table-column label="版本分支" min-width="100" prop="version_code" show-overflow-tooltip></el-table-column>

                <el-table-column label="申请人" prop="applicant" show-overflow-tooltip></el-table-column>

                <el-table-column label="状态" min-width="50" prop="status" show-overflow-tooltip>
                  <template #default="{ row }">
                    <el-tag :type="row.status | statusType">
                      {{ row.status | statusMap }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="queryForm.status !== 1 && queryForm.status !== 4"
                  header-align="center"
                  label="操作"
                  min-width="160"
                  show-overflow-tooltip
                >
                  <template #default="{ row }">
                    <div class="operation-buttons">
                      <el-button
                        v-if="row.status === 2"
                        size="mini"
                        style="color: #3f9eff"
                        @click="handleView(row)"
                      >
                        管理服务
                      </el-button>
                      <el-button
                        v-if="row.status === 2 && row.pre_uuid !== 'preprod'"
                        size="mini"
                        style="color: #ebb563"
                        @click="handleArchive(row)"
                      >
                        封存环境
                      </el-button>
                      <el-button
                        v-if="row.status === 3"
                        size="mini"
                        style="color: #67c23a"
                        @click="showExtensionDialog(row)"
                      >
                        申请延期
                      </el-button>
                      <el-button
                        v-if="row.status === 3"
                        size="mini"
                        style="color: #f68989"
                        @click="handleDelete(row)"
                      >
                        销毁环境
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                :current-page="queryForm.pageNo"
                :layout="layout"
                :page-size="queryForm.pageSize"
                :page-sizes="[15, 30, 50, 100]"
                :total="total"
                background
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              ></el-pagination>
            </el-card>
          </el-tab-pane>

          <!-- 用户规则页签 -->
          <el-tab-pane name="grays">
            <span slot="label">
              <i class="el-icon-user"></i>
              <b v-text="tabText_2"></b>
            </span>
            <el-card shadow="never">
              <vab-query-form>
                <vab-query-form-left-panel :span="12">
                  <el-button icon="el-icon-plus" type="primary" @click="handleGrayEdit">
                    规则
                  </el-button>
                </vab-query-form-left-panel>
                <vab-query-form-right-panel :span="12">
                  <el-form :inline="true" :model="queryGrayForm" @submit.native.prevent>
                    <el-form-item>
                      <el-input
                        v-model.trim="queryGrayForm.keyword"
                        clearable
                        placeholder="albumid/环境ID"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button icon="el-icon-search" type="primary" @click="queryGrayData">
                        查询
                      </el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-right-panel>
              </vab-query-form>

              <div class="gray-table">
                <el-table
                  v-if="isDataLoaded"
                  ref="tableRef"
                  v-loading="grayTableLoading"
                  :data="grayTableList"
                  :element-loading-text="elementLoadingText"
                  :span-method="arraySpanMethod"
                  :row-class-name="rowClassName"
                >
                  <el-table-column label="环境名" min-width="100" prop="pre_uuid" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="droppable" @drop="handleDrop(scope.row)" @dragover.prevent>
                        <el-link
                          :underline="false"
                          style="color: #3f9eff"
                          type="primary"
                          @click="handleRuleView(scope.row)"
                        >
                          {{ scope.row.pre_alias }}
                        </el-link>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="相册ID" min-width="160" prop="albumid" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="draggable" draggable="true" @dragstart="handleDragStart(scope.row)">
                        <el-link
                          :underline="false"
                          type="primary"
                          @click="handleShowAlbumIdInfo(scope.row.albumid)"
                        >
                          {{ scope.row.albumid }}
                        </el-link>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="备注" prop="memo" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="draggable" draggable="true" @dragstart="handleDragStart(scope.row)">
                        {{ scope.row.memo }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="环境状态" prop="status" show-overflow-tooltip>
                    <template #default="scope">
                      <el-tag :type="scope.row.status | statusType">
                        {{ scope.row.status | statusMap }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column header-align="center" label="操作" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="operation-buttons">
                        <el-button
                          v-if="scope.row.status === 2"
                          size="mini"
                          style="color: #f68989"
                          @click="handleDeleteAlbumId(scope.row)"
                        >
                          删除
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <el-pagination
                :current-page="queryGrayForm.pageNo"
                :layout="layout"
                :page-size="queryGrayForm.pageSize"
                :page-sizes="[15, 30, 50, 100]"
                :total="grayTotal"
                background
                @size-change="handleSizeChangeGray"
                @current-change="handleCurrentChangeGray"
              ></el-pagination>
            </el-card>
          </el-tab-pane>

          <!-- URI规则页签 -->
          <el-tab-pane name="uri">
            <span slot="label">
              <i class="el-icon-document"></i>
              <b>URI规则</b>
            </span>
            <el-card shadow="never">
              <vab-query-form>
                <vab-query-form-left-panel :span="12">
                  <el-button icon="el-icon-plus" type="primary" @click="handleUriEdit">
                    URI规则
                  </el-button>
                </vab-query-form-left-panel>
                <vab-query-form-right-panel :span="12">
                  <el-form :inline="true" :model="queryUriForm" @submit.native.prevent>
                    <el-form-item>
                      <el-input
                        v-model.trim="queryUriForm.keyword"
                        clearable
                        placeholder="URI / 环境ID"
                      />
                    </el-form-item>
                    <el-form-item>
                      <el-button icon="el-icon-search" type="primary" @click="queryUriData">
                        查询
                      </el-button>
                    </el-form-item>
                  </el-form>
                </vab-query-form-right-panel>
              </vab-query-form>

              <div class="uri-table">
                <el-table
                  v-if="uriDataLoaded"
                  ref="uriTableRef"
                  v-loading="uriTableLoading"
                  :data="uriRuleList"
                  :element-loading-text="elementLoadingText"
                >
                  <el-table-column
                    label="环境ID"
                    min-width="100"
                    prop="pre_uuid"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <div class="droppable" @drop="handleUriDrop(scope.row)" @dragover.prevent>
                        <el-link
                          :underline="false"
                          style="color: #3f9eff"
                          type="primary"
                          @click="handleUriEdit(scope.row)"
                        >
                          {{ scope.row.pre_uuid }}
                        </el-link>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    label="URI"
                    min-width="220"
                    prop="uri"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-link
                        :underline="false"
                        type="primary"
                        @click="handleUriEdit(scope.row)"
                      >
                        {{ scope.row.uri }}
                      </el-link>
                    </template>
                  </el-table-column>

                  <el-table-column
                    label="匹配类型"
                    min-width="100"
                    prop="match_type"
                    show-overflow-tooltip
                  >
                    <template #default="scope">
                      <el-tag :type="scope.row.match_type === 0 ? 'success' : 'danger'">
                        {{ scope.row.match_type === 0 ? '精准匹配' : '前缀匹配' }}
                      </el-tag>
                    </template>
                  </el-table-column>

                  <el-table-column label="备注" prop="memo" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="draggable" draggable="true" @dragstart="handleUriDragStart(scope.row)">
                        {{ scope.row.memo }}
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column
                    label="添加时间"
                    min-width="150"
                    prop="create_time"
                    show-overflow-tooltip
                  ></el-table-column>

                  <el-table-column header-align="center" label="操作" show-overflow-tooltip>
                    <template #default="scope">
                      <div class="operation-buttons">
                        <el-button
                          size="mini"
                          style="color: #f68989"
                          @click="handleDeleteUri(scope.row)"
                        >
                          删除
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <el-pagination
                :current-page="queryUriForm.pageNo"
                :layout="layout"
                :page-size="queryUriForm.pageSize"
                :page-sizes="[15, 30, 50, 100]"
                :total="uriTotal"
                background
                @size-change="handleSizeChangeUri"
                @current-change="handleCurrentChangeUri"
              ></el-pagination>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>

    <el-dialog
      :before-close="closeExtensionDialog"
      :visible.sync="extensionDialogVisible"
      title="申请延期"
      width="30%"
    >
      <el-form>
        <el-form-item label="延期时间">
          <el-date-picker
            v-model="valid_time"
            align="right"
            placeholder="计划截止时间"
            type="date"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="extensionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleApplyForExtension">
          确定
        </el-button>
      </span>
    </el-dialog>

    <el-dialog
      :before-close="closeDialogAlbumIdInfo"
      :title="dialogAlbumId + '信息'"
      :visible.sync="dialogAlbumIdInfo"
      width="50%"
    >
      <album-info :albumid="dialogAlbumId"></album-info>
    </el-dialog>

    <!-- 编辑组件 -->
    <env-edit
      ref="envedit"
      :all-user-list="allUserList"
      @fetch-data="fetchData"
    ></env-edit>

    <env-gray-edit
      ref="envgrayedit"
      @fetch-data-gray="fetchGrayData"
    ></env-gray-edit>

    <!-- 新增 URI 规则编辑组件 -->
    <env-gray-uri-edit
      ref="envgrayuriedit"
      @fetch-data-uri="fetchUriData"
    ></env-gray-uri-edit>
  </div>
</template>

<script>
import { getUserInfo } from '@/api/user'
import { getList } from '@/api/userManagement'
import EnvEdit from './components/PreManagementEdit.vue'
import EnvGrayEdit from './components/PreGrayruleEdit.vue'
import EnvGrayUriEdit from './components/PreGrayUriEdit.vue'
import AlbumInfo from './components/AlbumInfo.vue'
import SeamlessScroll from 'vue-seamless-scroll'
import {
  actionEnv,
  deleteEnv,
  DeleteRules,
  EnvLogs,
  EnvRules,
  getEnvs,
  PutRules,
  DeleteUriRules,
  EnvRulesUri,
  PutUriRules
} from '@/api/presm'
import 'driver.js/dist/driver.min.css'

export default {
  name: 'PreMain',
  components: {
    EnvEdit,
    EnvGrayEdit,
    EnvGrayUriEdit,
    SeamlessScroll,
    AlbumInfo,
  },
  filters: {
    typeMap(status) {
      const statusMap = {
        0: '开发联调',
        1: '系统测试',
        2: '灰度预发',
        4: '压力测试',
        5: '基线环境',
      }
      return statusMap[status]
    },
    statusMap(status) {
      const statusMap = {
        0: '申请中',
        1: '创建中',
        2: '运行中',
        3: '已封存',
        4: '已销毁',
        5: '创建失败',
      }
      return statusMap[status]
    },
    statusType(status) {
      const statusMap = {
        0: 'warning',
        1: 'warning',
        2: 'success',
        3: 'danger',
        4: 'info',
        5: 'danger',
      }
      return statusMap[status]
    },
  },
  data() {
    return {
      grayColors: ['#e1e4e6', '#ddebf9', '#c9f4ff'],
      colorIndex: 0,
      colors: ['#F0414A', '#1B6489', '#64B38D'],
      dialogAlbumIdInfo: false,
      dialogAlbumId: null,
      tabText_1: ' 环境管理',
      tabText_2: ' 用户规则',
      selectedOption: 'running',
      activeTab: 'premange',
      extensionDialogVisible: false,
      scrollOptions: {
        step: 1,
        speed: 100000,
        infinite: true,
        reset: true,
        autoWait: false,
      },
      allUserList: [],
      applicantDisplayNameMap: null,
      allBizlines: [],
      currentUser: '',
      isAdmin: '',
      total: 0,
      reverse: false,
      selectRows: '',
      layout: 'total, sizes, prev, pager, next, jumper',
      filteredlist: null,
      allEnvData: null,
      queryForm: {
        pageNo: 1,
        pageSize: 15,
        keyword: '',
      },
      queryGrayForm: {
        pageNo: 1,
        pageSize: 15,
        keyword: '',
      },
      queryUriForm: {
        pageNo: 1,
        pageSize: 15,
        keyword: '',
      },
      valid_time: null,
      row: null,
      activities: null,
      timer: 0,
      grayList: [],
      grayTotal: 0,
      grayTableList: [],
      listLoading: true,
      elementLoadingText: '正在加载...',
      grayTableLoading: false,
      uriTableLoading: false,
      uriDataLoaded: false,
      isAlternate: false,
      isDataLoaded: false,
      spanCache: [],
      rowClassCache: [],
      draggedAlbum: null,
      draggedUri: null,
      colorIndexes: {},
      dynamicStyleAdded: false,
      uriRuleList: [],
      uriTotal: 0,
    }
  },
  computed: {
    formattedActives() {
      if (!this.applicantDisplayNameMap || this.activities === null) {
        return []
      }
      return this.activities.filter((item) => {
        const stepName = item.step_name || ''
        const hasRuleKeyword =
          typeof stepName === 'string' && stepName.includes('规则')
        if (this.activeTab === 'premange') {
          return !hasRuleKeyword
        } else if (this.activeTab === 'grays') {
          return hasRuleKeyword
        }
        return true
      }).map((item) => {
        const displayName = this.applicantDisplayNameMap?.[item.applicant]
        let color = item.color
        if (!color) {
          color = '#67C23A'
        }
        return {
          ...item,
          applicant: displayName || item.applicant,
          color,
        }
      })
    },
    formattedApplicantList() {
      if (!this.applicantDisplayNameMap || !this.filteredlist) {
        return []
      }
      return this.filteredlist.map((item) => {
        const displayName = this.applicantDisplayNameMap?.[item.applicant]
        return {
          ...item,
          applicant: displayName || item.applicant,
        }
      })
    },
  },
  watch: {
    grayList: {
      handler: function () {
        this.updateFormattedGrayRuleList()
      },
      deep: true,
    },
    allEnvData: {
      handler: function (newVal, oldVal) {},
      deep: true,
    },
    applicantDisplayNameMap: {
      handler: function (newVal, oldVal) {},
      deep: true,
    },
  },
  created() {
    this.loadDataAndFormat()
    this.spanCache = new Array(this.formattedGrayRuleList().length).fill(null)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  mounted() {},
  methods: {
    handleClick(tab, event) {},
    setSelectRows(val) {
      this.selectRows = val
    },
    handleDragStart(row) {
      this.draggedAlbum = row
    },
    handleDrop(targetRow) {
      if (this.draggedAlbum) {
        const updatedData = {
          pre_uuid: targetRow.pre_uuid,
          applicant: this.draggedAlbum.applicant,
          memo: this.draggedAlbum.memo,
        }
        this.updateAlbumEnvironment(updatedData, this.draggedAlbum.albumid)
        this.draggedAlbum = null
      }
    },
    async updateAlbumEnvironment(updatedData, albumid) {
      try {
        const { msg } = await PutRules(updatedData, albumid)
        this.$baseMessage(msg, 'success')
        this.$emit('fetch-data')
      } catch (error) {
        console.error('Error Update albumId:', error)
        this.$baseMessage('更新失败', 'error')
      } finally {
        await this.fetchGrayData()
      }
    },
    async handleView(row) {
      if (!row.pre_uuid) {
        this.$baseMessage('没有找到对应环境', 'warning')
        return
      }
      if (row.status === 2) {
        if (row.pre_uuid === 'preprod') {
          const { user, groups } = await getUserInfo()
          this.currentUser = user
          if (groups.indexOf('admins') === -1) {
            this.$baseMessage('基线环境只能管理员操作，请联系管理员', 'warning')
            return
          } else {
            this.isAdmin = groups.indexOf('admin') !== -1
          }
        }
        this.$store.dispatch('consts/updateAllUserList', this.allUserList)
        this.$store.dispatch('consts/updateDisplayNameMap', this.applicantDisplayNameMap)
        this.$router.push({
          name: 'preManagement',
          params: {
            pre_uuid: row.pre_uuid,
            pre_alias: row.pre_alias,
          },
        })
      } else {
        this.$baseMessage('只能对运行中的环境进行操作', 'warning')
      }
    },
    handleRuleView(row) {
      const rulerow = this.filteredlist.find(item => item.pre_uuid === row.pre_uuid)
      if (rulerow) {
        rulerow.albumid = row.albumid
        this.handleView(rulerow)
      } else {
        this.$baseMessage('不是运行中的环境，无法跳转', 'warning')
      }
    },
    handleEnvEdit(row) {
      if (row && row.pre_uuid) {
        this.$refs['envedit'].showEdit(row)
      } else {
        this.$refs['envedit'].showEdit()
      }
    },
    handleGrayEdit(row) {
      if (row && row.pre_uuid) {
        this.$refs['envgrayedit'].showEdit(row)
      } else {
        this.$refs['envgrayedit'].showEdit()
      }
    },
    async handleArchive(row) {
      const pre_uuid = row.pre_uuid
      try {
        this.$baseConfirm('确认封存环境吗？', null, async () => {
          const { msg } = await actionEnv(pre_uuid, {}, 'archive')
          this.$baseMessage(msg, 'success')
          await new Promise((resolve) => setTimeout(resolve, 20))
          await Promise.all([
            this.fetchData(),
            this.fetchGrayData(),
            this.getLogsList(),
          ])
        })
      } catch (error) {
        console.error('Error archiving environment:', error)
      }
    },
    async handleDelete(row) {
      this.$baseConfirm('环境删除后数据将无法恢复，确认继续吗？', null, async () => {
        const { msg } = await deleteEnv(row.pre_uuid)
        this.$baseMessage(msg, 'success')
        await new Promise((resolve) => setTimeout(resolve, 20))
        await Promise.all([
          this.fetchData(),
          this.fetchGrayData(),
          this.getLogsList(),
        ])
        this.handleOptionChange('archived')
      })
    },
    showExtensionDialog(row) {
      this.extensionDialogVisible = true
      this.row = row
      this.valid_time = null
    },
    async handleApplyForExtension() {
      this.listLoading = true
      try {
        const params = { ...this.row, valid_time: this.valid_time }
        const { msg } = await actionEnv(params.pre_uuid, params, 'extension')
        this.$baseMessage(msg, 'success')
        await this.loadDataAndFormat()
      } catch (error) {
        console.error('延长申请失败:', error)
        this.$baseMessage('延长申请失败', 'error')
      } finally {
        this.listLoading = false
        this.closeExtensionDialog()
      }
    },
    closeExtensionDialog() {
      this.row = null
      this.valid_time = null
      this.extensionDialogVisible = false
    },
    closeDialogAlbumIdInfo() {
      this.dialogAlbumIdInfo = false
      this.dialogAlbumId = null
    },
    handleSizeChange(val) {
      this.queryForm.pageSize = val
      this.queryForm.pageNo = 1
      this.$nextTick(() => {
        this.applyFilters()
      })
    },
    handleCurrentChange(val) {
      this.queryForm.pageNo = val
      this.$nextTick(() => {
        this.applyFilters()
      })
    },
    handleSizeChangeGray(val) {
      this.queryGrayForm.pageSize = val
      this.queryGrayForm.pageNo = 1
      this.fetchGrayData()
    },
    handleCurrentChangeGray(val) {
      this.queryGrayForm.pageNo = val
      this.fetchGrayData()
    },
    handleShowAlbumIdInfo(albumid) {
      this.dialogAlbumIdInfo = true
      this.dialogAlbumId = albumid
    },
    applyFilters(filteredData = null) {
      try {
        const dataToFilter =
          filteredData ||
          this.allEnvData.filter(item => item.status === this.queryForm.status)
        this.filteredlist = dataToFilter.slice(
          (this.queryForm.pageNo - 1) * this.queryForm.pageSize,
          this.queryForm.pageNo * this.queryForm.pageSize
        )
        this.total = dataToFilter.length
      } catch (error) {
        console.error('过滤组件异常：', error)
        this.$baseMessage('过滤失败', 'error')
      } finally {
        this.listLoading = false
      }
    },
    queryData() {
      this.queryForm.pageNo = 1
      this.queryForm.pageSize = 15
      const keyword = this.queryForm.keyword?.toLowerCase().trim() || ''
      try {
        let filteredData = this.allEnvData.filter(
          item => item.status === this.queryForm.status
        )
        if (keyword) {
          const searchFields = ['bizline', 'pre_alias', 'pre_uuid', 'version_code']
          filteredData = filteredData.filter(item => {
            return searchFields.some(field => {
              const fieldValue = item[field]
              return String(fieldValue || '').toLowerCase().includes(keyword)
            })
          })
        }
        this.$nextTick(() => {
          this.applyFilters(filteredData)
        })
      } catch (error) {
        console.error('查询数据时发生错误:', error)
        this.$baseMessage('查询失败', 'error')
      }
    },
    // URI 规则相关方法
    queryUriData() {
      this.queryUriForm.pageNo = 1
      this.queryUriForm.pageSize = 15
      try {
        this.fetchUriData()
      } catch (error) {
        console.error(error)
      }
    },
    handleSizeChangeUri(val) {
      this.queryUriForm.pageSize = val
      this.queryUriForm.pageNo = 1
      this.fetchUriData()
    },
    handleCurrentChangeUri(val) {
      this.queryUriForm.pageNo = val
      this.fetchUriData()
    },
    async fetchGrayData() {
      try {
        const { data, totalCount } = await EnvRules(this.queryGrayForm)
        this.grayList = data ? data : []
        this.grayTotal = totalCount
        return data
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        this.isDataLoaded = true
        this.grayTableLoading = false
      }
    },
    async fetchUriData() {
      try {
        this.uriTableLoading = true
        const { data, totalCount } = await EnvRulesUri(this.queryUriForm)
        this.uriRuleList = data ? data : []
        this.uriTotal = totalCount
      } catch (error) {
        console.error('Error fetching uri data:', error)
      } finally {
        this.uriDataLoaded = true
        this.uriTableLoading = false
      }
    },
    queryGrayData() {
      this.queryGrayForm.pageNo = 1
      this.queryGrayForm.pageSize = 15
      try {
        this.fetchGrayData()
      } catch (error) {
        console.error(error)
      } finally {
        this.grayTableLoading = false
      }
    },
    formattedGrayRuleList() {
      if (!this.applicantDisplayNameMap || !this.grayList) {
        return []
      }
      const allEnvAliasMap = this.allEnvData
        ? this.allEnvData.reduce((map, item) => {
          if (item.status === 2 || item.status === 1) {
            map[item.pre_uuid] = item.pre_alias
          }
          return map
        }, {})
        : {}
      const allEnvStatus = this.allEnvData
        ? this.allEnvData.reduce((map, item) => {
          if (item.status === 2 || item.status === 1) {
            map[item.pre_uuid] = item.status
          }
          return map
        }, {})
        : {}
      return this.grayList.map(item => {
        const displayName = this.applicantDisplayNameMap?.[item.applicant]
        return {
          pre_uuid: item.pre_uuid,
          albumid: item.albumid,
          memo: item.memo,
          pre_alias: allEnvAliasMap[item.pre_uuid],
          applicant: displayName || item.applicant,
          status: allEnvStatus[item.pre_uuid],
        }
      })
    },
    updateFormattedGrayRuleList() {
      this.isDataLoaded = false
      this.grayTableList = this.formattedGrayRuleList()
      this.isDataLoaded = true
    },
    handleOptionChange(value) {
      switch (value) {
        case 'running':
          this.fetchData()
          break
        case 'creating':
          this.handleFilterCreate()
          break
        case 'archived':
          this.handleFilterArchive()
          break
        case 'deleted':
          this.handleFilterDelete()
          break
      }
    },
    handleFilterCreate() {
      this.queryForm.keyword = null
      this.queryForm.status = 1
      this.$nextTick(() => {
        this.applyFilters()
      })
    },
    handleFilterArchive() {
      this.queryForm.keyword = null
      this.queryForm.status = 3
      this.$nextTick(() => {
        this.applyFilters()
      })
    },
    handleFilterDelete() {
      this.queryForm.keyword = null
      this.queryForm.status = 4
      this.$nextTick(() => {
        this.applyFilters()
      })
    },
    async handleDeleteAlbumId(row) {
      this.$baseConfirm('确认删除吗？', null, async () => {
        try {
          const { msg } = await DeleteRules(row.albumid)
          this.$baseMessage(msg, 'success')
        } catch (error) {
          console.error('Error deleting album:', error)
          this.$baseMessage('删除失败', 'error')
        } finally {
          await this.fetchGrayData()
        }
      })
    },
    async handleDeleteUri(row) {
      this.$baseConfirm('确认删除吗？', null, async () => {
        try {
          const { msg } = await DeleteUriRules(row.id)
          this.$baseMessage(msg, 'success')
        } catch (error) {
          console.error('Error deleting URI rule:', error)
          this.$baseMessage('删除失败', 'error')
        } finally {
          await this.fetchUriData()
        }
      })
    },
    async fetchData() {
      try {
        this.listLoading = true
        const queryForm = { pageNo: 1, pageSize: 10000 }
        const { data } = await getEnvs(queryForm)
        this.allEnvData = data
        this.queryForm.status = 2
        this.$nextTick(() => {
          this.applyFilters()
        })
        return data
      } catch (error) {
        console.error('Error fetching data:', error)
        this.$baseMessage('数据获取失败', 'error')
      } finally {
        this.listLoading = false
      }
    },
    async getUserList() {
      try {
        const queryUser = { pageNo: 1, pageSize: 10000 }
        const { data: userData } = await getList(queryUser)
        this.applicantDisplayNameMap = userData.reduce((map, user) => {
          map[user.username] = user.displayname
          return map
        }, {})
        this.allUserList = userData
        return userData
      } catch (error) {
        console.error('数据加载失败:', error)
        this.$baseMessage('数据加载失败', 'error')
      } finally {
        this.listLoading = false
      }
    },
    async getLogsList() {
      const { data: envLogs } = await EnvLogs()
      this.activities = envLogs ? envLogs : []
      return this.activities
    },
    // URI规则相关方法
    handleUriEdit(row) {
      if (row && row.id) {
        this.$refs['envgrayuriedit'].showEdit(row)
      } else {
        this.$refs['envgrayuriedit'].showEdit()
      }
    },
    handleUriDragStart(row) {
      this.draggedUri = row
    },
    handleUriDrop(targetRow) {
      if (this.draggedUri) {
        const updatedData = {
          pre_uuid: targetRow.pre_uuid,
          applicant: this.draggedUri.applicant,
          memo: this.draggedUri.memo,
          uri: this.draggedUri.uri,
        }
        this.updateUriEnvironment(updatedData, this.draggedUri.id)
        this.draggedUri = null
      }
    },
    async updateUriEnvironment(updatedData, ruleId) {
      try {
        const { msg } = await PutUriRules(updatedData, ruleId)
        this.$baseMessage(msg, 'success')
        this.$emit('fetch-data-uri')
      } catch (error) {
        console.error('Error updating URI rule:', error)
        this.$baseMessage('更新失败', 'error')
      } finally {
        await this.fetchUriData()
      }
    },
    async loadDataAndFormat() {
      try {
        await Promise.all([
          this.getUserList(),
          this.fetchData(),
          this.fetchGrayData(),
          this.fetchUriData(),
          this.getLogsList(),
        ])
        this.grayTableList = this.formattedGrayRuleList()
      } catch (error) {
        console.error('数据加载失败:', error)
        this.$baseMessage('数据加载失败', 'error')
      } finally {
        this.listLoading = false
        this.grayTableLoading = false
        this.uriTableLoading = false
        this.isDataLoaded = true
      }
    },
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'pre_uuid') {
        const preUuid = row.pre_uuid
        const previousRow = this.grayTableList[rowIndex - 1]
        if (rowIndex === 0 || preUuid !== previousRow.pre_uuid) {
          let rowSpan = this.grayTableList.slice(rowIndex).findIndex(item => item.pre_uuid !== preUuid)
          rowSpan = rowSpan === -1 ? this.grayTableList.length - rowIndex : rowSpan
          return [rowSpan, 1]
        } else {
          return [0, 0]
        }
      }
      return [1, 1]
    },
    generateSequentialColor(index) {
      return this.grayColors[index % this.grayColors.length]
    },
    rowClassName({ row }) {
      const currentEnv = row.pre_uuid?.trim()
      let colorIndex = this.colorIndexes[currentEnv]
      if (colorIndex === undefined) {
        colorIndex = Object.keys(this.colorIndexes).length
        this.colorIndexes[currentEnv] = colorIndex
      }
      const colorClass = this.generateSequentialColor(colorIndex)
      return `row-bg-${colorClass.slice(1)}`
    },
  },
}
</script>

<style lang="scss" scoped>
.premain-container {
  padding: 0 !important;
  margin: 0 !important;
  background: #f5f7f8 !important;

  ::v-deep {
    .el-alert {
      padding: $base-padding;

      &--info.is-light {
        min-height: 82px;
        padding: $base-padding;
        margin-bottom: 15px;
        color: #909399;
        background-color: $base-color-white;
        border: 1px solid #ebeef5;
      }
    }

    /* 定义两种不同的背景颜色 */
    .gray-table {
      .el-table {
        .el-table__header {
          th {
            font-size: 14px;
            font-weight: 500;
          }
        }

        .el-table__body {
          td {
            font-size: 12px;
            padding: 8px 0;

            .el-link {
              font-size: 14px;
            }

            .el-tag {
              font-size: 12px;
            }

            .el-button {
              font-size: 12px;
            }
          }
        }

        .row-bg-e1e4e6 {
          background: #e1e4e6;
        }

        .row-bg-ddebf9 {
          background: #ddebf9;
        }

        .row-bg-c9f4ff {
          background: #c9f4ff;
        }

        .row-bg-0DCFDC {
          background: rgba(13, 207, 220, 0.61);
        }

        .el-table__cell {
          border-bottom: 0px solid rgba(194, 195, 200, 0.45);
        }
      }
    }

    .el-card {
      &.latest-dynamics {
        height: 580px;

        .el-card__body {
          padding: 10px;
        }

        .el-card__header {
          padding: 10px;

          .card-header {
            padding: 0 5px 0 0;
            font-size: 14px;
          }
        }
      }

      .scroll-content {
        height: 600px;
        overflow: auto;
        position: relative;
        display: flex;

        .dynamic-info {
          width: 100%;

          .el-timeline {
            padding-left: 5px;

            .el-timeline-item {
              padding-bottom: 15px;

              .el-timeline-item__timestamp {
                font-size: 11px;
                color: #909399;
              }

              .block-span {
                font-size: 12px;
                line-height: -1px;
                margin-bottom: 2px;
              }
              .activity-line {
                font-size: 12px;
                line-height: 1;
                margin-bottom: 2px;
              }
            }
          }

          .seamless-scroll__item {
            width: auto;
          }
        }
      }
    }
  }

  .bottom {
    padding-top: 20px;
    margin-top: 5px;
    color: #595959;
    text-align: left;
    border-top: 1px solid $base-border-color;
  }

  .table {
    width: 100%;
    color: #666;
    border-collapse: collapse;
    background-color: #fff;

    td {
      position: relative;
      min-height: 20px;
      padding: 9px 15px;
      font-size: 14px;
      line-height: 20px;
      border: 1px solid #e6e6e6;

      &:nth-child(odd) {
        width: 20%;
        text-align: right;
        background-color: #f7f7f7;
      }
    }
  }

  .icon-panel {
    height: 117px;
    text-align: center;
    cursor: pointer;

    svg {
      font-size: 40px;
    }

    p {
      margin-top: 10px;
    }
  }

  .bottom-btn {
    button {
      margin: 5px 10px 15px 0;
    }
  }

  .operation-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
  }

  .operation-buttons .el-button {
    margin: 0 10px;
  }

  .block-span {
    display: block;
  }
}

::v-deep {
  .even-row {
    background-color: #fff7e7;
  }

  .odd-row {
  }
}

.draggable {
  cursor: grab;
}

.droppable {
  border: 2px dashed transparent;
}

.droppable:hover {
  border-color: #3f9eff;
}

::v-deep {
  .custom-steps {
    width: 100%;
    padding: 5px 0px 0px 0px;
    margin-bottom: -5px;
    background-color: $base-color-white;
    border-radius: 4px;

    .el-steps {
      width: 100%;
      display: flex;
      align-items: flex-start;
    }

    .el-step {
      padding: 0 10px;

      &:nth-child(-n + 2) {
        flex: 0 0 100px !important;
        margin-right: 10px !important;
      }

      &:nth-child(n + 3) {
        flex: 1 !important;
        margin-right: 0 !important;
      }

      .el-step__description {
        margin-top: 10px;
        padding: 0 5px;
        font-size: 12px;
        word-break: break-word;
        line-height: 1.4;

        b {
          font-weight: 600;
          color: #409eff;
        }

        br {
          margin-bottom: 5px;
          content: '';
          display: block;
        }

        a {
          white-space: nowrap;
          display: inline-block;
        }
      }

      .el-step__line {
        margin: 0 -50%;
        height: 0px;
      }
    }
  }
}
