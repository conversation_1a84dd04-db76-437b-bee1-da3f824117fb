# StatisticsCards 组件【事件告警】集成实现

## 概述

本文档记录了在 StatisticsCards.vue 组件中集成【事件告警】卡片的实现过程，使其与 ActiveAlerts.vue 组件保持一致的功能和样式。

## 实现背景

### 需求描述
- 在 StatisticsCards.vue 组件中添加【事件告警】卡片
- 放置在【总告警数、严重、警告、已恢复】的后面
- 采用与 ActiveAlerts.vue 相同的水平分布垂直布局
- 按照【默认的时间或选择的时间】进行查询返回

### 技术要求
- 保持与现有卡片的样式一致性
- 支持时间范围参数传递
- 智能显示"当天"标签
- 与 HistoryAlerts.vue 组件完美集成

## 技术实现

### 1. 组件结构调整

#### 模板更新
```html
<template>
  <div class="statistics-cards">
    <el-row :gutter="20">
      <el-col v-for="(item, index) in statisticsItems" :key="index" :span="item.span || 4">
        <el-card
          shadow="hover"
          :body-style="{ padding: '15px' }"
          class="stat-card"
        >
          <div class="stat-content">
            <div class="stat-icon" :class="item.iconClass">
              <i :class="item.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-title">
                {{ item.title }}
                <el-tag 
                  v-if="item.showTag" 
                  class="stat-hint" 
                  effect="dark" 
                  type="warning" 
                  size="small"
                >
                  {{ item.tagText }}
                </el-tag>
              </div>
              <div class="stat-value">{{ item.value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
```

#### 关键变更
- 动态 `span` 属性：支持不同卡片宽度
- 条件标签显示：`v-if="item.showTag"`
- 标签样式：与 ActiveAlerts.vue 保持一致

### 2. Props 扩展

```javascript
props: {
  data: {
    type: Object,
    required: true,
    default: () => ({
      statistics: {
        status_stats: {},
        severity_stats: {},
        source_stats: {},
      },
      total_all: 0,
    }),
  },
  timeRange: {
    type: Array,
    default: () => null,
  },
  timeRangeType: {
    type: String,
    default: 'default',
  },
}
```

#### 新增属性
- `timeRange`: 时间范围数组，用于传递给事件统计接口
- `timeRangeType`: 时间范围类型，用于判断是否显示"当天"标签

### 3. 数据管理

```javascript
data() {
  return {
    eventStatistics: 0,
    eventLoading: false,
  }
}
```

#### 状态管理
- `eventStatistics`: 存储事件告警统计数据
- `eventLoading`: 控制加载状态

### 4. 计算属性优化

```javascript
computed: {
  statisticsItems() {
    const { statistics, total_all } = this.data
    const severityStats = statistics?.severity_stats || {}
    const statusStats = statistics?.status_stats || {}

    const total = total_all || 0
    const critical = severityStats.critical || 0
    const warning = severityStats.warning || 0
    const resolved = statusStats.OK || 0

    // 判断是否显示"当天"标签
    const isDefaultTimeRange = !this.timeRange || this.timeRangeType === 'default'

    return [
      {
        title: '总告警数',
        value: total,
        icon: 'el-icon-warning',
        iconClass: 'total',
        span: 4,
      },
      {
        title: '严重',
        value: critical,
        icon: 'el-icon-error',
        iconClass: 'critical',
        span: 4,
      },
      {
        title: '警告',
        value: warning,
        icon: 'el-icon-warning',
        iconClass: 'warning',
        span: 4,
      },
      {
        title: '已恢复',
        value: resolved,
        icon: 'el-icon-success',
        iconClass: 'resolved',
        span: 4,
      },
      {
        title: '事件告警',
        value: this.eventStatistics,
        icon: 'el-icon-document',
        iconClass: 'events',
        span: 4,
        showTag: isDefaultTimeRange,
        tagText: '当天',
      },
    ]
  },
}
```

#### 核心逻辑
- 动态计算是否显示"当天"标签
- 统一的卡片配置结构
- 事件告警卡片放在最后

### 5. 数据获取方法

```javascript
async fetchEventStatistics() {
  try {
    this.eventLoading = true
    
    let params = null
    // 如果有时间范围参数，则传递给接口
    if (this.timeRange && this.timeRange.length === 2) {
      params = {
        start_time: this.timeRange[0],
        end_time: this.timeRange[1]
      }
      console.log('StatisticsCards: 使用自定义时间参数', params)
    } else {
      console.log('StatisticsCards: 获取当天数据')
    }

    const response = await getEventStatistics(params)
    
    if (response.status === 'success' && response.data) {
      this.eventStatistics = response.data.total_events || 0
      console.log('StatisticsCards: 事件统计数据更新', this.eventStatistics)
    } else {
      console.warn('StatisticsCards: 获取事件统计数据失败', response.message)
      this.eventStatistics = 0
    }
  } catch (error) {
    console.error('StatisticsCards: 获取事件统计失败', error)
    this.eventStatistics = 0
  } finally {
    this.eventLoading = false
  }
}
```

#### 智能参数处理
- 有时间范围：传递 `start_time` 和 `end_time`
- 无时间范围：获取当天数据
- 完善的错误处理机制

### 6. 生命周期管理

```javascript
watch: {
  timeRange: {
    handler() {
      this.fetchEventStatistics()
    },
    deep: true,
  },
},
mounted() {
  this.fetchEventStatistics()
}
```

#### 响应式更新
- 监听时间范围变化
- 组件挂载时初始化数据

### 7. 样式扩展

```scss
.stat-icon {
  // 现有样式...
  
  &.events {
    background-color: #909399;
  }
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  min-height: 20px;
}

.stat-hint {
  font-size: 11px;
  font-weight: 300;
  margin-left: 4px;
  vertical-align: middle;
  height: 16px;
  line-height: 14px;
}
```

#### 样式特点
- 事件告警图标：灰色背景 (#909399)
- 标题布局：flex 布局，确保对齐
- 标签样式：与 ActiveAlerts.vue 保持一致

## 父组件集成

### HistoryAlerts.vue 修改

```html
<statistics-cards
  :data="{
    statistics: chartData.statistics,
    total_all: chartData.total_all || 0,
  }"
  :time-range="timeRange"
  :time-range-type="timeRangeType"
/>
```

#### 参数传递
- `time-range`: 传递当前选择的时间范围
- `time-range-type`: 传递时间范围类型

## 功能特性

### 1. 智能标签显示
- **默认时间范围**: 显示"当天"标签
- **自定义时间范围**: 隐藏标签
- **动态判断**: 基于 `timeRange` 和 `timeRangeType` 参数

### 2. 时间范围支持
- **有时间参数**: 调用接口并传递时间范围
- **无时间参数**: 获取当天数据
- **响应式更新**: 时间范围变化时自动刷新

### 3. 布局一致性
- **5个卡片**: 总告警数、严重、警告、已恢复、事件告警
- **等宽布局**: 每个卡片占 4/24 宽度
- **水平分布**: 单行显示所有卡片

### 4. 样式统一
- **图标样式**: 与其他卡片保持一致
- **标签样式**: 与 ActiveAlerts.vue 完全相同
- **悬停效果**: 统一的交互反馈

## 技术优势

### 1. 代码复用
- 复用 `getEventStatistics` API 函数
- 复用 dayjs 时间处理逻辑
- 复用样式和交互模式

### 2. 响应式设计
- 自动监听时间范围变化
- 智能的参数传递机制
- 完善的错误处理

### 3. 用户体验
- 一致的视觉风格
- 清晰的数据范围提示
- 流畅的交互响应

## 相关文件

- `src/views/monitoring/alertcenter/components/StatisticsCards.vue` - 主要组件
- `src/views/monitoring/alertcenter/components/HistoryAlerts.vue` - 父组件
- `src/api/monitoring.js` - API 函数
- `docs/前端组件/AlterCenter/ActiveAlerts-事件告警优化总结.md` - 相关文档

## 布局优化

### 2025-08-04 更新：Flex 布局优化

#### 优化背景
- 原有的 `el-row` 和 `el-col` 布局在响应式设计上存在局限性
- 为了与 ActiveAlerts.vue 组件保持一致的视觉效果和用户体验
- 提升在不同屏幕尺寸下的显示效果

#### 布局变更

**修改前（Grid 布局）**:
```html
<div class="statistics-cards">
  <el-row :gutter="20">
    <el-col v-for="(item, index) in statisticsItems" :key="index" :span="item.span || 4">
      <el-card>...</el-card>
    </el-col>
  </el-row>
</div>
```

**修改后（Flex 布局）**:
```html
<div class="statistics-cards">
  <div class="stats-container">
    <div v-for="(item, index) in statisticsItems" :key="index" class="stat-card-wrapper">
      <el-card>...</el-card>
    </div>
  </div>
</div>
```

#### 样式优化

**主容器样式**:
```scss
.stats-container {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;

  @media (max-width: 1200px) {
    flex-wrap: wrap;
    gap: 16px;
  }

  @media (max-width: 992px) {
    gap: 12px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 12px;
  }
}
```

**卡片包装器样式**:
```scss
.stat-card-wrapper {
  flex: 1;
  min-width: 0;

  @media (max-width: 1200px) and (min-width: 993px) {
    flex: 0 0 calc(33.333% - 14px);
  }

  @media (max-width: 992px) and (min-width: 769px) {
    flex: 0 0 calc(50% - 6px);
  }

  @media (max-width: 768px) {
    flex: none;
  }
}
```

#### 响应式设计

- **大屏幕 (>1200px)**: 5个卡片水平均匀分布
- **中等屏幕 (993px-1200px)**: 3个卡片一行，2个卡片一行
- **小屏幕 (769px-992px)**: 2个卡片一行
- **移动端 (<768px)**: 垂直堆叠，每行一个卡片

#### 视觉效果优化

- **图标尺寸**: 从 48px 调整为 40px，与 ActiveAlerts.vue 一致
- **图标圆角**: 从圆形改为 8px 圆角矩形
- **字体大小**: 标题 13px，数值 22px
- **颜色调整**: 标题颜色 #8c8c8c，数值颜色 #262626
- **悬停效果**: 轻微上移和阴影效果

## 更新记录

- **2025-08-04**: 初始版本，实现事件告警卡片集成
- **2025-08-04**: 布局优化，改为 Flex 布局，与 ActiveAlerts.vue 保持一致
- **作者**: AI Assistant
- **审核**: 待定
