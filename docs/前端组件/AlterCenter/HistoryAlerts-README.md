# HistoryAlerts 历史告警组件文档

![状态](https://img.shields.io/badge/状态-活跃-brightgreen) ![版本](https://img.shields.io/badge/版本-1.0.0-blue) ![技术栈](https://img.shields.io/badge/Vue-2.x-green) ![UI框架](https://img.shields.io/badge/ElementUI-2.x-blue)

## 组件概述

HistoryAlerts 组件用于展示已处理或已恢复的历史告警记录，提供查询、筛选和统计功能。

## API 接口规范

### 1. 获取历史告警列表

```http
GET /api/alerts/history
```

#### 请求参数

```typescript
interface HistoryQueryParams {
  page: number;           // 当前页码
  page_size: number;      // 每页数量
  start_time: string;     // 开始时间
  end_time: string;       // 结束时间
  severity?: string;      // 告警级别
  source?: string;        // 告警来源
  status?: string;        // 告警状态
  recovery_type?: string; // 恢复类型（自动/手动）
}
```

#### 响应数据结构

```typescript
interface HistoryAlertsResponse {
  alerts: HistoryAlert[];
  pagination: Pagination;
  statistics: HistoryStatistics;
}

interface HistoryAlert {
  fingerprint: string;    // 告警唯一标识
  summary: string;        // 告警概要
  description: string;    // 告警描述
  source: string;         // 告警来源
  severity: string;       // 告警级别
  status: string;         // 告警状态
  start_time: string;     // 开始时间
  end_time: string;       // 结束时间
  recovery_type: string;  // 恢复类型
  recovery_user?: string; // 恢复操作人
  duration: number;       // 持续时间（秒）
  labels: Record<string, string>;       // 标签
  annotations: Record<string, string>;   // 注解
}

interface HistoryStatistics {
  total: number;          // 总告警数
  auto_recovered: number; // 自动恢复数
  manual_recovered: number; // 手动恢复数
  by_severity: {
    critical: number;     // 严重告警数
    warning: number;      // 警告数
  };
}
```

## 组件结构

```mermaid
graph TD
  C[HistoryAlerts] --> I[时间筛选]
  C --> J[状态筛选]
  I --> O[日期选择器]
  J --> P[筛选下拉]
```

## 组件功能

### 统计信息

- 总告警数统计
- 自动恢复/手动恢复统计
- 各级别告警统计

### 筛选功能

- 时间范围选择（支持快速选择和自定义范围）
- 告警级别筛选
- 告警来源筛选
- 恢复类型筛选

### 数据展示

- 分页表格展示
- 持续时间展示
- 恢复信息展示

## 表格列定义

| 列名 | 说明 | 排序 | 筛选 |
|------|------|------|------|
| start_time | 开始时间 | ✓ | - |
| end_time | 结束时间 | ✓ | - |
| duration | 持续时间 | ✓ | - |
| source | 告警来源 | - | ✓ |
| severity | 告警级别 | - | ✓ |
| summary | 告警概要 | - | - |
| recovery_type | 恢复类型 | - | ✓ |
| recovery_user | 恢复操作人 | - | - |

## 事件通信

| 事件名 | 参数 | 说明 |
|--------|------|------|
| show-detail | alert: HistoryAlert | 显示历史告警详情 |
| export-data | filters: HistoryQueryParams | 导出筛选数据 |

## 数据导出

支持将筛选后的历史告警数据导出为以下格式：

- Excel (.xlsx)
- CSV
- PDF

## 性能优化

- 实现了表格虚拟滚动
- 大数据量分页处理
- 后端聚合统计
