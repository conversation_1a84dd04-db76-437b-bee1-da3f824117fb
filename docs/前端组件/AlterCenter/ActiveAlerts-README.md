# ActiveAlerts 组件文档

![状态](https://img.shields.io/badge/状态-活跃-brightgreen) ![版本](https://img.shields.io/badge/版本-1.0.0-blue) ![技术栈](https://img.shields.io/badge/Vue-2.x-green) ![UI框架](https://img.shields.io/badge/ElementUI-2.x-blue)

## 功能概述

ActiveAlerts组件用于展示当前活跃的告警信息，主要功能包括：

- 实时统计展示三种级别告警数量（严重、警告、警报）
- 支持按告警级别、状态（告警中/无数据）、来源进行筛选
- 告警卡片交互效果（鼠标悬停高亮）
- 告警列表分页展示与详情查看

## 核心功能

```mermaid
graph LR
  A[数据获取] --> B[统计卡片]
  A --> C[筛选表单]
  A --> D[告警列表]
  B --> E[鼠标悬停交互]
  C --> F[级别筛选]
  C --> G[状态筛选]
  C --> H[来源筛选]
```

## API 接口规范

### 1. 获取实时告警列表

```http
GET /api/alerts/active
```

#### 请求参数

```typescript
interface QueryParams {
  page: number;           // 当前页码
  page_size: number;      // 每页数量
  start_time: string;     // 开始时间
  end_time: string;       // 结束时间
  severity?: string;      // 告警级别
  source?: string;        // 告警来源
  status?: string;        // 告警状态
}
```

#### 响应数据结构

```typescript
interface ActiveAlertsResponse {
  alerts: Alert[];
  pagination: Pagination;
  statistics: AlertStatistics;
}

interface Alert {
  fingerprint: string;    // 告警唯一标识
  summary: string;        // 告警概要
  description: string;    // 告警描述
  source: string;         // 告警来源
  severity: string;       // 告警级别
  status: string;         // 告警状态
  start_time: string;     // 开始时间
  account_id?: string;    // 账号ID（可选）
  labels: Record<string, string>;       // 标签
  annotations: Record<string, string>;   // 注解
}

interface Pagination {
  total_records: number;  // 总记录数
  total_pages: number;    // 总页数
  current_page: number;   // 当前页
  page_size: number;      // 每页大小
}

interface AlertStatistics {
  severity_stats: {
    critical: number;     // 严重告警数
    warning: number;      // 警告数
    resolved: number;     // 已恢复数
  };
}
```

### 2. 获取告警来源统计

```http
GET /api/alerts/sources
```

#### 请求参数

```typescript
interface SourceQueryParams {
  start_time: string;     // 开始时间
  end_time: string;       // 结束时间
  status: string;         // 告警状态
}
```

#### 响应数据结构

```typescript
interface SourcesResponse {
  sources: SourceStat[];
}

interface SourceStat {
  source: string;         // 来源名称
  count: number;          // 告警数量
}
```

### 3. 恢复告警

```http
POST /api/alerts/recover
```

#### 请求参数

```typescript
interface RecoverRequest {
  fingerprint: string;    // 告警指纹
}
```

## 组件功能

### 统计卡片

- 严重告警统计
- 警告统计
- 总告警数统计

### 筛选功能

- 时间范围选择
- 告警级别筛选
- 告警来源筛选
- 告警状态筛选

### 数据展示

- 分页表格展示
- 告警来源饼图统计

## 事件通信

| 事件名 | 参数 | 说明 |
|--------|------|------|
| show-detail | alert: Alert | 显示告警详情 |
| refresh | - | 刷新数据 |

## 自动刷新

- 默认每5分钟自动刷新一次数据
- 告警来源统计每1分钟更新一次
