
# NamespaceStats 命名空间统计组件文档

![状态](https://img.shields.io/badge/状态-活跃-brightgreen) ![版本](https://img.shields.io/badge/版本-1.0.0-blue) ![技术栈](https://img.shields.io/badge/Vue-2.x-green) ![UI框架](https://img.shields.io/badge/ElementUI-2.x-blue) ![图表库](https://img.shields.io/badge/ECharts-5.x-orange)

## 功能概述

NamespaceStats组件用于展示按命名空间/产品分组的告警统计信息，主要功能包括：

- 按命名空间/产品分组展示告警数据
- 支持按时间范围筛选数据
- 展示各命名空间的告警级别分布（严重、警告）
- 展示各命名空间的告警来源分布
- 使用半圆仪表盘图表直观展示来源占比

## 核心功能

```mermaid
graph LR
  A[数据获取] --> B[时间范围筛选]
  A --> C[命名空间列表]
  C --> D[告警级别分布]
  C --> E[来源分布图表]
  E --> F[半圆仪表盘]
  D --> G[进度条展示]
```

## API 接口规范

### 1. 获取命名空间统计数据

```http
GET /api/monitoring/namespace-stats
```

#### 请求参数

```typescript
interface QueryParams {
  start_time: string;     // 开始时间
  end_time: string;       // 结束时间
}
```

#### 响应数据结构

```typescript
interface NamespaceStatsResponse {
  status: string;         // 响应状态
  message: string;        // 响应消息
  data: {
    stats: NamespaceStat[];  // 命名空间统计数据
  }
}

interface NamespaceStat {
  name: string;           // 命名空间/产品名称
  nameId: string;         // 命名空间唯一标识
  total: number;          // 总告警数
  source_count: number;   // 来源数量
  severity_items: {       // 告警级别统计
    critical: number;     // 严重告警数
    warning: number;      // 警告数
  };
  sources_items: Record<string, number>; // 各来源告警数量
}
```

## 组件功能

### 时间范围筛选

- 支持选择自定义时间范围
- 默认展示最近7天数据
- 时间选择器使用ElementUI的日期时间范围选择器

### 命名空间列表

- 表格形式展示各命名空间/产品的告警统计
- 支持按总数排序
- 点击行可触发命名空间详情查看

### 告警级别分布

- 使用进度条直观展示各级别告警占比
- 区分严重告警和警告告警
- 显示各级别具体数量

### 来源分布图表

- 使用ECharts半圆仪表盘展示各来源占比
- 根据来源类型使用不同颜色区分
- 中心显示总告警数
- 右侧列表展示各来源详细数量

## 图表实现

### 半圆仪表盘图表

- 使用ECharts的Gauge图表类型
- 每个来源使用不同颜色区分
- 根据来源占比计算角度范围
- 中心显示总数

### 来源颜色映射

- PunkSong/custom: 绿色 (#67C23A)
- pinpoint: 蓝色 (#409EFF)
- rocketmq: 橙色 (#E6A23C)
- 微购科技2: 灰色 (#909399)
- 其他来源: 红色 (#F34D37)

## 事件通信

| 事件名 | 参数 | 说明 |
|--------|------|------|
| namespace-click | row: NamespaceStat | 点击命名空间行触发 |

## 性能优化

- 图表实例管理与清理
- 排序后延迟重绘图表
- 防止重复初始化图表实例
- 组件销毁时清理资源

## 样式特点

- 紧凑的表格布局
- 进度条展示告警级别分布
- 半圆仪表盘展示来源分布
- 使用点状标记区分不同来源

## 使用示例

```vue
<template>
  <div>
    <namespace-stats @namespace-click="handleNamespaceClick" />
  </div>
</template>

<script>
import NamespaceStats from '@/views/monitoring/alertcenter/components/NamespaceStats.vue'

export default {
  components: {
    NamespaceStats
  },
  methods: {
    handleNamespaceClick(namespace) {
      console.log('点击了命名空间:', namespace.name)
      // 处理命名空间点击事件
    }
  }
}
</script>
```

## 注意事项

- 确保ECharts相关组件已正确注册
- 时间范围选择需要有效的开始和结束时间
- 图表容器需要有足够的高度和宽度
- 组件销毁时需要清理图表实例和定时器

        当前模型请求量过大，请求排队约 4 位，请稍候或切换至其他模型问答体验更流畅。
