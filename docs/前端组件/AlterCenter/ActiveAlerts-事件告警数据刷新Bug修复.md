# ActiveAlerts 组件【事件告警】数据刷新 Bug 修复

## 概述

本文档记录了 ActiveAlerts.vue 组件中【事件告警】统计卡片数据刷新的两个 bug 修复过程。

## Bug 描述

### Bug 1: 重置按钮问题
- **问题**: 点击【重置】按钮时，【事件告警】统计卡片的数据没有刷新更新
- **影响**: 用户重置后看到的事件告警数据可能不是当天的最新数据
- **场景**: 用户在任何时间范围下点击重置按钮

### Bug 2: 时间选择器切换问题
- **问题**: 从【自定义时间选择】切换回预设时间范围时，【事件告警】统计卡片的数据没有刷新
- **影响**: 用户从自定义时间切换到预设时间后，看到的仍是自定义时间范围的数据
- **场景**: 自定义时间 → 15分钟/30分钟/1小时/6小时

## 根因分析

### 原有逻辑问题

**fetchEventStatistics() 方法的问题逻辑**:
```javascript
async fetchEventStatistics() {
  // 只有在自定义时间范围时才调用接口
  if (this.timeRangeType !== 'custom') {
    console.log('事件告警：非自定义时间范围，跳过接口调用，保持当天数据')
    return  // 直接返回，不刷新数据
  }
  // ... 接口调用逻辑
}
```

### 数据流分析

#### Bug 1 数据流
```
用户点击重置 → resetQuery() → handleQuery() → fetchEventStatistics()
                                                      ↓
                                            timeRangeType !== 'custom'
                                                      ↓
                                                直接 return，不刷新
```

#### Bug 2 数据流
```
自定义时间 → 预设时间 → handleTimeRangeChange() → handleQuery() → fetchEventStatistics()
                                                                        ↓
                                                              timeRangeType !== 'custom'
                                                                        ↓
                                                                  直接 return，不刷新
```

## 修复方案

### 1. 修改 fetchEventStatistics 方法

**修复前**:
```javascript
async fetchEventStatistics() {
  if (this.timeRangeType !== 'custom') {
    return  // 直接返回，不刷新
  }
  // ... 接口调用
}
```

**修复后**:
```javascript
async fetchEventStatistics(forceRefresh = false) {
  // 如果是非自定义时间范围且不是强制刷新，则跳过
  if (this.timeRangeType !== 'custom' && !forceRefresh) {
    console.log('事件告警：非自定义时间范围，跳过接口调用，保持当天数据')
    return
  }

  let params = null
  if (this.timeRangeType === 'custom' && this.timeRange && this.timeRange.length === 2) {
    params = {
      start_time: this.timeRange[0],
      end_time: this.timeRange[1]
    }
    console.log("事件告警：自定义时间范围，使用时间参数", params)
  } else {
    console.log("事件告警：获取当天数据")
  }

  const response = await getEventStatistics(params)
  // ... 处理响应
}
```

**关键改进**:
- 添加 `forceRefresh` 参数，支持强制刷新
- 区分自定义时间和当天数据的参数传递
- 完善日志输出，便于调试

### 2. 修改 resetQuery 方法

**修复前**:
```javascript
async resetQuery() {
  this.queryParams = { severity: '', status: 'ALARM', source: '' }
  this.timeRangeType = '1h'
  this.handleTimeRangeChange('1h')
  this.currentPage = 1
  await this.handleQuery()
}
```

**修复后**:
```javascript
async resetQuery() {
  this.queryParams = { severity: '', status: 'ALARM', source: '' }
  this.timeRangeType = '1h'
  this.handleTimeRangeChange('1h')
  this.currentPage = 1
  await this.handleQuery()
  // 重置时强制刷新事件统计数据
  await this.fetchEventStatistics(true)
}
```

**关键改进**:
- 在重置后强制调用 `fetchEventStatistics(true)`
- 确保重置时获取最新的当天数据

### 3. 修改 handleTimeRangeChange 方法

**修复前**:
```javascript
handleTimeRangeChange(type) {
  // ... 时间范围处理逻辑
  
  if (type !== 'custom') {
    this.currentPage = 1
    this.handleQuery()
  }
}
```

**修复后**:
```javascript
handleTimeRangeChange(type) {
  // 记录之前的时间范围类型，用于判断是否从自定义切换到预设
  const previousTimeRangeType = this.timeRangeType

  // ... 时间范围处理逻辑

  if (type !== 'custom') {
    this.currentPage = 1
    this.handleQuery()
    
    // 如果从自定义时间切换到预设时间，强制刷新事件统计
    if (previousTimeRangeType === 'custom') {
      console.log('事件告警：从自定义时间切换到预设时间，强制刷新当天数据')
      this.fetchEventStatistics(true)
    }
  }
}
```

**关键改进**:
- 记录切换前的时间范围类型
- 检测从自定义时间到预设时间的切换
- 在切换时强制刷新事件统计数据

## 修复后的数据流

### Bug 1 修复后数据流
```
用户点击重置 → resetQuery() → handleQuery() → fetchEventStatistics(true)
                                                      ↓
                                              forceRefresh = true
                                                      ↓
                                              调用接口获取当天数据
```

### Bug 2 修复后数据流
```
自定义时间 → 预设时间 → handleTimeRangeChange() → 检测到切换 → fetchEventStatistics(true)
                                                              ↓
                                                      forceRefresh = true
                                                              ↓
                                                      调用接口获取当天数据
```

## 技术特点

### 1. 智能刷新机制
- **正常情况**: 预设时间范围下不调用接口，保持性能
- **特殊情况**: 重置和切换时强制刷新，确保数据准确性

### 2. 参数传递优化
- **自定义时间**: 传递 `start_time` 和 `end_time` 参数
- **预设时间**: 不传参数，获取当天数据
- **强制刷新**: 通过 `forceRefresh` 参数控制

### 3. 状态检测
- 通过 `previousTimeRangeType` 检测时间范围类型的变化
- 精确识别从自定义到预设的切换场景

## 测试验证

### 测试场景 1: 重置按钮
1. 选择任意时间范围
2. 点击【重置】按钮
3. 验证【事件告警】数据是否更新为当天数据

### 测试场景 2: 时间切换
1. 选择【自定义】时间范围，设置特定日期
2. 切换到【15分钟】、【30分钟】、【1小时】或【6小时】
3. 验证【事件告警】数据是否更新为当天数据

### 测试场景 3: 正常使用
1. 在预设时间范围之间切换
2. 验证不会产生不必要的接口调用
3. 确保性能不受影响

## 相关文件

- `src/views/monitoring/alertcenter/components/ActiveAlerts.vue` - 主要修复文件
- `src/api/monitoring.js` - 事件统计 API
- `docs/前端组件/AlterCenter/ActiveAlerts-事件告警优化总结.md` - 相关文档

## 更新记录

- **2025-08-04**: Bug 修复，解决重置和时间切换时的数据刷新问题
- **作者**: AI Assistant
- **审核**: 待定
