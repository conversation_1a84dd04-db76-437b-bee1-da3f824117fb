
# 告警中心组件文档

<img alt="Static Badge" src="https://img.shields.io/badge/%E7%8A%B6%E6%80%81-%E6%B4%BB%E8%B7%83-brightgreen">

<img alt="Static Badge" src="https://img.shields.io/badge/%E7%89%88%E6%9C%AC-3.3.2-blue">

<img alt="Static Badge" src="https://img.shields.io/badge/Vue-2.x-green">

<img alt="Static Badge" src="https://img.shields.io/badge/ElementUI-2.x-blue">

<img alt="Static Badge" src="https://img.shields.io/badge/echarts-5.1.2-green">

## 目录结构

```

alertcenter/
├── components/
│   ├── ActiveAlerts.vue      # 实时告警组件
│   ├── AlertDetail.vue       # 告警详情组件
│   ├── HealthBoard.vue       # 健康看板组件
│   ├── HistoryAlerts.vue     # 历史告警组件
│   └── NamespaceStats.vue    # 命名空间统计组件
└── index.vue                 # 主入口文件

```

## 组件说明

### 1. ActiveAlerts (实时告警组件)

#### 核心功能

- 实时告警数据展示
- 告警统计卡片（严重/警告/总数）
- 多维度筛选（级别/状态/来源/时间范围）
- 告警列表分页展示

#### API 接口

```http
GET /api/alerts/active       # 获取实时告警列表
GET /api/alerts/sources      # 获取告警来源统计
POST /api/alerts/recover     # 恢复告警
```

#### 事件通信

| 事件名 | 参数 | 说明 |
|--------|------|------|
| show-detail | alert: Alert | 显示告警详情 |
| refresh | - | 刷新数据 |

---

### 2. NamespaceStats (命名空间统计组件)

#### 核心功能

- 命名空间告警统计表格
- 告警级别分布（进度条展示）
- 来源分布（半圆仪表盘展示）
- 支持时间范围筛选

#### 组件功能

- 表格形式展示各命名空间/产品的告警统计
- 支持按总数排序
- 点击行可触发命名空间详情查看

#### 事件通信

| 事件名 | 参数 | 说明 |
|--------|------|------|
| namespace-click | row: NamespaceStat | 点击命名空间行触发 |

---

### 3. HealthBoard (健康看板组件)

#### 核心功能

- 健康状态统计卡片（正常/告警/数据不足/未配置）
- 告警来源健康状态表格
- 支持时间范围筛选
- 自动刷新功能（默认1分钟）

---

### 4. HistoryAlerts (历史告警组件)

#### 核心功能

- 历史告警数据展示
- 时间范围筛选（1天/3天/7天/自定义）
- 告警趋势图表
- 多维度筛选（级别/状态/来源）

---

### 5. AlertDetail (告警详情组件)

#### 核心功能

- 告警详细信息展示
- 支持标签和注解展示
- 根据来源类型显示不同详情格式
- 支持外部链接跳转

## 通用特性

- 使用 Element UI 组件库
- 响应式设计
- 自动刷新机制
- 性能优化（图表实例管理、延迟重绘等）

## 使用说明

1. 通过 `index.vue` 中的侧边栏导航切换不同功能模块
2. 各组件支持的时间范围筛选：
   - 实时告警：15分钟/30分钟/1小时/6小时/自定义
   - 历史告警：1天/3天/7天/自定义
   - 命名空间统计：自定义时间范围
3. 点击表格行或标签可查看详情或进行筛选

## 版本信息

这个 README.md 文档整合了所有组件的核心功能、API接口、事件通信和使用说明，可以作为项目的完整文档使用。文档结构清晰，包含了目录结构、组件说明和通用特性等关键信息。
