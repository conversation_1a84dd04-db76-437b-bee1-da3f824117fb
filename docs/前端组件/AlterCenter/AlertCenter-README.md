# AlertCenter 告警中心组件文档

![状态](https://img.shields.io/badge/状态-活跃-brightgreen) ![版本](https://img.shields.io/badge/版本-1.0.0-blue) ![技术栈](https://img.shields.io/badge/Vue-2.x-green) ![UI框架](https://img.shields.io/badge/ElementUI-2.x-blue)

## 组件概述

AlertCenter 是告警中心的主组件，整合了实时告警、历史告警、健康看板和告警配置等功能模块。组件提供统一的导航和布局，实现告警信息的集中管理和展示。

## 组件结构

```mermaid
graph TD
  A[AlertCenter] --> B[ActiveAlerts]
  A --> C[HistoryAlerts]
  A --> D[HealthBoard]
  A --> E[AlertDetail]
  B --> F[统计卡片]
  B --> G[筛选表单]
  B --> H[告警列表]
  C --> I[时间筛选]
  C --> J[状态筛选]
  D --> K[健康指标]
  D --> L[告警趋势]
```

## 流程图

```mermaid
flowchart TD
  A[用户访问] --> B{选择功能模块}
  B -->|实时告警| C[加载ActiveAlerts]
  B -->|历史告警| D[加载HistoryAlerts]
  B -->|健康看板| E[加载HealthBoard]
  C --> F[获取实时告警数据]
  D --> G[获取历史告警数据]
  E --> H[获取健康指标数据]
```

## 组件间调用关系

```mermaid
graph LR
  AlertCenter -->|事件:show-detail| AlertDetail
  AlertCenter -->|事件:refresh| ActiveAlerts
  AlertCenter -->|事件:refresh| HistoryAlerts
  AlertCenter -->|事件:refresh| HealthBoard
  ActiveAlerts -->|事件:export-data| AlertCenter
  HistoryAlerts -->|事件:export-data| AlertCenter
```

## 前后端交互图

```mermaid
sequenceDiagram
  participant 前端
  participant 后端
  前端->>后端: GET /alertscenter/api/v1/active
  后端-->>前端: 实时告警列表
  前端->>后端: GET /alertscenter/api/v1/trend
  后端-->>前端: 历史分析数据
  前端->>后端: GET /alertscenter/api/v1/healthboard
  后端-->>前端: 健康指标数据
  
```

## 功能模块

### 导航菜单

- 健康看板：系统整体健康状态
- 实时告警：当前活跃的告警信息
- 历史告警：已处理或已恢复的告警记录
- 告警配置：
  - MQ告警配置
  - PP告警配置
  - 错误码告警配置
  - HTTP告警配置

### 路由参数

组件支持通过 URL 参数进行导航和过滤：

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| tab | string | 当前激活的标签页 | `active`, `history`, `health` |
| source | string | 告警来源过滤 | `prometheus`, `grafana` |
| severity | string | 告警级别过滤 | `critical`, `warning` |

### 组件通信

组件间通过事件进行通信：

| 事件名 | 参数 | 说明 |
|--------|------|------|
| show-detail | alert: AlertInfo | 显示告警详情抽屉 |
| refresh | - | 刷新当前视图数据 |

## 自动刷新

- 默认每5分钟自动刷新一次数据
- 告警来源统计每1分钟更新一次
- 实时告警：当前活跃的告警信息
- 历史告警：已处理或已恢复的告警记录
- 告警配置：
  - MQ告警配置
  - PP告警配置
  - 错误码告警配置
  - HTTP告警配置

### 路由参数

组件支持通过 URL 参数进行导航和过滤：

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| tab | string | 当前激活的标签页 | `active`, `history`, `health` |
| source | string | 告警来源过滤 | `prometheus`, `grafana` |
| severity | string | 告警级别过滤 | `critical`, `warning` |

### 组件通信

组件间通过事件进行通信：

| 事件名 | 参数 | 说明 |
|--------|------|------|
| show-detail | alert: AlertInfo | 显示告警详情抽屉 |
| refresh | - | 刷新当前视图数据 |

## 数据类型定义

```typescript
interface AlertInfo {
  id: string;
  fingerprint: string;
  summary: string;
  description: string;
  source: string;
  severity: 'critical' | 'warning' | 'info';
  status: 'ALARM' | 'OK' | 'NO_DATA';
  start_time: string;
  end_time?: string;
  labels: Record<string, string>;
  annotations: Record<string, string>;
}
```

## 状态管理

组件维护以下状态：

| 状态名 | 类型 | 说明 |
|--------|------|------|
| activeTab | string | 当前激活的标签页 |
| isCollapse | boolean | 侧边栏是否折叠 |
| drawerVisible | boolean | 详情抽屉是否可见 |
| selectedAlert | AlertInfo | 当前选中的告警信息 |
