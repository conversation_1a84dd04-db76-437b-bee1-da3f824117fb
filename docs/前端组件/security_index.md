# 安全中心组件文档 (security/index.vue)

## 组件概述

- **组件名称**: SecurityIndex
- **文件路径**: `src/views/security/index.vue`
- **功能描述**: 安全中心主界面，包含概览、基础配置、反爬中心和流控中心等功能模块

## 组件结构

```vue
<template>
  <div class="alert-center-container">
    <!-- 侧边导航栏 -->
    <div class="side-nav">
      <!-- 导航标题和折叠按钮 -->
      <div class="nav-header">...</div>
      
      <!-- 菜单项 -->
      <el-menu>
        <el-menu-item index="health">健康看板</el-menu-item>
        <el-menu-item index="active">实时告警</el-menu-item>
        <el-menu-item index="history">历史告警</el-menu-item>
        <el-menu-item index="namespace">命名空间</el-menu-item>
        
        <!-- 告警配置子菜单 -->
        <el-submenu index="config">
          <el-menu-item index="mq">MQ告警</el-menu-item>
          <el-menu-item index="pinpoint">PP告警</el-menu-item>
          <el-menu-item index="errcode">错误码告警</el-menu-item>
          <el-menu-item index="http">HTTP告警</el-menu-item>
        </el-submenu>
      </el-menu>
    </div>

    <!-- 主内容区 -->
    <div class="content-container">
      <!-- 动态组件根据activeTab显示不同内容 -->
      <component :is="currentComponent"/>
      
      <!-- 告警详情抽屉 -->
      <el-drawer :visible.sync="drawerVisible">
        <alert-detail :alert="selectedAlert"/>
      </el-drawer>
    </div>
  </div>
</template>
```

## 数据属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| activeTab | String | 'health' | 当前激活的标签页 |
| isCollapse | Boolean | true | 侧边栏是否折叠 |
| drawerVisible | Boolean | false | 详情抽屉是否可见 |
| drawerTitle | String | '告警详情' | 详情抽屉标题 |
| selectedAlert | Object | null | 当前选中的告警详情 |
| currentConfigComponent | Component | null | 当前配置组件 |

## 方法说明

1. **handleSelect(tab)**  
   - 功能: 处理菜单项选择
   - 参数: tab - 选中的菜单项key
   - 逻辑: 根据tab值设置当前激活的组件

2. **handleDetail(row)**  
   - 功能: 显示告警详情
   - 参数: row - 告警数据对象
   - 逻辑: 设置选中的告警数据并打开详情抽屉

3. **handleDrawerClose(done)**  
   - 功能: 关闭详情抽屉
   - 参数: done - 关闭回调函数
   - 逻辑: 重置选中的告警数据并关闭抽屉

4. **handleNamespaceClick(namespace)**  
   - 功能: 处理命名空间点击事件
   - 参数: namespace - 命名空间名称
   - 逻辑: 调用子组件方法显示命名空间详情

## 样式说明

组件使用了以下主要样式类:

- `.alert-center-container`: 主容器样式
- `.side-nav`: 侧边导航栏样式
- `.content-container`: 主内容区样式
- `.custom-menu-icon`: 自定义菜单图标样式

组件使用了以下主要样式类:

- `.security-center-container`: 主容器样式
- `.side-nav`: 侧边导航栏样式
- `.content-container`: 主内容区样式
- `.custom-menu-icon`: 自定义菜单图标样式

## 依赖组件

- ActiveAlerts.vue - 实时告警组件
- HistoryAlerts.vue - 历史告警组件
- AlertDetail.vue - 告警详情组件
- HealthBoard.vue - 健康看板组件
- NamespaceStats.vue - 命名空间统计组件

## 数据流向

1. 用户通过侧边栏菜单选择功能模块
2. SecurityIndex动态加载对应子组件
3. 子组件通过API获取数据并渲染
4. 用户操作通过事件总线传递回父组件
5. 数据变更通过API保存到后端

## 核心功能模块

1. **DashBoard**  
   - 安全概览数据展示
   - 攻击IP和拦截类型统计

2. **BaseIndex**  
   - 基础配置管理
   - 配置项增删改查

3. **AntiBotWhitelist**  
   - IP白名单管理
   - 反爬IP配置

4. **AntiRestrictedUris**  
   - 核心API保护
   - URI访问限制配置

5. **FlowctrlConfig**  
   - 流控规则配置
   - 请求限制设置

6. **FlowctrlRule**  
   - 流控条件管理
   - 流控值设置

## 文件结构图

```plaintext
security/
├── index.vue                  # 主入口文件
├── dashboard/                 # 概览相关
│   └── dashboard.vue
├── base/                      # 基础配置
│   └── index.vue
├── antibot/                   # 反爬中心
│   ├── whitelist.vue          # IP白名单
│   └── restricteduris.vue     # 核心API
└── flowcontrol/               # 流控中心
    └── flowctrlConfig.vue     # 流控配置
```

## 组件架构图

```mermaid
graph TD
    A[index.vue] --> B[侧边导航栏]
    A --> C[主内容区]
    B --> D[菜单项]
    B --> E[子菜单]
    C --> F[动态组件]
    C --> G[详情抽屉]
    
    D --> H["概览(1-1)"]
    D --> I["基础配置(1-2)"]
    E --> J["反爬中心(2)"]
    E --> K["流控中心(3)"]
    J --> L["IP白名单(2-1)"]
    J --> M["核心API(2-2)"]
    K --> N["流控配置(3-1)"]
```

## 组件流程图

```mermaid
sequenceDiagram
    participant User
    participant index.vue
    participant SubComponent
    
    User->>index.vue: 点击菜单项
    index.vue->>index.vue: handleClick(index)
    alt 一级菜单
        index.vue->>SubComponent: 加载对应组件
    else 二级菜单
        index.vue->>SubComponent: 加载子菜单对应组件
    end
    SubComponent-->>index.vue: 渲染内容
    index.vue-->>User: 显示对应内容

```

```mermaid
stateDiagram-v2
    [*] --> 概览
    概览 --> 基础配置
    基础配置 --> 反爬中心
    反爬中心 --> IP白名单
    反爬中心 --> 核心API
    反爬中心 --> 流控中心
    流控中心 --> 流控配置
    流控配置 --> [*]
```

## 组件结构图

```mermaid
graph TD
    A[SecurityIndex] --> B[DashBoard]
    A --> C[BaseIndex]
    A --> D[AntiBotWhitelist]
    A --> E[AntiRestrictedUris]
    A --> F[FlowctrlConfig]
    A --> G[FlowctrlRule]
    C --> H[baseEditor]
    D --> I[ipWhileEditor]
    D --> J[ipAddressEditor]
    E --> K[restrictedUrisEditor]
    F --> L[configEditor]
    G --> M[ruleEditor]
```

## 注意事项

1. 组件使用了动态导入方式加载配置子组件
2. 侧边栏支持折叠/展开功能
3. 详情抽屉使用了destroy-on-close属性确保每次打开都是新实例
4. 组件监听了路由变化以支持通过URL参数初始化状态
