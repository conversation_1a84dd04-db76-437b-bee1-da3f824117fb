# IP黑名单组件文档

## 组件结构
IP黑名单组件主要包含两个部分：
1. 主界面（index.vue）：负责展示IP黑名单列表、搜索过滤和分页功能
2. 编辑对话框（ipBlackEditor.vue）：用于新增和编辑IP黑名单信息

## 主要功能
### 1. 数据展示
- 展示IP地址、状态、来源、备注、创建时间等字段
- 支持状态筛选（有效/无效/永不过期）
- 支持关键字搜索（IP地址/来源/备注）
- 支持分页显示数据

### 2. 状态管理
- 不同状态以不同颜色标签展示
- 提供状态筛选下拉菜单
- 支持清除状态筛选

### 3. 编辑功能
- 新增IP黑名单
- 编辑现有IP黑名单
- 表单验证确保数据完整性

## 组件交互
- 主界面与编辑组件通过ref进行通信
- 编辑完成后通过$emit事件触发数据刷新
- 使用watch监听查询条件变化自动刷新数据

## 技术实现
### API接口
- fetchIpBlacklist：获取IP黑名单数据
- saveIpBlacklist：保存IP黑名单信息

### 样式说明
- 使用SCSS进行样式定义
- 包含响应式布局设计
- 自定义表格样式和分页样式

## 组件使用
```vue
<template>
  <div class="security-dashboard">
    <Edit ref="edit" @fetch-data="refresh" />
    <!-- 其他界面元素 -->
  </div>
</template>

<script>
import Edit from './component/ipBlackEditor.vue'
export default {
  components: {
    Edit
  }
}
</script>
```

## 注意事项
- 确保API接口正确配置
- 注意组件间的通信方式
- 状态变更时需要处理相关业务逻辑