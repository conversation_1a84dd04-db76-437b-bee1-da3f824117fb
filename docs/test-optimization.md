# ActiveAlerts.vue 优化测试清单

## 优化内容总结

### 1. 数据展示优化 ✅
- [x] 移除状态列（仅展示实时告警）
- [x] 移除来源列
- [x] 添加告警时间列（显示相对时间，悬停显示完整时间）
- [x] 添加major级别支持

### 2. 视觉与交互优化 ✅
- [x] 使用颜色编码图标替代文字显示告警级别
- [x] 增强"查看详情"按钮的视觉突出性
- [x] 将"恢复"操作移至详情抽屉中

### 3. 统计图表优化 ✅
- [x] 将右侧饼图改为按告警对象进行聚合统计
- [x] 创建新的 AlertObjectPie 组件

### 4. 筛选器优化 ✅
- [x] 移除告警状态筛选（固定为ALARM）
- [x] 移除告警来源筛选
- [x] 保留告警级别筛选，添加major级别

### 5. 统计卡片优化 ✅
- [x] 调整为4列布局（严重、重要、警告、总数）
- [x] 添加major级别的统计和样式

## 测试步骤

### 手动测试
1. **页面加载测试**
   - 访问告警中心 -> 实时告警页面
   - 检查页面是否正常加载
   - 检查统计卡片是否显示4个级别

2. **表格显示测试**
   - 检查表格列：级别（图标）、告警时间、对象、告警名称、操作
   - 检查级别列是否显示彩色图标而非文字
   - 检查告警时间列是否显示标准时间格式（YYYY-MM-DD HH:mm:ss）
   - 检查对象列是否显示告警对象信息
   - 检查操作列是否只有"查看详情"按钮

3. **筛选功能测试**
   - 检查筛选器是否只有告警级别和时间范围
   - 测试告警级别筛选（全部、严重、重要、警告）
   - 测试时间范围筛选

4. **详情抽屉测试**
   - 点击"查看详情"按钮
   - 检查详情抽屉是否正常打开
   - 检查是否有"恢复告警"按钮（仅当状态为ALARM时）
   - 测试恢复功能

5. **统计图表测试**
   - 检查右侧饼图标题是否为"告警对象统计 (活跃)"
   - 检查饼图是否按告警对象聚合显示
   - 检查数据是否与表格数据一致

### 功能验证
- [ ] 级别图标正确显示（critical: 红色错误图标，major: 橙色警告图标，warning: 黄色警告图标）
- [ ] 时间显示正确（标准时间格式：YYYY-MM-DD HH:mm:ss）
- [ ] 对象列正确显示告警对象信息
- [ ] 详情按钮样式突出
- [ ] 恢复功能在详情抽屉中正常工作
- [ ] 告警对象统计正确聚合
- [ ] 筛选功能正常
- [ ] 自动刷新功能正常

## 文件修改清单

### 新增文件
- `src/views/monitoring/alertcenter/components/AlertObjectPie.vue`

### 修改文件
- `src/views/monitoring/alertcenter/components/ActiveAlerts.vue`
- `src/views/monitoring/alertcenter/components/AlertDetail.vue`
- `src/views/monitoring/alertcenter/index.vue`

## 兼容性说明
- 保持了原有的 emit 事件接口
- 保持了原有的数据结构兼容性
- 添加了对major级别的支持
- 恢复功能从表格移至详情抽屉，但功能保持不变
