# 健康看板V3接口文档

## 接口基本信息
- **接口名称**: 健康看板V3数据接口
- **请求方式**: GET
- **接口路径**: `/alertscenter/api/v1/healthboardV3`
- **返回格式**: JSON

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_time | string | 否 | 开始时间，格式：YYYY-MM-DD HH:MM:SS |
| end_time | string | 否 | 结束时间，格式：YYYY-MM-DD HH:MM:SS |

## 响应数据结构

### 成功响应
```json
{
  "status": "success",
  "data": {
    "statistics": {
      "ALARM": 289,
      "NO_DATA": 15,
      "OK": 228
    },
    "tableData": [
      // 表格数据项
    ]
  }
}
```

### 字段说明

#### 公共字段
- `status`: 请求状态，"success"表示成功
- `data`: 响应数据主体

#### data.statistics
告警统计信息：
- `ALARM`: 告警数量
- `NO_DATA`: 无数据数量  
- `OK`: 正常数量

#### data.tableData
表格数据数组，包含两种类型的数据项：

1. **分类项** (isCategory=true)
```json
{
  "isCategory": true,
  "name": "custom",
  "account_id": "PunkSong" // 仅腾讯云数据存在
}
```

2. **告警项**
```json
{
  "name": "cmgo-4awg7nl7_0-node-primary|实例名:文搜-新|IP:***********;***********;***********:27017",
  "now": "",
  "rss": true,
  "severitys": {
    "critical": "true",
    "success": "",
    "warning": ""
  }
}
```

#### 告警项字段说明
- `name`: 告警名称/对象
- `rss`: 固定为true
- `now`: 当前时间（暂未使用）
- `severitys`: 告警严重程度
  - `critical`: 严重告警
  - `warning`: 警告告警  
  - `success`: 正常状态

## 示例数据解析

### 腾讯云数据示例
```json
{
  "account_id": "PunkSong",
  "isCategory": true,
  "name": "mongodb-核心数据库"
},
{
  "name": "cmgo-4awg7nl7_0-node-primary|实例名:文搜-新|IP:***********;***********;***********:27017",
  "now": "",
  "rss": true,
  "severitys": {
    "critical": "true",
    "success": "",
    "warning": ""
  }
}
```
- 腾讯云数据会包含`account_id`字段表示所属账号
- 告警名称格式通常为"实例ID|实例名|IP"

### 普通告警示例
```json
{
  "isCategory": true,
  "name": "custom"
},
{
  "name": "CDN监控",
  "now": "",
  "rss": true,
  "severitys": {
    "critical": "",
    "success": "",
    "warning": "true"
  }
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 参数错误 |
| 500 | 服务器内部错误 |

## 注意事项
1. 腾讯云数据会有`account_id`字段标识所属账号
2. `severitys`中只会有一个状态为"true"
3. 分类项(isCategory=true)用于分组显示告警项
4. 时间参数不传时默认返回最近15分钟数据