# get_event_statistics API 接口文档

## 概述

`get_event_statistics` 是告警中心的事件统计接口，用于获取当天状态为 `NO_DATA` 的告警事件汇总统计信息。

## 接口信息

- **路由**: `/alertscenter/api/v1/events`
- **方法**: `GET`
- **功能**: 查询当天（今日 00:00:00 到 23:59:59）所有 `NO_DATA` 状态的告警事件统计
- **实现位置**: `blueprint/moniterCenter/alerts_center.py`

## 请求参数

该接口不需要任何请求参数，自动查询当天的数据。

### 请求示例

```bash
GET /alertscenter/api/v1/events
```

```javascript
// JavaScript 示例
fetch('/alertscenter/api/v1/events', {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => console.log(data));
```

```python
# Python 示例
import requests

response = requests.get('/alertscenter/api/v1/events')
data = response.json()
print(data)
```

## 响应格式

### 成功响应

**HTTP 状态码**: `200 OK`

```json
{
  "status": "success",
  "data": {
    "total_events": 15,
    "date_range": {
      "start": "2025-08-01 00:00:00",
      "end": "2025-08-01 23:59:59"
    },
    "statistics": {
      "source_stats": {
        "tencent_cloud": 8,
        "custom": 5,
        "alertmanager": 2
      },
      "severity_stats": {
        "warning": 10,
        "critical": 3,
        "info": 2
      }
    }
  },
  "message": "获取事件统计数据成功"
}
```

### 错误响应

**HTTP 状态码**: `500 Internal Server Error`

```json
{
  "status": "error",
  "message": "获取事件统计数据失败: [具体错误信息]"
}
```

## 响应字段说明

### 成功响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `status` | string | 响应状态，成功时为 "success" |
| `data` | object | 统计数据对象 |
| `data.total_events` | integer | 当天 NO_DATA 状态的告警事件总数 |
| `data.date_range` | object | 查询的日期时间范围 |
| `data.date_range.start` | string | 查询开始时间 (YYYY-MM-DD HH:MM:SS) |
| `data.date_range.end` | string | 查询结束时间 (YYYY-MM-DD HH:MM:SS) |
| `data.statistics` | object | 详细统计信息 |
| `data.statistics.source_stats` | object | 按告警来源分组的统计 |
| `data.statistics.severity_stats` | object | 按严重程度分组的统计 |
| `message` | string | 响应消息 |

### 告警来源 (source_stats) 可能的值

| 来源 | 说明 |
|------|------|
| `tencent_cloud` | 腾讯云告警 |
| `custom` | 自定义告警 |
| `alertmanager` | Prometheus Alertmanager |
| `pinpoint` | Pinpoint 监控 |
| `rocketmq` | RocketMQ 监控 |

### 严重程度 (severity_stats) 可能的值

| 严重程度 | 说明 |
|----------|------|
| `critical` | 严重 |
| `warning` | 警告 |
| `info` | 信息 |
| `minor` | 轻微 |

## 业务逻辑

### 查询条件

1. **时间范围**: 当天 00:00:00 到 23:59:59
2. **状态过滤**: 仅查询 `status = 'NO_DATA'` 的告警
3. **数据源**: `mon_center_alerts` 表

### 统计维度

1. **总数统计**: 符合条件的告警事件总数
2. **来源统计**: 按 `source` 字段分组统计
3. **严重程度统计**: 按 `severity` 字段分组统计

### 时间处理

- 使用 `datetime.now()` 获取当前时间
- 自动构造当天的开始时间 (00:00:00) 和结束时间 (23:59:59)
- 时间格式: `YYYY-MM-DD HH:MM:SS`

## 数据库查询

### 主查询

```sql
SELECT COUNT(*) as total_events
FROM mon_center_alerts 
WHERE starts_at >= '2025-08-01 00:00:00' 
  AND starts_at <= '2025-08-01 23:59:59'
  AND status = 'NO_DATA';
```

### 来源统计查询

```sql
SELECT source, COUNT(id) as count
FROM mon_center_alerts 
WHERE starts_at >= '2025-08-01 00:00:00' 
  AND starts_at <= '2025-08-01 23:59:59'
  AND status = 'NO_DATA'
GROUP BY source;
```

### 严重程度统计查询

```sql
SELECT severity, COUNT(id) as count
FROM mon_center_alerts 
WHERE starts_at >= '2025-08-01 00:00:00' 
  AND starts_at <= '2025-08-01 23:59:59'
  AND status = 'NO_DATA'
GROUP BY severity;
```

## 错误处理

### 异常捕获

- 所有数据库查询异常都会被捕获
- 错误信息会记录到应用日志中
- 返回标准的错误响应格式

### 日志记录

```python
# 成功日志
current_app.logger.info(f"获取事件统计数据: {today_start} - {today_end}")

# 错误日志
current_app.logger.error(f"获取事件统计数据失败: {str(e)}")
```

## 性能考虑

### 查询优化

1. **索引建议**: 在 `starts_at` 和 `status` 字段上建立复合索引
2. **时间范围**: 限制在当天范围内，减少查询数据量
3. **分组查询**: 使用数据库的 GROUP BY 功能，避免应用层聚合

### 缓存策略

- 可考虑对当天数据进行短时间缓存（如 5-10 分钟）
- 减少频繁查询对数据库的压力

## 使用场景

1. **监控大屏**: 显示当天数据异常事件统计
2. **运维报表**: 生成日常运维数据报告
3. **告警分析**: 分析不同来源和严重程度的数据异常分布
4. **系统健康度**: 评估监控系统的数据完整性

## 相关接口

- `/alertscenter/api/v1/active` - 获取实时告警状态
- `/alertscenter/api/v1/history` - 获取历史告警数据
- `/alertscenter/api/v1/trend` - 获取告警趋势分析
- `/alertscenter/api/v1/panel` - 获取健康面板数据

## 版本信息

- **创建时间**: 2025-08-01
- **版本**: v1.0.0
- **维护者**: wanglh
- **最后更新**: 2025-08-01
