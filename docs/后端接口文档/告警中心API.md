
# 告警中心 API 文档

## 基础信息

- 基础路径: `/alertscenter/api/v1`
- 响应格式: JSON

## 接口列表

### 1. 获取实时告警状态

获取指定时间范围内**触发且当前仍未恢复**的告警列表。默认查询最近**6小时内触发的未恢复告警**。

#### 请求信息

- 接口路径：`/active`
- 请求方法：`GET`
- 请求参数：

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 否 | 告警触发时间的开始时间，格式：YYYY-MM-DD HH:mm:ss，默认为当前时间前6小时 | 2025-01-01 00:00:00 |
| end_time | string | 否 | 告警触发时间的结束时间，格式：YYYY-MM-DD HH:mm:ss，默认为当前时间 | 2025-01-01 23:59:59 |
| severity | string | 否 | 告警级别筛选 | critical |
| page | integer | 否 | 页码，默认1 | 1 |
| per_page | integer | 否 | 每页条数，默认20 | 20 |

#### 响应信息

```json
{
  "data": {
    "alerts": [
      {
        "account_id": null,
        "acknowledged_at": null,
        "acknowledged_by": null,
        "alarm_status": "OK",
        "annotations": {
          "description": "\n\t告警时间：\t2025-04-14 08:14:14\n\t恢复时间：\t2025-04-14 08:34:47\n",
          "extra_info": {}
        },
        "created_at": "2025-04-14 08:34:47",
        "description": "【告警基本信息】\n告警标题: 告警恢复: 【警告】 错误码基础\n告警对象: 错误码告警\n告警级别: resolved\n\n【告警详情】\n描述: \n\t告警时间：\t2025-04-14 08:14:14\n\t恢复时间：\t2025-04-14 08:34:47\n\n\n【业务信息】\n业务ID: \n通知方式: Unknown",
        "ends_at": null,
        "external_id": null,
        "fingerprint": "ed54efb8572694c3",
        "id": 2649,
        "labels": {
          "alert_object": "错误码告警",
          "business_ids": "",
          "notify_methods": null,
          "severity": "resolved"
        },
        "severity": "resolved",
        "source": "custom",
        "starts_at": "2025-04-14 08:34:47",
        "status": "ACTIVE",
        "summary": "告警恢复: 【警告】 错误码基础",
        "updated_at": "2025-04-14 11:28:34"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 1,
      "total_pages": 1546,
      "total_records": 1546
    },
    "statistics": {
      "severity_stats": {
        "critical": 104,
        "resolved": 95,
        "warning": 1347
      }
    },
    "total": 1546
  },
  "status": "success"
}
```

#### 响应字段说明

alerts 字段详细说明：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | integer | 告警ID |
| source | string | 告警来源 |
| fingerprint | string | 告警指纹 |
| external_id | string | 外部系统ID |
| status | string | 告警状态，实时告警固定为"ACTIVE" |
| severity | string | 告警级别 |
| summary | string | 告警概要 |
| description | string | 告警详细描述 |
| labels | object | 告警标签 |
| annotations | object | 告警注释 |
| starts_at | string | 告警开始时间 |
| ends_at | string | 告警结束时间，实时告警为null |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| acknowledged_by | string | 确认人 |
| acknowledged_at | string | 确认时间 |
| alarm_status | integer | 告警状态码 |
| account_id | string | 账户ID |

statistics 字段说明：
- severity_stats: 告警级别统计信息

pagination 字段说明：
- current_page: 当前页码
- per_page: 每页记录数
- total_pages: 总页数
- total_records: 总记录数

#### 错误响应

```json
{
    "status": "error",
    "message": "错误信息描述"
}
```

常见错误：
1. 页码超出范围错误：
```json
{
    "status": "error",
    "message": "页码超出范围。最大页码为: 5，当前请求页码为: 6"
}
```

2. 服务器错误：
```json
{
    "status": "error",
    "message": "获取实时告警失败: ..."
}
```


### 2. 获取历史告警数据

获取指定时间范围内的所有告警记录。时间范围基于告警的最后更新时间（包括创建、确认、恢复等状态变更）。

#### 请求信息

- 接口路径：`/history`
- 请求方法：`GET`
- 请求参数：

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 是 | 开始时间，基于告警更新时间，格式：YYYY-MM-DD HH:mm:ss | 2025-01-01 00:00:00 |
| end_time | string | 是 | 结束时间，基于告警更新时间，格式：YYYY-MM-DD HH:mm:ss | 2025-01-01 23:59:59 |
| status | string | 否 | 告警状态筛选（ACTIVE/RESOLVED），ACTIVE 表示未恢复告警，RESOLVED 表示已恢复告警 | ACTIVE |
| severity | string | 否 | 告警级别筛选 | critical |
| source | string | 否 | 告警来源筛选 | pinpoint |
| page | integer | 否 | 页码，默认1 | 1 |
| per_page | integer | 否 | 每页条数，默认20 | 20 |

#### 响应信息

```json
{
  "data": {
    "alerts": [
      {
        "account_id": null,
        "acknowledged_at": null,
        "acknowledged_by": null,
        "alarm_status": "ALARM",
        "annotations": {
          "description": "\n\t告警类型：\t首次告警\n\t告警编码：\t2010001\n\t告警阈值：\t200\n\t当前统计值：\t6647\n\t对比统计值：\t3\n\t变化比例：\t12535.85%\n\t变化阈值：\t1000.0%\n\n",
          "extra_info": {
            "jump_url": "https://ops.szwego.com/#/devmonitor/errcode?keyword=2010001&granularity=10m",
            "持续时间": "0s",
            "涉及接口": "/album/personal/all"
          }
        },
        "created_at": "2025-04-16 17:51:14",
        "description": "【告警基本信息】\n告警标题: 【严重】 错误码波动 (波动性告警)\n告警对象: 错误码告警\n告警级别: major\n\n【告警详情】\n描述: \n\t告警类型：\t首次告警\n\t告警编码：\t2010001\n\t告警阈值：\t200\n\t当前统计值：\t6647\n\t对比统计值：\t3\n\t变化比例：\t12535.85%\n\t变化阈值：\t1000.0%\n\n\n\n【业务信息】\n业务ID: oc_b2fb55ef27390ab4632a42acee5bd399,oc_ec1fd76a4107925696dd7d063afdaf13\n通知方式: Unknown\n\n【额外信息】\n持续时间: 0s\n涉及接口: /album/personal/all\njump_url: https://ops.szwego.com/#/devmonitor/errcode?keyword=2010001&granularity=10m",
        "ends_at": null,
        "external_id": null,
        "fingerprint": "b236fb1cf43b9e5b",
        "id": 3951,
        "labels": {
          "alert_object": "错误码告警",
          "business_ids": "oc_b2fb55ef27390ab4632a42acee5bd399,oc_ec1fd76a4107925696dd7d063afdaf13",
          "notify_methods": null,
          "severity": "major"
        },
        "severity": "warning",
        "source": "custom",
        "starts_at": "2025-04-16 17:51:13",
        "status": "ALARM",
        "summary": "【严重】 错误码波动 (波动性告警)",
        "updated_at": "2025-04-17 09:43:11"
      },
      {
        "account_id": null,
        "acknowledged_at": null,
        "acknowledged_by": null,
        "alarm_status": "ALARM",
        "annotations": {
          "description": "\n\t告警类型：\t首次告警\n\t告警编码：\t1001005\n\t告警阈值：\t300\n\t当前数值：\t316\n\n",
          "extra_info": {
            "jump_url": "https://ops.szwego.com/#/devmonitor/errcode?keyword=1001005&granularity=10m",
            "持续时间": "0s",
            "涉及接口": "/album/personal/all"
          }
        },
        "created_at": "2025-04-16 21:22:50",
        "description": "【告警基本信息】\n告警标题: 【严重】 获取未关注相册数据\n告警对象: 错误码告警\n告警级别: major\n\n【告警详情】\n描述: \n\t告警类型：\t首次告警\n\t告警编码：\t1001005\n\t告警阈值：\t300\n\t当前数值：\t316\n\n\n\n【业务信息】\n业务ID: oc_b2fb55ef27390ab4632a42acee5bd399,oc_ec1fd76a4107925696dd7d063afdaf13\n通知方式: Unknown\n\n【额外信息】\n持续时间: 0s\n涉及接口: /album/personal/all\njump_url: https://ops.szwego.com/#/devmonitor/errcode?keyword=1001005&granularity=10m",
        "ends_at": null,
        "external_id": null,
        "fingerprint": "ecabb73e218813f7",
        "id": 3991,
        "labels": {
          "alert_object": "错误码告警",
          "business_ids": "oc_b2fb55ef27390ab4632a42acee5bd399,oc_ec1fd76a4107925696dd7d063afdaf13",
          "notify_methods": null,
          "severity": "major"
        },
        "severity": "warning",
        "source": "custom",
        "starts_at": "2025-04-16 21:22:49",
        "status": "ALARM",
        "summary": "【严重】 获取未关注相册数据",
        "updated_at": "2025-04-17 09:43:11"
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 2,
      "total_pages": 67,
      "total_records": 133
    },
    "statistics": {
      "severity_stats": {
        "critical": 24,
        "warning": 104
      },
      "source_stats": {
        "custom": 25,
        "pinpoint": 35,
        "tencent_cloud": 73
      },
      "status_stats": {
        "ALARM": 32,
        "NO_DATA": 3,
        "OK": 98
      }
    },
    "total": 133  
  },
  "status": "success",
  "total_all": 133
}
```

#### 响应字段说明

alerts 字段详细说明：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | integer | 告警ID |
| source | string | 告警来源 |
| fingerprint | string | 告警指纹 |
| external_id | string | 外部系统ID |
| status | string | 告警状态 |
| severity | string | 告警级别 |
| summary | string | 告警概要 |
| description | string | 告警详细描述 |
| labels | object | 告警标签 |
| annotations | object | 告警注释 |
| starts_at | string | 告警开始时间 |
| ends_at | string | 告警结束时间 |
| created_at | string | 创建时间 |
| updated_at | string | 更新时间 |
| acknowledged_by | string | 确认人 |
| acknowledged_at | string | 确认时间 |
| alarm_status | string | 告警状态码 |
| account_id | string | 账户ID |

statistics 字段说明：
- status_stats: 告警状态统计
- severity_stats: 告警级别统计
- source_stats: 告警来源统计

pagination 字段说明：
- current_page: 当前页码
- per_page: 每页记录数
- total_pages: 总页数
- total_records: 总记录数

total: 过滤条件后的总数

total_all: 时间范围内的所有告警

### 3. 获取告警来源统计

获取不同来源的**告警数量**统计信息。

#### 请求信息

status != 'OK'，统计未恢复的告警

- 接口路径：`/sources`
- 请求方法：`GET`
- 请求参数：

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 否 | 开始时间，格式：YYYY-MM-DD HH:mm:ss，默认为当天0点 | 2025-01-01 00:00:00 |
| end_time | string | 否 | 结束时间，格式：YYYY-MM-DD HH:mm:ss，默认为当天23:59:59 | 2025-01-01 23:59:59 |

#### 响应信息

```json
{
  "data": {
    "sources": [
      {
        "count": 148,
        "source": "custom"
      },
      {
        "count": 1218,
        "source": "tencent_cloud"
      },
      {
        "count": 65,
        "source": "pinpoint"
      }
    ]
  },
  "status": "success"
}
```

#### 响应字段说明

sources 字段详细说明：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| source | string | 告警来源名称 |
| count | integer | 该来源的未恢复告警数量 |

#### 错误响应

```json
{
    "status": "error",
    "message": "错误信息描述"
}
```

常见错误：
1. 服务器错误：
```json
{
    "status": "error",
    "message": "获取告警来源统计失败: ..."
}
```

#### 说明
- 此接口只统计未恢复（status != 'OK'）的告警数量
- 默认统计当天的告警数据
- 返回的告警数量按照告警来源进行分组统计


### 4. 获取告警趋势分析

获取指定时间范围内的告警趋势统计数据。根据时间跨度自动选择统计粒度（小时/天）。

#### 请求信息

- 接口路径：`/trend`
- 请求方法：`GET`
- 请求参数：

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| start_time | string | 是 | 开始时间，支持多种格式 | 2025-01-01T00:00:00Z |
| end_time | string | 是 | 结束时间，支持多种格式 | 2025-01-01T23:59:59Z |

支持的时间格式：
- ISO 8601格式：`YYYY-MM-DDTHH:MM:SSZ`
- ISO 8601带毫秒：`YYYY-MM-DDTHH:MM:SS.sssZ`
- 标准格式：`YYYY-MM-DD HH:MM:SS`
- 纯日期：`YYYY-MM-DD`

#### 响应信息

```json
{
  "status": "success",
  "data": {
    "interval": "hour",
    "trend": [
      {
        "timestamp": "2025-01-01 00:00:00",
        "total": 10,
        "critical": 2,
        "warning": 5,
        "resolved": 3
      }
    ],
    "summary": {
      "total": 150,
      "critical": 30,
      "warning": 80,
      "resolved": 40
    }
  }
}
```

```json
{
  "data": {
    "interval": "day",
    "summary": {
      "critical": 609,
      "resolved": 696,
      "total": 2813,
      "warning": 2204
    },
    "trend": [
      {
        "critical": 10,
        "resolved": 27,
        "timestamp": "2025-04-10",
        "total": 33,
        "warning": 23
      },
      {
        "critical": 99,
        "resolved": 94,
        "timestamp": "2025-04-11",
        "total": 241,
        "warning": 142
      },
      {
        "critical": 112,
        "resolved": 124,
        "timestamp": "2025-04-12",
        "total": 327,
        "warning": 215
      },
      {
        "critical": 128,
        "resolved": 106,
        "timestamp": "2025-04-13",
        "total": 253,
        "warning": 125
      },
      {
        "critical": 184,
        "resolved": 189,
        "timestamp": "2025-04-14",
        "total": 1699,
        "warning": 1515
      },
      {
        "critical": 52,
        "resolved": 58,
        "timestamp": "2025-04-15",
        "total": 127,
        "warning": 75
      },
      {
        "critical": 12,
        "resolved": 37,
        "timestamp": "2025-04-16",
        "total": 60,
        "warning": 48
      },
      {
        "critical": 12,
        "resolved": 61,
        "timestamp": "2025-04-17",
        "total": 73,
        "warning": 61
      }
    ]
  },
  "status": "success"
}
```

#### 响应字段说明

data 字段详细说明：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| interval | string | 统计粒度，"hour"表示按小时，"day"表示按天 |
| trend | array | 趋势数据数组 |
| trend[].timestamp | string | 时间点 |
| trend[].total | integer | 该时间点的总告警数 |
| trend[].critical | integer | 严重级别告警数 |
| trend[].warning | integer | 警告级别告警数 |
| trend[].resolved | integer | 已恢复告警数 |
| summary | object | 汇总统计信息 |
| summary.total | integer | 时间范围内总告警数 |
| summary.critical | integer | 严重级别总数 |
| summary.warning | integer | 警告级别总数 |
| summary.resolved | integer | 已恢复总数 |

#### 统计规则说明

1. 时间粒度自适应：
   - 时间跨度 ≤ 3天：按小时统计
   - 时间跨度 > 3天：按天统计

2. 告警分类统计：
   - critical：严重级别告警数量
   - warning：警告级别告警数量
   - resolved：已恢复的告警数量（status = 'ok'）

#### 错误响应

1. 参数错误（400）：
```json
{
    "status": "error",
    "message": "开始时间和结束时间为必填参数"
}
```

2. 时间格式错误（400）：
```json
{
    "status": "error",
    "message": "时间格式错误: 不支持的日期格式。支持的格式：YYYY-MM-DDTHH:MM:SS[.fff]Z, YYYY-MM-DD HH:MM:SS, YYYY-MM-DD"
}
```

3. 服务器错误（500）：
```json
{
    "status": "error",
    "message": "获取告警趋势数据失败: ..."
}
```



## 错误响应

当发生错误时，接口将返回以下格式的响应：

```json
{
    "status": "error",
    "message": "错误信息描述"
}
```

常见错误码：

| HTTP状态码 | 说明 |
|------------|------|
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 注意事项

1. 时间参数需要符合格式要求：YYYY-MM-DD HH:mm:ss
2. 实时告警接口默认查询最近6小时的数据
3. 历史告警接口必须提供时间范围
4. 所有时间均使用UTC时间

## 更新记录

- 2025-04-14: 接口拆分
  - 新增 /active 接口用于获取实时告警状态
  - 新增 /history 接口用于获取历史告警数据
