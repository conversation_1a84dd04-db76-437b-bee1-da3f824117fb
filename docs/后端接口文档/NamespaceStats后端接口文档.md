# 告警中心接口文档 - 命名空间/产品告警统计

## 接口说明

- **接口路径**：`/alertscenter/api/v1/namespace/stats`
- **请求方式**：GET
- **接口描述**：根据 namespace 或 product_show_name 获取告警聚合统计信息。

## 请求参数

| 参数名      | 类型   | 是否必填 | 说明           |
| ----------- | ------ | -------- | -------------- |
| start_time  | string | 是       | 开始时间，格式：`YYYY-MM-DD HH:MM:SS` |
| end_time    | string | 是       | 结束时间，格式：`YYYY-MM-DD HH:MM:SS` |

## 返回结果

- **status**：接口调用状态（success/error）
- **data**：统计数据对象
  - **stats**：告警聚合统计列表，每个元素为一个 namespace 或产品的统计项
    - **name**：命名空间/产品名称
    - **severity_items**：各级别告警数量（如 critical、major、warning）
    - **source**：告警来源（如 tencent_cloud、自建监控）
    - **source_count**：来源数量
    - **sources_items**：各来源的告警数量明细
    - **total**：该 namespace/产品下告警总数
  - **total**：所有告警总数

### 返回示例

```
{
  "status": "success",
  "data": {
    "stats": [
      {
        "name": "自建监控",
        "severity_items": {
          "critical": 6,
          "major": 2,
          "warning": 533
        },
        "source": "自建监控",
        "source_count": 3,
        "sources_items": {
          "custom": 478,
          "pinpoint": 62,
          "rocketmq": 1
        },
        "total": 541
      },
      {
        "name": "云数据库-TDSQL—C-MySQL",
        "severity_items": {
          "critical": 0,
          "major": 0,
          "warning": 75
        },
        "source": "tencent_cloud",
        "source_count": 2,
        "sources_items": {
          "PunkSong|37745980": 74,
          "微购科技2|100015637669": 1
        },
        "total": 75
      }
      // ... 其余统计项 ...
    ],
    "total": 1761
  }
}
```

## 字段说明

- **name**：统计分组名称，优先为 namespace，无则为 product_show_name，均无则为“自建监控”
- **severity_items**：各级别告警数量，key 为级别（如 critical、major、warning），value 为数量
- **source**：告警来源类型（如 tencent_cloud、自建监控）
- **source_count**：该分组下来源数量
- **sources_items**：来源明细，key 为来源标识，value 为该来源下告警数量
- **total**：该分组下告警总数

## 错误返回示例

```
{
  "status": "error",
  "message": "开始时间和结束时间为必填参数"
}
```

## 备注
- 该接口适用于监控大盘、健康看板等需要分组统计告警的场景。
- 告警来源（source）为“tencent_cloud”时，sources_items 的 key 通常为“账号名|账号ID”。
- severity_items 的 key 取决于实际告警数据中出现的所有级别。
