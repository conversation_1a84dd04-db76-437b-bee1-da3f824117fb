{"data": {"items": [{"critical_count": 2, "info_count": 0, "items": [{"account_id": null, "alert_details": [{"alertObject": "CDN监控", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "CDN监控", "severities": ["warning"], "warning_count": 11}, {"account_id": null, "alert_details": [{"alertObject": "错误码告警", "severity": "critical", "status": "ALARM"}], "critical_count": 1, "info_count": 0, "name": "错误码告警", "severities": ["major", "warning"], "warning_count": 18}, {"account_id": null, "alert_details": [{"alertObject": "错误码告警升级: 【警告】 错误码基础", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "错误码告警升级: 【警告】 错误码基础", "severities": ["warning"], "warning_count": 2}, {"account_id": null, "alert_details": [{"alertObject": "NginX状态码监控", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "NginX状态码监控", "severities": ["warning"], "warning_count": 184}, {"account_id": null, "alert_details": [{"alertObject": "RPC告警", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "RPC告警", "severities": ["warning"], "warning_count": 18}, {"account_id": null, "alert_details": [{"alertObject": "分支管理告警", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "分支管理告警", "severities": ["warning"], "warning_count": 3}, {"account_id": null, "alert_details": [{"alertObject": "基线分支反向合并通知", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "基线分支反向合并通知", "severities": ["warning"], "warning_count": 8}, {"account_id": null, "alert_details": [{"alertObject": "基线分支合并通知", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "基线分支合并通知", "severities": ["warning"], "warning_count": 2}], "severities": ["warning", "critical", "critical"], "source": "custom", "source_count": 248, "warning_count": 246}, {"critical_count": 33, "info_count": 0, "items": [{"account_id": "PunkSong", "alert_details": [{"alertObject": "cmgo-4awg7nl7_0-node-primary|实例名:文搜-新|IP:***********;***********;***********:27017", "severity": "critical", "status": "ALARM"}, {"alertObject": "cmgo-el79v6uh_0-node-slave1|实例名:customercloud|IP:***********:27017", "severity": "critical", "status": "ALARM"}, {"alertObject": "cmgo-nhkd894f_1-node-primary|实例名:portal|IP:***********:27017", "severity": "critical", "status": "ALARM"}, {"alertObject": "cmgo-76bw99f1_0-node-slave1|实例名:消息库|IP:*************:27017", "severity": "critical", "status": "ALARM"}, {"alertObject": "cmgo-00m3xz4d_0-node-slave0|实例名:支付日志|IP:***********:27017", "severity": "critical", "status": "ALARM"}], "critical_count": 16, "info_count": 0, "name": "mongodb-核心数据库", "severities": ["critical"], "warning_count": 0}, {"account_id": "PunkSong", "alert_details": [{"alertObject": "cmgo-4awg7nl7_0-node-primary|实例名:文搜-新|IP:***********;***********;***********:27017", "severity": "critical", "status": "ALARM"}, {"alertObject": "cmgo-r3ielssh_0-node-primary|实例名:commoditylogs|IP:************:27017", "severity": "critical", "status": "ALARM"}], "critical_count": 3, "info_count": 0, "name": "mongodb节点-后端基础-核心", "severities": ["critical"], "warning_count": 0}, {"account_id": "微购科技2", "alert_details": [{"alertObject": "实例:cynosdbmysql-ins-65kvdtlc|cynosdbmysql-ins-65kvdtlc|集群:cynosdbmysql-8meg7izx|海外独立部署|读写内网地址:***********:3306", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "腾讯-tdsql-c-告警", "severities": ["warning"], "warning_count": 1}, {"account_id": "PunkSong", "alert_details": [{"alertObject": "cmgo-el79v6uh_0-node-slave1|实例名:customercloud|IP:***********:27017", "severity": "critical", "status": "ALARM"}], "critical_count": 9, "info_count": 0, "name": "mongodb节点-客户云-核心", "severities": ["critical"], "warning_count": 0}, {"account_id": "PunkSong", "alert_details": [{"alertObject": "cdb-kvvla3kd", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "mysql-相册云-核心", "severities": ["warning"], "warning_count": 1}, {"account_id": "PunkSong", "alert_details": [{"alertObject": "cdb-kvvla3kd", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "MySQL", "severities": ["warning"], "warning_count": 1}, {"account_id": "微购科技2", "alert_details": [{"alertObject": "cls-4a066ctu|dsc-rcb|dsc-rcb-769df6f9f7-2nzsr", "severity": "warning", "status": "ALARM"}, {"alertObject": "cls-4a066ctu|rcb-cs|rcb-cs-79964b58b4-ct7j7", "severity": "warning", "status": "ALARM"}, {"alertObject": "cls-4a066ctu|dsc-rcb|dsc-rcb-769df6f9f7-l24xw", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "现网Pod告警", "severities": ["warning"], "warning_count": 3}, {"account_id": "PunkSong", "alert_details": [{"alertObject": "cmgo-nhkd894f_1-node-primary|实例名:portal|IP:***********:27017", "severity": "critical", "status": "ALARM"}, {"alertObject": "cmgo-76bw99f1_0-node-slave1|实例名:消息库|IP:*************:27017", "severity": "critical", "status": "ALARM"}], "critical_count": 4, "info_count": 0, "name": "mongodb节点-相册云-核心", "severities": ["critical"], "warning_count": 0}, {"account_id": "PunkSong", "alert_details": [{"alertObject": "cmgo-00m3xz4d_0-node-slave0|实例名:支付日志|IP:***********:27017", "severity": "critical", "status": "ALARM"}], "critical_count": 1, "info_count": 0, "name": "mongodb节点-生意云-核心", "severities": ["critical"], "warning_count": 0}, {"account_id": "微购科技2", "alert_details": [{"alertObject": "cls-05qyxlp2|job|job-644dff6478-2hpp2", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "预发布基线服务告警", "severities": ["warning"], "warning_count": 1}, {"account_id": "微购科技2", "alert_details": [{"alertObject": "cdwdoris-n4z6x5yh|************", "severity": "warning", "status": "ALARM"}], "critical_count": 0, "info_count": 0, "name": "腾讯-<PERSON>", "severities": ["warning"], "warning_count": 1}], "severities": ["critical", "warning"], "source": "tencent_cloud", "source_count": 41, "warning_count": 8}], "statistics": {"ALARM": 289, "NO_DATA": 15, "OK": 228}}, "status": "success"}