# 健康看板 V2 接口文档

## 接口概述

`healthboardV2` 接口是对原有 `healthboard` 接口的增强版本，提供了更详细的告警信息展示。该接口主要用于获取监控中心的健康看板数据，包括各类告警的统计信息和详细内容，并新增了告警对象详情数据。

## 请求方法和路由

- **请求方法**：GET
- **路由**：`/healthboardV2`

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|-------|-----|------|-------|------|
| start_time | String | 否 | 当前时间前15分钟 | 查询开始时间，格式为 "YYYY-MM-DD HH:MM:SS" |
| end_time | String | 否 | 当前时间 | 查询结束时间，格式为 "YYYY-MM-DD HH:MM:SS" |

## 响应数据结构

### 成功响应

```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "source": "string",           // 告警来源，如 "custom", "pinpoint", "rocketmq", "tencent_cloud" 等
        "severities": ["string"],     // 该来源下的告警严重程度列表
        "critical_count": number,     // 严重告警数量
        "warning_count": number,      // 警告告警数量
        "info_count": number,         // 信息告警数量
        "source_count": number,       // 该来源下的总告警数量
        "items": [
          {
            "name": "string",         // 告警名称
            "severities": ["string"], // 该告警的严重程度列表
            "critical_count": number, // 该告警的严重告警数量
            "warning_count": number,  // 该告警的警告告警数量
            "info_count": number,     // 该告警的信息告警数量
            "account_id": "string",   // 账户ID（仅适用于腾讯云告警）
            "alert_details": [        // 告警详情列表（V2新增）
              {
                "alertObject": "string", // 告警对象
                "status": "string",       // 告警状态
                "severity": "string",     // 告警严重程度
              }
            ]
          }
        ]
      }
    ],
    "statistics": {
      "ALARM": number,   // 告警状态的数量
      "OK": number,      // 正常状态的数量
      "NO_DATA": number  // 无数据状态的数量
    }
  }
}
```

### 失败响应

```json
{
  "status": "error",
  "message": "服务器内部错误"
}
```

## 与原 healthboard 接口的区别

1. **默认时间区间**：
   - V2 接口：如果未提供时间参数，默认使用当前时间的前15分钟到当前时间
   - 原接口：必须提供时间参数

2. **告警详情**：
   - V2 接口：在 `items[].items[]` 中增加了 `alert_details` 数组，包含每个告警的对象和状态信息
   - 原接口：不包含告警详情信息

3. **告警对象处理**：
   - V2 接口：根据不同的告警源类型，按照优先级规则获取告警对象，并进行特殊字符处理
   - 原接口：不包含告警对象信息

## 告警对象（alertObject）获取规则

1. **自定义告警（custom）**：
   - 优先从 `labels.alert_object` 获取
   - 如果不存在，则从 `labels.alert_service` 获取
   - 如果仍不存在，则从 `labels.alert_title` 获取

2. **RocketMQ/PinPoint 告警**：
   - 优先从 `labels.appname` 获取
   - 如果不存在，则从 `labels.topic` 获取
   - 如果仍不存在，则从 `labels.alertname` 获取

3. **腾讯云告警（tencent_cloud）**：
   - 优先从 `labels.dim_uInstanceId` 获取
   - 如果不存在，则从 `labels.dim_objName` 获取
   - 如果仍不存在，则从 `labels.dim_unInstanceId` 获取
   - 如果仍不存在，则从 `labels.product_show_name` 获取

4. **备选方案**：
   - 如果以上所有尝试都失败，则使用告警名称（name）作为告警对象

## 特殊字符处理

告警对象（alertObject）会进行以下特殊字符处理：
1. 将括号"()"和逗号","替换为竖线"|"
2. 按照竖线"|"分割成列表并过滤空元素
3. 重新用竖线"|"连接成字符串

## 示例请求和响应

### 请求示例

```
GET /healthboardV2?start_time=2025-05-28 00:00:00&end_time=2025-05-28 23:59:59
```

### 响应示例

```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "critical_count": 0,
        "info_count": 0,
        "items": [
          {
            "account_id": null,
            "alert_details": [
              {
                "alertObject": "api.szwego.com|错误码告警",
                "status": "ALARM",
                "severity": "warning"
              },
              {
                "alertObject": "m.szwego.com|错误码告警",
                "status": "ALARM",
                "severity": "warning"
              }
            ],
            "critical_count": 0,
            "info_count": 0,
            "name": "错误码告警",
            "severities": [
              "warning"
            ],
            "warning_count": 18
          }
        ],
        "severities": [
          "warning"
        ],
        "source": "custom",
        "source_count": 18,
        "warning_count": 18
      },
      {
        "critical_count": 5,
        "info_count": 0,
        "items": [
          {
            "account_id": "************",
            "alert_details": [
              {
                "alertObject": "ins-********|MySQL",
                "status": "ALARM",
                "severity": "critical"
              }
            ],
            "critical_count": 5,
            "info_count": 0,
            "name": "MySQL CPU使用率告警",
            "severities": [
              "critical"
            ],
            "warning_count": 0
          }
        ],
        "severities": [
          "critical"
        ],
        "source": "tencent_cloud",
        "source_count": 5,
        "warning_count": 0
      }
    ],
    "statistics": {
      "ALARM": 23,
      "NO_DATA": 2,
      "OK": 175
    }
  }
}
```

## 错误处理

接口可能返回以下错误：

1. **服务器内部错误**：
   - 状态码：500
   - 响应体：`{"status": "error", "message": "服务器内部错误"}`
   - 原因：服务器处理请求时发生异常

2. **参数格式错误**：
   - 如果提供的时间参数格式不正确，将使用默认时间区间（当前时间的前15分钟到当前时间）

## 注意事项

1. 时间参数应使用 "YYYY-MM-DD HH:MM:SS" 格式
2. 如果未提供时间参数，将默认使用当前时间的前15分钟到当前时间
3. 告警对象（alertObject）会进行特殊字符处理，以确保格式一致性
4. 相同告警名称下的多条告警会被聚合，其详情会存储在 `alert_details` 数组中