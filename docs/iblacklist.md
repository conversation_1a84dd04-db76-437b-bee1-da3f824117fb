# 接口文档：IP 黑名单管理

**基础信息** 
- 基础路径: /waf/ipblacklist
- 请求方式: GET, POST
- 依赖模型: WafIpBlackList (来自 model.py)
- 鉴权方式: 无（假设已由蓝图统一处理）


## 1. 查询 IP 黑名单列表

### 请求方式
```
GET /waf/ipblacklist
```

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| [keyword](file:///Users/<USER>/Documents/repo/wegooooo-gitlab/devopscicd/blueprint/cicd.py#L0-L0) | string | 否 | 关键词搜索，支持 ip 地址、来源、备注字段模糊匹配 |
| `pageIndex` | int | 否 | 分页页码，默认为 1 |
| `pageSize` | int | 否 | 每页数量，默认为 20 |
| `n_status` | int | 否 | 状态过滤，0:启用中, 1:禁用, 2:过期 |

### 响应示例

```json
{
  "code": 200,
  "data": [
    {
      "n_id": 1,
      "c_ip_addr": "***********",
      "c_ip_source": "恶意扫描",
      "c_remark": "测试添加",
      "t_create_time": 1719417600,
      "n_status": 0
    }
  ],
  "totalCount": 1
}
```

### 排序规则
- 优先按状态排序：
  - 0（启用中） → 2（过期） → 1（禁用）
- 同一状态下按创建时间倒序排列

## 2. 新增或更新 IP 黑名单记录

### 请求方式
```
POST /waf/ipblacklist
```

### 请求体 (`application/json`)

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| `n_id` | int | 否 | 记录 ID，存在则更新，否则新增 |
| `c_ip_addr` | string | 是 | IP 地址 |
| `c_ip_source` | string | 否 | 来源描述 |
| `c_remark` | string | 否 | 备注信息 |
| `n_status` | int | 是 | 状态，0:启用中, 1:禁用, 2:过期 |

> 注：其他字段如 `t_create_time` 等自动填充，无需提供。

### 响应示例（成功）

```json
{
  "code": 200,
  "msg": "更新成功"
}
```

### 响应示例（失败）

```json
{
  "code": 500,
  "msg": "数据库错误，请稍后再试"
}
```

## 前端调用建议

### 使用 Axios 调用示例

```javascript
import axios from 'axios';

const instance = axios.create({
  baseURL: '/api', // 根据实际部署配置
  timeout: 5000,
});

// 获取黑名单列表
export const fetchIpBlacklist = (params) => {
  return instance.get('/waf/ipblacklist', { params });
};

// 新增/更新黑名单
export const saveIpBlacklist = (data) => {
  return instance.post('/waf/ipblacklist', data);
};
```

### Vue 组件调用示例

```vue
<template>
  <div>
    <input v-model="form.c_ip_addr" placeholder="IP地址">
    <input v-model="form.c_ip_source" placeholder="来源">
    <input v-model="form.c_remark" placeholder="备注">
    <select v-model="form.n_status">
      <option value="0">启用中</option>
      <option value="1">禁用</option>
      <option value="2">过期</option>
    </select>
    <button @click="submit">提交</button>
  </div>
</template>

<script>
import { saveIpBlacklist } from '@/api/ipblacklist';

export default {
  data() {
    return {
      form: {
        c_ip_addr: '',
        c_ip_source: '',
        c_remark: '',
        n_status: 0
      }
    };
  },
  methods: {
    async submit() {
      try {
        const res = await saveIpBlacklist(this.form);
        console.log(res.data.msg);
      } catch (error) {
        console.error('保存失败:', error);
      }
    }
  }
};
</script>
```