#!/bin/bash

# ActiveAlerts.vue 优化测试脚本
echo "🚀 启动开发服务器以测试 ActiveAlerts.vue 优化..."

# 检查 node_modules 是否存在
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动开发服务器
echo "🌐 启动开发服务器..."
echo "📋 测试清单："
echo "   1. 访问 http://localhost:8080/#/alertcenter"
echo "   2. 点击'实时告警'标签"
echo "   3. 检查表格列：级别(图标)、告警时间、对象、告警名称、操作"
echo "   4. 检查右侧饼图：告警对象统计"
echo "   5. 测试详情抽屉中的恢复功能"
echo ""
echo "🔧 优化内容："
echo "   ✅ 移除状态列和来源列"
echo "   ✅ 添加告警时间列(标准格式)"
echo "   ✅ 保留对象列显示"
echo "   ✅ 级别显示为彩色图标"
echo "   ✅ 恢复操作移至详情抽屉"
echo "   ✅ 按告警对象统计"
echo ""

npm run serve
