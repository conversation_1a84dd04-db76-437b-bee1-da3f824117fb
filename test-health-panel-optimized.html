<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HealthPanel 组件优化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e6e6e6;
        }
        .feature-list {
            margin: 20px 0;
        }
        .feature-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .feature-item.completed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .api-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>HealthPanel.vue 组件优化完成</h1>
            <p>集成真实API接口，保留模拟数据备用方案</p>
        </div>

        <div class="success">
            <h3>✅ 优化完成</h3>
            <p>HealthPanel.vue 组件已成功优化，现在支持真实API数据和模拟数据的无缝切换。</p>
        </div>

        <h2>实现的功能特性</h2>
        <div class="feature-list">
            <div class="feature-item completed">
                <strong>✅ 真实API集成</strong>
                <p>使用 <code>getAlertsCenterPanel</code> 函数调用 <code>/alertscenter/api/v1/panel</code> 接口</p>
            </div>
            
            <div class="feature-item completed">
                <strong>✅ 模拟数据保留</strong>
                <p>完整保留原有的模拟数据生成逻辑，作为备用方案</p>
            </div>
            
            <div class="feature-item completed">
                <strong>✅ 智能数据处理</strong>
                <p>API数据自动映射到组件数据结构，支持多种数据格式</p>
            </div>
            
            <div class="feature-item completed">
                <strong>✅ 错误处理机制</strong>
                <p>API调用失败时自动回退到模拟数据，确保用户体验</p>
            </div>
            
            <div class="feature-item completed">
                <strong>✅ 加载状态指示</strong>
                <p>数据获取过程中显示加载动画，提供用户反馈</p>
            </div>
            
            <div class="feature-item completed">
                <strong>✅ 数据源指示器</strong>
                <p>清晰显示当前使用的数据源：实时数据、模拟数据或回退数据</p>
            </div>
        </div>

        <h2>API接口信息</h2>
        <div class="api-info">
            <h4>接口详情</h4>
            <ul>
                <li><strong>API函数：</strong><code>getAlertsCenterPanel(params)</code></li>
                <li><strong>接口地址：</strong><code>/alertscenter/api/v1/panel</code></li>
                <li><strong>请求方法：</strong>GET</li>
                <li><strong>参数：</strong>date, start_time, end_time</li>
            </ul>
        </div>

        <h2>数据流程</h2>
        <div class="code-block">
1. 组件挂载 → 初始化模拟数据
2. 调用真实API → getAlertsCenterPanel()
3. API成功 → 处理真实数据 → 更新界面
4. API失败 → 显示错误提示 → 使用模拟数据
5. 数据源指示器显示当前状态
        </div>

        <h2>数据源状态</h2>
        <div class="feature-list">
            <div class="feature-item">
                <strong>🟢 实时数据 (api)</strong>
                <p>成功从API获取的真实数据</p>
            </div>
            
            <div class="feature-item">
                <strong>🟡 模拟数据 (fallback)</strong>
                <p>API调用失败时的备用数据</p>
            </div>
            
            <div class="feature-item">
                <strong>🔵 模拟数据 (mock)</strong>
                <p>开发环境或初始状态的模拟数据</p>
            </div>
        </div>

        <h2>关键代码修改</h2>
        <div class="code-block">
// 新增数据源相关字段
data() {
  return {
    isUsingRealData: false,
    dataSource: 'mock', // 'api' | 'mock' | 'fallback'
    apiError: null,
    // ... 其他字段
  }
}

// 优化后的数据获取方法
async fetchHealthData() {
  try {
    const response = await getAlertsCenterPanel(params)
    this.processApiData(response.data)
    this.dataSource = 'api'
  } catch (error) {
    this.initializeMockData()
    this.dataSource = 'fallback'
    this.$message.warning('无法获取实时数据，当前显示模拟数据')
  }
}
        </div>

        <h2>使用说明</h2>
        <div class="warning">
            <h4>⚠️ 注意事项</h4>
            <ul>
                <li>组件会在挂载时自动尝试获取真实数据</li>
                <li>如果API不可用，会自动回退到模拟数据</li>
                <li>数据源指示器会显示当前使用的数据类型</li>
                <li>所有原有功能（筛选、日期导航等）保持不变</li>
            </ul>
        </div>

        <div class="success">
            <h4>✅ 优化完成</h4>
            <p>HealthPanel.vue 组件现在可以：</p>
            <ul>
                <li>优先使用真实API数据</li>
                <li>在API不可用时无缝回退到模拟数据</li>
                <li>提供清晰的数据源指示</li>
                <li>保持所有原有功能的完整性</li>
            </ul>
        </div>
    </div>
</body>
</html>
