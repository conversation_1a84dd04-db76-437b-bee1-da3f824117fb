#!/bin/bash
# 定义 sourceDir 变量
sourceDir="$HOME/Documents/repo/gitee/wegoopsadmin/node_modules"

# 定位到目标目录
cd "$HOME/Documents/repo/gitlab/opsadmin_back" || exit

# 获取当前目录下的所有子文件夹名称
subFolders=$(find . -maxdepth 1 -type d)

# 遍历所有子文件夹
for folder in $subFolders; do
    # 检查文件夹是否在 sourceDir 下存在
    if [ -d "$sourceDir/$(basename "$folder")" ]; then
        echo "Deleting folder: $folder"
        rm -rf "$folder"
    else
        echo "Folder $folder does not exist in sourceDir, skipping."
    fi
done

echo "Script completed."
